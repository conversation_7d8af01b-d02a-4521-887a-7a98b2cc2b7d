stages:
  - build
  - deploy

variables:
  NODE_VERSION: "22"
  ARTIFACTS_DIR: "artifacts"
  SHORT_COMMIT: $CI_COMMIT_SHORT_SHA
  

before_script:
  - chcp 65001
  - $OutputEncoding = [System.Text.Encoding]::UTF8
  - $OutputEncoding = [Console]::OutputEncoding = [System.Text.Encoding]::UTF8

  - Invoke-Expression (& fnm env --use-on-cd | Out-String)

  - fnm use $NODE_VERSION
  - pnpm config set store-dir ${BUILD_DRIVE}pnpm-store

build_windows:
  stage: build
  script:
    - $time = $CI_COMMIT_TIMESTAMP -replace '\+.*$', ''
    - $time = $time -replace 'T', '_'
    - $time = $time -replace ':', ''
    - $time = $time -replace '-', ''
    - $sanitized = $env:CI_COMMIT_REF_NAME -replace '[\\\/:\*\?"<>|]', '_'
    - $filename = "teamAI-$time-$sanitized-$CI_COMMIT_SHORT_SHA.msix"
    - echo "filename=$filename" > build.env
    - echo $filename
    - echo "download_url=${env:HTTP_SERVER}builds/teamai/${filename}" > build.env
    # - exit(1)

    # - echo ${BUILD_PATH}edge
    - pnpm install --no-frozen-lockfile
    - echo "Building Windows package..."

    # update version
    - $manifestPath = '.\apps\edge\msix-build\AppxManifest_x64.xml'
    - "[xml]$xml = Get-Content $manifestPath"
    - 'Write-Host "App name: $($xml.Package.Identity.Version)"'
    - $xml.Package.Identity.Version = "1.0.0.$env:CI_PIPELINE_IID"
    - echo $xml.Package.Identity.Version 
    - $manifestFullPath = Join-Path $env:CI_PROJECT_DIR $manifestPath
    - echo $manifestFullPath
    - $xml.Save($manifestFullPath)
 
    - pnpm build:msix

    # - powershell -Command "& 'C:\\Program Files\\7-Zip\\7z.exe' a '$filename' '.\\apps\\edge\\dist\\win-unpacked\\*'" 
    - powershell -Command "Copy-Item -Path '.\\apps\\edge\\msix-out\\Lenovo Team AI_x64.msix' -Destination ${BUILD_DRIVE}web\\builds\\teamai\\${filename}"

  # variables:
  #   GIT_STRATEGY: none
  artifacts:
    reports:
      dotenv: build.env

deploy_notify:
  stage: deploy
  needs: [build_windows]
  variables:
    GIT_STRATEGY: none
  script:
    - echo "Version is $filename"
    - echo ${HTTP_SERVER}build/edge/${filename}

    - |
      $json = @{
        type = "message"
        attachments = @(
          @{
            contentType = "application/vnd.microsoft.card.adaptive"
            content = @{
              schema = "http://adaptivecards.io/schemas/adaptive-card.json"
              type = "AdaptiveCard"
              version = "1.5"
              comment = "$env:CI_COMMIT_MESSAGE"
              commiter = "$env:GITLAB_USER_NAME"
              job = "$env:CI_JOB_URL"
              url = "$download_url" 
              branch = "$env:CI_COMMIT_REF_NAME"
              commitid = "$env:CI_COMMIT_SHORT_SHA"
              body = @(
                @{
                  type = "TextBlock"
                  text = "自动部署通知"
                }
              )
            }
          }
        )
      } | ConvertTo-Json -Depth 5

      Invoke-RestMethod -Uri $EDGE_WEBHOOK_RECORD`
        -Method Post `
        -Body $json `
        -ContentType "application/json"

deploy-notify-teams:
  stage: deploy
  needs: [build_windows]
  variables:
    GIT_STRATEGY: none
  script:
    - chcp 65001
    - echo "Version is $download_url"

    - echo $env:CI_COMMIT_TIMESTAMP
    - $time = $CI_COMMIT_TIMESTAMP -replace '\+.*$', ''
    - $time = $time -replace 'T', ' '

    - |
      $json = @"
        {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "speak": "Version 2.2 performance optimization",
                        "body": [
                            {
                                "type": "ColumnSet",
                                "columns": [
                                    {
                                        "type": "Column",
                                        "width": "auto",
                                        "items": [
                                            {
                                                "type": "Icon",
                                                "name": "Branch",
                                                "horizontalAlignment": "Center",
                                                "size": "xSmall",
                                                "color": "Good"
                                            }
                                        ],
                                        "verticalContentAlignment": "Center"
                                    },
                                    {
                                        "type": "Column",
                                        "width": "stretch",
                                        "items": [
                                            {
                                                "color": "Default",
                                                "text": "[$env:CI_COMMIT_SHA]($env:CI_PROJECT_URL/-/commit/$env:CI_COMMIT_SHA)",
                                                "wrap": true,
                                                "spacing": "Small",
                                                "type": "TextBlock"
                                            }
                                        ],
                                        "spacing": "Small"
                                    }
                                ],
                                "spacing": "None"
                            },
                            {
                                "columns": [
                                    {
                                        "width": "auto",
                                        "items": [
                                            {
                                              "type": "Image",
                                              "url": "$ICON_EDGE",
                                              "size": "Small"
                                            }
                                        ],
                                        "type": "Column"
                                    },
                                    {
                                        "width": "stretch",
                                        "items": [
                                            {
                                                "size": "Large",
                                                "text": "$env:CI_COMMIT_MESSAGE",
                                                "weight": "Bolder",
                                                "wrap": true,
                                                "type": "TextBlock"
                                            }
                                        ],
                                        "verticalContentAlignment": "Center",
                                        "spacing": "Small",
                                        "type": "Column"
                                    }
                                ],
                                "spacing": "Small",
                                "type": "ColumnSet"
                            },
                            {
                                "type": "Table",
                                "columns": [
                                    {
                                        "width": 1
                                    },
                                    {
                                        "width": 2
                                    }
                                ],
                                "rows": [
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Committer",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "$env:GITLAB_USER_NAME",
                                                        "wrap": true
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            }
                                        ]
                                    },
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Date",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ]
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "$time",
                                                        "wrap": true
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Branch",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "ColumnSet",
                                                        "columns": [
                                                            {
                                                                "type": "Column",
                                                                "width": "stretch",
                                                                "items": [
                                                                    {
                                                                        "color": "Default",
                                                                        "text": "$env:CI_COMMIT_REF_NAME",
                                                                        "wrap": true,
                                                                        "spacing": "Small",
                                                                        "type": "TextBlock"
                                                                    }
                                                                ],
                                                                "spacing": "Small"
                                                            }
                                                        ],
                                                        "spacing": "Small"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            }
                                        ]
                                    },
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Url",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "ColumnSet",
                                                        "columns": [
                                                            {
                                                                "type": "Column",
                                                                "width": "stretch",
                                                                "items": [
                                                                    {
                                                                        "type": "TextBlock",
                                                                        "text": "$download_url",
                                                                        "wrap": true
                                                                    }
                                                                ],
                                                                "spacing": "Small"
                                                            }
                                                        ]
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ],
                                "firstRowAsHeaders": false,
                                "showGridLines": false
                            },
                            {
                                "actions": [
                                    {
                                        "title": "Code",
                                        "type": "Action.OpenUrl",
                                        "url": "$env:CI_PROJECT_URL/-/commit/$env:CI_COMMIT_SHA"
                                    },
                                    {
                                        "type": "Action.OpenUrl",
                                        "url": "$CI_PROJECT_URL/-/pipelines/$CI_PIPELINE_ID",
                                        "title": "Pipeline"
                                    },
                                    {
                                        "type": "Action.OpenUrl",
                                        "url": "$download_url",
                                        "title": "Download",
                                        "style": "positive"
                                    }
                                ],
                                "type": "ActionSet",
                                "spacing": "ExtraLarge"
                            }
                        ],
                        "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",
                        "version": "1.5"
                    }
                }
            ]
        }
      "@
      Write-Output $json
      # $json = $json -replace "PLACEHOLDER_COMMIT_HASH", "$env:CI_COMMIT_SHA"
      # $json = $json -replace "PLACEHOLDER_COMMIT_URL", "$env:CI_PROJECT_URL/-/commit/$env:CI_COMMIT_SHA"

      Invoke-RestMethod -Uri $EDGE_WEBHOOK_TEAMS -Method Post -Body $json -ContentType 'application/json'

   


deploy-notify-teams-on-failure:
  stage: deploy
  needs: [build_windows]
  variables:
    GIT_STRATEGY: none
  when: on_failure
  script:
    - chcp 65001

    - echo $env:CI_COMMIT_TIMESTAMP
    - $time = $CI_COMMIT_TIMESTAMP -replace '\+.*$', ''
    - $time = $time -replace 'T', ' '

    - |
      $json = @"
        {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "speak": "Version 2.2 performance optimization",
                        "body": [
                            {
                                "type": "ColumnSet",
                                "columns": [
                                    {
                                        "type": "Column",
                                        "width": "auto",
                                        "items": [
                                            {
                                                "type": "Icon",
                                                "name": "Branch",
                                                "horizontalAlignment": "Center",
                                                "size": "xSmall",
                                                "color": "Good"
                                            }
                                        ],
                                        "verticalContentAlignment": "Center"
                                    },
                                    {
                                        "type": "Column",
                                        "width": "stretch",
                                        "items": [
                                            {
                                                "color": "Default",
                                                "text": "[$env:CI_COMMIT_SHA]($env:CI_PROJECT_URL/-/commit/$env:CI_COMMIT_SHA)",
                                                "wrap": true,
                                                "spacing": "Small",
                                                "type": "TextBlock"
                                            }
                                        ],
                                        "spacing": "Small"
                                    }
                                ],
                                "spacing": "None"
                            },
                            {
                                "columns": [
                                    {
                                        "width": "auto",
                                        "items": [
                                            {
                                                "type": "Icon",
                                                "name": "HeartBroken",
                                                "horizontalAlignment": "Center",
                                                "color": "Attention",
                                                "style": "Filled",
                                                "size": "Medium"
                                            }
                                        ],
                                        "type": "Column"
                                    },
                                    {
                                        "width": "stretch",
                                        "items": [
                                            {
                                                "size": "Large",
                                                "text": "$env:CI_COMMIT_MESSAGE",
                                                "weight": "Bolder",
                                                "wrap": true,
                                                "type": "TextBlock"
                                            }
                                        ],
                                        "verticalContentAlignment": "Center",
                                        "spacing": "Small",
                                        "type": "Column"
                                    }
                                ],
                                "spacing": "Small",
                                "type": "ColumnSet"
                            },
                            {
                                "type": "Table",
                                "columns": [
                                    {
                                        "width": 1
                                    },
                                    {
                                        "width": 2
                                    }
                                ],
                                "rows": [
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Committer",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "$env:GITLAB_USER_NAME",
                                                        "wrap": true
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            }
                                        ]
                                    },
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Date",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ]
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "$time",
                                                        "wrap": true
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    {
                                        "type": "TableRow",
                                        "cells": [
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "TextBlock",
                                                        "text": "Branch",
                                                        "wrap": true,
                                                        "weight": "Bolder"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            },
                                            {
                                                "type": "TableCell",
                                                "items": [
                                                    {
                                                        "type": "ColumnSet",
                                                        "columns": [
                                                            {
                                                                "type": "Column",
                                                                "width": "stretch",
                                                                "items": [
                                                                    {
                                                                        "color": "Default",
                                                                        "text": "$env:CI_COMMIT_REF_NAME",
                                                                        "wrap": true,
                                                                        "spacing": "Small",
                                                                        "type": "TextBlock"
                                                                    }
                                                                ],
                                                                "spacing": "Small"
                                                            }
                                                        ],
                                                        "spacing": "Small"
                                                    }
                                                ],
                                                "verticalContentAlignment": "Center"
                                            }
                                        ]
                                    }
                                     
                                ],
                                "firstRowAsHeaders": false,
                                "showGridLines": false
                            },
                            {
                                "actions": [
                                    {
                                        "title": "Code",
                                        "type": "Action.OpenUrl",
                                        "url": "$env:CI_PROJECT_URL/-/commit/$env:CI_COMMIT_SHA"
                                    },
                                    {
                                        "type": "Action.OpenUrl",
                                        "url": "$CI_PROJECT_URL/-/pipelines/$CI_PIPELINE_ID",
                                        "title": "Pipeline"
                                    } 
                                ],
                                "type": "ActionSet",
                                "spacing": "ExtraLarge"
                            }
                        ],
                        "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",
                        "version": "1.5"
                    }
                }
            ]
        }
      "@
      Write-Output $json
      # $json = $json -replace "PLACEHOLDER_COMMIT_HASH", "$env:CI_COMMIT_SHA"
      # $json = $json -replace "PLACEHOLDER_COMMIT_URL", "$env:CI_PROJECT_URL/-/commit/$env:CI_COMMIT_SHA"

      Invoke-RestMethod -Uri $EDGE_WEBHOOK_TEAMS -Method Post -Body $json -ContentType 'application/json'

   