# ainow-ui
(文档)[https://km.xpaas.lenovo.com/pages/viewpage.action?pageId=459509655]

## 简介

安装依赖：

```
pnpm install
```

桌面端：

```
pnpm dev
pnpm build:win

web 版：

```
pnpm dev:web        # http://localhost:5173/  
pnpm build:web  
pnpm preview:web    # http://localhost:3000/
```

## 更新DB

- 更新 `packages\shared\src\prisma\schema.prisma`
- `pnpm init:db`
- 将打印结果中新的迁移名（类似 `20250312084952_init`）更新到 `packages\shared\src\utils\prisma\dbConstants.js`

# 功能
- [x] 设置页
- [ ] 聊天页
- [ ] API

# 配置
- [ ] 编译web端
- [ ] 服务端
- [x] 桌面端
- [x] 添加暗黑支持
- [ ] 添加环境变量
- [x] 添加多语言-渲染进程  
- [ ] 添加多语言-主进程
- [ ] 添加数据库SQLite3



# 接口
* ollama
* 云端
* 大模型
