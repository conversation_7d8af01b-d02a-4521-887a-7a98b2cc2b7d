import express from "express";
import url from "url";
import path from "path";
import fs from "fs";
import cors from "cors";
//import executeAinow from "../../packages/shared/src/AINowService"; // "ainow"; //
import { executeAinow } from "@ainow/services";
import type { AINowTypes } from "@ainow/shared";
const app = express();
const port = 10086;
app.use(cors());

const { ChatReq, AinowCommand, ChatResData } = AINowTypes
// console.log("executeAinow", executeAinow);

const chatReq: ChatReq = {
  command: AinowCommand.CHAT,
  plugInID: AinowCommand.PLUGINID,
  data: { query: "hello" },
};

// 流式 todo
app.get("/chat", function (req, res) {
  res.setHeader("Content-type", "text/event-stream");
  chatReq.data = { query: req.query.query as string };
  const result = {} as ChatResData; // 接口数据
  let collectAns = "";
  let count = 0;
  executeAinow(chatReq, (aires) => {
    const { done, intentCategory, chatAnswer, DeviceIntentContent } =
      aires.data;
    if (!done) {
      switch (intentCategory) {
        case "DEVICE_VANTAGE":
        case "DEVICE_WINDOWS":
          result.DeviceIntentContent = DeviceIntentContent;
          break;
        default:
      }
      // JSON.stringify({ ...aires.data, ...result })
      console.log("chatAnswer", done, "done", chatAnswer);
      //res.write(`${chatAnswer} done successfully.\n`);
      collectAns += chatAnswer;
    }
    // while (count < 10) {
    //   count++;
    //   console.log("count", count);
    //   setTimeout(() => {
    //     res.write(`${count} done successfully.\n`);
    //   }, count * 100);
    // }
  })
    .then((aires) => {
      result.chatAnswer = collectAns;
      // 返回内容
      res.end(JSON.stringify({ ...aires.data, ...result }));
    })
    .catch((err) => {
      res.statusCode = 403;
      res.end(`chat response err:${err}`);
    });
});

const server = app.listen(port, () => {
  console.log(server.address(), `Example app listening on port ${port}`);
});
