{"name": "@ainow/backend", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"dev": "pnpm copy --dev && cross-env NODE_ENV=development nodemon", "build": "pnpm copy && pnpm init-db && tsup src/index.ts", "copy": "node ./copyfiles.js", "init-db": "cross-env DATABASE_URL=\"file:../storage/ainow.db\" dotenv -e ./dist/.env.web -- prisma migrate dev --name init --schema ./dist/schema.prisma && dotenv -e ./dist/.env.web -- prisma generate --schema ./dist/schema.prisma", "lint": "eslint src --ext .ts", "test": "jest", "testserver": "ts-node ainow.ts"}, "author": "", "license": "ISC", "dependencies": {"@ainow/services": "workspace:*", "@ainow/shared": "workspace:*", "@ainow/types": "workspace:*", "@prisma/client": "catalog:@prisma/client", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "copyfiles": "^2.4.1", "cross-env": "7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prisma": "5.3.1", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tsup": "^8.4.0", "typescript": "^5.3.2"}}