import { Request, Response, NextFunction } from 'express'
import { AppError } from '../middleware/errorHandler'
import { executeAinow } from '@ainow/services'
import {
  PROVIDER,
  ChatReq,
  PLUGINID,
  AINOW_MODEL,
  ChatResData,
  IntentCategory,
  IChatResponseParams
} from '@ainow/types'

const {
  LLMResponse,
  streamChat: streamChatLLM,
  fetchOpenAICompatibilityStreamChat
} = require('@ainow/shared')

export class ChatController {
  constructor(/*private chatService = createChatService()*/) {}

  streamChat(req: Request, res: Response, next: NextFunction) {
    console.log(req.body, 'req.body')
    try {
      const { accountId, threadId, provider, model, message, messageId } = req.body

      res.setHeader('Cache-Control', 'no-cache')
      res.setHeader('Content-Type', 'text/event-stream')
      res.setHeader('Access-Control-Allow-Origin', '*')
      res.setHeader('Connection', 'keep-alive')
      res.flushHeaders()

      switch (provider) {
        case PROVIDER.EMBEDDING:
          fetchOpenAICompatibilityStreamChat('https://embedding.lenovo.com/v1/chat/completions', {
            ...req.body,
            callback: (chunk: any) =>
              res.write(`data: ${JSON.stringify({ id: messageId, chunk })}\n\n`),
            isDone: (chunk: string) => chunk === '[DONE]',
            doneCallback: (chunk: any) =>
              res.write(`data: ${JSON.stringify({ id: messageId, chunk })}\n\n`)
          })
          break
        case PROVIDER.DFAI:
          fetchOpenAICompatibilityStreamChat(
            'https://dfai.lenovo.com/e/e8i7uek48mlgr9bo/chat/completions',
            {
              ...req.body,
              callback: (chunk: any) =>
                res.write(`data: ${JSON.stringify({ id: messageId, chunk })}\n\n`),
              isDone: (chunk: string) => chunk === '[DONE]',
              doneCallback: (chunk: any) =>
                res.write(`data: ${JSON.stringify({ id: messageId, chunk })}\n\n`)
            }
          )
          break
        case PROVIDER.AINOW:
          const chatReq: ChatReq = {
            command: AINOW_MODEL.AINOW_MODEL_1,
            plugInID: PLUGINID.MAIN,
            data: { query: message }
          }
          const result = {} as ChatResData // 接口数据
          let collectAns = ''
          executeAinow(chatReq, (aires) => {
            const { done, intentCategory, chatAnswer, DeviceIntentContent } = aires.data
            if (!done) {
              switch (intentCategory) {
                case IntentCategory.DEVICE_VANTAGE:
                case IntentCategory.DEVICE_WINDOWS:
                  result.DeviceIntentContent = DeviceIntentContent
                  break
                default:
              }

              console.log('chatAnswer', done, 'done', chatAnswer)
              const chunk = JSON.stringify({
                textResponse: chatAnswer || '',
                close: done,
                provider,
                model,
                threadId
              })
              res.write(`data: ${JSON.stringify({ id: messageId, chunk })}\n\n`)

              collectAns += chatAnswer
            }
          })
            .then((aires) => {
              result.chatAnswer = collectAns
              // 返回内容
              res.end(JSON.stringify({ ...aires.data, ...result }))
            })
            .catch((err) => {
              res.statusCode = 403
              res.end(`chat response err:${err}`)
            })
          break
        case PROVIDER.OLLAMA:
        default:
          const myResponse = new LLMResponse()
          myResponse.on('data', (data: string) => {
            // if (data.includes('think')) console.log('THINKKKKK', data)
            try {
              const json = JSON.parse(data.replace(/^data:\s*/, ''))
              const chunk = JSON.stringify({ ...json, provider, model, threadId })

              if (!res.writableEnded) {
                // 发送SSE格式的数据
                // console.log('==※==', chunk)
                res.write(`data: ${JSON.stringify({ id: messageId, chunk })}\n\n`)
              }
              if (json.close) {
                console.log('==END==')
                res.end()
              }
            } catch (ex) {
              console.error(ex)
            }
          })
          streamChatLLM(message, {
            accountId,
            threadId,
            response: myResponse,
            chatProvider: provider,
            chatModel: model
          })
          break
      }
    } catch (error) {
      console.log(error)
      next(new AppError(String(error), 404))
    }
  }
}
