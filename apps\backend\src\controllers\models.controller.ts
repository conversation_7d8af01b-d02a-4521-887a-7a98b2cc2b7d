import { Request, Response, NextFunction } from 'express'
import { AppError } from '../middleware/errorHandler'
import sharedFacade, { type ISharedFacade } from '@ainow/shared'

const { listModels } = (sharedFacade as unknown as ISharedFacade).LLMModels

export class ModelsController {
  constructor() {}

  async getModels(req: Request, res: Response, next: NextFunction) {
    try {
      const { provider } = req.query

      if (!provider) {
        res.status(400).json({
          error: 'Invalid AI provider name'
        })
        return
      }

      const response = await listModels(provider as string)
      res.json(response)
    } catch (error) {
      next(new AppError('Failed to get examples', 500))
    }
  }
}
