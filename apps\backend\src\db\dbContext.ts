import path from 'node:path'
import type { IContextDB } from '@ainow/shared'

const isDev = process.env.NODE_ENV === 'development'

const distDir = isDev ? path.resolve(__dirname, '../../dist') : __dirname

export const dbContext: IContextDB = {
  getDBPath: () => path.resolve(__dirname, isDev ? '../..' : './', 'ainow.db'),
  getPrismaEnginesDir: () => distDir,
  getPrismaEnginesBase: () => './',
  getSchemaPrismaPath: () => path.resolve(distDir, 'schema.prisma'),
  getPrismaPath: () => (isDev ? void 0 : path.resolve(distDir, 'prisma/build/index.js')),
  getEnvPath: () => path.resolve(distDir, '.env.web')
}
