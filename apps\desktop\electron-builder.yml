# TODO 更新文档中的公司信息等
appId: 'com.electron.app'
productName: 'AI Now 2.0'
directories:
  buildResources: 'build'
  output: 'dist'
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - 'out/**/*'
  - 'package.json'
  - 'resources/**/*'
  - "!**/*.map"
  - "!**/__tests__/**"
  - '../../packages/shared/node_modules/prisma/**/*'
  - '../../packages/shared/node_modules/@prisma/engines/**/*'
  - '!../../packages/shared/node_modules/prisma/*.db'
  # @prisma is not needed in the packed app unless using prisma migrate
  - '!**/node_modules/@prisma/engines/introspection-engine*'
  - '!**/node_modules/@prisma/engines/schema-engine*'
  - '!**/node_modules/@prisma/engines/prisma-fmt*'
  - '!**/node_modules/@prisma/engines/query_engine-*'
  - '!**/node_modules/@prisma/engines/libquery_engine*'
  - '!**/node_modules/prisma/query_engine*'
  - '!**/node_modules/prisma/libquery_engine*'
  - '!**/node_modules/prisma/**/*.mjs'
asarUnpack:
  - resources/**
extraResources: # Only if you need to run prisma migrate
  - '../../packages/shared/node_modules/@prisma/engines/**/*'
  - from: ../../packages/shared/node_modules/@prisma/engines/schema-engine-windows.exe
    to: prisma/schema-engine-windows.exe
  - from: ../../packages/shared/node_modules/@prisma/engines/query_engine-windows.dll.node
    to: prisma/query_engine-windows.dll.node
  - from: ../../packages/shared/src/prisma/schema.prisma
    to: prisma/schema.prisma
  - from: ../../packages/shared/src/prisma/.env
    to: prisma/.env
  - from: ../../packages/shared/src/prisma/migrations
    to: prisma/migrations
  - from: ../../packages/shared/dist/storage
    to: storage
  - from: ../../packages/services/assets/tools/ainow.node
    to: tools/ainow.node
  - from: ../../packages/services/assets/tools/ainow.dll
    to: tools/ainow.dll
  - from: ../../packages/shared/src/prisma/ainow.db.tmp
    to: prisma/ainow.db.tmp
win:
  icon: build/icon.ico
  executableName: AI Now 2.0
  asarUnpack:
    - node_modules/prisma
    - node_modules/@prisma
    - resources/**
  #   - ../../packages/shared/node_modules/prisma
  #   - ../../packages/services/assets/tools
nsis:
  artifactName: '${productName} Setup ${version}.${ext}'
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac: null
dmg:
  artifactName: ${name}-${version}.${ext}
linux: null
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
