{"name": "@ainow/desktop", "version": "1.0.0", "description": "ainow build with electron", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "chcp 65001 && electron-vite dev -- --watch", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "rimraf dist && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false pnpm run build && electron-builder --win --dir", "build:installer": "electron-builder --win --prepackaged ./dist/win-unpacked", "build:win": "electron-builder --win", "build:mac": "pnpm run build && electron-builder --mac", "build:linux": "pnpm run build && electron-builder --linux", "msix": "node msix-build/build-msix.cjs", "build:msix": "rimraf msix-out && rimraf out && rimraf dist && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false pnpm run build && electron-builder --dir --win && pnpm msix", "dev:web": "vite", "build:web": "vite build"}, "dependencies": {"@ainow/services": "workspace:*", "@ainow/shared": "workspace:*", "@ainow/types": "workspace:*", "@ainow/msix": "workspace:*", "@ant-design/icons-vue": "^7.0.1", "@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@langchain/core": "^0.3.39", "@langchain/ollama": "^0.1.5", "@langchain/openai": "^0.4.3", "@prisma/client": "5.3.1", "@tailwindcss/postcss": "^4.0.6", "@xitanggg/node-selection": "^1.3.0", "@xitanggg/node-selection-win32-x64-msvc": "^1.3.0", "ant-design-vue": "~4.2.6", "axios": "^1.8.3", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "electron-is-dev": "^3.0.1", "electron-localshortcut": "^3.2.1", "electron-log": "^5.3.0", "electron-updater": "^6.1.7", "events": "^3.3.0", "form-data": "^4.0.2", "fuse.js": "^7.1.0", "he": "^1.2.0", "highlight.js": "^11.11.1", "i18n": "^0.15.1", "langchain": "^0.3.15", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-code-copy": "^0.2.1", "markdown-it-katex": "^2.0.3", "node-api-dotnet": "^0.8.19", "node-selection": "0.2.0-alpha.0", "pinia": "^2.3.1", "readline": "^1.3.0", "regedit": "^5.1.3", "request": "^2.88.2", "rimraf": "^5.0.5", "tailwindcss": "^4.0.6", "tiny-pinyin": "^1.3.2", "truncate": "^3.0.0", "uiohook-napi": "^1.5.4", "uuid": "^11.0.5", "vite-plugin-svg-icons": "^2.0.1", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0", "vue3-lottie": "^3.3.1"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.10.3", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "autoprefixer": "^10.4.20", "cross-env": "7.0.3", "electron": "34.2.0", "electron-builder": "26.0.11", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.26.0", "less": "^4.2.2", "postcss": "^8.5.2", "prettier": "^3.3.2", "prisma": "5.3.1", "typescript": "^5.5.2", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^5.3.1", "vue": "^3.4.30", "vue-tsc": "^2.0.22"}}