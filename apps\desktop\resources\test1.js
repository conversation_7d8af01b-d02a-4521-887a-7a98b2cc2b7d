const path = require('node:path')
const addon = require('./stt/speech')
const readline = require('readline')

const nodePath = 'C:\\Users\\<USER>\\my_work\\ainow-ui\\apps\\desktop\\resources\\stt'
addon.loadDLL(nodePath)
// addon.loadDLL('.\\stt')

const sttPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\@ainowdesktop\\resources\\sttmodels'

console.log(addon.debug(sttPath)) // true or false

addon.startSTT(sttPath, (type, text) => {
  console.log('node Received STT:', type, ' : ', text)
})

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

rl.question('请输入一些内容：', (userInput) => {
  console.log('你输入的是：' + userInput)
  rl.close()
  addon.stopSTT()
})

console.debug('end')
