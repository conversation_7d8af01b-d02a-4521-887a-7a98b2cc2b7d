// TODO 不要和 main/preload/renderer并列
import path from 'path'
import { fileURLToPath } from 'url'
import { app } from 'electron'
export function getResourcesPath(fullPath: string): string {
  return fullPath.replace('app.asar', 'app.asar.unpacked')
}
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
export const getMicroPath = () => path.join(app.getPath('userData'), 'AINowPlugins')
export const getToolPath = () =>
  getResourcesPath(path.join(__dirname, '../..', import.meta.env.MAIN_VITE_TOOLS_PATH))

export const getPluginPath = () =>
  getResourcesPath(path.join(__dirname, '../..', import.meta.env.MAIN_VITE_PLUGIN_PATH))

export const GlobalConfig = {
  packageName: 'package.json',
  downloadDir: path.join(getMicroPath(), '/downloads'),
  downloadTempDir: path.join(getMicroPath(), '/downloads', '/temp'),
  installPath: '',
  isLogin: false,
  isTest: false
}
