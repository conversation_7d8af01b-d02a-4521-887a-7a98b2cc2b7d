import { BrowserWindow, ipcMain } from 'electron'
import { is } from '@electron-toolkit/utils'
import { join } from 'path'
import WindowManager from './utils/WindowManager'

let localChatWin: BrowserWindow
const winMgr = new WindowManager()

export function createLocalChat(): BrowserWindow {
  if (localChatWin && !localChatWin.isDestroyed()) {
    localChatWin.show()
  } else {
    localChatWin = new BrowserWindow({
      width: 500,
      height: 500,
      show: false, // 初始隐藏
      frame: false, // 去除最大化，最小化和关闭
      autoHideMenuBar: true,
      skipTaskbar: true, // 任务栏不显示
      resizable: false, // 窗口不可调整大小
      webPreferences: {
        preload: join(__dirname, '../preload/index.js'),
        sandbox: false,
        additionalArguments: ['LocalChat']
      }
    })
    localChatWin.setParentWindow(global.mainWindow)
    localChatWin.on('ready-to-show', () => {
      if (localChatWin) {
        if (localChatWin.isMinimized()) {
          localChatWin.restore()
        }
        localChatWin.show()
      }
    })
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      localChatWin.loadURL(`${process.env['ELECTRON_RENDERER_URL']}`).catch(console.error)
    } else {
      localChatWin.loadFile(join(__dirname, '../renderer/index.html')).catch(console.error)
    }
  }
  winMgr.register(localChatWin)
  // 处理自定义标题栏的窗口操作
  ipcMain.on('close-local-chat', () => {
    localChatWin.close()
  })
  localChatWin.on('closed', () => {
    localChatWin.removeAllListeners()
  })
  return localChatWin
}
