import { shell, BrowserWindow } from 'electron'
import { is } from '@electron-toolkit/utils'
import { join } from 'path'
//import icon from '../../resources/icon.png?asset'
import { handleScheme } from './utils/auth'
import {
  MAIN_WINDOW_HEIGHT,
  MAIN_WINDOW_WIDTH,
  MINI_WINDOW_HEIGHT,
  MINI_WINDOW_WIDTH
} from './utils/constants'
import WindowManager from './utils/WindowManager'

const winMgr = new WindowManager()

export async function createMainWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: MAIN_WINDOW_WIDTH,
    height: MAIN_WINDOW_HEIGHT,
    minWidth: MINI_WINDOW_WIDTH,
    minHeight: MINI_WINDOW_HEIGHT,
    show: false,
    autoHideMenuBar: true,
    useContentSize: true,
    frame: false,
    // icon,
    title: 'mainwindow',
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })
  global.mainWindow = mainWindow
  winMgr.register(mainWindow)

  mainWindow.on('ready-to-show', () => {
    const args = process.argv.slice(1)
    // console.log(process.argv, args, 'process.argv')
    // 如果是开机自启 则不显示窗口
    if (args.includes('--autoRun')) {
      console.log('开机自启')
    } else {
      console.log('非开机自启')
      mainWindow.show() //
    }
    //mainWindow.show()
    // 监听首次打开时 数据处理 window平台
    handleScheme()
    // 监听 mac平台
  })
  //mainWindow.webContents.openDevTools()

  mainWindow.on('close', (e) => {
    if (!global.isQuitFromTray) {
      e.preventDefault() // 阻止退出程序
      mainWindow.setSkipTaskbar(true) // 取消任务栏显示
      mainWindow.hide() // 隐藏主程序窗口
    }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
    // mainWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/FloatingBall`)
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    // mainWindow.loadFile(join(__dirname, '../renderer/index.html'), { hash: 'FloatingBall' })
  }

  return mainWindow
}
