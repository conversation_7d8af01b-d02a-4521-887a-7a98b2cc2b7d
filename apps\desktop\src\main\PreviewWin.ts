import { BrowserWindow, ipcMain } from 'electron'
import WindowManager from './utils/WindowManager'

let previewWin: BrowserWindow
const winMgr = new WindowManager()

export function createPreviewWin(previewPath: string): BrowserWindow {
  if (previewWin && !previewWin.isDestroyed()) {
    previewWin.show()
  } else {
    previewWin = new BrowserWindow({
      width: 500,
      height: 500,
      show: false, // 初始隐藏
      autoHideMenuBar: true,
      skipTaskbar: true, // 任务栏不显示
      resizable: false, // 窗口不可调整大小
      minimizable: false
    })
    previewWin.setParentWindow(global.mainWindow)
    previewWin.on('ready-to-show', () => {
      if (previewWin) {
        if (previewWin.isMinimized()) {
          previewWin.restore()
        }
        previewWin.show()
      }
    })
  }
  previewWin.loadURL(previewPath).catch(console.error)
  winMgr.register(previewWin)
  // 处理自定义标题栏的窗口操作
  ipcMain.on('close-local-chat', () => {
    previewWin.close()
  })
  previewWin.on('closed', () => {
    previewWin.removeAllListeners()
  })
  return previewWin
}
