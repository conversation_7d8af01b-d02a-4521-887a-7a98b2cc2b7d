// @ts-nocheck
// import { BroadcastMsg } from '@/types'
import EventBus from '../../utils/EventBus'
import { getToolPath } from '../../../config'
import { v4 as uuidV4 } from 'uuid'
// import { <PERSON><PERSON>erWindow } from 'electron'
import path from 'path'
import { BroadcastCB, BroadcastChannel } from '../../../types/Broadcast'
// import { getRegeditValue } from '../RegeditService'
// eslint-disable-next-line @typescript-eslint/no-var-requires
const SystemUtils = require(path.join(getToolPath(), '/SystemUtils.node'))

export class BroadcastMsg<T extends Record<string, unknown> | string = Record<string, unknown>> {
  constructor(
    Params: Partial<BroadcastMsg<T>> &
      Pick<BroadcastMsg<T>, 'Data' | 'MessageType' | 'MessageDestination'>
  ) {
    this.Data = Params.Data
    this.MessageType = Params.MessageType
    this.MessageID = uuidV4()
    this.MessageSource = Params.MessageSource || 8
    this.MessageDestination = Params.MessageDestination
    this.Timestamp = Params.Timestamp || Date.now()
    this.ErrorCode = this.ErrorCode = Params.ErrorCode || 0
    this.ErrorMessage = Params.ErrorMessage || ''
  }
  Data: T
  MessageType: BroadcastChannel
  MessageSource: number
  MessageDestination: number
  Timestamp: number
  MessageID: string
  ErrorCode: number
  ErrorMessage: string
}
class PipeClient extends EventBus {
  constructor() {
    super()
    this.initPipeClient()
  }
  pipeClient?: {
    CallMethod: (method: string, params: string, timeout: number) => string
    Connect: (method: string) => void
    RegisterHandler: (method: string, cb: (reply: string) => void) => void
  }
  pipeClientLoading: boolean = false
  async initPipeClient() {
    let time = 0
    this.pipeClientLoading = true
    try {
      // const HKEY = {
      //   HKEY_CLASSES_ROOT: 0,
      //   HKEY_CURRENT_USER: 1,
      //   HKEY_LOCAL_MACHINE: 2,
      //   HKEY_USERS: 3
      // }
      // const installPath = SystemUtils.GetRegeditValue(
      //   HKEY.HKEY_LOCAL_MACHINE,
      //   'SOFTWARE\\Lenovo\\Lenovo AI Now',
      //   'InstallPath',
      //   ''
      // )
      // console.log('installPath:', installPath)
      // const values = Object.values(regeditValues).map((val) => val.values)
      // const InstallPath = values[0].InstallPath.value as string
      // console.log('InstallPath', regeditValues)

      this.pipeClient = new SystemUtils.PipeClient(path.join(getToolPath(), '/IpcClient.dll'))
      const ret = this.pipeClient?.Connect('LENOVO_AINOW_SERVICE')
      //console.log('Connect:', ret)
      if (!ret) {
        throw new Error('pipe connect fail')
      }
      this.pipeClientLoading = false
      this.pipeClient?.RegisterHandler('BroadcastMessage', (reply) => {
        try {
          const msg = JSON.parse(reply) as BroadcastMsg
          console.log('BroadcastMessage', msg, reply)

          if (msg.ErrorCode) {
            return
          }
          msg.MessageDestination === 8 && this.emit(msg.MessageType.toString(), msg)
        } catch (error) {
          console.log('----error', error)
        }
      })
    } catch (error) {
      if (time < 5) {
        setTimeout(() => {
          time++
          //console.log('retry connect ' + time)
          this.initPipeClient()
        }, 10000)
      } else {
        //console.log('stop retry connect ')
      }
      //console.log(error)
      this.pipeClientLoading = false
    }
  }
  getPipeClient() {
    if (!this.pipeClient) {
      this.initPipeClient()
      console.log('no pipe client initializing')
    } else {
      return this.pipeClient
    }
  }
  sendMessage<T extends Record<string, unknown>>(msg: BroadcastMsg<T>) {
    try {
      this.getPipeClient()?.CallMethod('BroadcastMessageByServer', JSON.stringify(msg), 3000)
    } catch (error) {
      console.log(error)
    }
  }
  async getToken() {
    let token = this.getPipeClient()?.CallMethod('GetLIDWithToken', JSON.stringify({}), 3000)
    try {
      if (typeof token === 'string' && token !== '') {
        token = JSON.parse(token).data.ainowToken
      }
    } catch (error) {
      console.error(error)
    }
    return token
  }
  listenBroadcast<T extends BroadcastChannel>(msgType: T, cb: BroadcastCB<T>) {
    this.on(msgType.toString(), cb)
  }
}

export default new PipeClient()
