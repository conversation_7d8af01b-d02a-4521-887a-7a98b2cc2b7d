/* eslint-disable @typescript-eslint/no-var-requires */
import { GlobalConfig, getToolPath } from '../../../config'
import { <PERSON><PERSON>erWindow } from 'electron'
import path from 'path'
import { map, ACTION_TYPE } from '../../../types'
import PipeClient from './PipeClient'
import { openApp } from '../BaseService'
const SystemUtils = require(path.join(getToolPath(), '/SystemUtils.node'))
export function getInstallPath() {
  const HKEY = {
    HKEY_CLASSES_ROOT: 0,
    HKEY_CURRENT_USER: 1,
    HKEY_LOCAL_MACHINE: 2,
    HKEY_USERS: 3
  }
  let installPath = ''

  installPath = SystemUtils.GetRegeditValue(
    HKEY.HKEY_LOCAL_MACHINE,
    'SOFTWARE\\Lenovo\\Lenovo AI Now',
    'InstallPath',
    ''
  )
  console.log('installPath:', installPath)
  GlobalConfig.installPath = installPath
  console.log(GlobalConfig.installPath)

  // return installPath
}
// export function getLid() {
//   const HKEY = {
//     HKEY_CLASSES_ROOT: 0,
//     HKEY_CURRENT_USER: 1,
//     HKEY_LOCAL_MACHINE: 2,
//     HKEY_USERS: 3
//   }
//   const Lid = SystemUtils.GetRegeditValue(
//     HKEY.HKEY_CURRENT_USER,
//     'SOFTWARE\\Lenovo\\Lenovo AI Now\\MainSetting',
//     'CurrentLid',
//     ''
//   )

//   return Lid
// }
function ainowHooks() {
  console.log('ainowHooks')

  const startAINow = async (installPath: string): Promise<boolean> => {
    const ProcessName = 'Lenovo AINow Mini.exe'
    let ret = SystemUtils.IsProcessExist(ProcessName)
    console.log('IsProcessExist ret:', ret)
    const wait = (ms: number) => {
      return new Promise((resolve) => {
        setTimeout(resolve, ms)
      })
    }
    if (!ret) {
      ret = SystemUtils.StartProcess(installPath, installPath + ProcessName, '')
      console.log('StartProcess ret:', ret)

      if (!ret) {
        console.error('StartProcess failed ret:', ret)
        return false
      }

      ret = SystemUtils.IsProcessExist(ProcessName)
      for (let i = 0; i < 30; i++) {
        await wait(1000)
        ret = SystemUtils.IsProcessExist(ProcessName)
        console.error('IsProcessExist ret:', ret)
        if (ret) {
          return true
        }
      }
    }

    return true
  }
  const initAINow = async (): Promise<boolean> => {
    return startAINow(GlobalConfig.installPath)
  }

  const sendWindowMessage = async (
    win: BrowserWindow,
    { ChatText = 'this is question', type }
  ): Promise<boolean> => {
    const { x, y } = win.getBounds()
    const obj = map.get(type)
    const msg = {
      ChatType: obj!.ChatType, //必须 explain+cloudask32 其余都是--0
      AskFiles: [], //附带的文件 可以为空
      IsPopupOpen: false, //可以不加
      AskType: obj!.AskType, //问的类型  本地search--0 本地ask--1 Explain--2  Translate--3 Polish--4 Summarize--5 Cloud提问--6 pc-assistant--7
      IsLocateCursor: false, //小窗是否跟随鼠标 true 跟随 false 不跟随
      Location: { X: x, Y: y }, //小窗启动位置
      ChatText,
      Intention: obj!.Intention, //如果是翻译的话直接写WORK_ASSISTANT_TRANSLATION就行，localask是GENERAL_GENERATION，如果是其他功能就不用管这个，可以为空
      AdditionalWords: type === ACTION_TYPE.MINITRANSLATE ? ChatText : '' //划词，如果没有划词，可以为空
    }
    console.log('ACTION_TYPE', type)
    console.log('minisendmsg:', msg)
    /**Intention：
     * Search File: WORK_ASSISTANT_DOCUMENT_SEARCH
     * Summary: WORK_ASSISTANT_DOCUMENT_SUMMARY
     * Polish: WORK_ASSISTANT_DOCUMENT_POLISHING
     */
    const result = await initAINow()
    if (!result) {
      return false
    }
    const ret = SystemUtils.SendWindowMessageByClipboard(
      'AINowMiniMessageWindow',
      JSON.stringify(msg)
    )
    console.log('SendWindowMessageByClipboard ret:', ret)
    return ret
  }
  const openSystemSettings = (cmd = 'ms-settings:privacy-eyetracker') => {
    //https://learn.microsoft.com/en-us/windows/uwp/launch-resume/launch-settings-app
    const ret = SystemUtils.OpenSystemSettings(cmd)
    console.log('OpenSystemSettings ret:', ret)
    return ret
  }

  // const startAinowPipe = () => {
  //   const pipeClient = new SystemUtils.PipeClient(
  //     path.join(getResourcesPath(getToolPath()), '/IpcClient.dll')
  //   )
  //   const ret = pipeClient.Connect('LENOVO_AINOW_SERVICE')
  //   console.log('Connect:', ret)
  //   pipeClient.RegisterHandler('ServiceStatusChanged', function (reply, context) {
  //     console.log('======================js RegisterHandler ServiceStatusChanged:', reply)
  //   })
  //   console.log('RegisterHandler:', ret)

  //   pipeClient.RegisterHandler('BroadcastMessage', function (reply, context) {
  //     console.log('----------BroadcastMessage:', reply)
  //   })
  //   // let index = 0
  //   // setInterval(() => {
  //   //   pipeClient.CallMethod('BroadcastMessageByServer', JSON.stringify({ test: '222233' }), 3000)
  //   //   console.log('CallMethod BroadcastMessageByServer:', ret, ',index:', index)
  //   //   index++
  //   // }, 2000)
  // }
  // startAinowPipe()
  const openAinow = async () => {
    getInstallPath()
    const ProcessName = 'Lenovo AINow.exe'
    const installPath = GlobalConfig.installPath
    openApp(installPath + ProcessName)
  }
  return {
    PipeClient,
    sendWindowMessage,
    openSystemSettings,
    openAinow
  }
}
export default ainowHooks()
