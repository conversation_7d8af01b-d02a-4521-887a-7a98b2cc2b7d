import { BrowserWindow, screen } from 'electron'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'
import AINowService from '../AINowService'
import { getAinowConfig } from '../BaseService'
import { BroadcastMsg } from '../AINowService/PipeClient'

export function createFloatingBall(): BrowserWindow {
  // 设定悬浮球尺寸
  const desiredPhysicalSize = 52

  const floatingBall = new BrowserWindow({
    width: 62,
    height: 62,
    title: 'floatingball',
    show: false, // 初始隐藏
    frame: false, // 无边框
    alwaysOnTop: true, // 保持窗口在最上层
    transparent: true, // 透明窗口
    skipTaskbar: true, // 任务栏不显示
    resizable: false, // 窗口不可调整大小
    movable: true, // 窗口不可拖拽
    webPreferences: {
      preload: join(__dirname, '../preload/SearchBar.js'),
      sandbox: false,
      additionalArguments: ['Floating']
    }
  })

  floatingBall.setAlwaysOnTop(true, 'screen-saver')
  //   floatingBall.shadow = true
  //   floatingBall.setHasShadow(true)

  // 加载渲染器
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    console.log('加载 URL:', `${process.env['ELECTRON_RENDERER_URL']}/#/FloatingBall`)
    floatingBall
      .loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/FloatingBall`)
      .catch(console.error)
  } else {
    floatingBall
      .loadFile(join(__dirname, '../renderer/index.html'), { hash: 'FloatingBall' })
      .catch(console.error)
  }
  const BroadcastMsgSend = new BroadcastMsg({
    Data: { Status: false },
    MessageType: 4099,
    MessageSource: 8,
    MessageDestination: 1
  })
  async function toggleFloatingBallVisibility(floatingBall: BrowserWindow) {
    const config = await getAinowConfig()
    console.log(config.AINowBallSwitch, '-----------------------------configMsg')
    if (config.AINowBallSwitch === 'True') {
      floatingBall.show()
    } else {
      BroadcastMsgSend.Data.Status = false
      floatingBall.hide()
    }
    AINowService.PipeClient.sendMessage(BroadcastMsgSend)
  }

  // 监听ainow登录的状态~
  //   function monitorLoginStatus(floatingBall: BrowserWindow) {
  //     const interval = setInterval(async () => {
  //       const configLogin = await updateGlobalConfig()
  //       if (!configLogin.isLogin) {
  //         floatingBall.hide()
  //       }
  //     }, 2000)

  //     // 窗口关闭时清除定时器
  //     floatingBall.on('closed', () => {
  //       clearInterval(interval)
  //     })
  //   }

  // 监听 `ready-to-show` 事件
  floatingBall.once('ready-to-show', async () => {
    const config = await getAinowConfig()
    //console.log('config--------------------------', config)
    if (config.IsLogin === 'True') {
      if (config.AINowBallSwitch === 'True') floatingBall.show()
    } else {
      floatingBall.hide()
    }

    // monitorLoginStatus(floatingBall)
    // 获取当前屏幕的大小和缩放比例
    const updatedDisplay = screen.getPrimaryDisplay()
    const { width, height } = updatedDisplay.workAreaSize
    const updatedScaleFactor = updatedDisplay.scaleFactor
    // 重新计算窗口尺寸
    const updatedWindowWidth = Math.round(desiredPhysicalSize * updatedScaleFactor)
    const updatedWindowHeight = Math.round(desiredPhysicalSize * updatedScaleFactor)

    // 计算悬浮球的位置
    const x = Math.round(width - (updatedWindowWidth + 15)) // 靠右，15px 偏移
    const y = Math.round(height - 324 - updatedWindowHeight) // 距离底部 324px

    // 设置窗口位置和大小
    floatingBall.setBounds({
      x: x,
      y: y
    })
    AINowService.PipeClient.listenBroadcast(4098, async (msg) => {
      console.log(msg, '4098------------------------------')
      await toggleFloatingBallVisibility(floatingBall)
    })

    AINowService.PipeClient.listenBroadcast(4097, async (msg) => {
      console.log(msg, '4097------------------------------')
      if (msg.Data.IsActive) {
        BroadcastMsgSend.Data.Status = false
        floatingBall.show()
      } else {
        BroadcastMsgSend.Data.Status = true
        floatingBall.hide()
      }
      AINowService.PipeClient.sendMessage(BroadcastMsgSend)
    })

    AINowService.PipeClient.listenBroadcast(4100, async (msg) => {
      console.log('4100------------------------------', msg)
      const config = await getAinowConfig()
      if (msg.Data.LoginStatus && config.AINowBallSwitch === 'True') {
        floatingBall.show()
        BroadcastMsgSend.Data.Status = false
      } else {
        BroadcastMsgSend.Data.Status = true
        floatingBall.hide()
      }
      AINowService.PipeClient.sendMessage(BroadcastMsgSend)
    })
  })

  // 监听显示器参数变化
  screen.on('display-metrics-changed', () => {
    floatingBall.setBounds({
      width: 62,
      height: 62
    })
  })

  return floatingBall
}
