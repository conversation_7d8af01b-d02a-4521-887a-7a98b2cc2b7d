// @ts-nocheck
import { BrowserWindow, ipcMain, Menu, MenuItemConstructorOptions, screen } from 'electron'
import { IPCRes } from '../../../types'
import { getMoveWinFunc } from '../BaseService'
import AINowService from '../AINowService'
import { BroadcastMsg } from '../AINowService/PipeClient'

export function FloatingRegister(win: BrowserWindow) {
  // 统一返回 IPC 响应的结构
  function handleIpcResponse<T>(success: boolean, res?: T): IPCRes<T> {
    const timestamp = Date.now()
    return { success, res, timestamp }
  }
  // 监听 "show-context-menu" 消息，显示右键菜单
  ipcMain.on('show-context-menu', (event) => {
    const template: MenuItemConstructorOptions[] = [
      { label: 'Open AI Now', click: () => event.sender.send('menu-item-clicked', 'Open AI Now') },
      //   { label: 'Settings', click: () => event.sender.send('menu-item-clicked', 'Settings') },
      { label: 'Hide Widget', click: () => event.sender.send('menu-item-clicked', 'Hide Widget') }
    ]

    const menu = Menu.buildFromTemplate(template)

    // 获取屏幕边界和悬浮球窗口位置
    const { width: screenWidth } = screen.getPrimaryDisplay().workAreaSize
    const { x, y, width } = win.getBounds()

    let popupX = 0
    let popupY = -80
    // 判断靠右边
    if (x + width >= screenWidth - 62) {
      popupX = -10 // 菜单向左偏，防止出界
      popupY = -25
    }
    // 默认居中
    else {
      popupX = -22
    }

    menu.popup({
      window: win,
      x: popupX,
      y: popupY
    })
  })

  // 监听 "hide-window" 消息，隐藏窗口
  ipcMain.on('hide-window', () => {
    win.hide()
    const msg = new BroadcastMsg({
      Data: { Status: true }, // 消息的具体数据
      MessageType: 4099, // 消息类型
      MessageDestination: 8
    })
    AINowService.PipeClient.sendMessage(msg)
  })

  ipcMain.on('open-ainow', () => {
    AINowService.openAinow()
  })

  // 移动窗口
  const moveWin = getMoveWinFunc(win)
  ipcMain.on('window:move-drag', (_, canMoving) => {
    moveWin.execFunc(canMoving)
  })
}
