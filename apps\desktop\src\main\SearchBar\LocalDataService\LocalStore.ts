// @ts-nocheck
export class LocalStore<T extends Record<string, unknown> & { id: string; keywords: string[] }> {
  constructor(name: string) {
    this.storeName = name
  }
  storeName: string

  store: T[] = []
  initData(items: T[]) {
    this.store = items
  }
  appendData(item: T) {
    this.store.push(item)
  }
  concatData(items: T[]) {
    this.store = this.store.concat(items)
  }
  // getAllStore(){
  //     return this.store
  // }
  // deleteStoreItem(id:string){
  //    const i= this.store.findIndex(item=>item.id==id)
  // this.store.splice(i,1)
  // }
  // getStoreItem(id:string){
  //    const i= this.store.findIndex(item=>item.id==id)
  //     return this.store[i]
  // }
  // setStoreItem(id:string){
  //    const i= this.store.findIndex(item=>item.id==id)
  //     return this.store[i]
  // }
}

export default function initStore<
  T extends Record<string, unknown> & { id: string; keywords: string[] }
>(name: string, data: T[]) {
  const store = new LocalStore<T>(name)
  store.initData(data)
  return store
}
export function pushStore<T extends Record<string, unknown> & { id: string; keywords: string[] }>(
  store: LocalStore<T>,
  item: T
) {
  //console.log(item, 'pushStore')
  store.concatData(item)
}
