// @ts-nocheck
import { SearchItem, ListItem, bing, ACTION_TYPE } from '../../../types'
import initStore, { LocalStore, pushStore } from './LocalStore'
import _ from 'lodash'
import fs from 'fs'
import Fuse from 'fuse.js'
import { nextWorkDataMap } from '../SearchService'
import path from 'path'
import { Worker } from 'worker_threads'
import { app } from 'electron'
const handleArray = (array) => {
  // 使用groupBy按type字段分组
  const groupedByType = _.groupBy(array, 'type')
  // 对分组进行预定义的排序
  const orderedGroup = {
    [ACTION_TYPE.APP]: groupedByType.app,
    [ACTION_TYPE.BING]: [bing],
    [ACTION_TYPE.SETTING]: groupedByType.setting,
    [ACTION_TYPE.URL]: groupedByType.Website
  }

  // 对每个分组按照type字段排序 从每个分组中取出前三个元素
  const limitedByThree = _.map(orderedGroup, (group) => {
    return _.take(group, 1)
  })

  // 将所有分组的元素合并成一个新的数组
  const result = _.flatMap(limitedByThree, (item) => item)
  return result
}
export let appStore: LocalStore<SearchItem> | null = null
export function SearchLibInit(data: SearchItem[]) {
  appStore = initStore('SearchLib', data)
}

export async function SearchPush(data: SearchItem[]) {
  //console.log(data, 'data----SearchPush-----------data')
  //
  //console.log(appStore.store, 'appStore')
  if (data && data.length > 0) {
    pushStore(appStore, data)
  } else {
    const jsonFilePath = path.join(__dirname, '../../resources', 'site.json')
    const datas = await fs.promises.readFile(jsonFilePath, 'utf-8')
    const jsonArray = JSON.parse(datas) as SearchItem[]
    console.log(jsonArray, '------------jsonArray')
    const networkData = await nextWorkDataMap(jsonArray.data)
    console.log(networkData, 'networkData')
    pushStore(appStore, networkData)
  }
}

const fuseOptions = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  includeMatches: true,
  //findAllMatches: true,
  minMatchCharLength: 2,
  // location: 0,
  threshold: 0.45,
  distance: 25,
  useExtendedSearch: true,
  // ignoreLocation: true,
  // ignoreFieldNorm: false,
  fieldNormWeight: 2,
  keys: ['keywords']
}
let fuse
export async function searchFn(keyword: string): Promise<ListItem[]> {
  if (!fuse) {
    const data = appStore.store
    fuse = new Fuse(data, fuseOptions)
  }
  let result = fuse.search(keyword, { limit: 9 })
  //console.log(result, result.length, 'fuse result')
  result = result.map((item) => {
    // 去掉fuse加的item这一层 并处理为promise类型的geticon字段
    const realItem = item.item
    return { ...realItem, getIcon: typeof realItem.getIcon === 'function' ? '' : realItem.getIcon }
  })
  result = handleArray(result)

  //   const p = result.map((item) => {
  //     return new Promise((resolve) => {
  //       if (typeof item.getIcon === 'function') {
  //         item
  //           .getIcon()
  //           .then((res) => {
  //             item.getIcon = res.toDataURL()
  //           })
  //           .catch(() => {
  //             item.getIcon = ''
  //           })
  //           .finally(() => {
  //             resolve(item)
  //           })
  //       } else {
  //         resolve(item)
  //       }
  //     })
  //   })
  //   const final = await Promise.all(p)
  //   console.log(final, 'final')
  //   return final
  // for (let i = 0; i < result.length; i++) {
  //   const item = result[i]
  //   const icon = ''
  //   if (typeof item.getIcon === 'function') {
  //     //   const imgObj = await item.item.getIcon()
  //     //   icon = imgObj.toDataURL()
  //     //icon = JSON.stringify(item.item.getIcon)
  //   }
  //   // 更改数据源 todo
  //   item.getIcon = icon
  // }

  // console.log(result, 'result')
  return result
}

async function getFileIconHandler(target: string): Promise<string | null> {
  try {
    const iconBuffer = await app.getFileIcon(target, { size: 'normal' })
    if (iconBuffer.isEmpty()) {
      console.error('Icon buffer is empty.')
      return null
    }
    const base64Icon = iconBuffer.toDataURL()
    return base64Icon
  } catch (error) {
    console.error('Error getting icon:', error)
    return null
  }
}

export async function processIconsInMultipleWorkers(data: ListItem[]): Promise<ListItem[]> {
  const enrichedData = data.map((item) => {
    const target = item.id || item.command || item.name // 补充 target 没有target会报错。。。
    if (!target) {
      console.error('没有target了....:', item)
    }
    return { ...item, target }
  })

  // 获取os cpu核数 根据cpu核数分块worker主要进行切分处理~
  const cpuCount = require('os').cpus().length
  const chunkSize = Math.ceil(enrichedData.length / cpuCount)
  const chunks = Array.from({ length: cpuCount }, (_, i) =>
    enrichedData.slice(i * chunkSize, (i + 1) * chunkSize)
  )

  const workerPromises = chunks.map(
    (chunk) =>
      new Promise<SearchItem[]>((resolve, reject) => {
        const worker = new Worker(path.join(__dirname, '../../resources', 'iconWorker.js'), {
          workerData: chunk //  Worker的补充数据呗
        })

        // 主要监听getIcon 事件呗~
        worker.on('message', async (message) => {
          if (message.action === 'getIcon') {
            const base64Icon = await getFileIconHandler(message.target)
            worker.postMessage({ id: message.id, base64Icon })
          }
        })

        // Worker 完成了返回个结果呗~
        worker.on('message', (result) => {
          if (result.type === 'final') {
            resolve(result.data)
          }
        })

        worker.on('error', reject)
        worker.on('exit', (code) => {
          if (code !== 0) {
            reject(new Error(`Worker exited with code ${code}`))
          }
        })
      })
  )

  const results = await Promise.all(workerPromises)
  return results.flat()
}
