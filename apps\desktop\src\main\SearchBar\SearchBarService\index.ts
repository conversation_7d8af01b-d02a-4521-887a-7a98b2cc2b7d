import { join } from 'path'
import { BrowserWindow, shell, screen, Display, globalShortcut } from 'electron'
import localShortcut from 'electron-localshortcut'
import { is } from '@electron-toolkit/utils'
import { Bounds } from '../../../types'
import AINowService from '../AINowService'
import { getShortcutKeyFunc, handleWinHide, handleWinState } from '../BaseService'

// import { BroadcastMsg } from '../AINowService/PipeClient'
// import { log } from 'console'
// import icon from '../../resources/icon.ico?asset'
export function createSearchWin(): BrowserWindow {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 500,
    height: 56,
    show: false,
    frame: false,
    hasShadow: true,
    useContentSize: true,
    alwaysOnTop: true,
    transparent: false,
    resizable: true,
    skipTaskbar: true,
    title: 'searchbar',
    // icon,
    // ...(process.platform === 'linux' ? {  } : {}),
    webPreferences: {
      zoomFactor: 1.0,
      preload: join(__dirname, '../preload/SearchBar.js'),
      sandbox: false
    }
  })
  mainWindow.shadow = true
  mainWindow.setHasShadow(true)

  // mainWindow.a

  // mainWindow.on('', () => {
  //   console.log(mainWindow.getBounds(), 'getbounds')
  // })
  mainWindow.on('will-resize', (e, newBounds) => {
    console.warn(e, newBounds, 'will-resize')
  })
  // mainWindow.shadow = true
  mainWindow.setAlwaysOnTop(true, 'screen-saver')
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/#/SearchBar')
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'), { hash: 'SearchBar' })
  }
  function getDisplaysBetween(windowBounds: Bounds, displays: Display[]) {
    const crossingDisplays = [] as Display[]

    displays.forEach((display) => {
      const workArea = display.workArea

      // 判断窗体是否跨越当前显示器的水平范围（x坐标）和垂直范围（y坐标）
      const isCrossingHorizontal =
        windowBounds.x < workArea.x + workArea.width &&
        windowBounds.x + windowBounds.width > workArea.x
      const isCrossingVertical =
        windowBounds.y < workArea.y + workArea.height &&
        windowBounds.y + windowBounds.height > workArea.y

      // 如果窗体与当前显示器的范围相交，则记录该显示器
      if (isCrossingHorizontal && isCrossingVertical) {
        crossingDisplays.push(display)
      }
    })

    return crossingDisplays
  }
  const displays = screen.getAllDisplays() // 获取所有显示器的信息
  //console.log(displays, 'displays')
  const limitWindowPosition = () => {
    const windowBounds = mainWindow.getBounds() // 获取窗体的位置和尺寸
    const { x: winX, y: winY, width: winWidth, height: winHeight } = mainWindow.getContentBounds() // 获取窗体位置
    // mainWindow.webContents
    const currentScreen = screen.getDisplayNearestPoint({ x: winX, y: winY }) // 获取窗体所在屏幕
    const { x, y, width: WAWidth, height: WAHeight } = currentScreen.workArea // 获取屏幕工作区大小

    let newX = winX
    let newY = winY
    const maxWinX = winX + winWidth
    const maxWinY = winY + winHeight
    if (winX < x) newX = x // 限制左边界
    if (winY < y) newY = y // 限制上边界
    if (maxWinX > x + WAWidth) newX = x + WAWidth - winWidth // 限制右边界
    if (maxWinY > y + WAHeight) newY = y + WAHeight - winHeight // 限制下边界

    // 如果位置被调整，设置新位置
    if (newX !== winX || newY !== winY) {
      const currentDisplays = getDisplaysBetween(windowBounds, displays)
      if (currentDisplays.length < 2) {
        mainWindow.setBounds({
          x: newX,
          y: newY,
          width: winWidth,
          height: winHeight
        })
      }
    }
  }

  //   mainWindow.on('move', limitWindowPosition)
  screen.on('display-metrics-changed', () => {
    limitWindowPosition() // 更新窗体位置，确保仍然在屏幕内
    const { width, height } = mainWindow.getBounds()
    console.log('Display metrics changed, updating window constraints...', width, height)
    mainWindow.setBounds({
      width,
      height
    })
  })

  mainWindow.on('blur', () => {
    handleWinHide(mainWindow)
  })
  mainWindow.on('show', () => {
    // handleWinHide(mainWindow)

    mainWindow.focus()
    mainWindow.webContents.send('visible-focus')
  })
  const msgFunc = () => () => {
    console.log(mainWindow, 'mainWindow')
    // mainWindow.restore()
    mainWindow.show()
    // const win = handleWinState(mainWindow)
    // const curWin=BaseWindow.getFocusedWindow()
    // curWin.blur() //TODO 不知道为啥先这样
    //     setTimeout(() => {
    //     curWin?.focus()
    //   console.log(mainWindow.isVisible(), 'winisVisible')
    //   console.log(mainWindow.isFocused(), 'winisFocused')
    //   console.log(curWin, 'curWin')
    //   console.log(mainWindow, 'win')

    // //   mainWindow.hide() //TODO 不知道为啥先这样
    // //   setTimeout(() => {
    // //     mainWindow.show() //TODO 不知道为啥先这样
    // //   })
    //   //   mainWindow.focusOnWebView()
    // }, 10000)
  }
  AINowService.PipeClient.listenBroadcast(4096, msgFunc())

  mainWindow.on('ready-to-show', () => {
    // setTimeout(() => {
    //     // console.log('send 4096');

    //   }, 5000);
    // mainWindow.show()

    const setShortCut = getShortcutKeyFunc('SearchBarShortcutKey')
    setShortCut(() => {
      handleWinState(mainWindow)
    })

    // globalShortcut.register('CommandOrControl+Q', () => {})

    localShortcut.register(mainWindow, 'ESC', () => {
      // TODO 判断mainWindow为小窗
      if (mainWindow.isVisible()) {
        handleWinHide(mainWindow)
      }
    })

    AINowService.PipeClient.listenBroadcast(4098, () => {
      setShortCut(() => {
        handleWinState(mainWindow)
      })
    })
  })
  return mainWindow
}
