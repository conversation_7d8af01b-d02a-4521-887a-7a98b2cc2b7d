import { exec } from 'child_process'
import path from 'path'
import { convertToPinyin } from 'tiny-pinyin'
import { getPluginPath } from '../../../config'
import { ACTION_TYPE } from '../../../types'

// PowerShell 命令来获取已安装的应用列表
function getStartApps() {
  const command = 'Get-StartApps | Select-Object Name, AppID| ConvertTo-Json'
  return new Promise((resolve, reject) => {
    // exec(`powershell.exe -Command "${command}"`, (error, stdout) => {
    exec(
      `powershell.exe -Command "[Console]::OutputEncoding = [System.Text.Encoding]::UTF8 ${command}"`,
      { encoding: 'utf8' },
      (error, stdout) => {
        if (error) {
          reject(error)
          return
        }
        const appList = JSON.parse(stdout)
        resolve(appList)
      }
    )
  })
}

function isUWPApp(appId) {
  // 定义正则表达式匹配 UWP 应用的 AppID 格式 :  AppID: "Microsoft.WindowsCalculator_8wekyb3d8bbwe!App",
  const uwpAppIdPattern = /^[^_]+_[^_]+!.+$/
  return uwpAppIdPattern.test(appId)
}

// 关键搜索词
function getAppKeywords(arr: string[] = []) {
  const pinyinList = arr.reduce((accumulator: string[], current: string) => {
    const pinyin = convertToPinyin(current)
    if (current !== pinyin) {
      accumulator.push(pinyin)
    }
    return accumulator
  }, [])
  return arr.concat(pinyinList)
}

// 根据命令行获取UWP应用列表
async function getUWPAppsByCommand(appArr) {
  // const guidExeRegex = /\{[0-9A-F-]+\}\\([^\\]+\.exe)/;
  let appList: any
  try {
    appList = await getStartApps()
  } catch (error) {
    console.error('获取应用列表时出错:', error)
    return []
  }
  const nameSet = new Set()
  appArr.forEach((item) => {
    nameSet.add(item.name)
  })

  const uwpApps: any = []
  appList.forEach((app) => {
    if (!nameSet.has(app.Name)) {
      // 如果appid 正则包含.exe/.msc ,正则提取出文件名：比如：{1AC14E77-02E7-4E5D-B744-2EB1AE5198B7}\\osk.exe
      if (!isUWPApp(app.AppID)) {
        return
      }
      uwpApps.push({
        name: app.Name,
        id: app.AppID,
        appId: app.AppID,
        type: ACTION_TYPE.APP,
        isUWP: true,
        getIcon: '',
        keywords: getAppKeywords([app.Name])
      })
    }
  })
  return uwpApps
}

// 通过C#获取UWP应用列表
function getUWPAppsByCSharp() {
  const dllPath = path.join(getPluginPath(), '/PackageLauncher.node')
  console.log('uwp-dllPath:', dllPath)
  const packageHelper = require(dllPath)
  try {
    if (!packageHelper.PackageHelper.isWindows10OrGreater()) {
      console.log('This application requires Windows 10 or later')
      return
    }

    const appList = packageHelper.PackageHelper.getInstalledApps()
    const uwpApps = appList.map((app) => {
      return {
        name: app.name,
        id: app.location,
        appId: app.installLocation,
        command: app.location + '/' + app.packageName + '.exe',
        type: ACTION_TYPE.APP,
        isUWP: true,
        getIcon: 'atom:\\' + app.iconPath,
        keywords: getAppKeywords([app.name])
      }
    })
    console.info('获取UWP应用列表:', uwpApps.length)
    return uwpApps
  } catch (error) {
    console.error('获取UWP应用列表时出错:', error)
    return []
  }
}

export async function getUWPApps(_appArr = []) {
  // return getUWPAppsByCommand(appArr)
  return getUWPAppsByCSharp()
}
