// @ts-nocheck
import { app, shell } from 'electron'
import { getFilesRecursively } from '../BaseService'
import { ACTION_TYPE, SearchItem } from '../../../types'
import { SearchLibInit, SearchPush } from '../LocalDataService'
import { toPinyin } from '../PinyinService/Pinyin'
// import pinyin from 'tiny-pinyin'
import { getAll } from '../services'
import path from 'path'
import fs from 'fs'
import AINowService from '../AINowService'
import _ from 'lodash'
import { getUWPApps } from './UwpApp'
import { formatUrl } from '../../utils/urlUtil'

export async function initSearchData() {
  const userStartUpPath = `${app.getPath('appData')}\\Microsoft\\Windows\\Start Menu\\Programs`
  const localStartUpPath = 'C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs'
  const list = getFilesRecursively(localStartUpPath)
  const userList = getFilesRecursively(userStartUpPath)
  try {
    // const allItems = list
    //   .concat(userList)
    //   .filter((item, index, self) => index === self.findIndex((t) => t.id === item.id))
    const allItems = userList.concat(list)
    // ].map((item) => JSON.parse(item))
    // userList.concat(list)
    const SearchData = [] as SearchItem[]
    console.log('Start', Date.now())
    const pattern = new RegExp('uninstall|uninst', 'i')
    allItems.forEach((item) => {
      try {
        //const pathsArr = item.split('\\')
        //pathsArr.pop()
        const name = path.parse(item).name
        const { args, target, icon, iconIndex } = shell.readShortcutLink(item)
        const idName = path.parse(target).name
        const keywords = [name as string, idName] //...pathsArr.slice(pathsArr.length - 1)
        const pinyinList = keywords.reduce((pre, cur) => {
          const pinyin = toPinyin(cur)
          if (cur !== pinyin) {
            pre.push(pinyin)
          }
          return pre
        }, [] as string[])
        const id = target + args
        if (name && !pattern.test(id)) {
          // 过滤掉应用的卸载
          SearchData.push({
            id,
            name,
            type: ACTION_TYPE.APP,
            // getIcon: () =>
            //   app.getFileIcon(target, {
            //     size: 'small'
            //   }),

            command: target,
            args,
            keywords: keywords.concat(pinyinList)
          })
        }
      } catch (error) {
        // return {}
      }
    })
    // 按照id去重
    const finalSearchData = _.uniqBy(SearchData, (item) => {
      return item.id
    })

    // 获取设置数据源
    const settingData = await getWindowsSettings()
    // console.log(SearchData, '总appdata')
    // const filterList = SearchData.filter(
    //   (item, index, self) => index === self.findIndex((t) => t.id === item.id)
    // )
    let totalData = finalSearchData.concat(settingData)
    // const test = filterList.filter((item) => item.name.includes('腾讯会议'))
    // console.log('filter', test)
    const uwpApps = await getUWPApps(totalData)
    totalData = totalData.concat(uwpApps)
    SearchLibInit(totalData)
    getNetWorkToken()
  } catch (err) {
    console.log(err)
  }
}

// export async function getDataAll() {
//   try {
//     const res = await getAll('123')
//     // console.log(res.data, '请求的接口数据')
//     // return res.data || []
//   } catch (error) {
//     console.error('Error fetching data from API:', error)
//     return []
//   }
// }

export async function getWindowsSettings() {
  try {
    const jsonFilePath = path.join(__dirname, '../../resources', 'WindowsSettingResult.json')
    const data = await fs.promises.readFile(jsonFilePath, 'utf-8')
    const jsonArray = JSON.parse(data) as SearchItem[]
    // console.log(jsonArray, 'windows设置数据文件')
    return jsonArray
  } catch (error) {
    console.error('Error processing WindowsSettings.json:', error)
    return null
  }
}

const TIMEOUT = 3 * 60 * 1000 // 超过 3 分钟的超时限制

export async function getNetWorkToken() {
  const timeoutPromise = new Promise((_, reject) =>
    setTimeout(() => reject(new Error('Request timed out')), TIMEOUT)
  )

  try {
    const token = await Promise.race([AINowService.PipeClient.getToken(), timeoutPromise])
    if (!token) {
      setTimeout(() => getNetWorkToken(), 1000)
    } else {
      await getNetWorkData()
      return token
    }
  } catch (error) {
    console.error('Error fetching data from API:', error)
    return []
  }
}

export async function getNetWorkData() {
  try {
    const res = await getAll()
    //console.log(res, '请求的接口数据')
    const jsonArray = await nextWorkDataMap(res.data)
    //console.log(jsonArray, 'network数据')
    SearchPush(jsonArray)
    return jsonArray
  } catch (error) {
    console.log(error, 'getNetWorkData Error')
    return []
  }
}

export async function nextWorkDataMap(data) {
  try {
    return data.map((item) => {
      item.keywords.push(item.url, 'https://' + item.url, formatUrl(item.url)) //todo url中关于是否有www的返回不对
      return {
        ...item,
        name: item.siteName,
        command: 'https://' + item.url,
        getIcon: item.getIcon || item.icon,
        icon: '', // 都走geticon 设置图
        type: ACTION_TYPE.URL
      }
    })
  } catch (error) {
    console.log(error, 'nextWorkDataMap Error')
    return []
  }
}
