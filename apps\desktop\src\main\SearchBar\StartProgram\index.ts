import { exec, spawn } from 'child_process'
export const startApp2 = (appPath: string) => {
  const app = exec(appPath, (error, stdout, stderr) => {
    console.log(error, stdout, stderr)
  })

  return app
}
export const startApp = (exePath: string, ...args) => {
  console.log(exePath, args, '打开app参数')
  // 假设这是Windows下的可执行文件路径，你需要根据实际情况修改
  // let exePath = 'C:\\Windows\\System32\\cmd.exe';
  // let exePath = 'C:\\Program Files (x86)\\Common Files\\LenovoAppStoreNotify\\LenovoAppStoreNotify.exe';
  // const exePath = 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe' // let args = ['shortcut_desktopxxxx'];
  // const args = ['www.baidu.com'] // let args = ['/k', 'color 4F'];
  // let args = [ ];
  const exeProcess = spawn(exePath, args) // 可以对exeProcess进行进一步的处理，比如监听它的事件等
  // 例如，监听进程的错误事件
  exeProcess.on('error', (err) => {
    console.log('启动可执行文件出错:', err)
  }) // 监听进程的退出事件，并获取退出码
  exeProcess.on('exit', (code) => {
    console.log('可执行文件已退出，退出码:', code)
  })
  return exeProcess
}

export const launchUWP = (appId: string) => {
  const command = `powershell.exe -Command "Start-Process shell:AppsFolder\\${appId}"` // uwp应用的appid
  // const command = `powershell.exe -Command "Start-Process '${appId}'"`;

  exec(command, (error, _stdout, stderr) => {
    if (error) {
      console.error(`执行错误: ${error}`)
      return
    }
    if (stderr) {
      console.error(`错误输出: ${stderr}`)
      return
    }

    console.log(`应用 ${appId} 已启动`)
  })
}
