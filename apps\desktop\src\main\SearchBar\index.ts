// @ts-nocheck
import { app } from 'electron'
import { electronApp, optimizer } from '@electron-toolkit/utils'
import { IpcRegister } from './ipc'
import { createSearchWin } from './SearchBarService'

import { createFloatingBall } from './FloatingBallService' // 悬浮球
import { FloatingRegister } from './FloatingBall'

import { initSearchData } from './SearchService'
import AINowService from './AINowService'
import { handleWinShow, handleWinState, updateGlobalConfig } from './BaseService'
// import { BroadcastMsg } from './AINowService/PipeClient'
// import { updateGlobalConfig } from '@/config'
/*

// AINowService.PipeClient.sendMessage(new BroadcastMsg({ MessageType: '4096', MessageDestination: 8, Data: { is: 'ttest' } }))
updateGlobalConfig().then(() => {
  initSearchData()
})
// getAll()
// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  const gotTheLock = app.requestSingleInstanceLock()
  // protocol.registerSchemesAsPrivileged([dddddd
  if (!gotTheLock) {
    console.log('Another instance is running.')
    app.quit()
  }
  console.log('main mode=>', import.meta.env.MODE)
  console.log('env', import.meta.env.VITE_ENV)

  // getAinowConfig()

  // initBaseService()
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')
  // protocol.handle('micro', async (req) => {
  //   const filePath = req.url.slice('micro://'.length)
  //   // const basePath=filePath.slice(0, filePath.indexOf('/'))
  //   const localPath = path.join(getMicroPath(), filePath)

  //   if (filePath.includes('assets')) {
  //     const url = getResourcesPath(path.join(getMicroPath(), filePath))

  //     return net.fetch(url)
  //   }

  //   return net.fetch(localPath)
  // })
  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  // ipcMain.on('ping', () => console.log('pong'))
  const searchBarInit = () => {
    const searchWin = createSearchWin() // 搜索框创建
    searchWin.on('close', (e) => {
      console.log('close')
      e.preventDefault()
      searchWin.hide()
    })
    app.on('second-instance', () => {
      console.log('second-instance', 'second instance detected.')
      searchWin.isVisible() || handleWinShow(searchWin)
    })
    AINowService.PipeClient.listenBroadcast(4100, (msg) => {
      console.log('4100', msg)

      updateGlobalConfig()
      // handleWinState(searchWin)
    })
    IpcRegister(searchWin)
  }
  const floatingBall = createFloatingBall() // 悬浮球创建
  // ainowHooks(searchWin)

  FloatingRegister(floatingBall)
  floatingBall.on('close', (e) => {
    console.log('close')
    e.preventDefault()
    floatingBall.hide()
  })
  searchBarInit()
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// function getResourcesPath(arg0: string) {
//   throw new Error('Function not implemented.')
// }
// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
*/
export const initSearchbar = () => {
  // AINowService.PipeClient.sendMessage(new BroadcastMsg({ MessageType: '4096', MessageDestination: 8, Data: { is: 'ttest' } }))
  updateGlobalConfig().then(() => {
    initSearchData()
  })

  const searchBarInit = () => {
    const searchWin = createSearchWin() // 搜索框创建
    searchWin.on('close', (e) => {
      console.log('searchWin close')
      e.preventDefault()
      searchWin.hide()
    })
    // app.on('second-instance', () => {
    //   console.log('second-instance', 'second instance detected.')
    //   searchWin.isVisible() || handleWinShow(searchWin)
    // })
    AINowService.PipeClient.listenBroadcast(4100, (msg) => {
      console.log('4100', msg)

      updateGlobalConfig()
      // handleWinState(searchWin)
    })
    IpcRegister(searchWin)
    return searchWin
  }

  const floatingBall = createFloatingBall() // 悬浮球创建
  // ainowHooks(searchWin)
  FloatingRegister(floatingBall)
  floatingBall.on('close', (e) => {
    console.log('floatingBall close')
    e.preventDefault()
    floatingBall.hide()
  })

  const win = searchBarInit()

  return { floatingBall, searchWindow: win }
}
