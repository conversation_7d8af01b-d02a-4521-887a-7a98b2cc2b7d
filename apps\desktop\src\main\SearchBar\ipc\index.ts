// @ts-nocheck
import { BrowserWindow, ipcMain } from 'electron'
import { Bounds, IPCRes, ListItem } from '../../../types'
import {
  openWebSite,
  openApp,
  getMoveWinFunc,
  setBounds,
  openProgram,
  testOpen2,
  handleWinState
} from '../BaseService'
import AINowService from '../AINowService'
import { processIconsInMultipleWorkers, searchFn } from '../LocalDataService'
import { launchUWP } from '../StartProgram'

// import { LogType } from 'vite'

export function IpcRegister(win: BrowserWindow) {
  function handleIpcResponse<T>(success: boolean, res?: T): IPCRes<T> {
    const timestamp = Date.now()
    return { success, res, timestamp }
  }

  const moveHandler = getMoveWinFunc(win)
  ipcMain.on('setWinBounds', (e, bound: Bounds) => {
    setBounds(win, bound)
    // moveHandler.setCurWinSize({ width: bound.width, height: bound.height })
  })
  ipcMain.on('window:move', (e, canMoving) => {
    moveHandler.execFunc(canMoving)
  })

  ipcMain.handle('getBounds', () => {
    const res = win.getBounds()
    return handleIpcResponse(!!res, res)
  })
  ipcMain.on('setIgnoreMouseEvents', (e, ignore: boolean, option: { forward: boolean }) => {
    win.setIgnoreMouseEvents(ignore, option) //
  })
  ipcMain.handle('openWebSite', (e, url: string) => {
    return openWebSite(url)
  })
  ipcMain.handle('openApp', (e, path: string) => {
    return openApp(path)
  })
  ipcMain.handle('openUWP', (e, appId: string) => {
    return launchUWP(appId)
  })
  ipcMain.handle('startApp', (e, path: string, args) => {
    return openProgram(path, args)
  })
  ipcMain.on('testOpen2', (e, path: string) => {
    testOpen2(path)
  })

  ipcMain.handle('sendMiniMsg', (e, param: { ChatText?: string | undefined; type: unknown }) => {
    return AINowService.sendWindowMessage(win, { ...param }) //AskType: ASK_TYPE.ASK
  })
  // ipcMain.handle('translateMiniMsg', (e, param: Record<string, unknown>) => {
  //   return AINowService.sendWindowMessage(win, {
  //     ...param,
  //     AskType: ASK_TYPE.TRANSLATE,
  //     Intention: 'WORK_ASSISTANT_TRANSLATION'
  //   })
  // }),
  ipcMain.on('setWindowBlur', () => {
    win.blur()
  })

  // 监听来自窗口悬浮球的消息
  ipcMain.on('show-search-window', () => {
    handleWinState(win)
  })
  ipcMain.handle('openSystemSettings', (e, param: string) => {
    return AINowService.openSystemSettings(param)
  })
  ipcMain.handle('search', async (e, keyword: string): Promise<ListItem[]> => {
    const result = (await searchFn(keyword)) as ListItem[] // 假设 searchFn 提供了初始数据
    //const processedResults = await processIconsInMultipleWorkers(result)
    return result
  })
  ipcMain.handle('searchIcon', async (e, data: ListItem[]) => {
    const processedResults = await processIconsInMultipleWorkers(data)
    return processedResults
  })
}
