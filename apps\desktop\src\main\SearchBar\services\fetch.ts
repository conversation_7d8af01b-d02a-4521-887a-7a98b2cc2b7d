import AINowService from '../AINowService'
import { updateGlobalConfig } from '../BaseService'

let baseURL = ''
;(async () => {
  try {
    const globalConfig = await updateGlobalConfig()
    const isTest = globalConfig.isTest
    baseURL = isTest ? 'https://ainowrowtest.lenovo.com' : 'https://ainowrow.lenovo.com'
    console.log(`HTTP baseURL set to: ${baseURL}`)
  } catch (error) {
    console.error('Failed to set HTTP baseURL:', error)
  }
})()

async function fetchWithToken(url: string, options: RequestInit = {}) {
  const token = options.headers?.['token'] || (await AINowService.PipeClient.getToken())
  const headers = {
    'Content-Type': 'application/json',
    token: token,
    ...options.headers
  }

  const response = await fetch(`${baseURL}${url}`, {
    ...options,
    headers
  })

  if (!response.ok) {
    console.error('HTTP error:', response.statusText)
    throw new Error(response.statusText)
  }

  return response.json()
}

export default fetchWithToken
