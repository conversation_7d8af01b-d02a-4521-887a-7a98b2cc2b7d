import AINowService from '../AINowService'
import { updateGlobalConfig } from '../BaseService'
import axios, { AxiosResponse } from 'axios'
// import https from 'https'
const http = axios.create({
  baseURL: '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})
;(async () => {
  try {
    const globalConfig = await updateGlobalConfig()
    const isTest = globalConfig.isTest
    http.defaults.baseURL = isTest
      ? 'https://ainowrowtest.lenovo.com'
      : 'https://ainowrow.lenovo.com'
    console.log(`HTTP baseURL set to: ${http.defaults.baseURL}`)
  } catch (error) {
    console.error('Failed to set HTTP baseURL:', error)
  }
})()
http.interceptors.request.use(
  async (config) => {
    const token = config.headers?.token
    if (!token) {
      config.headers['token'] = await AINowService.PipeClient.getToken()
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
http.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error) => {
    console.error('HTTP error:', error)
    return Promise.reject(error)
  }
)

export default http
