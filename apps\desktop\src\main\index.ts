import {
  app,
  BrowserWindow,
  ipcMain,
  nativeTheme,
  WebContentsView,
  BaseWindow,
  shell
} from 'electron'
import { electronApp, optimizer } from '@electron-toolkit/utils'
import './utils/logs'
import {
  initDB,
  handlePersistenceAction,
  handleProviderChange,
  handleStreamChat,
  handleAbortChat
} from './utils'
import { getPKBFiles } from './utils/PKB'
import { createTray } from './tray'
import { getAccessToken, registerDefaultProtocol } from './utils/auth'
import { protoHandle } from './utils/protocol'
import WindowManager from './utils/WindowManager'
import { WindowActions } from '@ainow/types'
import { isDev } from './utils/isDev'
import { createMainWindow } from './MainChat'
import { cliFeatures, APP_FEATURES } from './utils/features'
import { initSearchbar } from './SearchBar'
import { WordSniffer } from './window/WordSniffer'
import { handleSTTAction } from './utils/stt'
import { createPreviewWin } from './PreviewWin'
import SettingsManager from './utils/SettingsManager'
// import '../../../searchbar/src/main/index'
global.isElectronDev = isDev
global.electronApp = app
global.isQuitFromTray = false
global.isDBReady = false

const winMgr = new WindowManager()
const features = [
  // exe 之后 --feature=xxx --featrue=xxx 的启动参数
  ...cliFeatures,
  // TODO 打包之前先手动控制这里的特性
  APP_FEATURES.TRAY,
  APP_FEATURES.MAIN_CHAT,
  APP_FEATURES.MINI_CHAT,
  APP_FEATURES.WORD_SNIFFER,
  APP_FEATURES.SEARCHBAR,
  APP_FEATURES.SPEECH_STT
]

// 热启动处理  单例模式
const isSingleLockApp = app.requestSingleInstanceLock()
if (!isSingleLockApp) {
  app.quit()
}
// const autoRun = () => {
//   app.setLoginItemSettings({
//     openAtLogin: true, // 开机时启动应用
//     openAsHidden: false, // 是否以隐藏方式启动
//     path: process.execPath, // 应用的可执行文件路径
//     args: ['--autoRun'] // 自定义启动参数
//   })
// }
//autoRun()
app.whenReady().then(() => {
  const settingsManager = new SettingsManager()
  async function getSettings() {
    await settingsManager.initSettings()
    return settingsManager.getSettings()
  }
  async function syncSettings(e: any, obj: any) {
    console.log('e------', e)
    await settingsManager.handleSettingConfig(obj)
  }
  // 初始化和点击settings去做同步
  getSettings()
  ipcMain.handle('getSettings', getSettings)
  ipcMain.handle('syncSettings', syncSettings)
  registerDefaultProtocol('ainow')
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // 为窗口绑定或优化快捷键行为
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  if (features.includes(APP_FEATURES.TRAY)) {
    // 创建托盘
    createTray({
      hasMainWin: features.includes(APP_FEATURES.MAIN_CHAT),
      hasMiniWin: features.includes(APP_FEATURES.MINI_CHAT)
    })
  }

  if ([APP_FEATURES.MINI_CHAT, APP_FEATURES.MAIN_CHAT].some((f) => features.includes(f))) {
    // 持久化操作
    initDB().then(() => {
      global.isDBReady = true
    })
    ipcMain.handle('llm:persistence-action', handlePersistenceAction)
    ipcMain.handle('get-access-token', getAccessToken)

    // 处理ainow对话中的图片等
    protoHandle()
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    app.on('activate', function () {
      if (BrowserWindow.getAllWindows().length === 0) createMainWindow()
    })
    // 处理自定义标题栏的窗口操作
    ipcMain.on('window-action', (_, id: number, action: WindowActions) =>
      winMgr.handleAction(id, action)
    )

    // 模型通信操作
    ipcMain.handle('llm:change-provider', handleProviderChange)
    ipcMain.handle('llm:stream-chat', handleStreamChat)
    ipcMain.handle('getPKBFileList', getPKBFiles)
    ipcMain.handle('previewFile', (_, previewFilePath: string) => {
      createPreviewWin(previewFilePath)
    })
    ipcMain.handle('llm:abort-chat', handleAbortChat)

    // 处理颜色模式
    ipcMain.handle('dark-mode:toggle', () => {
      if (nativeTheme.shouldUseDarkColors) {
        nativeTheme.themeSource = 'light'
      } else {
        nativeTheme.themeSource = 'dark'
      }
      return nativeTheme.shouldUseDarkColors
    })
    ipcMain.handle('dark-mode:system', () => {
      nativeTheme.themeSource = 'system'
    })
    ipcMain.handle('openFile', async (_, filePath: string) => {
      shell.openPath(filePath)
    })
    if (features.includes(APP_FEATURES.SPEECH_STT)) {
      ipcMain.handle('speech:stt-action', handleSTTAction)
    }
  }

  if (features.includes(APP_FEATURES.MAIN_CHAT)) {
    // 创建主窗
    createMainWindow()
  }

  if (features.includes(APP_FEATURES.SEARCHBAR)) {
    const { floatingBall, searchWindow } = initSearchbar()
    app.on('before-quit', () => {
      floatingBall.destroy()
      searchWindow.destroy()
    })
  }
  // if (features.includes(APP_FEATURES.WORD_SNIFFER) || isDev) {
  //   const ws = new WordSniffer()
  //   app.on('before-quit', () => {
  //     ws.getWindow().destroy()
  //     ws.stop()
  //     // TODO events?
  //   })
  // }
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (!features.includes(APP_FEATURES.TRAY)) {
      app.quit()
    }
  }
})
