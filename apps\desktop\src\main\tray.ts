import {
  Tray,
  Menu,
  nativeImage,
  globalShortcut,
  app,
  BrowserWindow,
  type MenuItemConstructorOptions
} from 'electron'
import type { TrayOption } from './types'
import path from 'path'
import {
  MAIN_WINDOW_HEIGHT,
  MAIN_WINDOW_WIDTH,
  MINI_WINDOW_HEIGHT,
  MINI_WINDOW_WIDTH
} from './utils/constants'
// import { createLocalChat } from './LocalChat'
import { displayName } from '../../../../package.json'

function mainWinCbk() {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length > 0) {
    const firstWindow = allWindows[0]
    firstWindow.show()
    const [w, h] = firstWindow.getSize()
    if (w <= MINI_WINDOW_WIDTH || h <= MINI_WINDOW_HEIGHT) {
      firstWindow.setSize(MAIN_WINDOW_WIDTH, MAIN_WINDOW_HEIGHT)
    }
  }
}

function miniWinCbk() {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length > 0) {
    const firstWindow = allWindows[0]
    firstWindow.setSize(MINI_WINDOW_WIDTH, MINI_WINDOW_HEIGHT)
    firstWindow.show()
  }
  // createLocalChat(global.mainWindow);

  // const miniWin = createLocalChat(global.mainWindow)
  // global.miniWindow = miniWin
  // //默认加载小窗
  // miniWin.loadURL('http://localhost:5173/#/localchatMini')
}

function exitCbk() {
  global.isQuitFromTray = true
  console.log('***TRAY EXIT***', global.isQuitFromTray)
  try {
    app.quit()
    process.exit(1)
  } catch (ex) {
    console.log(ex)
  }
}

export function createTray(trayOption: TrayOption = {}) {
  const iconPath = path.join(
    app.getAppPath().replace('app.asar', 'app.asar.unpacked'),
    'resources',
    'icon.png'
  )
  const icon = nativeImage.createFromPath(iconPath)
  const tray = new Tray(icon)

  const menuItems: MenuItemConstructorOptions[] = []
  if (trayOption.hasMainWin)
    menuItems.push({
      label: 'Main window (Ctrl+Alt+Q)',
      click: () => {
        mainWinCbk()
        trayOption?.mainWinCbk?.()
      }
    })
  if (trayOption.hasMiniWin)
    menuItems.push({
      label: 'Mini window',
      click: () => {
        miniWinCbk()
        trayOption?.miniWinCbk?.()
      }
    })
  if (menuItems.length) menuItems.push({ type: 'separator' })
  menuItems.push({
    label: 'Exit',
    click: () => {
      exitCbk()
      trayOption?.exitCbk?.()
    }
  })

  const contextMenu = Menu.buildFromTemplate(menuItems)
  tray.setContextMenu(contextMenu)

  tray.setToolTip(displayName)

  tray.on('click', () => {
    global.mainWindow?.show()
  })

  globalShortcut.register('Ctrl+Alt+Q', () => {
    mainWinCbk()
    trayOption?.mainWinCbk?.()
  })

  return tray
}
