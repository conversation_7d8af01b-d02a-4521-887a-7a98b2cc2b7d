import type { BaseWindow, MenuItem, WebContents } from 'electron'
import { AINOW_MODEL } from '@ainow/types'

export interface IStreamChatParams {
  accountId: string
  threadId: string
  provider: string
  model: string & AINOW_MODEL
  message?: any
  messageId: string
  ainowParams?: any
  from: string
}

export interface EdgeStreamChatParams extends IStreamChatParams {
  model: string
}

export interface TrayOption {
  hasMainWin?: boolean
  hasMiniWin?: boolean
  mainWinCbk?: () => void
  miniWinCbk?: () => void
  exitCbk?: () => void
}
