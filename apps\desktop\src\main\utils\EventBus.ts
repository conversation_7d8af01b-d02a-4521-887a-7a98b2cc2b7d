/* eslint-disable @typescript-eslint/ban-types */
export default class EventBus {
  events: Record<string, { id: string; cb: Function }[]> = {}
  on<K extends string, T extends Function>(key: K, cb: T) {
    if (!this.events[key]) {
      this.events[key] = []
    }
    const id = key + this.events[key].length
    this.events[key].push({ id, cb })
    return id
  }
  emit(event: string, ...args: unknown[]) {
    this.events[event].forEach((item) => item.cb(...args))
  }
  removeEvent(key: string) {
    this.events[key] = []
  }
  removeListen(key: string, id: string) {
    const i = this.events[key].findIndex((item) => item.id === id)
    this.events[key].splice(i, 1)
  }
}

export const emitter = new EventBus()
