// @ts-ignore TODO 打包机会报类型错误阻断build:win
import { executeAinow } from '@ainow/services'
type StatusP = 'SUCCESSED' | 'FAILED' | 'WAITTING' | 'ILLEGAL' | 'ADDED' | 'SUSPEND' | 'REMOVING'
interface PKBInfo {
  targetPath: string | null
  fileStatus: Array<StatusP>
  fileType: number
  searchType: number
  messageId: string
  [propName: string]: any
}
export async function getPKBFiles(_, params: PKBInfo) {
  // lid 生成 返回匹配 处理
  // params.data.lid = params.messageId;
  let obj = {
    ipcfunc: 'GetCurPathFileInfo',
    data: params
  }
  const res = await executeAinow(
    // @ts-ignore
    obj,
    () => {},
    'call',
    {}
  )
  return res
}
