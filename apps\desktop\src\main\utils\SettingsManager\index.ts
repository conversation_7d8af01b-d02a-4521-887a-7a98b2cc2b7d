import { globalShortcut } from 'electron'
import fs from 'node:fs'
import chokidar from 'chokidar'
import { settingsConfig } from './config'
interface SettingType {
  extensions: 'Extensions' // 插件
  softwares: 'Softwares' // 软件
  tools: 'Tools' //小窗的工具栏
  common: 'Common' // 正常配置
}
type InfoType = SettingType[keyof SettingType]
interface SettingInfo {
  desc: string // 功能描述
  keyboard?: string // 键位
  isOpen: boolean // 是否开启
  isShow?: boolean // 是否展示在settings上
  isShowKeyboard?: boolean // 是否在settings展示键位
  module?: InfoType // 分类
  callback?: () => void // 函数类型
  [propName: string]: boolean | string | undefined | (() => void)
}
interface AinowSettings {
  [name: string]: SettingInfo
}
/**
 * 管理全局设置
 * @params
 */
export default class SettingsManager {
  private static instance: SettingsManager
  private ainowSettings!: AinowSettings
  settingsPath?: string
  private isSync?: boolean // 代表是否同步过
  private isInit: boolean
  constructor() {
    if (!SettingsManager.instance) {
      SettingsManager.instance = this
      this.isSync = false
    }
    this.isInit = false
    return SettingsManager.instance
  }
  // 初始化配置
  public async initSettings() {
    if (this.isInit) {
      return Promise.resolve()
    }
    this.settingsPath = settingsConfig.getSettingPath()
    const sourceSettings = settingsConfig.getResourceSettings()
    const res = require(sourceSettings)
    // 写入settings.json
    const writeRes = await this.writeSettings(res)
    console.log('writeRes------', writeRes)
    // 文件准备好监听文件变化
    this.ainowSettings = await this.readSettings(this.settingsPath)
    this.watchFileToSettings() // 文件更改同步数据
    this.isInit = true
  }
  getSettings() {
    return this.ainowSettings
  }
  private async watchFileToSettings() {
    const self = this
    const watcher = chokidar.watch(this.settingsPath!, { ignored: /(^|[\/\\])\../ })
    watcher
      .on('add', (path) => console.log(`文件${path}被添加`))
      .on('change', async (path) => {
        console.log(`文件${path}被修改`)
        // 修改文件之后重新读取
        const ainowSettings = await self.readSettings(path)
        self.ainowSettings = { ...self.ainowSettings, ...ainowSettings }
      })
  }
  // 注册同步快捷键和功能(只对外抛出自定义快捷键和功能)
  async registeSettingKeyboard(settingName: string, keyboard: string, callback?: () => void) {
    if (settingName in this.ainowSettings) {
      const settingKeyboard = this.ainowSettings[settingName].keyboard
      let settingCallback
      // 传入callback绑定传入的，否则绑定原来的
      if (callback) {
        settingCallback = callback
      } else if (this.ainowSettings[settingName].callback) {
        settingCallback = this.ainowSettings[settingName].callback
      }
      if (!settingKeyboard) {
        // 未定义直接定义
        this.ainowSettings[settingName].keyboard = keyboard
        settingCallback && globalShortcut.register(keyboard, settingCallback)
      } else {
        if (settingKeyboard !== keyboard) {
          // 键位不一致先注销原先快捷键
          globalShortcut.unregister(settingKeyboard)
          this.ainowSettings[settingName].keyboard = keyboard
        }
        settingCallback && globalShortcut.register(keyboard, settingCallback)
      }
      settingCallback && (this.ainowSettings[settingName].callback = settingCallback)
    }
  }
  // settings配置同步
  async handleSettingConfig(configInfo: { [key: string]: SettingInfo }) {
    let settings = await this.readSettings(this.settingsPath!)
    console.log('configInfo---------', configInfo, settings)
    // 同步ainowSettings
    for (let [key, value] of Object.entries(configInfo)) {
      if (key in this.ainowSettings) {
        for (let k in value) {
          if (value.hasOwnProperty(k) && k in this.ainowSettings[key]) {
            this.ainowSettings[key][k] = value[k]
          }
        }
      }
      if (key in settings) {
        for (let k in value) {
          if (value.hasOwnProperty(k) && k in settings[key]) {
            settings[key][k] = value[k]
          }
        }
      }
    }
    // 写入文件
    this.writeSettings(settings)
  }
  private async writeSettings(settings: AinowSettings) {
    // 异步写入文件
    try {
      const data = fs.promises.writeFile(this.settingsPath!, JSON.stringify(settings, null, '\t'))
      return data
    } catch (err) {
      if (err) {
        return ''
      }
    }
  }
  private async readSettings(path: string) {
    try {
      const data = await fs.promises.readFile(path, 'utf-8')
      return JSON.parse(data)
    } catch (error) {
      console.log(error)
      return ''
    }
  }
}
