import { app } from 'electron'
import path from 'path'
import { handlePostMsg } from './handleMsg'
//定义全局变量用来缓存我们取到的参数
let schemeTemp = null
//const scheme = 'ainow.row'
let scheme
const baseURL = 'https://passport.lenovo.com'
let refreshToken = ''
let accessToken = '' // 存储系统的accessToken
let userInfo = ''
// 窗口通知协议
const NOTIFYSCHEME = {
  USERINFO: 'scheme_userinfo',
  LOGINCODE: 'scheme_ainow.row'
}

//由于macOS和windows系统注册自定义协议的参数不同所以创建一个函数处理注册
export function registerDefaultProtocol(schemepara = 'ainow.row') {
  scheme = schemepara || 'ainow.row'
  //判断系统
  if (process.platform === 'win32') {
    const args: string[] = []
    if (!app.isPackaged) {
      //开发阶段调试阶段需要将运行程序的绝对路径加入启动参数
      args.push(path.resolve(process.argv[1]))
    }
    //添加--防御自定义协议漏洞，忽略后面追加参数
    args.push('--')
    //判断是否已经注册
    if (!app.isDefaultProtocolClient(scheme, process.execPath, args)) {
      app.setAsDefaultProtocolClient(scheme, process.execPath, args)
    }
  } else {
    //判断是否已经注册
    if (!app.isDefaultProtocolClient(scheme)) {
      app.setAsDefaultProtocolClient(scheme)
    }
  }
}

app.on('second-instance', (event, argv) => {
  //argv字段就是第二实例的process.argv
  //只处理windows系统
  if (process.platform === 'win32') {
    console.log('第二实例启动, second-instance调用handleArgv--', event)
    handleScheme(argv)
  }
})
//处理macOS系统
app.on('open-url', (event, url) => {
  //由于open-url事件里面的参数url是系统直接帮我们拿到的自定义协议
  console.log('第二实例启动,  open-url --event', event)
  handleURL(url)
  if (schemeTemp) {
    const urlobj = new URL(schemeTemp)
    if (urlobj.protocol.includes('ainow.row')) {
      global.mainWindow.webContents.send(NOTIFYSCHEME.LOGINCODE, schemeTemp)
    } else {
      handlePostMsg(schemeTemp)
    }

    schemeTemp = null
  }
})

export function handleScheme(param?) {
  if (param) {
    handleArgv(param, scheme)
  } else {
    handleArgv(process.argv, scheme)
  }
  if (schemeTemp) {
    const urlobj = new URL(schemeTemp)
    if (urlobj.protocol.includes('ainow.row')) {
      global.mainWindow.webContents.send(NOTIFYSCHEME.LOGINCODE, schemeTemp)
    } else {
      handlePostMsg(schemeTemp)
    }
    console.log('发送scheme事件 scheme_${scheme}', `scheme_${scheme}`)

    schemeTemp = null
  }
}

// 冷启动处理
function handleArgv(argv, scheme) {
  console.log('--------handleArgv eargv', argv, scheme)
  // argv样例数据 [
  // apps/desktop dev:   'E:\\workspace\\ainow-ui\\node_modules\\.pnpm\\electron@34.2.0\\node_modules\\electron\\dist\\electron.exe',
  // apps/desktop dev:   '--allow-file-access-from-files',
  // apps/desktop dev:   'E:\\workspace\\ainow-ui\\apps\\desktop',
  // apps/desktop dev:   '--',
  // apps/desktop dev:   'ainow.row:///?code=15b8f8852d59bb6e914e574137260c6187cf9dede29dc9326bcb7c6a7f44e455idccn&scope=openid&state=s7XSqRNy-DFK7t0thNYeUccZHW8uFFlTA5hmNlATkJY'
  // apps/desktop dev: ]
  let offset = 1
  if (!app.isPackaged) {
    offset++
  }
  const mySchemeURL = argv.find((item, index) => {
    return index >= offset && item.startsWith(`${scheme}://`)
  })
  // mySchemeURL :ainow.row:///?code=15b8f8852d59bb6e914e574137260c6187cf9dede29dc9326bcb7c6a7f44e455idccn&scope=openid&state=s7XSqRNy-DFK7t0thNYeUccZHW8uFFlTA5hmNlATkJY
  console.log('自定义协议', mySchemeURL)
  schemeTemp = mySchemeURL
}

function handleURL(url) {
  //利用URL类解析自定义协议，当然也可以自己写正则，因为大多数的自定协议都遵循'st://module?key1=value1&key2=value2'所以可以直接借助URL解析
  // let obj = new URL(url)
  // let typeKey = obj.pathname.slice(2)
  // let params = obj.searchParams
  // let data = {
  //   typeKey,
  //   params
  // }
  // //使用一个变量暂存
  // schemeTemp = data
  schemeTemp = url
}

/**
 * 根据用户授权码获取token信息
 * @param _ event事件
 * @param tokenUrl
 * @param params
 */
export async function getAccessToken(_, tokenUrl: string) {
  // res样例数据{
  //     "access_token": "lenovo1b5c930fc5c4dc7fa5a162c8cf947071efccdfce743c935573ca7895ba24fb4459b43e6dd9383ba1be84fa86be508dc0idccn",
  //     "expires_in": 86400,
  //     "id_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjA5NzU4YjliYTU0NzQ0YWZiZjU0MzA2NjcxZDkxYjJiIn0.********************************************************************************************************************************************************************************************************************************************.deme4t3KNhOiZF6J4ypxoVhRAiBugqfPlca6SV0XQx7FcA1g-8FUUU8cbUg4K8wQ0ReWGwlaaQROuNx4ht3f5RqLUHe3tDkLt4w3-lXbrebo8VXxGWGYHnrabr5SzFIOYOgIb0sYEApWP8tex-ck-him_CD02nJkD4icsU33uRadBM7fa1S2uIWTKkoIFSvX77yCce6OkChp5wya2SqiflUUl_Puv27HUEPVfYdCUe091_5xT8TG4qu0bbGvBDOXdc5SGAxYO7NR3XSqZG4HhD8onVL3SluI0N0-qW3JXm9haxPoK-w7c9lHgBdYD2g4cJlQuLtjmmGVfBdGm-AsmQ",
  //     "refresh_expires_in": 2592000,
  //     "refresh_token": "lenovocb39387371066bfd957ba078aa99fa0904d7958d806ee3ce74d87b64b9e5a659848b16b9f5c83d24ab52af95455216edidccn",
  //     "scope": "openid",
  //     "token_type": "Bearer"
  //     }
  console.log('----getAccessToken', tokenUrl)
  const response = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  })
  try {
    if (response.status == 200) {
      const tokenData = await response.json()
      refreshToken = tokenData.refresh_token
      accessToken = tokenData.access_token
      console.log('Access Token:', tokenData, refreshToken, accessToken)
      //根据access_token获取用户信息
      getUserInfoByToken(accessToken)
      // todo 将refresh_token及时间戳存入注册表HKEY_CURRENT_USER\Software\Lenovo\Lenovo AI Now\MainAppInfo\rtk
      // todo 开启定时服务， 使用refresh_token定时刷新token，刷新间隔30分钟
    } else {
      console.log('getAccessToken failed:', response)
    }
  } catch (error) {
    console.log('getAccessToken error:', error, response)
  }
}
/**根据access_token获取用户信息 */
async function getUserInfoByToken(accessToken: string) {
  const url = `${baseURL}/v1.0/utility/lenovoid/oauth2/me?access_token=${accessToken}`
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  })
  if (response.status == 200) {
    userInfo = await response.json()
    console.log('getUserInfoByToken userInfo:', userInfo)
    // 通知页面更新并存储数据
    global.mainWindow.webContents.send(NOTIFYSCHEME.USERINFO, userInfo)
  } else {
    console.log('getUserInfoByToken failed:', response)
  }
}
/**刷新token */
/*async function freshLidToken(freshToken:string, codeVerifier:string){
    const url = `${baseURL}/v1.0/utility/lenovoid/oauth2/token?grant_type=refresh_token&client_id=<clientId>&refresh_token=${freshToken}&code_verifier=${codeVerifier}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
      },
    });
    console.log('-------getAccessToken -res---',response)
    // todo 更新存储的access_token和refresh_token, 
    // 更新注册表HKEY_CURRENT_USER\Software\Lenovo\Lenovo AI Now\MainAppInfo\rtk
   }*/
