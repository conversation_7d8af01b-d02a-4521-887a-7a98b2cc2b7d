// @ts-ignore TODO 打包机会报类型错误阻断build:win
import { executeAinow } from '@ainow/services'
import { PLUGINID, PROVIDER, sessionType, IChatResponseParams } from '@ainow/types'
import { IStreamChatParams, EdgeStreamChatParams } from '../types'
const {
  LLMResponse,
  streamChat: streamChatLLM,
  fetchOpenAICompatibilityStreamChat,
  recentChatHistory
  // eslint-disable-next-line @typescript-eslint/no-var-requires
} = require('@ainow/shared')

function send(params: IChatResponseParams) {
  global.mainWindow.webContents.send(
    'stream-chat-response-chunk',
    params.messageId,
    JSON.stringify({
      ...params.json,
      provider: params.provider,
      model: params.model,
      threadId: params.threadId
    })
  )
}

function dealChunk(
  content: string,
  status: {
    reference: number
    recommandation: number
  }
) {
  let res = content

  if (content.includes('$$$$Reference:$$')) {
    // 引用
    const [, c = ''] = content.split('$$$$Reference:$$')
    const [t = '', h = ''] = c.split(' $$ ')
    const title = t.trim()

    if (status.reference === 0) {
      res = '\n ### 引用网页 \n'
      res += `* [${title}](${h.trim()} "${title}")\n`

      status.reference++
    } else {
      res = `* [${title}](${h.trim()} "${title}")\n`
    }
  } else if (content.includes('$$$$Recommendation:$$')) {
    // 推荐
    const [, c = ''] = content.split('$$$$Recommendation:$$')
    if (status.recommandation === 0) {
      res = '\n ### 关联问题 \n'
      res += `* ${c.trim()}\n`
      status.recommandation++
    } else {
      res = `* ${c.trim()}\n`
    }
  }

  return res
}
async function parseEdgeStreamChat(
  {
    accountId, // 账号
    threadId, // 对话
    provider,
    model,
    message,
    messageId
  }: EdgeStreamChatParams,
  abortSignal?: AbortSignal
) {
  let history = {
    chatHistory: []
  }
  const isWeb = model === 'edge.web'

  try {
    history = await recentChatHistory({
      chatOpt: {
        accountId,
        threadId
      },
      messageLimit: 3
    })
  } catch (e) {
    console.error(e)
  }

  const options = {
    method: 'POST',
    headers: {
      'content-type': 'application/json'
    },
    body: JSON.stringify({
      query: message,
      history: history?.chatHistory || []
    }),
    signal: abortSignal
  }

  // kbqa

  try {
    const response = await fetch(
      `https://workflow.lenovo.com/${isWeb ? 'query_qa_web' : 'query_qa'}`,
      options
    )
    const status = { reference: 0, recommandation: 0 }

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }
    console.log('Response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))
    const reader = response.body?.getReader()
    const decoder = new TextDecoder('utf-8')
    while (true) {
      const { done, value } = await reader!.read()
      if (done) {
        send({
          messageId,
          provider,
          model,
          threadId,
          json: {
            type: 'textResponseChunk',
            textResponse: '',
            close: true,
            error: false
          }
        })
        break
      }
      const chunk = decoder
        .decode(value, { stream: true })
        .trim()
        .split('\n\n')
        .map((s) => {
          return dealChunk(
            s
              .replace(/^data:\s*/, '')
              .replace(/[\r\n]*$/, '')
              .replace(/(\\n)*$/, '\n')
              .replace(/[\n]*$/, '')
              .replace(/!\[(.*?)\]\((.*?)\s*(?:["'](.+?)["'])?\)/, ''),
            status
          )
        })
        .join('')

      if (chunk === '[DONE]') break
      try {
        !!chunk.length &&
          send({
            messageId,
            provider,
            model,
            threadId,
            json: {
              type: 'textResponseChunk',
              textResponse: chunk,
              close: false,
              error: false
            }
          })
      } catch (ex) {
        send({
          messageId,
          provider,
          model,
          threadId,
          json: {
            type: 'textResponseChunk',
            textResponse: '',
            close: true,
            error: true
          }
        })
      }
    }
    // console.log('Response stream ended.', fullText)
  } catch (err) {
    console.error('Request error:', err)
    send({
      messageId,
      provider,
      model,
      threadId,
      json: {
        type: 'textResponseChunk',
        textResponse: '',
        close: true,
        error: true
      }
    })
  }
}

async function parseAINowStreamChat(
  {
    accountId, // 账号
    threadId, // 对话
    provider,
    model,
    messageId,
    ainowParams
  }: IStreamChatParams,
  abortSignal?: AbortSignal
) {
  // TODO 处理这两个维度的逻辑
  console.log(accountId, threadId)
  try {
    ainowParams.data.lid = messageId
    // if (model === AINOW_MODEL.AINOW_MODEL_2) {
    //   data = null
    // }
    console.log('ainow传的参数', JSON.stringify(ainowParams))
    // lid 生成 返回匹配 处理
    const res = await executeAinow(
      ainowParams,
      (data) => {
        if (data.data.lid === ainowParams.data.lid) {
          // 只将流式的数据返回 done=true时 数据会是空 也需要返回 用于持久化存储的结束标识； 并且校验id匹配
          console.log(JSON.stringify(data), data.data.done, 'ainow返回的string化的data')
          // 发送分流数据
          send({
            messageId,
            provider,
            model,
            threadId,
            json: data
          })
        }
      },
      'request',
      abortSignal
    )
    console.log('ainow应答最终', res)
  } catch (e) {
    console.log('ainow服务异常', e)
  }
}

async function parseOllamaStreamChat(
  {
    accountId, // 账号
    threadId, // 对话
    provider,
    model,
    message,
    messageId
  }: IStreamChatParams,
  abortSignal?: AbortSignal
) {
  // OLLAMA
  const myResponse = new LLMResponse()
  console.log('[main thread] streamChat', accountId, threadId, provider, model, message)
  myResponse.on('data', (data) => {
    try {
      const json = JSON.parse(data.replace(/^data:\s*/, ''))
      send({
        messageId,
        provider,
        model,
        threadId,
        json
      })
    } catch (ex) {
      console.error(ex)
    }
  })
  streamChatLLM(
    message,
    {
      accountId,
      threadId,
      response: myResponse,
      chatProvider: provider,
      chatModel: model
    },
    abortSignal
  )
}

export function streamChat(params: IStreamChatParams, abortSignal?: AbortSignal) {
  switch (params.provider) {
    case PROVIDER.EMBEDDING:
      fetchOpenAICompatibilityStreamChat('https://embedding.lenovo.com/v1/chat/completions', {
        ...params,
        abortSignal,
        callback: (data) =>
          send({
            messageId: params.messageId,
            provider: params.provider,
            model: params.model,
            threadId: params.threadId,
            json: data
          }),
        isDone: (chunk) => chunk === '[DONE]',
        doneCallback: (data) =>
          send({
            messageId: params.messageId,
            provider: params.provider,
            model: params.model,
            threadId: params.threadId,
            json: data
          })
      })
      break
    case PROVIDER.DFAI:
      fetchOpenAICompatibilityStreamChat(
        'https://dfai.lenovo.com/e/e8i7uek48mlgr9bo/chat/completions',
        {
          ...params,
          abortSignal,
          callback: (data) =>
            send({
              messageId: params.messageId,
              provider: params.provider,
              model: params.model,
              threadId: params.threadId,
              json: data
            }),
          isDone: (chunk) => chunk === '[DONE]',
          doneCallback: (data) =>
            send({
              messageId: params.messageId,
              provider: params.provider,
              model: params.model,
              threadId: params.threadId,
              json: data
            })
        }
      )
      break
    case PROVIDER.EDGE:
      parseEdgeStreamChat(params, abortSignal)
      break
    case PROVIDER.AINOW:
      parseAINowStreamChat(params, abortSignal)
      break
    case PROVIDER.OLLAMA:
    default:
      parseOllamaStreamChat(params, abortSignal)
      break
  }
}
