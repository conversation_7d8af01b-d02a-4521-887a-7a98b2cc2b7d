import fs from 'node:fs'
import path from 'node:path'
import { app } from 'electron'
import type { PrismaClient } from '@prisma/client'
import type { ISharedFacade, PrismaMigration } from '@ainow/shared'
import * as sharedFacade from '@ainow/shared'
import { dbContext } from './dbContext'

const { getPrisma, getDBConstants, DBModels, runPrismaCommand } =
  sharedFacade as unknown as ISharedFacade
const { getAccountModel, getThreadModel, getThreadMessageModel } = DBModels
const { dbPath, latestMigration } = getDBConstants(dbContext)

const schemaPath = global.isElectronDev
  ? dbContext.getSchemaPrismaPath!()
  : path.join(process.resourcesPath, 'prisma', 'schema.prisma')
const prismaPath = dbContext.getPrismaPath?.()

process.env.DATABASE_URL = `file:${dbPath}`
const prisma: PrismaClient = getPrisma(dbContext)

const modelsFactory = {
  Account: getAccountModel,
  Thread: getThreadModel,
  ThreadMessage: getThreadMessageModel
}

const runPrisma = (...args: string[]) =>
  runPrismaCommand({
    ctx: dbContext,
    command: [...args, '--schema', schemaPath],
    prismaPath
  })

const makeEmptyDb = () => {
  const from = path.join(process.resourcesPath, 'prisma', 'ainow.db.tmp')
  const to = path.join(app.getPath('userData'), 'ainow.db')
  const toDir = path.dirname(to)
  try {
    if (!fs.existsSync(toDir)) {
      fs.mkdirSync(toDir, { recursive: true })
    }
    fs.copyFileSync(from, to)
    console.log(`empty db file copied successfully from ${from} to ${to}`)
  } catch (error: any) {
    console.error(`Failed to copy db file: ${error?.message as string}`)
  }
}

export async function handlePersistenceAction(
  _,
  model: string,
  action: string,
  ...args: unknown[]
) {
  try {
    const modelInstanceGetter = modelsFactory[model]
    if (!modelInstanceGetter) throw new Error(`factory function for ${model} not found`)
    const m = modelInstanceGetter(prisma)
    if (!m) throw new Error(`model ${model} not found`)
    return m[action](...args)
  } catch (error) {
    console.error(error)
  }
  return null
}

// 每次应用启动时运行 Prisma 的 migrate deploy 命令
// 确保用户 sqlite 数据库在应用更新且数据库模式更改时能自动迁移
export async function initDB() {
  let needsMigration = false
  const dbExists = fs.existsSync(dbPath)
  if (!dbExists) {
    needsMigration = true
    // 确保数据库文件存在
    fs.closeSync(fs.openSync(dbPath, 'w'))
  }

  try {
    const latest: PrismaMigration[] =
      await prisma.$queryRaw`select * from _prisma_migrations order by finished_at`
    const mname = latest[latest.length - 1]?.migration_name
    needsMigration = !mname.endsWith(latestMigration)
    console.log(`db latest migration: ${mname}, want migration suffix: ${latestMigration}`)
  } catch (e) {
    // @ts-ignore debug
    console.error('[db.ts SELECT *]', e, prisma?._engine?.datasourceOverrides)
    needsMigration = true
  }
  if (!needsMigration) {
    console.log(`%c Does not need migration -- ${schemaPath}`, 'color: green')
    return
  }

  try {
    console.log(
      `%c Needs a migration. Running prisma migrate with schema path ${schemaPath}`,
      'color: red'
    )
    await runPrisma('migrate', 'deploy')
    console.log('√ Migration done.')
  } catch (e) {
    console.error('× Migration failed.', e)
    try {
      await runPrisma('migrate', 'reset', '--force')
      await runPrisma('migrate', 'deploy')
      console.log('Migration reset.')
    } catch (ex) {
      makeEmptyDb()
      await runPrisma('migrate', 'deploy')
      console.error('🐝 Migration again and anain', ex)
    }
  }
}
