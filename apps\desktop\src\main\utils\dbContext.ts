import path from 'node:path'
import { app } from 'electron'
import type { IContextDB } from '@ainow/shared'
import { isDev } from './isDev'

export const dbContext: IContextDB = {
  getDBPath: () =>
    isDev
      ? // 开发目录 packages/shared/src/storage/ainow.db
        path.posix.resolve('../../packages/shared/src/storage/ainow.db')
      : // ~\AppData\Roaming\@ainow\ainow.db
        // 这里要注意 package.json 中的 productName 会影响到
        path.join(app.getPath('userData'), 'ainow.db'),

  getPrismaEnginesDir: () => app.getAppPath().replace('app.asar', ''),

  getPrismaEnginesBase: () =>
    isDev
      ? '../../packages/shared/node_modules/@prisma/engines/'
      : path.resolve(app.getAppPath().replace('app.asar', ''), 'prisma/'),

  getSchemaPrismaPath: () =>
    isDev
      ? path.join(
          app.getAppPath().replace('app.asar', 'app.asar.unpacked'),
          '../../packages/shared/src/prisma/',
          'schema.prisma'
        )
      : path.resolve(app.getAppPath().replace('app.asar', ''), 'prisma/schema.prisma'),

  getPrismaPath: () =>
    isDev
      ? void 0
      : path.resolve(
          app.getAppPath().replace('app.asar', 'app.asar.unpacked'),
          'node_modules/prisma/build/index.js'
        )
}

console.log(
  [
    '%c 💻 desktop db context ⌨',
    isDev,
    dbContext.getPrismaPath?.(),
    dbContext.getSchemaPrismaPath?.(),
    dbContext.getDBPath(),
    dbContext.getPrismaEnginesBase?.()
  ].join('\n'),
  'color: red;'
)
