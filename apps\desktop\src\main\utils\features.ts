function parseCliArgs(key: string) {
  // 动态判断环境：开发环境需保留前两个系统参数，生产环境只需跳过第一个
  const skipCount = process.defaultApp ? 2 : 1
  const args = process.argv.slice(skipCount)
  console.log('🐱 启动参数', args)

  const features = [] as string[]
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    if (arg === key && args[i + 1]) {
      features.push(args[i + 1])
      i++
    } else if (arg.startsWith(`${key}=`)) {
      features.push(arg.split('=')[1])
    }
  }
  return features
}

export const cliFeatures = parseCliArgs('--feature')

export enum APP_FEATURES {
  MAIN_CHAT,
  MINI_CHAT,
  SEARCHBAR,
  SCREENSHOT,
  WORD_SNIFFER,
  TRAY,
  SPEECH_STT
}
