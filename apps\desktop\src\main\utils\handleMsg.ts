import { shell, screen } from 'electron'
import { WindowType, ASK_TYPE } from '@ainow/types'
console.log('handlePostMsg', WindowType, ASK_TYPE)
import {
  // MAIN_WINDOW_HEIGHT,
  // MAIN_WINDOW_WIDTH,
  MINI_WINDOW_HEIGHT,
  MINI_WINDOW_WIDTH
} from './constants'
const getXY = () => {
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  const x = width - MINI_WINDOW_WIDTH
  const y = height - MINI_WINDOW_HEIGHT
  return { x, y }
}
// import { createLocalChat } from '../LocalChat'
export const handlePostMsg = (url: string) => {
  // ainow://open?target=AdditionalWords=AskType=coord={x:0,y:0}
  const urlobj = new URL(url)
  const searchparam = urlobj.searchParams
  const target = searchparam.get('target') // 打开大窗还是小窗
  const AskType = searchparam.get('AskType') // 提问类型
  const AdditionalWords = searchparam.get('AdditionalWords') // 划词的选择的文本
  console.log(searchparam.get('coord'), typeof searchparam.get('coord'), 'coooord')
  const coord = JSON.parse(searchparam.get('coord') || '{}')

  console.log('从schemeurl解析到的参数', target, AskType, AdditionalWords)
  switch (target) {
    case WindowType.MAIN:
      console.log('打开大窗')
      global.mainWindow.show()
      global.mainWindow.webContents.send('postChannelMsg', { target, AskType, AdditionalWords })
      break
    case WindowType.MINI:
      console.log('打开小窗')
      // 坐标默认右下角
      if (global.mainWindow.isFullScreen()) {
        global.mainWindow.setFullScreen(false)
      }
      if (global.mainWindow.isMaximized()) {
        global.mainWindow.unmaximize()
      }

      global.mainWindow.setBounds({
        x: coord.x || getXY().x,
        y: coord.y || getXY().y,
        width: MINI_WINDOW_WIDTH,
        height: MINI_WINDOW_HEIGHT
      })

      global.mainWindow.show()
      global.mainWindow.webContents.send('postChannelMsg', { target, AskType, AdditionalWords })
      // global.miniWindow = createLocalChat()
      // global.miniWindow.webContents.send('postChannelMsg', { target, AskType, AdditionalWords })
      break
    case WindowType.WEBSITE:
      shell.openExternal('https://www.example.com')
  }
}
