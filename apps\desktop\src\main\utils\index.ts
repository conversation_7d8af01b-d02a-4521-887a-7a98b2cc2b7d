export { handlePersistenceAction, initDB } from './db'
import { streamChat } from './chat'
import { getPKBFiles } from './PKB'
import type { IStreamChatParams } from '../types'
import sharedFacade, { type ISharedFacade } from '@ainow/shared'

const { listModels } = (sharedFacade as unknown as ISharedFacade).LLMModels

// 模型通信操作
export function handleProviderChange(_, name: string) {
  return listModels(name)
}

// 流式对话
const aborts = new Map<string, AbortController>()
export function handleStreamChat(_, params: IStreamChatParams) {
  const abortController = new AbortController()
  const promptId = params.messageId
  aborts.set(promptId, abortController)
  // console.log('handleStreamChat', promptId, abortController.signal)
  // abortController.signal.addEventListener('abort', (ev) => {
  //   console.log('BBB', abortController.signal.reason)
  // })
  return streamChat(params, abortController.signal)
}
export function handleAbortChat(_, promptId?: string) {
  if (!promptId) return
  const abortController = aborts.get(promptId)
  if (abortController) {
    abortController.abort()
    // console.log('handleAbortChat', promptId, abortController.signal.aborted)
  }
}
