import { join } from 'path'
import dayjs from 'dayjs'
import { app, crashReporter } from 'electron'
import log from 'electron-log/main'

// TODO 日志开启、有效期等

log.initialize() // 本地日志文件 ~\AppData\Roaming\@ainow\desktop\logs\xxx.log
log.errorHandler.startCatching()
log.transports.file.resolvePathFn = () => {
  const date = dayjs().format('YYYY-MM-DD')
  const logsDir = app.getPath('userData')
  const logFileName = `main-${date}.log`
  return join(logsDir, 'logs', logFileName)
}
Object.assign(console, log.functions)
global.console = console
// 获取崩溃堆栈文件存放路径
// const crashFilePath = ''
let crashDumpsDir = ''
try {
  crashDumpsDir = app.getPath('crashDumps')
  console.log('————————crashDumpsDir:', crashDumpsDir)
} catch (e) {
  console.error('获取崩溃文件路径失败', e)
}
// 开启crash捕获
crashReporter.start({
  productName: 'ainow',
  companyName: 'aaa',
  // submitURL: 'https://www.xxx.com', // 上传到服务器的地址
  uploadToServer: false, // 不上传服务器
  ignoreSystemCrashHandler: false // 不忽略系统自带的崩溃处理，为 true 时表示忽略，崩溃时不会生成崩溃堆栈文件
})
