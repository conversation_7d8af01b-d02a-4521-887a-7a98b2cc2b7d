import { protocol, net } from 'electron'
import url from 'url'
protocol.registerSchemesAsPrivileged([
  {
    scheme: 'atom',
    privileges: { bypassCSP: true, standard: true, secure: true, supportFetchAPI: true }
  }
])
import path from 'path'

export const protoHandle = () => {
  protocol.handle('atom', (request) => {
    console.log('request---', request.url)

    const filePath = request.url.slice('atom://'.length)
    const realPath = filePath.slice(0, 1) + ':' + filePath.slice(1)
    console.log('filePath---', realPath, filePath)
    const decodedPath = decodeURIComponent(realPath)
    const windowsPath = path.normalize(decodedPath)
    console.log(realPath, decodedPath, 'decodedPath', windowsPath)
    return net.fetch(url.pathToFileURL(windowsPath).toString())
  })
}
