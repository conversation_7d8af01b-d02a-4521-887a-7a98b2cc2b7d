import fs from 'fs'

// import logInit from './log'
// import path from 'path'

export function getResourcesPath(fullPath: string): string {
  return fullPath.replace('app.asar', 'app.asar.unpacked')
}
export const getCurrentUTCDateStr = () => {
  const currentDate = new Date()
  return (
    currentDate.getFullYear().toString() +
    (currentDate.getUTCMonth() + 1).toString().padStart(2, '0') +
    currentDate.getUTCDate().toString().padStart(2, '0')
  )
}
export const checkFileExistsSync = (filePath: string) => {
  try {
    fs.statSync(filePath)
    return true
  } catch (error) {
    return false
  }
}
export const createFile = (path: string, initStr: string = '', force = false) => {
  try {
    if (force) {
      fs.writeFileSync(path, initStr)
    } else {
      const isExist = checkFileExistsSync(path)
      if (!isExist) {
        fs.writeFileSync(path, initStr)
      }
    }
  } catch (err) {
    console.error(err)
  }
}
// export const ChineseTest=(str:string)=>{}
export const getFile = async (path: string) => {
  try {
    const data = await fs.promises.readFile(path, 'utf-8')
    return data
  } catch (error) {
    console.log(error)
    return ''
  }
}
