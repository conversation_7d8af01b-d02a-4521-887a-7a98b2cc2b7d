import path from 'path'
import { app, systemPreferences } from 'electron'
import { v4 as uuidV4 } from 'uuid'
import { isDev } from './isDev'

const getSTTPath = () =>
  path
    .join(__dirname, '../..', import.meta.env.MAIN_VITE_STT_PATH)
    .replace('app.asar', 'app.asar.unpacked')

// TODO 让renderer调用
enum STT_TYPE {
  ERROR = 0,
  SPEECHING = 1,
  PAUSE = 2
}

interface ISTTParam {
  dllPath: string
  sttPath: string
  callback: (type: STT_TYPE, text: string) => void
}

const addon = require(path.join(getSTTPath(), '/speech.node'))

let hasLoaded = false

function startSTT(id, { dllPath, sttPath, callback }: ISTTParam) {
  console.log('🎤STT', dllPath, sttPath)

  if (!hasLoaded) {
    addon.loadDLL(dllPath)
    hasLoaded = true
  }

  if (addon.debug(sttPath)) {
    addon.startSTT(sttPath, (type, text) => {
      console.log(type, text)
      callback(type, text)
    })
    console.log('🎤STT after call startSTT')
  }

  return () => {
    addon.stopSTT()
    // addon.unloadDll()
    console.log('stt exit', id)
  }
}

const sttCache: Record<string, { stop: Function; callback: Function }> = {}

const toWin32path = (p: string) => {
  let result: string = path.win32.normalize(p).replace(/\\/g, '\\\\')
  if (!result.endsWith('\\')) result = result + '\\\\'
  return result
}

export async function handleSTTAction(_, type: 'start' | 'stop', sstId?: string) {
  if (type === 'stop') {
    if (!sstId) throw new Error('no sst id')
    console.log('sttaction stop', sstId, sttCache[sstId])
    try {
      sttCache[sstId]?.stop()
      delete sttCache[sstId]
      return null
    } catch (ex) {
      console.log('handleSTTAction', ex)
    }
    return null
  }

  if (type !== 'start') return null
  try {
    const id = uuidV4()
    const callback = (type, text) =>
      global.mainWindow.webContents.send('stt-response', id, type, text)
    const dllPath = toWin32path(
      isDev
        ? getSTTPath()
        : path.resolve(app.getAppPath().replace('app.asar', 'app.asar.unpacked'), 'resources/stt')
    )
    const sttPath = toWin32path(path.join(app.getPath('userData'), 'sttmodels'))

    const stop = startSTT(id, {
      dllPath,
      sttPath,
      callback
    })
    sttCache[id] = { callback, stop }

    return id
  } catch (error) {
    console.error(error)
    return null
  }
}
