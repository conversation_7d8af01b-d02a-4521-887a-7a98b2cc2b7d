import { BrowserWindow, clipboard, ipcMain, Menu, screen, shell } from 'electron'
import { getSelection } from 'node-selection'
import { getSelectionText } from '@xitanggg/node-selection'
import { uIOhook, UiohookKey } from 'uiohook-napi'
import path, { join } from 'path'
import { AppEvent } from '../core/AppEvent'
import { is } from '@electron-toolkit/utils'
import { WordSnifferChannel } from '../../preload/apis/WordSnifferApi'
import { ASK_TYPE } from '@ainow/types'
import { handlePostMsg } from '../utils/handleMsg'

// 与其它模块交互的事件，添加模块的前缀，避免冲突 例如：ws:copy
export const WordSnifferEvent = {
  CLOUD_CHAT: 'ws:cloud_chat',
  LOCAL_CHAT: 'ws:local_chat',
  TRANSLATE: 'ws:translate',
  POLISH: 'ws:polish',
  SEARCH_FILE: 'ws:search_file',
  CLOUD_ASK: 'ws:cloud_ask',
  SUMMARIZE: 'ws:summarize',
  COPY: 'ws:copy',
  SETTING: 'ws:setting'
} as const

export class WordSniffer {
  private currentMenu: Menu | null = null
  private mainWindow: BrowserWindow
  private selectedText: string = ''
  private isSelecting: boolean = false
  private isDragging: boolean = false
  private lastMouseDownTime: number = 0
  private lastClickTime: number = 0
  private clickTimes: Array<number> = []
  // private lastMousePosition = { x: 0, y: 0 }
  private mouseDownPosition = { x: 0, y: 0 }
  private selectionTimeout: NodeJS.Timeout | null = null
  private readonly DOUBLE_CLICK_TIMEOUT = 500

  constructor() {
    this.mainWindow = this.createWindow()
    this.addEvents()
    this.addIpc()
    this.initHook()
    this.start()
  }

  private createWindow(): BrowserWindow {
    const window = new BrowserWindow({
      width: 400,
      height: 40,
      show: false,
      frame: false,
      modal: false,
      alwaysOnTop: true,
      transparent: true,
      skipTaskbar: true,
      autoHideMenuBar: true,
      resizable: false,
      webPreferences: {
        preload: join(__dirname, '../preload/index.js'),
        sandbox: false
      }
    })

    // window.setVisibleOnAllWorkspaces(true)
    window.setAlwaysOnTop(true, 'floating')
    window.on('ready-to-show', () => {
      window.hide() // 初始化时隐藏窗口
      // window.webContents.openDevTools()
    })

    window.webContents.setWindowOpenHandler((details) => {
      shell.openExternal(details.url)
      return { action: 'deny' }
    })

    // 加载界面
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      window.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/#word/')
    } else {
      window.loadFile(join(__dirname, '../renderer/index.html'), { hash: 'word' })
    }

    return window
  }

  private addEvents() {
    AppEvent.on(WordSnifferEvent.SUMMARIZE, (text) => {
      console.log('cloud chat:', text)
      const url = new URLSearchParams(text).toString()
      // console.log('cloud chat url:', url)
      //url 样例： target=mini&AdditionalWords=%E6%9F%A5%E8%AF%A2%E5%AD%97%E7%AC%A6%E4%B8%B2&AskType=5
      handlePostMsg('ainow://open?' + url)
    })
    AppEvent.on(WordSnifferEvent.CLOUD_ASK, (text) => {
      console.log('cloud chat:', text)
      const url = new URLSearchParams(text).toString()
      handlePostMsg('ainow://open?' + url)
    })
    AppEvent.on(WordSnifferEvent.SEARCH_FILE, (text) => {
      console.log('cloud chat:', text)
      const url = new URLSearchParams(text).toString()
      handlePostMsg('ainow://open?' + url)
    })
  }
  private addIpc() {
    ipcMain.on(WordSnifferChannel.HIDE_WINDOW, () => {
      this.mainWindow.hide()
    })
    ipcMain.on(WordSnifferChannel.SHOW_CONTEXT_MENU, (event) => {
      console.log('show context menu')
      const template: Electron.MenuItemConstructorOptions[] = [
        {
          label: 'Search Flies',
          icon: path.join(__dirname, '../../resources/menu', 'search.png'),
          click: () => {
            event.reply(WordSnifferChannel.MENU_ACTION, 'search')
            AppEvent.emit(WordSnifferEvent.SEARCH_FILE, {
              target: 'mini',
              AdditionalWords: this.selectedText,
              AskType: ASK_TYPE.SEARCH
            })
          },
          type: 'normal',
          enabled: true,
          visible: true
        },
        {
          label: 'Cloud Ask',
          icon: path.join(__dirname, '../../resources/menu', 'cloud.png'),
          click: () => {
            event.reply(WordSnifferChannel.MENU_ACTION, 'cloud')
            AppEvent.emit(WordSnifferEvent.CLOUD_ASK, {
              target: 'mini',
              AdditionalWords: this.selectedText,
              AskType: ASK_TYPE.CLOUD_ASK
            })
          },
          type: 'normal',
          enabled: true,
          visible: true
        },
        {
          label: 'Summarize',
          icon: path.join(__dirname, '../../resources/menu', 'summarize.png'),
          click: () => {
            event.reply(WordSnifferChannel.MENU_ACTION, 'summarize')
            AppEvent.emit(WordSnifferEvent.SUMMARIZE, {
              target: 'mini',
              AdditionalWords: this.selectedText,
              AskType: ASK_TYPE.SUMMARIZE
            })
          },
          type: 'normal',
          enabled: true,
          visible: true
        },
        {
          label: 'Copy',
          icon: path.join(__dirname, '../../resources/menu', 'copy.png'),
          click: () => {
            event.reply(WordSnifferChannel.MENU_ACTION, 'copy')
            clipboard.writeText(this.selectedText)
          },
          type: 'normal',
          enabled: true,
          visible: true
        },
        {
          type: 'separator'
        },
        {
          label: 'Settings',
          icon: path.join(__dirname, '../../resources/menu', 'setting.png'),
          click: () => {
            event.reply(WordSnifferChannel.MENU_ACTION, 'setting')
            AppEvent.emit(WordSnifferEvent.SETTING, this.selectedText)
          },
          type: 'normal',
          enabled: true,
          visible: true
        }
      ]

      this.currentMenu = Menu.buildFromTemplate(template)
      const browserWindow = BrowserWindow.fromWebContents(event.sender)
      if (browserWindow) {
        this.currentMenu.popup({
          window: browserWindow
        })
      } else {
        console.warn('No valid BrowserWindow found for context menu.')
      }
    })
  }

  private isValidSelection(
    mouseDownPos: { x: number; y: number },
    mouseUpPos: { x: number; y: number }
  ): boolean {
    const minDistance = 40
    const dx = mouseUpPos.x - mouseDownPos.x
    const dy = mouseUpPos.y - mouseDownPos.y
    return Math.sqrt(dx * dx + dy * dy) >= minDistance
  }

  private async showSelectionWindow(x: number, y: number): Promise<void> {
    try {
      this.selectedText = await this.selectionText()
      console.log('selectedText:', this.selectedText)
      if (this.selectedText == '') return

      const currentDisplay = screen.getDisplayNearestPoint({ x, y })
      const bounds = this.mainWindow.getBounds()

      const windowWidth = bounds.width
      const windowHeight = bounds.height
      const mousePos = screen.getCursorScreenPoint()
      let posX = mousePos.x
      let posY = mousePos.y + 10

      if (posX + windowWidth > currentDisplay.bounds.x + currentDisplay.bounds.width) {
        posX = currentDisplay.bounds.x + currentDisplay.bounds.width - windowWidth
      }
      if (posY + windowHeight > currentDisplay.bounds.y + currentDisplay.bounds.height) {
        posY = y - windowHeight - 10
      }

      this.mainWindow.setPosition(posX, posY)
      this.mainWindow.show()
      this.mainWindow.setFocusable(false)
      this.mainWindow.webContents.send(WordSnifferChannel.SELECTED_TEXT, this.selectedText)

      // if (this.selectionTimeout) {
      //   clearTimeout(this.selectionTimeout)
      // }
      // this.selectionTimeout = setTimeout(() => {
      //   if (this.mainWindow && !this.mainWindow.isFocused()) {
      //     this.mainWindow.hide()
      //   }
      // }, 3000)
    } catch (error) {
      console.error('Error handling selection:', error)
    }
  }

  private async _selectionText(): Promise<string> {
    try {
      const selection = await getSelection()
      return selection.text?.trim() ?? ''
    } catch (error) {
      console.error('Error getting selection:', error)
      return ''
    }
  }

  private async selectionText(): Promise<string> {
    try {
      const selection = getSelectionText()
      return selection.trim()
    } catch (error) {
      console.error('Error getting selection:', error)
      return ''
    }
  }

  private async hasValidSelection(): Promise<boolean> {
    const text = await this.selectionText()
    return text != ''
  }

  private isPointInWindow(x: number, y: number): boolean {
    const bounds = this.mainWindow.getBounds()
    const display = screen.getDisplayMatching(bounds)
    const scaleFactor = display.scaleFactor

    const scaledBounds = {
      x: bounds.x * scaleFactor,
      y: bounds.y * scaleFactor,
      width: bounds.width * scaleFactor,
      height: bounds.height * scaleFactor
    }

    // console.log('scaledBounds', x, y, scaledBounds)

    return (
      x >= scaledBounds.x &&
      x <= scaledBounds.x + scaledBounds.width &&
      y >= scaledBounds.y &&
      y <= scaledBounds.y + scaledBounds.height
    )
  }

  private initHook(): void {
    uIOhook.on('mousedown', (event) => {
      const currentTime = Date.now()
      const timeSinceLastClick = currentTime - this.lastClickTime

      // if (timeSinceLastClick <= this.DOUBLE_CLICK_TIMEOUT) {
      //   setTimeout(() => {
      //     this.showSelectionWindow(event.x, event.y)
      //   }, 50)
      // }

      this.lastClickTime = currentTime
      this.mouseDownPosition = { x: event.x, y: event.y }
      this.lastMouseDownTime = currentTime
      this.isDragging = false
      this.isSelecting = true
    })

    uIOhook.on('mousemove', (event) => {
      const dx = event.x - this.mouseDownPosition.x
      const dy = event.y - this.mouseDownPosition.y

      if (!this.isDragging && (Math.abs(dx) > 5 || Math.abs(dy) > 5)) {
        this.isDragging = true
      }

      // this.lastMousePosition = { x: event.x, y: event.y }
      // console.log(this.lastMousePosition)
    })

    uIOhook.on('mouseup', async (event) => {
      this.clickTimes.push(Date.now())
      let timeDiff = 10 * 1000
      if (this.clickTimes.length > 2) {
        this.clickTimes.shift()
        timeDiff = this.clickTimes[this.clickTimes.length - 1] - this.clickTimes[0]
      }

      const windowWasVisible = this.mainWindow.isVisible()

      const isClickInWindow = windowWasVisible && this.isPointInWindow(event.x, event.y)
      // console.log('isClickInWindow', isClickInWindow, this.isPointInWindow(event.x, event.y))

      //console.log('isDragging', this.isDragging, timeDiff)
      if (
        (this.isDragging &&
          this.isValidSelection(this.mouseDownPosition, { x: event.x, y: event.y })) ||
        timeDiff <= this.DOUBLE_CLICK_TIMEOUT
      ) {
        const hasSelection = await this.hasValidSelection()
        if (hasSelection) {
          this.showSelectionWindow(event.x, event.y)
        } else if (windowWasVisible && !isClickInWindow) {
          this.hideWindow()
        }
      } else if (windowWasVisible && !isClickInWindow) {
        this.hideWindow()
      }

      this.isSelecting = false
      this.isDragging = false
    })

    uIOhook.on('wheel', (_event) => {
      if (this.mainWindow && this.mainWindow.isVisible()) {
        this.hideWindow()
      }
    })

    uIOhook.on('keydown', (event) => {
      if (event.keycode === UiohookKey.Escape && this.mainWindow) {
        this.hideWindow()
      }
    })
  }

  private hideWindow(): void {
    this.mainWindow.hide()
    this.currentMenu?.closePopup()
  }

  public start(): void {
    uIOhook.start()
  }

  public stop(): void {
    uIOhook.stop()
    if (this.selectionTimeout) {
      clearTimeout(this.selectionTimeout)
    }
  }

  public getWindow(): BrowserWindow {
    return this.mainWindow
  }
}
