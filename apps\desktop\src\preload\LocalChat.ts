import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { sessionType } from '@ainow/types/index'
// Custom APIs for renderer
const localChatApi = {
  closeLocalChat: () => ipcRenderer.send('close-local-chat'),
  sessionType: sessionType.AINowMini
}
const chatProps = {
  sessionType: sessionType.AINowGeneral
}
// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('localChatApi', localChatApi)
    contextBridge.exposeInMainWorld('chatProps', chatProps)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.localChatApi = localChatApi
  //window.chatProps = chatProps
}
