import { contextBridge, ipc<PERSON><PERSON><PERSON>, web<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { Bounds, ListItem } from '../types'

// Custom APIs for renderer
const api = {
  windowMove: (canMoving: boolean) => {
    ipcRenderer.send('window:move', canMoving)
  },
  windowMoveDrag: (canMoving: boolean) => {
    ipcRenderer.send('window:move-drag', canMoving)
  },
  openWebSite: (url: string) => ipcRenderer.invoke('openWebSite', url),
  openApp: (path: string) => ipcRenderer.invoke('openApp', path),
  openUWP: (path: string) => ipcRenderer.invoke('openUWP', path),
  openProgram: (path: string, args) => ipcRenderer.invoke('startApp', path, args),
  testOpen2: (path: string) => ipcRenderer.send('testOpen2', path),
  // handleMoveWin: (bounds: Bounds) => ipcRenderer.send('handleMoveWin', bounds),
  setIgnoreMouseEvents: (ignore: boolean, option?: { forward: boolean }): void =>
    ipcRenderer.send('setIgnoreMouseEvents', ignore, option),

  sendMiniMsg: (param: Record<string, unknown>): Promise<boolean> =>
    ipcRenderer.invoke('sendMiniMsg', param),
  translateMiniMsg: (param: Record<string, unknown>): Promise<boolean> =>
    ipcRenderer.invoke('translateMiniMsg', param),
  openSystemSettings: (param: string) => ipcRenderer.invoke('openSystemSettings', param),
  setBounds: (bounds: Partial<Bounds>) => {
    ipcRenderer.send('setWinBounds', bounds)
  },
  search: async (keyword: string): Promise<ListItem[]> =>
    await ipcRenderer.invoke('search', keyword),
  searchIcon: async (data: ListItem[]): Promise<ListItem[]> =>
    await ipcRenderer.invoke('searchIcon', data),
  setWindowBlur: () => {
    //设置窗口失去焦点 多次打开mini小窗时 没有触发失去焦点 需手动设置
    ipcRenderer.send('setWindowBlur')
  },
  onVisibleFocus: (callback: (value) => void) =>
    ipcRenderer.on('visible-focus', (_event, value) => callback(value)),
  sendRightClick: () => {
    ipcRenderer.send('show-context-menu')
  }, // 发送右键点击消息给主进程
  selectMuenItem: (callback: (args: IpcRendererEvent, param: string) => void) =>
    ipcRenderer.on('menu-item-clicked', callback), // 菜单项点击
  hideWindow: () => ipcRenderer.send('hide-window'), // 发送隐藏窗口的消息
  openAinow: () => ipcRenderer.send('open-ainow'), // 打开AInow
  // 新增：打开新窗口的方法
  openSecondaryWindow: () => {
    ipcRenderer.send('show-search-window') // 向主进程发送打开新窗口的请求
  }
}

window.addEventListener('DOMContentLoaded', () => {
  webFrame.setZoomFactor(1) // 在 DOM 加载前设置
  console.log('设置 webFrame 缩放因子为 1.0', Date.now())
})

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
