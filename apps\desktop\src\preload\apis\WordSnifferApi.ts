import { ipc<PERSON><PERSON><PERSON> } from 'electron'

// 划词模块ipc事件
export const WordSnifferChannel = {
  HIDE_WINDOW: 'ws:hide_window',
  SHOW_CONTEXT_MENU: 'ws:show_context_menu',
  SELECTED_TEXT: 'ws:selected_text',
  MENU_ACTION: 'ws:menu_action'
} as const

export const wordSnifferApi = {
  onSelectText: (callback) => ipcRenderer.on(WordSnifferChannel.SELECTED_TEXT, callback),
  hideWordSniffer: () => ipcRenderer.send(WordSnifferChannel.HIDE_WINDOW),
  showContextMenu: () => ipcRenderer.send(WordSnifferChannel.SHOW_CONTEXT_MENU),
  onMenuAction: (callback) =>
    ipcRenderer.on(WordSnifferChannel.MENU_ACTION, (_event, value) => callback(value))
}
