import { contextBridge, ipc<PERSON>enderer } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { IChatParams, WindowActions, sessionType } from '@ainow/types'
import { wordSnifferApi } from './apis/WordSnifferApi'

// Custom APIs for renderer
const api = {
  setTitle: (title: string) => ipcRenderer.send('set-title', title),
  dbStatus: (status: boolean) => ipcRenderer.send('db-status', status),
  windowAction: (windowId: number, action: WindowActions) => {
    ipcRenderer.send('window-action', windowId, action)
  },
  changeModel: (name: string) => ipcRenderer.invoke('llm:change-model', name),
  changeProvider: (name: string) => ipcRenderer.invoke('llm:change-provider', name),
  streamChat: (option: IChatParams) => ipcRenderer.invoke('llm:stream-chat', option),
  abortChat: (promptId: string) => ipcRenderer.invoke('llm:abort-chat', promptId),
  onStreamChatResponseChunk: (callback) =>
    ipcRenderer.on('stream-chat-response-chunk', (_event, ...args) => callback.apply(null, args)),
  persistenceAction: (model: string, action: string, ...args) =>
    ipcRenderer.invoke('llm:persistence-action', model, action, ...args),
  sttAction: (type, id) => ipcRenderer.invoke('speech:stt-action', type, id),
  onSttResponse: (callback) =>
    ipcRenderer.on('stt-response', (_event, ...args) => callback.apply(null, args)),
  onAuthToken: (callback) =>
    ipcRenderer.on('scheme_ainow.row', (_event, ...args) => callback.apply(null, args)),
  getAccessToken: (url: string) => ipcRenderer.invoke('get-access-token', url),
  onUpdateUserInfo: (callback) =>
    ipcRenderer.on('scheme_userinfo', (_event, ...args) => callback.apply(null, args)),
  toggle: () => ipcRenderer.invoke('dark-mode:toggle'),
  system: () => ipcRenderer.invoke('dark-mode:system'),
  listenerMsg: (callback) =>
    ipcRenderer.on('postChannelMsg', (_event, ...args) => callback.apply(null, args)),
  ...wordSnifferApi,
  openSettingModal: () => ipcRenderer.invoke('getSettings'),
  syncSettingsConfig: (option: any) => {
    ipcRenderer.invoke('syncSettings', option)
  },
  openFile: (filePath: string) => ipcRenderer.invoke('openFile', filePath),
  getPKBFileList: (option: any) => ipcRenderer.invoke('getPKBFileList', option),
  previewTargetFile: (previewFilePath: string) => ipcRenderer.invoke('previewFile', previewFilePath)
}
const chatProps = {
  sessionType: sessionType.AINowGeneral
}
// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
    contextBridge.exposeInMainWorld('chatProps', chatProps)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
  //window.chatProps = chatProps
}
