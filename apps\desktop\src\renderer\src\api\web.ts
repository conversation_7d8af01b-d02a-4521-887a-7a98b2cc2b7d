import { IChatParams } from '@ainow/types/index'
import { ApiService } from './types'

export class WebApiService implements ApiService {
  private baseUrl = '/'
  private aborts = new Map<string, AbortController>()

  getData() {
    return Promise.resolve('')
  }

  async getModels(providerName: string): Promise<{ id: string }[]> {
    const requestOptions = {
      method: 'GET'
    }
    const res = await fetch(`${this.baseUrl}v1/models?provider=${providerName}`, requestOptions)
    return res.json()
  }

  async streamChat(params: IChatParams) {
    const abortController = new AbortController()
    const promptId = params.messageId
    this.aborts.set(promptId, abortController)

    const res = await fetch(`${this.baseUrl}v1/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params),
      keepalive: true,
      signal: abortController.signal
    })

    const reader = res.body!.pipeThrough(new TextDecoderStream()).getReader()
    return reader
  }

  async abortChat(promptId?: string) {
    if (!promptId) return
    const abortController = this.aborts.get(promptId)
    if (abortController) {
      abortController.abort()
      // console.log('handleAbortChat', promptId, abortController.signal.aborted)
    }
  }

  async persistenceAction(model: string, action: string, args: any) {
    const res = await fetch(`${this.baseUrl}v1/db`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        action,
        args
      })
    })
    const json = await res.json()
    return json
  }
}
