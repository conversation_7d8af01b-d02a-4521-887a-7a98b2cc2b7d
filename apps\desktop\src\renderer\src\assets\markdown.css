.github-dark {
  background-color: #2d2d2d;
  color: #d3d3d3;
}

.github {
  background-color: #f7f7f7;
  color: #333;
}

code {
  padding: 0 5px;
  color: #ccc;
}
pre > code {
  padding: 0;
}

/* 样式用于代码块 */
.code-block {
  padding: 0 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  position: relative;
  line-height: 1;
}

/* 代码块头部样式 */
.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.language {
  font-size: 0.8em;
  color: #999;
}

.copy-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #fff;
}

.icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.responsive-image {
  max-width: 100%;
  height: auto;
}

.image-container {
  text-align: center;
  margin: 16px 0;
}

.markdown,
.markdown > * {
  font-weight: 400;
}

.markdown h1 {
  font-size: xx-large;
  line-height: 1.7;
  padding-left: 0.3rem;
}

.markdown h2 {
  line-height: 1.5;
  font-size: x-large;
  padding-left: 0.3rem;
}

.markdown h3 {
  line-height: 1.4;
  font-size: large;
  padding-left: 0.3rem;
}

/* Table Styles */

.markdown table {
  border-collapse: separate;
}

.markdown th {
  border-top: none;
}

.markdown td:first-child,
.markdown th:first-child {
  border-left: none;
}

/* .markdown table {
  width: 100%;
  border-collapse: collapse;
  color: #bdbdbe;
  font-size: 13px;
  margin: 10px 0px;
  border-radius: 10px;
  overflow: hidden;
  font-weight: normal;
}

.markdown table thead {
  color: #666;
  text-transform: uppercase;
  font-weight: bolder;
}

.markdown hr {
  border: 0;
  border-top: 1px solid #cdcdcd40;
  margin: 1rem 0;
}

.markdown table th,
.markdown table td {
  padding: 8px 15px;
  border-bottom: 1px solid #cdcdcd2e;
  text-align: left;
}

.markdown table th {
  padding: 14px 15px;
} */
:root {
  --ainow-md-th-color: rgba(0, 0, 0, 0.9);
  --ainow-md-th-bg-color: #f5f5f5;
  --ainow-md-td-color: rgba(0, 0, 0, 0.6);
  --ainow-md-td-bg-color-odd: #fff;
  --ainow-md-td-bg-color-even: #f5f5f5;
}
.markdown table {
  font-size: 14px;
  border-collapse: collapse;
  border-spacing: 0;
  min-width: 100%;
  /* width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content; */
  width: auto;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px; /* TODO 表格无效 */
  margin-top: 8px;
  margin-bottom: 8px;
}
.markdown th {
  background-color: var(--ainow-md-th-bg-color);
  color: var(--ainow-md-th-color);
  font: var(--ainow-font-body-medium);
  text-align: left !important;
  font-weight: bold;
}
.markdown tbody tr:nth-child(2n) td {
  background-color: var(--ainow-md-td-bg-color-even);
}
.markdown td {
  background-color: var(--ainow-md-td-bg-color-odd);
  color: var(--ainow-md-td-color);
  font: var(--ainow-font-body-medium);
  text-align: left !important;
}
.markdown th,
.markdown td {
  padding: 0.66em 1em;
  vertical-align: middle;
  text-align: center;
  max-width: 448px;
  white-space: normal;
  box-sizing: border-box;
}

@media (max-width: 600px) {
  .markdown table th,
  .markdown table td {
    padding: 10px;
  }
}

[data-theme='light'] .markdown table,
[data-theme='light'] .markdown table th,
[data-theme='light'] .markdown table td {
  color: #000;
}

/* List Styles */
.markdown ol {
  list-style: decimal-leading-zero;
  padding-left: 0px;
  padding-top: 10px;
  margin: 10px;
}

.markdown ol li {
  margin-left: 20px;
  padding-left: 10px;
  position: relative;
  transition: all 0.3s ease;
  line-height: 1.4rem;
}

.markdown ol li::marker {
  padding-top: 10px;
}

.markdown ol li p {
  margin: 0.5rem;
  padding-top: 10px;
}

.markdown ol li a {
  text-decoration: underline;
}

.markdown ol li p a {
  text-decoration: underline;
}

.markdown ul {
  list-style: revert-layer;
  /* color: #cfcfcfcf; */
  padding-left: 0px;
  padding-top: 10px;
  padding-bottom: 10px;
  margin: 10px;
}

.markdown ul li::marker {
  color: #d0d0d0cf;
  padding-top: 10px;
}

.markdownul li {
  margin-left: 20px;

  padding-left: 10px;
  transition: all 0.3s ease;
  line-height: 1.4rem;
}

.markdownul li a {
  text-decoration: underline;
}

.markdown ul li > ul {
  padding-left: 20px;
  margin: 0px;
}

.markdown p {
  font-weight: 400;
  margin: 0.35rem;
}

.markdown > p > a,
.markdown p a {
  text-decoration: underline;
}

.markdown {
  text-wrap: wrap;
}

.markdown pre {
  /* margin: 20px 0; */
}

.markdown strong {
  font-weight: 600;
  color: #666;
}
