<template>
  <div ref="chatContent" class="chat-content">
    <List item-layout="horizontal" :data-source="data">
      <template #renderItem="{ item }">
        <ListItem :class="item.role || 'sys'">
          <template v-if="item.role === 'user'">
            <QuestionContent
              :text="item.description"
              :additional-words="item.additionalWords"
            ></QuestionContent>
          </template>
          <ListItemMeta v-else>
            <template #title>
              <div class="chat-content-header">
                <span>{{ item.title }}</span>
                <span class="chat-content-header_time">{{
                  dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss')
                }}</span>
              </div>
            </template>
            <template #avatar>
              <Avatar :src="item.avatar" />
            </template>
            <template #description>
              <div class="chat-content_answer">
                <Skeleton v-if="item.status === ThreadMessageStatus.PENDING" active />
                <RenderChatContent
                  v-else
                  :message="item.description || 'no answer'"
                  :provider="item.provider"
                  :messaage-id="item.promptId"
                ></RenderChatContent>
              </div>
            </template>
          </ListItemMeta>
        </ListItem>
      </template>
    </List>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, provide } from 'vue'
import { List, ListItem, ListItemMeta, Avatar, Skeleton } from 'ant-design-vue'
import ainow from '@renderer/assets/icon.png'
import ollama from '@renderer/assets/ollama.png'
import dfai from '@renderer/assets/dfai.png'
import user from '@renderer/assets/user.svg'
import { storeToRefs } from 'pinia'
import { useLLMStore } from '../stores/llm'
import useDB from '@renderer/hooks/useDB'
import {
  userThreadMessageStore,
  ThreadMessageStatus,
  ThreadMessageUI
} from '../stores/threadMessage'
import dayjs from 'dayjs'
import RenderChatContent from './RenderChatContent.vue'
import { debounce } from '@renderer/utils'
import { PROVIDER } from '@ainow/types/index'
import ChatScrollTracker from '../utils/ChatScrollTracker'
import QuestionContent from './ContentItem/Question.vue'
import { chatService } from '@/renderer/src/utils/chatService'
import type { Thread } from '@prisma/client'

const { listThreadMessages } = useDB()
const { currentAccount, currentThread } = storeToRefs(useLLMStore())
const threadMessageStore = userThreadMessageStore()
const { allChats } = storeToRefs(threadMessageStore)
const chatContent = ref<HTMLElement | null>(null)

const data = ref<ThreadMessageUI[]>([])
let scrollTracker: ChatScrollTracker

onMounted(() => {
  scrollTracker = new ChatScrollTracker(chatContent.value)
  scrollTracker.start()
})

const matchAvatar: Map<string, string> = new Map([
  [PROVIDER.AINOW, ainow],
  [PROVIDER.OLLAMA, ollama],
  [PROVIDER.DFAI, dfai]
  // TODO 更多
])
const matchFn = ({ role, model, provider }: ThreadMessageUI) => {
  const info = {} as { title?: string; avatar?: string }
  if (role === 'user') {
    info.title = 'user'
    info.avatar = user
  } else {
    info.title = model
    info.avatar = matchAvatar.get(provider as string)
  }
  return info
}

const updateChatContent = (chatRecords: ThreadMessageUI[]) => {
  data.value = chatRecords
    .reduce((acc, record) => {
      if (currentThread.value?.id === record.threadId) {
        const obj = {
          role: record.role,
          title: matchFn(record).title,
          status: record.status,
          description: record.message || record.response,
          additionalWords: record.additionalWords,
          avatar: matchFn(record).avatar,
          promptId: record.promptId,
          updateTime: record.updateTime,
          provider: record.provider
        }
        acc.push(obj)
      }
      return acc
    }, [] as ThreadMessageUI[])
    .sort((a, b) => a.createTime! - b.createTime!)

  // 将滚动条置于底部
  if (!scrollTracker?.isActive) scrollTracker?.start()
  nextTick(() => {
    if (chatContent.value) {
      // @ts-ignore TODO 完善类型
      chatContent.value.scrollTop = chatContent.value.scrollHeight
    }
  })
}
watch(() => allChats.value, updateChatContent, {
  deep: true,
  immediate: true
})

const RESTORE_LIMIT = 3
const restoreHistory = async (account: any, thread: Thread) => {
  if (!account?.id || !thread?.id) return
  // 读取当前对话历史
  const messages = await listThreadMessages(account.id, thread.id, RESTORE_LIMIT)
  console.log(
    '[ChatContent] 账号或对话更新',
    messages,
    account.username,
    thread.name,
    messages.length
  )
  // 更新到对话中
  const list: ThreadMessageUI[] = []
  messages.forEach((msg) => {
    list.push({
      role: 'user',
      threadId: msg.threadId,
      message: msg.prompt,
      promptId: msg.promptId
      // updateTime: msg.updateTime
    })

    const response = msg.response
      .replace(/^"/g, '')
      .replace(/\\{1,2}n/g, '\n')
      .replace(/\\{1,2}"/g, '"')
    console.log('格式response--', response)
    list.push({
      role: 'assistant',
      provider: msg.chatProvider,
      model: msg.chatModel,
      status: ThreadMessageStatus.STREAMING,
      threadId: msg.threadId,
      prompt: msg.prompt,
      promptId: msg.promptId,
      response: msg.chatProvider === PROVIDER.AINOW ? msg.response : response
      // updateTime: msg.updateTime
    })
  })
  threadMessageStore.setChats(list)
}
watch(() => [currentAccount.value, currentThread.value], restoreHistory, { immediate: true })

const handleRetry = async (messaageId: string, params: any) => {
  const items = data.value.filter(
    (item) => item.promptId === messaageId || item.promptId === 'qs:' + messaageId
  )
  console.log('handleRetry--', messaageId, items)
  const questionObj = items.find((item) => item.role === 'user')
  const answerObj = items.find((item) => item.role === 'assistant')

  if (!questionObj || !answerObj) return

  chatService.retryMessage(questionObj, answerObj, params)
}
provide(/* 注入名 */ 'handleRetry', /* 值 */ handleRetry)
</script>

<style scoped lang="less">
.chat-content {
  overflow-y: auto;
  overflow-x: hidden;
  .user {
    border: none;
  }
  &_answer {
    font-size: 16px;
    think {
      display: block;
      font-size: 12px;
    }
  }
  :deep(.chat-content-header) {
    padding-right: 10px;
    display: flex;
    gap: 10px;
  }
  :deep(.chat-content-header_time) {
    color: #666;
  }
  .ant-avatar > img {
    object-fit: contain;
  }
  .assistant,
  .sys {
    .chat-content_answer {
      min-width: 300px;
      max-width: 100%;
      display: inline-block;
      background-color: #fff;
      border-radius: 4px 20px 20px 20px;
      padding: 16px 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
