<template>
  <div class="chat-input-container">
    <SearchList class="hidden" :message="message" @clear-message="clearMessage" />
    <div :class="['gradient-border', { 'active-chat-bg': isFocusedInput }]"></div>
    <div :class="['chatInput', { 'active-chat-input': isFocusedInput }]">
      <STTAnim :status="isSTTRecording" />
      <img v-if="!isSTTRecording" :src="UploadFileImg" class="upload-icon" />
      <img v-if="!isSTTRecording" :src="KnowlgdgeImg" class="knowlgdge-icon" />
      <Textarea
        v-model:value="message"
        class="chat-area"
        style="overflow-y: auto; resize: none"
        placeholder="Ask me a question"
        :auto-size="{ minRows: 1 }"
        :disabled="isSTTRecording && isSTTSpeaking"
        @press-enter="send"
      >
      </Textarea>

      <STTButton
        v-if="showSTT && !activeMessage"
        :prev-msg="prevMsgForSTT"
        @text-received="onSTTReceived"
        @say-stop="onSTTSayStop"
        @recording="onSTTStatus"
      />

      <SvgIcon
        v-if="activeMessage"
        name="audio-stop"
        size="32px"
        class="btn"
        @click="stop"
      ></SvgIcon>
      <img
        v-else
        :src="SendMsgImg"
        :class="{ 'send-msg': true, 'disabled-img': isDisabled }"
        @click="send"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue'
import { Textarea } from 'ant-design-vue'
import { useLLMStore } from '../stores/llm'
import { ThreadMessageStatus, ThreadMessageUI } from '../stores/threadMessage'
import { v4 as uuidv4 } from 'uuid'
import { storeToRefs } from 'pinia'
import useDB from '@renderer/hooks/useDB'
import UploadFileImg from '@renderer/assets/upload-file.svg'
import KnowlgdgeImg from '@renderer/assets/knowlgdge-assistant.svg'
import SvgIcon from '@renderer/components/SvgIcon'
import SendMsgImg from '@renderer/assets/send-msg.svg'
import SearchList from './SearchList.vue'
import {
  ASK_TYPE,
  CHAT_TYPE,
  sessionType,
  TYPE,
  ChatCategory,
  ipcfunc,
  PROVIDER,
  AINOW_MODEL,
  CLOUD_MODEL
} from '@ainow/types/index'
import { chatService } from '../utils/chatService'
import type { Thread } from '@prisma/client'
import { STTButton, STTAnim, useSTT } from './stt'
import { RES_STREAM_EVENT } from '@renderer/utils/chatEvents'
const { createNewThread } = useDB()

// 小窗参数
const askType = ref<ASK_TYPE>() //ASK_TYPE.EXPLAIN 对应不同意图
const chatType = ref(CHAT_TYPE.CHAT) //测试云端聊天
const winType = ref()
const AdditionalWords = ref('')

const { currentAccount } = storeToRefs(useLLMStore())
const llmStore = useLLMStore()
const { createDefaultThread, listThreads } = useDB()

const message = ref<string>('')
const currentPrompt = ref<string>('')
const currentMessageId = ref<string>('')
const isDisabled = computed(() => !!isSTTRecording.value || !message.value)

const activeMessage = ref<ThreadMessageUI | null>(null)
window.addEventListener(RES_STREAM_EVENT, (evt) => {
  const record = (evt as CustomEvent).detail as ThreadMessageUI
  if (record.done) {
    activeMessage.value = null
  } else if (activeMessage.value?.promptId !== record.promptId) {
    activeMessage.value = record
  }
})
const stop = () => {
  if (!activeMessage.value) return
  chatService.stop(activeMessage.value.promptId)
  activeMessage.value = null
}
onBeforeUnmount(stop)

const isFocusedInput = ref<boolean>(false) // input框聚焦
// 聚焦和失焦逻辑
let activeInput: HTMLCollectionOf<Element>
const focusChatInput = () => {
  isFocusedInput.value = true
}
const blurChatInput = () => {
  isFocusedInput.value = false
}

onMounted(() => {
  activeInput = document.getElementsByClassName('chat-area')
  activeInput[0].addEventListener('focus', focusChatInput)
  activeInput[0].addEventListener('blur', blurChatInput)
})
onBeforeUnmount(() => {
  activeInput[0].removeEventListener('focus', focusChatInput)
  activeInput[0].removeEventListener('blur', blurChatInput)
})
watch(
  () => currentAccount.value,
  async (account) => {
    if (!account?.id) return
    // 恢复上一次对话 OR 创建默认对话
    let thread: Thread | null
    const historyThreads = await listThreads(account)
    if (historyThreads?.length) {
      thread = historyThreads[0]
      llmStore.setThread(thread)
    } else {
      thread = await createDefaultThread(account)
    }
    console.log('chatinput thread:', thread?.name)
  },
  { immediate: true, deep: true }
)

if (__ELECTRON__) {
  window.api.onStreamChatResponseChunk(chatService.chatCallback)

  // 监听来自minibar的消息
  window.api.listenerMsg((msg) => {
    console.log('监听到的channel消息---', msg)
    if (msg && msg.target == 'mini') {
      setMiniPage(msg)
    } else if (msg && winType.value == sessionType.AINowMini) {
      winType.value == null
    }
  })
}

const clearMessage = () => {
  message.value = ''
  setTimeout(() => {
    ;(activeInput[0] as HTMLTextAreaElement).focus()
  })
}
const sendMessage = async () => {
  const messageId = uuidv4()
  const prompt = message.value.slice()

  currentMessageId.value = messageId
  currentPrompt.value = prompt
  message.value = ''

  console.log('SEND', llmStore.currentThread?.name)
  // 发送后先把问题和空白答案展示到页面中
  chatService.appendQustion(prompt, messageId)
  // 发送消息
  if (chatType.value === CHAT_TYPE.CLOUDCHAT) {
    await chatService.sendCloudMessage(messageId, chatService.chatCallback, {
      data: {
        sessionType: sessionType.AINowCloud,
        modelCode: CLOUD_MODEL.GPT_40_MINI,
        msg: prompt
      },
      ipcfunc: ipcfunc.AIGCChat
    })
  } else {
    await chatService.sendLocalMessage(messageId, chatService.chatCallback, {
      data: {
        sessionType:
          winType.value == sessionType.AINowMini ? sessionType.AINowMini : sessionType.AINowGeneral,
        msg: prompt
      },
      ipcfunc: ipcfunc.AIGCChat
    })
  }
}

const send = (event: MouseEvent | KeyboardEvent) => {
  // 阻止默认回车行为
  if (event instanceof KeyboardEvent && event.key === 'Enter') {
    event.preventDefault()
  }
  // 没数据不提交
  if (!message.value) return
  // shift + enter换行
  if (event instanceof KeyboardEvent && event?.shiftKey && event.key === 'Enter') {
    // 阻止默认行为（例如，不提交表单）
    event.preventDefault()
    // 在当前光标位置插入换行符
    const textarea = event.target! as HTMLTextAreaElement
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    textarea.value = textarea.value.substring(0, start) + '\n' + textarea.value.substring(end)
    // 将光标移动到新插入的换行符之后
    textarea.selectionStart = textarea.selectionEnd = start + 1
    return
  }
  sendMessage()
}

/**处理来自minibar的消息 */
const processMiniMessage = async (askType: ASK_TYPE, additionalWords: string) => {
  console.log(' onMounted receiveMessageFromMinibar--', askType, additionalWords)
  const askModel = chatService.receiveMessageFromMinibar({
    askType: askType,
    additionalWords: additionalWords
  })
  const messageId = uuidv4()
  currentMessageId.value = messageId

  let defaultRes
  // 设置默认翻译卡片参数，用于数据返回之前显示
  if (askType === ASK_TYPE.TRANSLATE) {
    defaultRes = JSON.stringify({
      type: TYPE.WORK_ASSISTANT_TRANSLATION,
      category: ChatCategory.WORK,
      data: {}
    })
  }
  chatService.appendQustion(
    askModel.ChatText,
    messageId,
    additionalWords,
    askType == ASK_TYPE.TRANSLATE ? ThreadMessageStatus.PREPAREING : ThreadMessageStatus.PENDING,
    defaultRes
  )
  if (askType === ASK_TYPE.CLOUD_ASK || askType === ASK_TYPE.EXPLAIN) {
    // sendCloudMiniMessage()
  } else {
    chatService.sendLocalMessage(messageId, chatService.chatCallback, {
      data: askModel,
      ipcfunc: ipcfunc.AIGCChat
    })
  }
}
const handleNewThread = async () => {
  const account = llmStore.currentAccount
  if (!account) {
    console.error('no account')
    return
  }
  await createNewThread(account)
}
const setMiniPage = async (
  params: { target: string; AskType: ASK_TYPE; AdditionalWords: string } | any
) => {
  // 切换到mini模式，创建新的会话id
  llmStore.setProvider(PROVIDER.AINOW)
  llmStore.setModel(AINOW_MODEL.AINOW_MODEL_1)
  await handleNewThread()
  console.log('切换到mini模式，创建新的会话id', llmStore.currentThread?.id)
  winType.value = sessionType.AINowMini //表示小窗口模式
  askType.value = (+params.AskType as ASK_TYPE) || ASK_TYPE.ASK
  AdditionalWords.value = params.AdditionalWords || ''
  processMiniMessage(askType.value, AdditionalWords.value)
}

const {
  showSTT,
  isSTTRecording,
  isSTTSpeaking,
  prevMsgForSTT,
  onSTTReceived,
  onSTTSayStop,
  onSTTStatus
} = useSTT(message, function onStatus(v: boolean) {
  isFocusedInput.value = v
})
</script>

<style scoped lang="less">
.chat-input-container {
  position: relative;
  width: calc(100% - 50px);

  .gradient-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96c93d);
    border-radius: 10px;
    z-index: -1;
    animation: rotate 3s linear infinite;
    display: none;

    &.active-chat-bg {
      display: block;
    }
  }

  @keyframes rotate {
    0% {
      filter: hue-rotate(0deg);
    }
    100% {
      filter: hue-rotate(360deg);
    }
  }

  /* 添加模糊效果 */
  .input-wrapper::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: inherit;
    border-radius: 10px;
    z-index: -2;
    filter: blur(10px);
    opacity: 0.5;
  }
  .chatInput {
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px 16px;
    box-sizing: border-box;
    min-height: 48px;
    border-image: linear-gradient(180deg, #d9e6ff 0%, #bfcce4 100%);
    box-shadow: 0px 3px 12px 0px #0000001a;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 2px solid transparent;
    &.active-chat-input {
      // border: 2px solid;
      // border-image: linear-gradient(145.77deg, #7785ff 3.76%, #4663ff 79.76%) 2 / 2px;
    }
    & > .upload-icon,
    .knowlgdge-icon {
      width: 18px;
      height: 18px;
      margin-right: 24px;
      cursor: pointer;
    }
    .ant-input {
      flex: 1;
      outline: none;
      min-height: 32px;
      max-height: 76px;
      border-color: transparent;
      box-shadow: none;
      &::placeholder {
        color: #52525b;
        font-family: Lato;
      }
    }
    .send-msg {
      cursor: pointer;
      width: 32px;
      height: 32px;
      margin-left: 15px;
      &.disabled-img {
        pointer-events: none; /* 阻止鼠标事件 */
        opacity: 0.6; /* 降低透明度 */
      }
    }
  }
}

.mini-hide {
  .upload-icon,
  .knowlgdge-icon {
    display: none;
  }
}
@import '@renderer/assets/global.less';
.is-web.is-mobile {
  .mini-hide;
}
@media @app-mini-width-query {
  .chat-input-container .hidden {
    display: none;
  }
}
</style>
