<template>
  <ListItemMeta class="user">
    <template #title>
      <p v-if="additionalWords">“{{ additionalWords }}”</p>
    </template>
    <template #description v-if="text">
      <p>{{ text }}</p>
    </template>
  </ListItemMeta>
</template>

<script setup lang="ts">
import { ListItemMeta } from 'ant-design-vue'
const props = defineProps({
  text: String,
  additionalWords: String
})
</script>

<style scoped lang="less">
.user {
  .ant-list-item-meta {
    flex-direction: row-reverse;
  }

  :deep(.ant-list-item-meta-content) {
    text-align: right;
  }
  :deep(.chat-content-header) {
    flex-direction: row-reverse;
  }
  :deep(.ant-list-item-meta-description) {
    display: inline-block;
    background-color: #4663ff;
    padding: 12px 24px;
    color: #fff !important;
    border-radius: 20px 4px 20px 20px;
  }
  :deep(.ant-list-item-meta-description) span p {
    color: #fff;
    margin: 0;
  }
  :deep(.ant-list-item-meta-avatar) {
    display: none;
  }
  :deep(.ant-list-item-meta-title) {
    display: block;
    background-color: transparent;
    padding: 8px;
    color: #7b7b7c !important;
  }
}
</style>
