<template>
  <a-descriptions
    :title="title"
    bordered
    :column="1"
    size="small"
    :labelStyle="{ background: 'transparent', 'border-right': 'none' }"
  >
    <a-descriptions-item v-for="item in infolist" :key="item.key" :label="item.key">{{
      item.value
    }}</a-descriptions-item>
  </a-descriptions>
  <div v-if="potentialList.length > 0" class="potential">
    <a-button v-for="item in potentialList" :key="item.text" @click="handleClick(item)"
      >{{ item.text }}<RightOutlined
    /></a-button>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, reactive, watch } from 'vue'
import { chatService } from '../../../utils/chatService'
import { v4 as uuidv4 } from 'uuid'
import { ipcfunc } from '@ainow/types/index'
import { userThreadMessageStore } from '../../../stores/threadMessage'
import { message } from 'ant-design-vue'

const chatsStore = userThreadMessageStore()
const props = defineProps({
  message: String,
  messageId: String
})
const title = ref('')
let allTypeList: object | null = null
type InfoListItem = { key: string; value: string }
type PotentialListItem = { bindType: string; category: string; text: string }
const infolist = reactive<InfoListItem[]>([])
const potentialList = reactive<PotentialListItem[]>([])
watch(
  () => props.message,
  () => {
    console.log('diercengchat message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done) {
      title.value = obj.data?.content?.title
      if (obj.data?.content?.form) {
        Object.assign(infolist, obj.data.content.form)
        console.log('infolist:', infolist)
      }
      Object.assign(potentialList, obj.potentialTypeList ?? [])
      console.log('potentialList:', potentialList)
      allTypeList = obj.allTypeList ?? null
    }
  },
  { immediate: true }
)

const handleClick = (item: PotentialListItem) => {
  console.log('handleClick', item)
  // TODO:Ask Lena for the warranty status, 直接提示, 后续添加验证处理
  if (item.bindType == 'SERVICE_ASK_WARRANTY_STATUS') {
    message.error('Sorry, Lena is currently not available in your location.')
    return
  }
  const chatQ = chatsStore.allChats.find((chat) => chat.promptId === 'qs:' + props.messageId)
  const question = chatQ?.message
  console.log('question:', question)
  const askModel = {
    msg: `assuming you are a PC device manager or PC professional tool, when i input "${question}", what would you explain the reason to me?`,
    type: item.bindType,
    allTypeList: allTypeList
    // chatText: item.text,
  }
  // 清空potentialList
  potentialList.length = 0
  const messageId = uuidv4()
  chatService.appendQustion(item.text, messageId)

  chatService.sendLocalMessage(messageId, chatService.chatCallback, {
    data: askModel,
    ipcfunc: ipcfunc.AIGCChat
  })
}
</script>

<style lang="less" scoped>
.potential {
  margin-top: 20px;
  button {
    display: block;
    margin: 10px 0;
    border-color: rgba(0, 0, 0, 0.06);
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
