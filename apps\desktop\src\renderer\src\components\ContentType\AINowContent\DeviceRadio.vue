<template>
  <div class="device-radio">
    <div class="device-radio_title">{{ response.content.title }}</div>
    <RadioGroup v-model:value="selectedDevice">
      <List item-layout="horizontal" :data-source="response.content.list">
        <template #renderItem="{ item }">
          <Radio :value="item.bindType" class="device-radio-item">
            <ListItem class="device-radio-listitem">
              <ListItemMeta>
                <template #title>
                  <span class="device-radio-itemtitle">{{ item.text }}</span>
                </template>
                <template #description>{{ item.description }}</template>
              </ListItemMeta>
            </ListItem>
          </Radio>
        </template>
      </List>
    </RadioGroup>
    <a-button type="primary" @click="handleSubmit">{{ response.content.button.text }}</a-button>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { chatService } from '../../../utils/chatService'
import { Radio, RadioGroup, List, ListItem, ListItemMeta, Avatar, Skeleton } from 'ant-design-vue'
import { ipcfunc } from '@ainow/types/index'
import { v4 as uuidv4 } from 'uuid'
const props = defineProps({
  message: String
})

const selectedDevice = ref<string>('')
const response = ref()
watch(
  () => props.message,
  () => {
    console.log('第二层变化 message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done) {
      response.value = obj.data
      selectedDevice.value =
        response.value.content.list.find((item) => item.selected)?.bindType || ''
    }
  },
  { immediate: true }
)
const handleSubmit = () => {
  const messageId = uuidv4()
  chatService.sendAiNowActionMessage({
    dynamicParams: {
      data: {
        data: { content: null, vantageType: selectedDevice.value }
      },
      ipcfunc: ipcfunc.DeviceControl
    },
    messageId,
    callback: chatService.chatCallback
  })
}
</script>

<style scoped lang="less">
@import '@renderer/assets/global.less';
.device-radio {
  width: 500px;
  &_title,
  &-itemtitle {
    .fontStyle;
  }
  &-item {
    display: flex;

    :deep(span:nth-of-type(2)) {
      flex: 1;
    }
  }
  .ant-radio-group {
    display: block;
  }
}
</style>
