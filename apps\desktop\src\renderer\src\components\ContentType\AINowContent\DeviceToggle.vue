<template>
  <div class="device-toggle">
    <div class="device-toggle_title">
      <span>{{ response.content.title }}</span>
      <Switch :checked="isToggled" @change="() => toggleDevice('button')"></Switch>
    </div>
    <div class="device-toggle_content">
      {{ response.content.text }}
    </div>
    <div class="device-toggle_footer">
      <Checkbox :checked="checked" @change="() => toggleDevice('checkbox')">{{
        response.content.checkbox.text
      }}</Checkbox>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Switch, Checkbox } from 'ant-design-vue'
import { chatService } from '../../../utils/chatService'
import { ipcfunc } from '@ainow/types/index'
import { v4 as uuidv4 } from 'uuid'
const props = defineProps({
  message: String
})
const isToggled = ref(false)
const checked = ref(false)
const toggleDevice = (type: 'button' | 'checkbox') => {
  const messageId = uuidv4()
  let vantageType
  if (type === 'button') {
    isToggled.value = !isToggled.value
    vantageType = isToggled.value
      ? response.value.content[type].bindTypeEnable
      : response.value.content[type].bindTypeDisable
  } else if (type === 'checkbox') {
    checked.value = !checked.value
    vantageType = checked.value
      ? response.value.content[type].bindTypeEnable
      : response.value.content[type].bindTypeDisable
    console.log('vantageType:', vantageType)
  }

  chatService.sendAiNowActionMessage({
    dynamicParams: {
      data: {
        data: { content: null, vantageType }
      },
      ipcfunc: ipcfunc.DeviceControl
    },
    messageId,
    callback: chatService.chatCallback
  })
}
const response = ref()
watch(
  () => props.message,
  () => {
    console.log('第二层变化devicetoge message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done) {
      response.value = obj.data
      isToggled.value = obj.data.content.button.status
      checked.value = obj.data.content.checkbox.status
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
/* Add your styles here */
@import '@renderer/assets/global.less';
.device-toggle {
  width: 500px;
  &_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      .fontStyle;
    }
  }
  &_footer {
    margin-top: 10px;
  }
}
</style>
