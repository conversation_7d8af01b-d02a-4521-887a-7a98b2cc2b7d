<template>
  <div class="document-search">
    <div class="document-search_title">Found {{ response.docNum ?? 0 }} file(s) for you</div>
    <template v-if="response.docNum">
      <ul class="document-search_result">
        <li v-for="item in response.documentList" :key="item.id">
          <img src="" />
          <span>{{ decodeURIComponent(item.targetFileName) }}</span>
          <div class="result-hover">
            <span @click="() => select(item)">select</span>
            <span @click="() => preview(item)">preview</span>
          </div>
        </li>
      </ul>
    </template>
    <template v-else></template>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { DOCITEM } from '@ainow/types/index'
const props = defineProps({
  message: String
})
const response = ref({} as DOCITEM)
watch(
  () => props.message,
  () => {
    console.log('第二层变化 message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done && obj.data.docNum) {
      response.value = obj.data
    }
  },
  { immediate: true }
)
const select = (item: DOCITEM) => {
  console.log('select')
}
const preview = (item: DOCITEM) => {
  console.log('preview')
  window.api.openFile(item.targetPath)
}
</script>

<style scoped lang="less">
.document-search {
  padding: 16px;
}

input {
  margin-right: 8px;
}

button {
  margin-right: 8px;
}
</style>
