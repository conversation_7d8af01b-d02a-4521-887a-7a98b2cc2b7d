<template>
  <div class="general-content">
    <span class="markdown" style="color: #333" :innerHTML="safeHTML"></span>
    <a-button v-if="error" class="btn" type="primary" @click="handleGotoCloud"
      >Go to Cloud Chat</a-button
    >
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import DOMPurify from 'dompurify'
import { renderMarkdown } from '@renderer/utils/markdown'
const props = defineProps({
  message: String
})

const answer = ref('')
const error = ref('')
const safeHTML = computed(() => DOMPurify.sanitize(renderMarkdown(answer.value || error.value)))
console.log(safeHTML, 'safeHTML')
watch(
  () => props.message,
  () => {
    console.log('diercengchat message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    answer.value += obj.data?.chat || ''
    if (obj.data?.error) {
      error.value = `${obj.data?.errorCode}: ${obj.data?.error}`
    }
  },
  { immediate: true }
)
const handleGotoCloud = () => {
  console.log('goto cloud chat')
}
</script>

<style scoped>
/* .general-content { */
/* padding: 16px 24px; */
/* border: 1px solid #ccc; */
/* } */

.general-content h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.general-content p {
  font-size: 16px;
}
.general-content .btn {
  margin: 10px 0;
  display: block;
}
</style>
