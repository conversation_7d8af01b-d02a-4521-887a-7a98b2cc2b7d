<template>
  <div class="image-search">
    <div class="image-search_title">Found {{ docs ? docs.length : 0 }} image</div>

    <div v-if="docs" class="image-search_images">
      <Image
        v-for="(item, index) in docs"
        :key="index"
        :src="'atom:\\\\' + item.targetPath"
        alt="Image"
        style="width: 200px; height: 150px"
      />
    </div>

    <!-- Add your template code here -->
  </div>
</template>

<script lang="ts" setup>
import { Image } from 'ant-design-vue'
import { DOCITEM } from '@ainow/types/index'
import { ref, watch } from 'vue'
//const test = 'C:\\Users\\<USER>\\Downloads\\dog.jpg'
// Define your component logic here
const props = defineProps({
  message: String
})

const docs = ref<DOCITEM[]>([])
watch(
  () => props.message,
  () => {
    console.log('第er层props变化message:', props.message)
    try {
      docs.value = JSON.parse(props.message!).data.documentList
    } catch (e) {
      console.log('error:', e, props.message, '收到的消息报错时的')
    }
    console.log('docs:', docs.value)
  }
)
</script>

<style scoped lang="less">
.image-search {
  /* Add your styles here */
  :deep(.ant-image) {
    margin-right: 10px;
    margin-bottom: 10px;
    img {
      object-fit: cover;
    }
  }
  &_title {
    margin-bottom: 10px;
  }
}
</style>
