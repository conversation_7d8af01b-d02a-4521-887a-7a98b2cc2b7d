<template>
  <div class="install-vantage">
    <div>{{ response.content.text }}</div>
    <Button type="primary" @click="goInstall">Install lenovo vantage</Button>
  </div>
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { Button } from 'ant-design-vue'
const props = defineProps({
  message: String
})
const response = ref()
const goInstall = () => {
  window.open('ms-windows-store://pdp/?ProductId=9WZDNCRFJ4MV')
}
watch(
  () => props.message,
  () => {
    console.log('第二层变化devicetoge message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done) {
      response.value = obj.data
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.install-vantage {
  div {
    margin-bottom: 6px;
  }
}
</style>
