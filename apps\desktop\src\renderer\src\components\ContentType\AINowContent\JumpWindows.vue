<template>
  <div class="jump-windows">
    <div class="jump-windows_title">{{ response.content.title }}</div>
    <div class="jump-windows_content">{{ response.content.text }}</div>
    <Button type="link" @click="bindJumpSetting">{{ response.content.button.text }}</Button>
  </div>
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { Button } from 'ant-design-vue'
import { chatService } from '../../../utils/chatService'
import { ipcfunc } from '@ainow/types/index'
import { v4 as uuidv4 } from 'uuid'
const props = defineProps({
  message: String
})
const response = ref()
const bindJumpSetting = () => {
  const messageId = uuidv4()
  console.log(chatService.sendAiNowActionMessage, 'sendAiNowActionMessage')
  chatService.sendAiNowActionMessage({
    dynamicParams: {
      data: {
        data: { content: null, vantageType: response.value.content.button.bindType }
      },
      ipcfunc: ipcfunc.DeviceControl
    },
    messageId,
    callback: chatService.chatCallback
  })
}
//response.content.button.bindType //"redirect_windows_settings_privacy_location_internal",

watch(
  () => props.message,
  () => {
    console.log('第二层变化devicetoge message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done) {
      response.value = obj.data
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.jump-windows {
  width: 500px;
  &_title {
    font-size: 16px;
    color: #000;
  }
}
</style>
