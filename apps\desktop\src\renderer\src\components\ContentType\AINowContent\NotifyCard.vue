<template>
  <div class="notify-card">
    <CheckCircleFilled style="color: #389e0d; font-size: 18px; vertical-align: middle" />
    <span class="notify-card-text">{{ response.content.text }}</span>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CheckCircleFilled } from '@ant-design/icons-vue'
const props = defineProps({
  message: String
})
const response = ref()
watch(
  () => props.message,
  () => {
    console.log('第二层变化noti message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    if (!obj.done) {
      response.value = obj.data
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.notify-card {
  &-text {
    margin-left: 6px;
    vertical-align: middle;
  }
}
</style>
