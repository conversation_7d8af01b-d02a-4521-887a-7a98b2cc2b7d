<template>
  <div class="translation-content">
    <div class="model-select">
      <Row :gutter="8">
        <Col :span="10">
          <a-select
            v-model:value="sourceLang"
            style="width: 100%"
            :options="sourceOptions"
          ></a-select>
        </Col>
        <Col :span="4" style="text-align: center"><ArrowRightOutlined /></Col>
        <Col :span="10">
          <a-select
            v-model:value="destLang"
            style="width: 100%"
            :options="destOptions"
            @change="handleChange"
          ></a-select>
        </Col>
      </Row>
    </div>
    <div style="min-height: 30px; margin-top: 10px">
      {{ answer }}<LoadingOutlined v-if="!answer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, inject } from 'vue'
import { Row, Col, SelectProps } from 'ant-design-vue'
import { ArrowRightOutlined } from '@ant-design/icons-vue'
const props = defineProps({
  message: String,
  messageId: String
})
const handleRetry = inject('handleRetry')

const answer = ref('')
const sourceLang = ref<string>('')
const destLang = ref<string>('English')
const sourceOptions = ref<SelectProps['options']>([{ label: 'Auto', value: '' }])
const destOptions = ref<SelectProps['options']>([
  { label: 'English', value: 'English' }, //English
  { label: 'Chinese', value: 'Chinese' }, //简体中文
  { label: 'French', value: 'French' }, //Français
  { label: 'German', value: 'German' }, //Deutsch
  { label: 'Spanish', value: 'Spanish' } //Español
])
const handleChange = (value: string) => {
  // 清空当前answer，重新获取
  console.log('value:', value, props.messageId)
  answer.value = ''
  if (handleRetry && typeof handleRetry === 'function') {
    handleRetry(props.messageId, { translate: { dest: value, source: '' }, isRetry: false })
  }
}

watch(
  () => props.message,
  () => {
    console.log('diercengchat message:', props.message)
    // @ts-ignore
    const obj = JSON.parse(props.message)
    answer.value += obj.data?.chat || ''
  },
  { immediate: true }
)
</script>

<style scoped>
/* .translation-content {
  padding: 16px 24px;
} */

.translation-content h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.translation-content p {
  font-size: 16px;
}
</style>
