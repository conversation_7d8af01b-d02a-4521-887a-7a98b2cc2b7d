<template>
  <div class="render-chat-content">
    <div v-if="categoryForAINow.category === ChatCategory.GENERAL">
      <GeneralContent
        v-if="
          categoryForAINow.intention === TYPE.GENERAL_GENERATION ||
          categoryForAINow.intention === TYPE.ERROR ||
          categoryForAINow.intention === TYPE.DEVICE_DEFAULT
        "
        :message="props.message"
      ></GeneralContent>
    </div>
    <div v-else-if="categoryForAINow.category === ChatCategory.WORK">
      <ImageSearch
        v-if="categoryForAINow.intention === TYPE.WORK_ASSISTANT_IMAGE_SEARCH"
        :message="props.message"
      ></ImageSearch>
      <TranslationMiniVue
        v-else-if="categoryForAINow.intention === TYPE.WORK_ASSISTANT_TRANSLATION"
        :message="props.message"
        :message-id="props.messaageId"
      ></TranslationMiniVue>
      <DocumentSearch
        v-else-if="categoryForAINow.intention === TYPE.WORK_ASSISTANT_DOCUMENT_SEARCH"
        :message="props.message"
      ></DocumentSearch>
    </div>

    <div v-else-if="categoryForAINow.category === ChatCategory.DEVICE_VANTAGE">
      <DeviceToggle
        v-if="categoryForAINow.templateID === TemplateID.DeviceToggleSwitchWithCheckBox"
        :message="props.message"
      ></DeviceToggle>
      <DeviceRadio
        v-else-if="categoryForAINow.templateID === TemplateID.DeviceRadioButton"
        :message="props.message"
      ></DeviceRadio>
      <DeviceGrid
        v-else-if="categoryForAINow.templateID === TemplateID.DeviceDataGrid"
        :message="props.message"
        :message-id="props.messaageId"
      ></DeviceGrid>
      <InstallVantage
        v-else-if="categoryForAINow.templateID === TemplateID.Error"
        :message="props.message"
      ></InstallVantage>
      <NotifyCard
        v-else-if="
          categoryForAINow.templateID === TemplateID.Info ||
          categoryForAINow.templateID === TemplateID.BLANK
        "
        :message="props.message"
      ></NotifyCard>
      <JumpWindows
        v-else-if="categoryForAINow.templateID === TemplateID.JumpWindows"
        :message="props.message"
      ></JumpWindows>
    </div>
    <div v-else>Hello, I am your exclusive assistant</div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import ImageSearch from './ImageSearch.vue'
import GeneralContent from './GeneralContent.vue'
import DeviceToggle from './DeviceToggle.vue'
import DeviceRadio from './DeviceRadio.vue'
import NotifyCard from './NotifyCard.vue'
import InstallVantage from './InstallVantage.vue'
import JumpWindows from './JumpWindows.vue'
import TranslationMiniVue from './TranslationMini.vue'
import DocumentSearch from './DocumentSearch.vue'
import DeviceGrid from './DeviceGrid.vue'
import { ChatCategory, TYPE, TemplateID } from '@ainow/types/index'

const props = defineProps({
  message: String,
  messaageId: String
})

// find image about dog
// 根据字段category 判断渲染的内容
const categoryForAINow = computed(() => {
  try {
    const obj = props.message ? JSON.parse(props.message) : {}
    console.log('收到的消息parse后的', obj)
    const cardObj: any = {
      category: obj.category,
      intention: obj.type
    }
    if (cardObj.category === ChatCategory.DEVICE_VANTAGE) {
      cardObj.templateID = obj.data?.content?.templateID // 流式返回 最后一条会是空
    }
    return cardObj
  } catch (e) {
    console.log('error:', e, props.message, '收到的消息报错时的')
    return { category: '' }
  }
})
watch(
  () => props.message,
  () => {
    console.log('第一层props变化message:', props.message)
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.render-chat-content {
  /* Your styles go here */
}
</style>
