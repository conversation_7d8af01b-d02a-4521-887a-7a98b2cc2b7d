<template>
  <div class="render-chat-content">
    <MarkDownContent :message="content"></MarkDownContent>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import MarkDownContent from './MarkDownContent.vue'

const props = defineProps({
  message: String
})

const { message } = toRefs(props)

const content = computed(() => {
  if (!message?.value) return ''
  const detailsMatches = message.value.match(/<\/details>/g)
  if (!detailsMatches || detailsMatches.length < 2)
    return message.value.replace(/<details.*?>\s([\s\S]+?)<\/details>/gm, '$1')
  return message.value
    .replace(/<details.*?>\s([\s\S]+?)<\/details>/m, '<think>$1</think>') // 将第一个details替换为think
    .replace(/<details.*?>\s([\s\S]+?)<\/details>/gm, '$1') // 其他作为正文
})
</script>

<style scoped lang="less">
.render-chat-content {
  /* Your styles go here */
}
</style>
