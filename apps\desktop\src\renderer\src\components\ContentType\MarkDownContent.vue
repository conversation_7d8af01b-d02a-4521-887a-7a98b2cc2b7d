<template>
  <div @mouseup="copyToClipboard(message)">
    <ThoughtChain v-if="thoughtChain" :content="thoughtChain" :expanded="props.expanded" />
    <!--eslint-disable vue/no-v-html-->
    <span
      class="markdown"
      style="color: #333"
      @click.prevent="handleLinkClick"
      v-html="safeHTML"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import DOMPurify from 'dompurify'
import { ProtocolSchemeRegex } from '@renderer/utils'
import { renderMarkdown } from '@renderer/utils/markdown'
import '@renderer/assets/markdown.css'
import '@renderer/assets/github.css'
import '@renderer/assets/github-dark.css'
import ThoughtChain, {
  THOUGHT_REGEX_CLOSE,
  THOUGHT_REGEX_COMPLETE,
  THOUGHT_REGEX_OPEN
} from '../ThoughtChain'

const props = defineProps({
  message: String,
  expanded: Boolean
})

const { message /*, expanded*/ } = toRefs(props)

async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text)
  } catch (err) {
    console.error('Copy error:', err)
  }
}

const thoughtChain = computed(() => {
  if (typeof message?.value !== 'string' || !message?.value) return null
  if (message.value.match(THOUGHT_REGEX_COMPLETE)) {
    return message.value.match(THOUGHT_REGEX_COMPLETE)?.[0]
  }
  if (message.value.match(THOUGHT_REGEX_OPEN) && message.value.match(THOUGHT_REGEX_CLOSE)) {
    const closingTag = message.value.match(THOUGHT_REGEX_CLOSE)?.[0] as string
    const splitMessage = message.value.split(closingTag)
    return splitMessage[0] + closingTag
  }
  return null
})

const msgToRender = computed(() => {
  if (typeof message?.value !== 'string' || !message?.value) return ''
  if (thoughtChain.value) {
    return message.value.replace(THOUGHT_REGEX_COMPLETE, '')
  }
  return message.value
})

// const sanitizedMessage = computed(() => DOMPurify.sanitize(renderMarkdown(message.value)))
const safeHTML = computed(() => DOMPurify.sanitize(renderMarkdown(msgToRender.value)))

const handleLinkClick = (event: MouseEvent) => {
  if ((event.target as HTMLElement)?.nodeName === 'A') {
    event.preventDefault()
    const url = (event.target as HTMLLinkElement).getAttribute('href')
    if (!url) return
    // 拦截http(s)或自定义协议
    if (!url.match(ProtocolSchemeRegex)) return
    window.open(url, '_blank')
  }
}
</script>
