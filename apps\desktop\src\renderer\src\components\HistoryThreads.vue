<template>
  <a-list item-layout="horizontal" :data-source="displayList">
    <template #renderItem="{ item }">
      <a-list-item style="cursor: pointer" @click="onThreadClick(item)">
        <a-list-item-meta :description="item.updateTime">
          <template #title>
            {{ item.name }}
          </template>
        </a-list-item-meta>
      </a-list-item>
    </template>
  </a-list>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { computed } from 'vue'

const props = defineProps(['list'])
const emit = defineEmits(['change'])

const displayList = computed(() =>
  props.list.map((item) => ({
    ...item,
    updateTime: dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss')
  }))
)

const onThreadClick = (item: any) => {
  emit('change', item)
}
</script>

<style scoped></style>
