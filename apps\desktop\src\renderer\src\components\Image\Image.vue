<template>
  <img
    ref="imgRef"
    src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwXzg4NThfMTAxMTY2KSI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTkgNS41SDVDNC43MjM4NiA1LjUgNC41IDUuNzIzODYgNC41IDZWOS41SDE5LjVWNkMxOS41IDUuNzIzODYgMTkuMjc2MSA1LjUgMTkgNS41Wk00LjUgMThWMTFIMTkuNVYxOEMxOS41IDE4LjI3NjEgMTkuMjc2MSAxOC41IDE5IDE4LjVINUM0LjcyMzg2IDE4LjUgNC41IDE4LjI3NjEgNC41IDE4Wk01IDRDMy44OTU0MyA0IDMgNC44OTU0MyAzIDZWMThDMyAxOS4xMDQ2IDMuODk1NDMgMjAgNSAyMEgxOUMyMC4xMDQ2IDIwIDIxIDE5LjEwNDYgMjEgMThWNkMyMSA0Ljg5NTQzIDIwLjEwNDYgNCAxOSA0SDVaTTE3Ljc1IDguNUMxOC4xNjQyIDguNSAxOC41IDguMTY0MjEgMTguNSA3Ljc1QzE4LjUgNy4zMzU3OSAxOC4xNjQyIDcgMTcuNzUgN0MxNy4zMzU4IDcgMTcgNy4zMzU3OSAxNyA3Ljc1QzE3IDguMTY0MjEgMTcuMzM1OCA4LjUgMTcuNzUgOC41Wk0xNiA3Ljc1QzE2IDguMTY0MjEgMTUuNjY0MiA4LjUgMTUuMjUgOC41QzE0LjgzNTggOC41IDE0LjUgOC4xNjQyMSAxNC41IDcuNzVDMTQuNSA3LjMzNTc5IDE0LjgzNTggNyAxNS4yNSA3QzE1LjY2NDIgNyAxNiA3LjMzNTc5IDE2IDcuNzVaTTEyLjc1IDguNUMxMy4xNjQyIDguNSAxMy41IDguMTY0MjEgMTMuNSA3Ljc1QzEzLjUgNy4zMzU3OSAxMy4xNjQyIDcgMTIuNzUgN0MxMi4zMzU4IDcgMTIgNy4zMzU3OSAxMiA3Ljc1QzEyIDguMTY0MjEgMTIuMzM1OCA4LjUgMTIuNzUgOC41WiIgZmlsbD0iYmxhY2siLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF84ODU4XzEwMTE2NiI+CjxyZWN0IHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0id2hpdGUiLz4KPC9jbGlwUGF0aD4KPC9kZWZzPgo8L3N2Zz4K"
  />
</template>
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
const imgRef = ref()
const props = defineProps({
  dataSrc: {
    type: String,
    required: true
  }
})
watch(
  () => props.dataSrc,
  (newSrc) => {
    if (newSrc) {
      loadImg()
    }
  }
  // { immediate: true }
)
const loadImg = () => {
  const img = new Image()
  img.src = props.dataSrc
  img.onload = () => {
    imgRef.value.src = props.dataSrc
  }
}
onMounted(() => {
  loadImg()
})
</script>
<style scoped>
img {
  width: 24px;
}
</style>
