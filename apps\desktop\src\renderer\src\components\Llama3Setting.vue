<script setup lang="ts">
import { ref } from 'vue'
import { Select, SelectProps } from 'ant-design-vue'
import { SelectValue } from 'ant-design-vue/es/select'

let textBoxContent = ref('')
const llmOptions = ref<SelectProps['options']>([
  { value: 'Llama38B', label: 'Llama3-8B' },
  { value: 'Llama370B', label: 'Llama3-70B' }
])
let selectedOption = ref<SelectValue>(llmOptions.value?.[0].value as string)

const handleChangeLLM: any = (value: string) => {
  console.log(`selected ${value}`)
  selectedOption.value = value
}
const handleFocus = () => {
  console.log('focus')
}
const handleBlur = () => {
  console.log('blur')
}
function handleInput(event) {
  textBoxContent.value = event.target.value
}
</script>

<template>
  <div class="text-box">
    <label>{{ $t('api-path') }}</label>
    <input
      v-model="textBoxContent"
      type="text"
      :placeholder="$t('default-textBox')"
      @input="handleInput"
    />
  </div>
  <hr class="min-margin" />
  <Select
    v-model:value="selectedOption"
    style="width: 200px"
    :options="llmOptions"
    @focus="handleFocus"
    @blur="handleBlur"
    @change="handleChangeLLM"
  ></Select>
  <hr class="min-margin" />
  <max-context-message-count-slider
    class="min-margin"
    :title="$t('MaxContextMessageCount')"
    :minVal="0" :maxVal="100" :currnetVal="25"
  />
</template>

<style scoped lang="less">
.min-margin {
  margin-bottom: 13px;
  margin-top: 13px;
}

input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}
.text-box {
  margin-bottom: 10px;
  margin-top: 10px;
}
.text-box label {
  display: block;
  margin-bottom: 5px;
  color: #333;
}
</style>
