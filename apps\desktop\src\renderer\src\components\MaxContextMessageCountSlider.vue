<script setup lang="ts">
import { ref, computed, defineProps } from 'vue'

const params = defineProps(['title', 'minVal', 'maxVal', 'currnetVal'])
const minValue = params.minVal as number
const maxValue = params.maxVal as number
const value = ref(params.currnetVal)
const sliderBarRef = ref(null)

const sliderWidth = computed(() => {
  console.log(((value.value - minValue) / (maxValue - minValue)) * 100)
  return ((value.value - minValue) / (maxValue - minValue)) * 100
})

const updateValue = () => {}

const handleClickOnSlider = (event: MouseEvent) => {
  const sliderBarRect = (sliderBarRef.value! as HTMLElement).getBoundingClientRect()

  const offsetX = event.clientX - sliderBarRect.left
  const newValue = Math.round((offsetX / sliderBarRect.width) * (maxValue - minValue) + minValue)
  value.value = Math.max(Math.min(newValue, maxValue), minValue)
}
</script>

<template>
  <label>{{ title }}</label>
  <div class="slider-container">
    <div ref="sliderBarRef" class="slider-bar" @click="handleClickOnSlider">
      <div class="slider-filled" :style="{ width: sliderWidth + '%' }"></div>
    </div>
    <input
      v-model.number="value"
      type="number"
      :min="minValue"
      :max="maxValue"
      class="input-box"
      @input="updateValue"
    />
  </div>
</template>

<style scoped lang="less">
.slider-container {
  width: 100%;
  display: flex;
  align-items: center;
  margin: 0px, 10px, 10px, 10px;
}

.input-box {
  width: 60px;
  margin: 10px;
  text-align: center;
}

.slider-bar {
  width: 100%;
  height: 10px;
  background-color: #ddd;
  border-radius: 5px;
  position: relative;
  cursor: pointer;
}

.slider-filled {
  height: 100%;
  background-color: #007bff;
  border-radius: 5px 0 0 5px;
  position: absolute;
  top: 0;
  left: 0;
}
</style>
