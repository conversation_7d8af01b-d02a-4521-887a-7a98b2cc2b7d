<template>
  <div class="model-select">
    <Row :gutter="8">
      <Col>
        <Select
          v-model:value="selectedProvider"
          show-search
          :placeholder="$t('provider-placeholder')"
          style="width: 200px"
          :options="providers"
          :filter-option="filterOption"
          @change="handleProviderChange"
        ></Select>
      </Col>
      <Col>
        <Select
          v-model:value="selectedModel"
          show-search
          :placeholder="$t('model-placeholder')"
          style="width: 200px"
          :options="models"
          :filter-option="filterOption"
          @change="handleModelChange"
        ></Select>
      </Col>
    </Row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Select, Row, Col, SelectProps } from 'ant-design-vue'
import { WebApiService } from '@renderer/api/web'
import { PROVIDER } from '@ainow/types/index'
import { useLLMStore } from '../stores/llm'

const llmStore = useLLMStore()

const api = new WebApiService()

const providers = ref<SelectProps['options']>(
  [PROVIDER.DFAI, PROVIDER.EDGE, PROVIDER.AINOW, PROVIDER.EMBEDDING, PROVIDER.OLLAMA].map((p) => ({
    value: p,
    label: p
  }))
)
const selectedProvider = ref<string | undefined>(providers.value?.[0].value as string)

const handleProviderChange: any = async (value: string) => {
  llmStore.setProvider(value)

  const modelList = __ELECTRON__
    ? await window.api.changeProvider(value)
    : await api.getModels(value)

  console.log('modelList', modelList)
  models.value = modelList.map((m) => ({ value: m.id, label: m.id }))
  selectedModel.value = modelList?.[0]?.id
  handleModelChange(selectedModel.value)
}

const selectedModel = ref<string | undefined>('')
const models = ref<SelectProps['options']>([])
const handleModelChange: any = async (value: string) => {
  llmStore.setModel(value)
  console.log(`selected ${value}`)
}

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
onMounted(() => {
  if (__ELECTRON__) {
    window.api.listenerMsg((msg) => {
      console.log(msg, '监听到的channel消息')
      // 唤起小窗时 要把供应商change成ainow
      // selectedProvider.value = PROVIDER.AINOW
      // handleProviderChange(providers.value?.[2].value)
    })
  }
})
// window.api.changeProvider(selectedProvider.value)
handleProviderChange(providers.value?.[0].value)
</script>

<style scoped>
.model-select {
  max-width: calc(100% - 32px);
  :deep(.ant-col) {
    max-width: calc(50% - 8px);
    :first-child {
      max-width: 100%;
    }
  }
}
</style>
