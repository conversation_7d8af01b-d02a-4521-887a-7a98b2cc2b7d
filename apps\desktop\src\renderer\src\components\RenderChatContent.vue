<template>
  <div class="render-chat-content">
    <AINowContent v-if="provider === PROVIDER.AINOW" :message="message" :messaageId="messaageId" />
    <DfaiContent v-else-if="provider === PROVIDER.DFAI" :message="message" />
    <MarkDownContent v-else :message="message"></MarkDownContent>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from 'vue'
import MarkDownContent from './ContentType/MarkDownContent.vue'
import AINowContent from './ContentType/AINowContent/index.vue'
import DfaiContent from './ContentType/DfaiContent.vue'
import { PROVIDER } from '@ainow/types/index'

const props = defineProps({
  message: String,
  provider: String,
  messaageId: String
})

const { message, provider } = toRefs(props)
</script>

<style scoped lang="less">
.render-chat-content {
  /* Your styles go here */
}
</style>
