<template>
  <div class="search-list" :style="{ display: isShowSearch ? '' : 'none' }">
    <div
      v-for="(item, index) in searchPrompt"
      :key="item.AssistantName"
      class="search-item"
      :tabindex="index"
      role="button"
      @click="selectSearchItem(item)"
      @keydown.enter="selectSearchItem(item)"
    >
      <span class="icon">{{ item.Icon }}</span>
      <div class="search-name">{{ item.AssistantName }}</div>
      <div class="search-desc">{{ item.AssistantDescribe }}</div>
      <span class="enter-icon icon" :style="{ display: activeTabIndex === index ? '' : 'none' }">{{
        huicheIcon
      }}</span>
    </div>
  </div>
  <div class="PE-ques" :style="{ display: isShowPE ? '' : 'none' }">
    <div class="PE-ques-title">
      <span class="icon">{{ activeSearchItem.icon }}</span>
      <span>{{ activeSearchItem.assistantName }}</span>
    </div>
    <CloseOutlined class="PE-ques-close" @click="closePE" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, reactive, nextTick, watch } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
const searchPrompt = [
  {
    Icon: '\uE6F6',
    AssistantName: 'Document Summary',
    AssistantDescribe: 'Summarize content and list key points from your document(s).'
  },
  {
    Icon: '\uE6F3',
    AssistantName: 'Document Search',
    AssistantDescribe: 'Search for documents by theme, content, or keywords.'
  },
  {
    Icon: '\uE6F1',
    AssistantName: 'Document Insights',
    AssistantDescribe: 'Extract insights and analysis from the content of your document(s).'
  },
  {
    Icon: '\uE6F7',
    AssistantName: 'Content Translator',
    AssistantDescribe: 'Translate the content.'
  },
  {
    Icon: '\uE6F4',
    AssistantName: 'Content Enhancer',
    AssistantDescribe: 'Refine and improve the content of your documents.'
  },
  {
    Icon: '\uE6F5',
    AssistantName: 'Document Comparison',
    AssistantDescribe: 'Compare similarities and differences of selected documents.'
  },
  {
    Icon: '\uE6F2',
    AssistantName: 'PC Assistant',
    AssistantDescribe: 'Get help for PC control or service.'
  }
]
const props = defineProps({
  message: String
})
const emit = defineEmits(['clearMessage'])
const isShowPE = ref(false) // 是否展示单个pe title
const activeSearchItem = reactive({
  // searchList激活的item
  assistantName: '',
  icon: ''
})
const isShowSearch = ref<boolean>(false) // 是否展示searchList
const activeTabIndex = ref<number>(0) // seatchList激活的index
const huicheIcon = ref<string>('\uE6F0') // 回车按钮
let searchItemsList
watch(
  () => props.message,
  (val) => {
    if (val === '/') {
      if (!isShowPE.value) {
        isShowSearch.value = true
      } else {
        isShowSearch.value = false
      }
      nextTick(() => {
        if (!searchItemsList) {
          searchItemsList = document.querySelector('.search-list')
          searchItemsList.addEventListener('keydown', (event) => {
            if (event.key === 'ArrowUp') {
              activeTabIndex.value = Math.max(activeTabIndex.value - 1, 0)
            } else if (event.key === 'ArrowDown') {
              activeTabIndex.value = Math.min(
                activeTabIndex.value + 1,
                searchItemsList.querySelectorAll('.search-item').length - 1
              )
            }
            searchItemsList.querySelector(`div[tabindex="${activeTabIndex.value}"]`).focus()
          })
        }
        searchItemsList.querySelector(`div[tabindex="${activeTabIndex.value}"]`).focus()
      })
    } else {
      isShowSearch.value = false
      activeTabIndex.value = 0
    }
  }
)
const selectSearchItem = (item) => {
  activeSearchItem.icon = item.Icon
  activeSearchItem.assistantName = item.AssistantName
  emit('clearMessage')
  isShowPE.value = true
  isShowSearch.value = false
}
const closePE = () => {
  isShowPE.value = false
  emit('clearMessage')
}
</script>
<style scoped lang="less">
@import url('../assets/font/font.css');
.search-list {
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 8px;
  padding: 8px;
  .search-item {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 3px 24px;
    .icon {
      margin-right: 0;
      width: 24px;
      height: 24px;
      font-size: 20px;
      margin-bottom: 6px;
    }
    .search-desc {
      flex: 1;
      font-size: 12px;
      font-weight: 400;
    }
    &:active,
    &:focus,
    &:focus-visible,
    &:hover {
      background-color: rgba(236, 244, 255, 1);
    }
    .search-name {
      font-size: 14px;
      font-weight: 600;
    }
    > div {
      margin-left: 18px;
    }
  }
}
.PE-ques {
  height: 32px;
  width: 100%;
  border-radius: 10px 10px 0 0;
  background: linear-gradient(272.73deg, #4663ff 0.08%, #675eff 57.09%, #8459ff 99.71%);
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  align-items: center;
  color: #fff;
  &-title {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
  }
  &-close {
    cursor: pointer;
  }
}
@media (max-width: 750px) {
  .search-list .search-item .search-desc {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
