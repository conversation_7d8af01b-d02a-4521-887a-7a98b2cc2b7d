<template>
  <div class="setting-modal">
    <Modal :open="props.open" :title="$t('setting')" @ok="handleOk" @cancel="handleCancel">
      <Select
        v-model:value="llm"
        class="min-margin"
        style="width: 200px"
        :options="options"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChangeLang"
      ></Select>
      <hr class="min-margin" />
      <Llama3Setting v-if="isLlama" />
      <OllamaSetting v-if="!isLlama" />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue'
import { Modal, Select, SelectProps } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
import { useStore } from '../stores'
import Llama3Setting from './Llama3Setting.vue'
import OllamaSetting from './OllamaSetting.vue'
import { SelectValue } from 'ant-design-vue/es/select'
//import {type Settings} from '../types'
let isLlama = true
const store = useStore()
const options = ref<SelectProps['options']>([
  { value: 'Llama3', label: 'Llama3' },
  { value: 'ollama', label: 'ollama' }
])

const llm = ref<SelectValue>(options.value?.[0].value as string) //computed(() => store.llm)

const handleChangeLang: any = (value: string) => {
  console.log(`selected ${value}`)
  console.log(store, 'store')
  store.setLLM(value)
  if (value != 'Llama3') {
    isLlama = false
  } else {
    isLlama = true
  }
}

const handleBlur = () => {
  console.log('blur')
}
const handleFocus = () => {
  console.log('focus')
}
interface Props {
  open: boolean
}
const props = withDefaults(defineProps<Props>(), {
  open: false
})

const emit = defineEmits<{
  (e: 'updateOpen', status: boolean): void
}>()
const handleOk = () => {
  emit('updateOpen', false)
  if (!llm.value) return
  locale.value = llm.value as string
  localStorage.setItem('llm', llm.value as string)
}

const handleCancel = () => {
  emit('updateOpen', false)
}

//let llmModel: Settings = {id:"123",modelName:"deepSeep",domainName:"1",apiPath:"/api", modelNumber:"1.8", maxContextMessageCount:20,maxContextMessageMinCount:0,maxContextMessageMaxCount:100}
</script>

<style scoped>
.min-margin {
  margin-bottom: 10px;
  margin-top: 10px;
}
</style>
