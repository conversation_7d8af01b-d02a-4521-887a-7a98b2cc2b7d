<template>
  <div id="darkModel">
    <a-select ref="select" v-model:value="store.theme" @change="changeTheme">
      <a-select-option v-for="item in modes" :value="item" :key="item">{{ item }}</a-select-option>
    </a-select>
  </div>
</template>
<script setup lang="ts">
import { useStore } from '@/renderer/src/stores'
import { SelectValue } from 'ant-design-vue/es/select'
import { defineEmits } from 'vue'
const store = useStore()
const emit = defineEmits(['updateMode'])
const modes = ['Light', 'Dark', 'UserSet']
const changeTheme = (val: SelectValue) => {
  emit('updateMode', val)
}
</script>
