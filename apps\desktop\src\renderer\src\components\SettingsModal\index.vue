<template>
  <a-modal
    v-model:open="props.isOpen"
    title="Settings"
    @ok="syncSettingsConfig"
    @cancel="emit('handleIsOpen')"
  >
    <div class="settings-container">
      <div v-for="item in Object.keys(settingsConfig)" :key="item">
        <div v-if="settingsConfig[item].isShow">
          <div>{{ item }}:</div>
          <div class="settings-info">
            <div>{{ settingsConfig[item].desc }}</div>
            <a-switch
              v-model:checked="settingsConfig[item].isOpen"
              v-if="typeof settingsConfig[item].isOpen !== 'undefined'"
            />
          </div>
          <div>
            <DarkMode v-if="item === 'darkMode'" @updateMode="updateMode" />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import DarkMode from './DarkMode.vue'
import { reactive } from 'vue'
interface Info {
  desc: string // 功能描述
  isOpen: boolean // 是否开启
  name: string // 名称
  isShow?: boolean // 是否展示
  component?: any
  [propName: string]: boolean | string | undefined | (() => void)
}
interface SettingConfig {
  [key: Info['name']]: Info
}
interface Props {
  isOpen: boolean
  settingConfig: SettingConfig
}
const props = defineProps<Props>()
const emit = defineEmits<{
  (fn: 'handleIsOpen'): void
  (fn: 'changeDarkMode', mode: string): void
}>()
let settingsConfig = reactive<{ [key: Info['name']]: Info }>({})
// 将传入值处理成数组
function getListByConfig() {
  if (props.settingConfig) {
    Object.keys(props.settingConfig).forEach((item) => {
      settingsConfig[item] = props.settingConfig[item]
    })
  }
}
function updateMode(mode: string) {
  // 同步settings数据
  settingsConfig.darkMode = { ...settingsConfig.darkMode, mode }
  emit('changeDarkMode', mode)
}
function syncSettingsConfig() {
  window.api.syncSettingsConfig(toRaw(settingsConfig))
  emit('handleIsOpen')
}
watch(
  () => props.isOpen,
  (val) => {
    if (val) {
      getListByConfig()
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="less">
@import '@renderer/assets/global.less';
.settings-container {
  .settings-info {
    display: flex;
    justify-content: space-between;
  }
}
</style>
