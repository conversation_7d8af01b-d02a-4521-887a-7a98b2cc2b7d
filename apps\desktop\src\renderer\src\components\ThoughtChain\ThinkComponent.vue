<template>
  <div class="thought-chain-container">
    <div v-if="hasThinkContent" class="content-wrapper">
      <div :style="containerStyle" :class="[expandClass, 'content-box']">
        <BulbOutlined
          v-if="isThinking || isComplete"
          :class="thinkingIconClass"
          style="align-self: flex-start"
          :data-tooltip-id="thinkingTooltipId"
          :data-tooltip-content="thinkingTooltipContent"
          :aria-label="thinkingAriaLabel"
        />
        <span
          v-if="!isExpanded && !autoExpand"
          class="preview-text"
          v-html="sanitizedContentPreview"
        ></span>
        <span v-else class="full-text" v-html="sanitizedContentFull"></span>
        <button
          :data-tooltip-id="expandTooltipId"
          :data-tooltip-content="expandTooltipContent"
          :aria-label="expandAriaLabel"
          class="expand-button"
          style="align-self: flex-end"
          @click="handleExpandClick"
        >
          <CaretDownOutlined v-if="!isExpanded" :class="caretClass" />
          <CaretUpOutlined v-else :class="caretClass" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import DOMPurify from 'dompurify'
import truncate from 'truncate'
import { BulbOutlined, CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue'
import { renderMarkdown } from '../../utils/markdown'
import {
  THOUGHT_REGEX_OPEN,
  THOUGHT_REGEX_CLOSE,
  THOUGHT_REGEX_COMPLETE,
  THOUGHT_PREVIEW_LENGTH
} from './constants'

const props = defineProps({
  content: String,
  expanded: Boolean
})

// const emit = defineEmits(['update'])

const localContent = ref(props.content)
const isExpanded = ref(props.expanded)

const hasThinkContent = ref(false)

watch(
  () => props.content,
  (newVal) => {
    localContent.value = newVal
    hasThinkContent.value = removeInvisibleChars(DOMPurify.sanitize(newVal)).length
  },
  { immediate: true }
)

function removeInvisibleChars(str) {
  return str.replace(/[\n\r\t\s]/g, '')
}

const isThinking = computed(
  () =>
    localContent.value.match(THOUGHT_REGEX_OPEN) && !localContent.value.match(THOUGHT_REGEX_CLOSE)
)
const isComplete = computed(
  () =>
    localContent.value.match(THOUGHT_REGEX_COMPLETE) ||
    localContent.value.match(THOUGHT_REGEX_CLOSE)
)
const tagStrippedContent = computed(() =>
  localContent.value.replace(THOUGHT_REGEX_OPEN, '').replace(THOUGHT_REGEX_CLOSE, '')
)
const autoExpand = computed(
  () => isThinking.value && tagStrippedContent.value.length > THOUGHT_PREVIEW_LENGTH
)
const canExpand = computed(() => tagStrippedContent.value.length > THOUGHT_PREVIEW_LENGTH)

const sanitizedContentPreview = computed(() =>
  DOMPurify.sanitize(truncate(tagStrippedContent.value, THOUGHT_PREVIEW_LENGTH))
)
const sanitizedContentFull = computed(() =>
  DOMPurify.sanitize(renderMarkdown(tagStrippedContent.value))
)

function handleExpandClick() {
  if (!canExpand.value) return
  isExpanded.value = !isExpanded.value
}

const containerStyle = computed(() => ({
  transition: 'all 0.1s ease-in-out',
  borderRadius: isExpanded.value || autoExpand.value ? '6px' : '24px'
}))

const expandClass = computed(() => ({
  'hover-bg-theme-sidebar-item-hover': canExpand.value && !(isExpanded.value || autoExpand.value)
}))

const thinkingIconClass = computed(() => ({
  'icon-style': true,
  'thinking-pulse': isThinking.value,
  'thinking-done': !isThinking.value
}))

const caretClass = computed(() => ({
  'caret-icon': true,
  'rotate-caret': isExpanded.value
}))

const thinkingTooltipId = 'cot-thinking'
const thinkingTooltipContent = computed(() =>
  isThinking.value ? 'Model is thinking...' : 'Model has finished thinking'
)
const thinkingAriaLabel = computed(() =>
  isThinking.value ? 'Model is thinking...' : 'Model has finished thinking'
)

const expandTooltipId = 'expand-cot'
const expandTooltipContent = computed(() =>
  isExpanded.value ? 'Hide thought chain' : 'Show thought chain'
)
const expandAriaLabel = computed(() =>
  isExpanded.value ? 'Hide thought chain' : 'Show thought chain'
)
</script>

<style scoped>
.thought-chain-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  transition: all 0.2s;
  width: 100%;
  max-width: 800px;
}

.content-wrapper {
  padding-bottom: 0.5rem;
  width: 100%;
}

.content-box {
  padding: 0.5rem 1rem;
  background-color: var(--theme-bg-chat-input);
  border: 1px solid var(--theme-sidebar-border);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.preview-text,
.full-text {
  font-family: monospace;
  color: var(--theme-text-secondary);
  font-style: italic;
}

.expand-button {
  border: none;
  background: none;
  cursor: pointer;
  color: var(--theme-text-secondary);
  transition:
    color 0.3s,
    background 0.3s;
  padding: 0.25rem;
  border-radius: 50%;
}

.icon-style {
  width: 1rem;
  height: 1rem;
  margin-top: 0.25rem;
}

.thinking-pulse {
  color: blue;
  animation: pulse 1s infinite;
}

.thinking-done {
  color: green;
}

.caret-icon {
  width: 1rem;
  height: 1rem;
  transform: rotate(0deg);
  transition: transform 0.2s;
}

.rotate-caret {
  /* transform: rotate(180deg); */
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
