export const test = {
  allTypeList: {
    others: ['WORK_ASSISTANT_KEY_PAGE_SEARCH', 'WORK_ASSISTANT_DOCUMENT_SEARCH'],
    top: 'WORK_ASSISTANT_IMAGE_SEARCH'
  },
  category: 'WORK',
  data: {
    docNum: 1,
    documentList: [
      //C:\Users\<USER>\AppData\Local\Lenovo\Lenovo AI Now\KBRootPath\a5cdc96ef1fda080f1f07b6e61a7ba12\document\PKBData
      {
        createTime: '2025-03-04 18:59:20',
        docName: '\u56fe\u50cf (2).jpg',
        editTime: '2025-03-04 18:59:23',
        errorDescription: '4007-Chunk is null, unable to extract text',
        fileName: '\u56fe\u50cf (2).jpg.lnk',
        filePath:
          'atom:\\\\users\\lenovo\\appdata\\local\\lenovo\\lenovo ai now\\kbrootpath\\a5cdc96ef1fda080f1f07b6e61a7ba12\\document\\pkbdata\\dog.jpg',
        id: 1,
        isDeleted: 0,
        keywords: [''],
        labelPriority: 1,
        linkLastModifyTime: '2025-03-04 18:59:20',
        md5: '745f7e517f0aef0e7720e0dde0026b8c',
        ownerId: 'default',
        priority: 1,
        retrievalType: 'CLIP',
        score: 0.19966429999999999,
        status: 'COMPLETED',
        summarizedStatus: 'TO_BE_SUMMARIZED',
        targetLastModifyTime: '2025-02-27 15:56:22',
        targetPath: 'C:\\Users\\<USER>\\Downloads\\\u56fe\u50cf (2).jpg'
      }
    ],
    maybeRelevantDocumentList: [],
    stop: true
  },
  errorCode: 0,
  errorMsg: '',
  language: 'EN',
  lid: '398E4857-E0F6-48D4-A3C2-BB8B139B410F',
  type: 'WORK_ASSISTANT_IMAGE_SEARCH'
}
