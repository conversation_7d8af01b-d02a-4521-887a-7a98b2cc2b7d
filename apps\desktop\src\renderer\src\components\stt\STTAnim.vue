<template>
  <Vue3Lottie
    v-if="status"
    class="anim"
    :animation-data="AnimRecording"
    :height="28"
    :width="84"
    :loop="true"
    :auto-play="true"
  />
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import { Vue3Lottie } from 'vue3-lottie'
import AnimRecording from '@renderer/assets/audio-recording.json'

const props = defineProps(['status'])
</script>

<style scoped>
.anim {
  display: inline-block;
}
</style>
