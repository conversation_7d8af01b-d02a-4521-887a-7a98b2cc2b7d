<template>
  <SvgIcon :name="buttonIcon" size="32px" class="btn" @click="toggleRecording"></SvgIcon>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, defineEmits } from 'vue'
import SvgIcon from '@renderer/components/SvgIcon'
import { STOP_TEXT } from './constants'

const emit = defineEmits(['text-received', 'say-stop', 'recording'])
const props = defineProps(['prevMsg'])

const isRecording = ref(false)
const pauseTimer = ref<NodeJS.Timeout | null>(null)
const sttId = ref<string>('')
const buttonIcon = computed(() => (isRecording.value ? 'audio-stop' : 'audio-record'))

// TODO 类型
const handleSTTCallback = (data: { type: number; text?: string }) => {
  // console.log('[handleSTTCallback]', data.type, data.text)
  const { type, text } = data
  switch (type) {
    case 1:
      if (text) {
        if (text === STOP_TEXT) {
          stopRecording()
          emit('say-stop')
          return
        }
        emit('text-received', type, `${props.prevMsg || ''}${text}`)
        clearPauseTimer()
      }
      break
    case 2:
      emit('text-received', type, `${props.prevMsg || ''}${text}`)
      pauseTimer.value = setTimeout(() => {
        console.log('自动停止')
        stopRecording()
      }, 10000)
      break
  }
}

window.api.onSttResponse((sId: string, type: number, text: string) => {
  if (sId !== sttId.value) return
  handleSTTCallback({ type, text })
})

const clearPauseTimer = () => {
  if (pauseTimer.value) {
    clearTimeout(pauseTimer.value)
    pauseTimer.value = null
  }
}

const toggleRecording = async () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

const startRecording = async () => {
  isRecording.value = true
  clearPauseTimer()

  try {
    const id = await window.api.sttAction('start')
    sttId.value = id
    console.log('start sst id', id)
  } catch (error) {
    console.error('STT启动失败:', error)
    isRecording.value = false
    clearPauseTimer()
  }

  emit('recording', true)
}

// 停止录音
const stopRecording = () => {
  isRecording.value = false
  clearPauseTimer()
  if (!sttId.value) return
  console.log('stopRecording')
  window.api.sttAction('stop', sttId.value)
  emit('recording', false)
}

// 组件卸载时停止录音
onUnmounted(() => {
  stopRecording()
})
</script>

<style scoped>
.btn {
  cursor: pointer;
}
</style>
