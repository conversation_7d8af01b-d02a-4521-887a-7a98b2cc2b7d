import type { Ref } from 'vue'
import { STOP_TEXT } from './constants'

export default function useSTT(message: Ref<string>, onStatus?: (v: boolean) => void) {
  const showSTT = __ELECTRON__
  const isSTTRecording = ref(false)
  const isSTTSpeaking = ref(false)
  const prevMsgForSTT = ref(message.value)
  const onSTTReceived = (type: number, text: string) => {
    message.value = text
    if (type === 2) {
      isSTTSpeaking.value = false
      prevMsgForSTT.value = message.value
    } else {
      isSTTSpeaking.value = true
    }
  }
  const onSTTSayStop = () => {
    const keyword = STOP_TEXT
    const text = message.value
    for (let i = keyword.length - 1; i >= 0; i--) {
      const partial = keyword.slice(0, i)
      if (text.endsWith(partial)) {
        message.value = text.slice(0, text.length - i)
        break
      }
    }
  }
  const onSTTStatus = (v: boolean) => {
    isSTTRecording.value = v
    onStatus?.(v)
  }

  return {
    showSTT,
    isSTTRecording,
    isSTTSpeaking,
    prevMsgForSTT,
    onSTTReceived,
    onSTTSayStop,
    onSTTStatus
  }
}
