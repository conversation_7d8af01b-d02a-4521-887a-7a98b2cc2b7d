export function electronHooks() {
  function handleIgnoreMouseEvents(el: HTMLElement) {
    const { setIgnoreMouseEvents } = window.api
    // console.log(view)

    el.addEventListener('mouseover', (e: MouseEvent) => {
      if ((e.target as HTMLElement).dataset.ignore) {
        setIgnoreMouseEvents(true, { forward: true })
      } else {
        setIgnoreMouseEvents(false)
      }
    })
  }
  if (window.api) {
    return { ...window.api, handleIgnoreMouseEvents }
  }
  console.error('not running in Electron')
  return null
}
