import { ref, onMounted } from 'vue'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'
import { lightTheme } from '@renderer/theme/light'
import { darkTheme } from '@renderer/theme/dark'
import { useStore } from '@renderer/stores'
export function useTheme(): {
  isDark: ReturnType<typeof ref<boolean>>
  currentTheme: ReturnType<typeof ref<ThemeConfig>>
  toggleTheme: (theme?: string) => void
  getTheme: () => string
} {
  const isDark = ref(false)
  const store = useStore()
  const currentTheme = ref(lightTheme)

  // 切换主题
  const toggleTheme = (theme?: string) => {
    currentTheme.value = theme === 'Light' ? lightTheme : darkTheme
    console.log('currentTheme', isDark.value)
    isDark.value = theme === 'Light' ? false : true
    document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light')
    store.setTheme(theme!)
    //localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  }

  const getTheme = () => {
    return store.theme
  }

  // 初始化主题
  onMounted(() => {
    // const savedTheme = localStorage.getItem('theme')
    // if (savedTheme === 'dark') {
    //   isDark.value = true
    //   currentTheme.value = darkTheme
    //   document.documentElement.setAttribute('data-theme', 'dark')
    // }
    const themeMedia = window.matchMedia('(prefers-color-scheme: light)')
    const themeChange = () => {
      const isLight = themeMedia.matches
      // alert(isLight)
      if (isLight) {
        isDark.value = false
        currentTheme.value = lightTheme
        document.documentElement.setAttribute('data-theme', 'light')
      } else {
        isDark.value = true
        currentTheme.value = darkTheme
        document.documentElement.setAttribute('data-theme', 'dark')
      }
    }
    themeChange()
    themeMedia.addEventListener('change', () => {
      themeChange
    })
  })

  return {
    isDark,
    currentTheme,
    toggleTheme,
    getTheme
  }
}
