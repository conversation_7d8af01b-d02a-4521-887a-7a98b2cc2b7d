import { createI18n } from 'vue-i18n'
import messages from '@renderer/locales'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import App from './App.vue'
import routers from '@renderer/routers'
import './theme/index.css'
import './assets/main.css'
import 'virtual:svg-icons-register'

// @ts-ignore TODO
let testBuild = ''
if (__ELECTRON__) {
  testBuild = 'Electron'
}
if (__WEB__) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  testBuild = 'web'
}

// 创建 Pinia 实例并挂载到 Vue 应用
const pinia = createPinia()
// 创建 VueI18n 实例
const i18n = createI18n({
  locale: localStorage.getItem('lang') || 'zh-CN', // 设置默认语言
  messages // 加载国际化消息
})
createApp(App).use(pinia).use(i18n).use(routers).mount('#app')
