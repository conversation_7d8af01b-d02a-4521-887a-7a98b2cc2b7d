import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'main',
    redirect: '/localchat',
    component: async () => await import('@renderer/views/Layout/index.vue'),
    children: [
      {
        path: '/localchat',
        name: 'localchat',
        component: async () => await import('@renderer/views/Main/index.vue')
      },
      // {
      //   path: '/localchatMini',
      //   name: 'localchatMini',
      //   component: async () => await import('@renderer/views/Main/Mini.vue')
      // },
      {
        path: '/explore',
        name: 'explore',
        component: async () => await import('@renderer/views/Explore/index.vue')
      },
      {
        path: '/knowledge',
        name: 'knowledge',
        component: async () => await import('@renderer/views/Knowledge/index.vue')
      }
    ]
  },
  {
    path: '/agents',
    name: 'agents',
    component: async () => await import('@renderer/views/Agents/index.vue')
  },
  {
    path: '/theme',
    name: 'theme',
    component: async () => await import('@renderer/views/Theme/index.vue')
  },
  {
    path: '/word',
    name: 'word',
    component: async () => await import('@renderer/views/Word/index.vue')
  },
  {
    path: '/login',
    name: 'login',
    component: async () => await import('@renderer/views/Login/index.vue')
  },
  __ELECTRON__
    ? {
        name: 'SearchBar',
        path: '/SearchBar',
        component: async () => await import('@renderer/views/SearchBar')
      }
    : null,
  __ELECTRON__
    ? {
        name: 'FloatingBall',
        path: '/FloatingBall',
        component: async () => await import('@renderer/views/FloatingBall')
      }
    : null
].filter(Boolean) as RouteRecordRaw[]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
