// stores/counter.js
import { defineStore } from 'pinia'

export const useStore = defineStore('counter', {
  state: () => {
    return {
      packageName: 'Lenovo AI Agent',
      lang: localStorage.getItem('lang') || 'zh-CN',
      theme: localStorage.getItem('theme') || 'light',
      llm: localStorage.getItem('llm')
    }
  },
  actions: {
    setPackageName(name: string) {
      this.packageName = name
    },
    setLang(lang: string) {
      this.lang = lang
      //localStorage.setItem('lang', lang)
    },
    setTheme(theme: string) {
      this.theme = theme
      // localStorage.setItem('theme', theme)
    },
    setLLM(llm: string) {
      this.llm = llm
    }
  }
})
