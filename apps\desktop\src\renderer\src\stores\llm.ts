import { defineStore } from 'pinia'
import { type Account, type Thread } from '@prisma/client'

interface IState {
  account: Account | null
  thread: Thread | null
  provider: string
  model: string
}

export const useLLMStore = defineStore('llm', {
  state: (): IState => ({
    account: null,
    thread: null,
    provider: '',
    model: ''
  }),
  actions: {
    setAccount(account: Account) {
      this.account = account
    },
    setThread(thread: Thread) {
      this.thread = thread
    },
    setProvider(provider: string) {
      this.provider = provider
    },
    setModel(model: string) {
      this.model = model
    }
  },
  getters: {
    currentAccount: (state) => state.account,
    currentThread: (state) => state.thread,
    currentProvider: (state) => state.provider,
    currentModel: (state) => state.model
  }
})
