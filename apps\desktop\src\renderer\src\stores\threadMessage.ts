import { defineStore } from 'pinia'
import { useLLMStore } from './llm'
import useDB from '@renderer/hooks/useDB'
import { PROVIDER } from '@ainow/types/index'
const llmStore = useLLMStore()
const { saveThreadMessage } = useDB()

// 标识回答的状态 0为预置占位 1为准备中（ainow使用）2为模型的回答
export enum ThreadMessageStatus {
  PENDING = 0,
  PREPAREING = 1,
  STREAMING = 2
}

export interface IThreadMessage {
  role: 'user' | 'sys' | 'assistant'
  // accountId?: string; // 所属的某个账号
  threadId: string // 所属的某次对话
  prompt: string | null // 某个具体问题
  promptId: string // 某个具体问题的id，在消息发送时由聊天界面生成
  response: string
  message?: string | null
  additionalWords?: string | null
  updateTime: number // TODO 改为数据库生成的
  createTime?: number // TODO 改为数据库生成的
  done?: boolean
  status?: ThreadMessageStatus
  model?: string
  provider?: string
}

export type ThreadMessageUI = Partial<
  IThreadMessage & {
    title?: string
    description: string
    avatar: any
    promptId: string
    updateTime: number
    createTime: number
  }
>

export const userThreadMessageStore = defineStore('chats', {
  state: () => ({
    chats: [] as ThreadMessageUI[]
  }),
  actions: {
    setChats(chats: ThreadMessageUI[]) {
      this.chats = chats
    },
    async appendChat(chatRecord: ThreadMessageUI, needJoin = true) {
      const { threadId, prompt, promptId, response, updateTime, done, status } = chatRecord
      const existingChatIndex = this.chats.findIndex((c) => c.promptId === promptId)
      console.log(existingChatIndex, '是否在已有记录找到')
      if (existingChatIndex > -1) {
        // 正在进行的对话
        const duplicatedDone = this.chats[existingChatIndex].done && done && !response
        console.log(
          '[store::threadMessage] 是否重复',
          this.chats[existingChatIndex].done,
          done,
          response
        )
        if (!duplicatedDone) {
          // 流式对话
          if (response) {
            if (needJoin) {
              this.chats[existingChatIndex].response += response
            } else {
              const temp = JSON.parse(response)
              // ainow 处理 数据全部返回的直接赋值 一个字一个字返回的需要拼接 todo如何判断？
              if (!temp.done) {
                // done是true时返回的数据是空 不进行历史存储 //保证完整的响应
                this.chats[existingChatIndex].response = response
              }
            }
          }
          this.chats[existingChatIndex].updateTime = updateTime
          this.chats[existingChatIndex].status = status
          if (!done) return

          console.log(
            '[store::threadMessage] 一次对话完成',
            chatRecord
            // this.chats[existingChatIndex].response
          )
          // 持久化
          const error = await saveThreadMessage({
            accountId: llmStore.currentAccount?.id,
            threadId,
            prompt: this.chats[existingChatIndex].prompt, // 存储提问的问题
            promptId,
            // ainow是一个对象 其他是字符串
            response:
              llmStore.currentProvider === PROVIDER.AINOW
                ? JSON.parse(this.chats[existingChatIndex].response ?? '{}')
                : this.chats[existingChatIndex].response,
            chatProvider: llmStore.currentProvider,
            chatModel: llmStore.currentModel
          })
          console.log('[store::threadMessage] 持久化', error)
          if (error) throw error
        }
      } else {
        // 新对话
        this.chats.push(chatRecord)
      }
    },
    getChat(threadId: string) {
      return this.chats.find((c) => c.threadId === threadId)
    },
    removeChat(threadId: string) {
      this.chats = this.chats.filter((c) => c.threadId !== threadId)

      // TODO 持久化
    }
  },
  getters: {
    allChats: (state) => state.chats
  }
})
