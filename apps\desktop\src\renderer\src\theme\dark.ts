import { theme } from 'ant-design-vue'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'

// 暗色主题
export const darkTheme: ThemeConfig = {
  token: {
    colorPrimary: '#00b96b',
    colorSuccess: '#1dc779',
    colorWarning: '#ffb302',
    colorError: '#cf4444',
    colorInfo: '#1677ff',
    colorText: '#ffffff',
    colorTextLightSolid: '#ffffff',
    borderRadius: 6
  },
  algorithm: theme.darkAlgorithm // 使用暗色算法
}
