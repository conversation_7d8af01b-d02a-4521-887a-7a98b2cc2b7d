/* 导入主题文件 */
@import './light.css';
@import './dark.css';

/* 全局基础样式 */
body {
  /* background-color: var(--bg-color-primary); */
  color: var(--text-color-primary);
  transition: background-color 0.3s, color 0.3s;
}
@media (prefers-color-scheme: light) {
 
}
@media (prefers-color-scheme: dark) {
 
  
  
}

 @media screen and (forced-colors: active) {
  body{
    forced-color-adjust: none;
  }
  
  /* .nav-item-selected {
      forced-color-adjust: none;
      background-color: highlight;
      color: HighlightText;
  } */
} 