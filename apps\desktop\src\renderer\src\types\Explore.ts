export interface EntryItem {
  icon: string
  description: string
  promptText?: string
  hintText?: string
  actionType: string
  intentionOp?: string
}

export interface EntryCardProps {
  title?: string
  description?: string
  entries: EntryItem[]
  visibleCount: number
  backgroundColor?: string
}

export interface PartnerCardProps {
  icon: string
  title: string
  description: string
  status: string
}
export enum FeatureActionNameType {
  ToPKBCommand = 'ToPKBCommand',
  ToPCAssistantCommand = 'ToPCAssistantCommand',
  ToCloudCommand = 'ToCloudCommand'
}
