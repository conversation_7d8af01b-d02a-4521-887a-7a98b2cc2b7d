export enum ShellExchangeFunctionType {
  APPLETINFO = 'w.AppletInfo', // 小程序商店列表页、小程序详情
  LOADEDCOMPELETED = 'w.LoadedCompeleted', // 是否loaded
  GETAUTHENTICATION = 'w.GetAuthentication', // 获取鉴权信息
  //   GETPAGE = "w.GetPage", // 获取page
  TOAUTHENTICATION = 'c.ToAuthentication', // 发送TOKEN等鉴权信息
  OPENSERVICECONTRACT = 'w.open.ServiceContract', // 发送TOKEN等鉴权信息
  REDIRECTTOPAYMENT = 'w.RedirectToPayment',
  WLENAEXIT = 'w.LenaExit',
  CSENDREQUEST = 'c.SendRequest',
  WOPENLINK = 'w.OpenLink',
  CSENDHISTROYLIST = 'c.SendHistroyList',
  WSESSIONCALLBACK = 'w.SessionCallback',
  CSENDENVIRONMENT = 'c.SendEnvironment',

  // NPS
  W_NPS_LOAD_COMPLETED = 'w.NPS_LOAD_COMPLETED',
  C_NPS_INFO = 'c.NPS_INFO',
  W_NPS_CLOSE = 'w.NPS_CLOSE',

  // Explore
  C_EXPLORE_PARTNERS_INFO = 'c.EXPLORE_PARTNERS_INFO',
  W_EXPLORE_LOAD_COMPLETED = 'w.EXPLORE_LOAD_COMPLETED',
  W_EXPLORE_CLICK_PE = 'w.EXPLORE_CLICK_PE',
  W_EXPLORE_CLICK_PARTNER_APP = 'w.EXPLORE_CLICK_PARTNER_APP',
  W_EXPLORE_CLICK_MORE_LUDP = 'w.EXPLORE_CLICK_MORE_LUDP'
}

export type APPLETINFOTYPE = {
  miniappid: string
  miniappurl: string
  width: string
  height: string
  resizieable: string
  minheight: string
  minwidth: string
  maxheight: string
  maxwidth: string
}

export type LOADEDCOMPELETED = {
  success: number
  message?: string
}

export type TOAUTHENTICATIONTYPE = {
  privatekey: string
  appid: string
  token: string | undefined
  aitoken?: string
  accessToken?: string
  avatar?: string
  text?: string
  historyList?: HISTORYLISTLENA[]
}

export type HISTORYLISTLENA = {
  id: number
  lena_content: string
  timespan: string
}

export type appCommunication = {
  action: string
  data: TOAUTHENTICATIONTYPE
}

export type ConfigType = APPLETINFOTYPE | LOADEDCOMPELETED | TOAUTHENTICATIONTYPE

export type ExplorePEData = {
  actionName: string
  actionType: string
  peName: string
  intentionOp: string
}
export enum ExploreLudpActionType {
  PKB = 'PKB',
  PC = 'PC',
  Cloud = 'Cloud'
}
