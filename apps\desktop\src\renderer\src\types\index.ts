export type ModelProvider = 'openai' | 'anthropic' | 'ollama'

export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: string
}

export interface Session {
  id: string
  name: string
  provider: ModelProvider
  model?: string // 具体的模型名称
  messages: Message[]
  createdAt: string
}

export interface ModelConfig {
  openaiApiKey?: string
  anthropicApiKey?: string
  ollamaBaseUrl?: string
}

export interface OllamaModelInfo {
  name: string
  size: number
  modified_at: string
}

export interface Setting {
  lang: string
  theme: string
}

export interface LLMModel {
  id: string
  modelName: string
  domainName: string
  apiPath: string
  modelNumber: string
  maxContextMessageCount: number
}

export interface Settings extends LLMModel {
  maxContextMessageMinCount: number
  maxContextMessageMaxCount: number
}

// 标识回答的状态 0为预置占位 1为准备中（ainow使用） 2为模型的回答
export enum ThreadMessageStatus {
  PENDING = 0,
  PREPAREING = 1,
  STREAMING = 2
}

export interface IThreadMessage {
  // accountId?: string; // 所属的某个账号
  threadId: string // 所属的某次对话
  prompt: string // 某个具体问题
  promptId: string // 某个具体问题的id，在消息发送时由聊天界面生成
  response: string
  updateTime: number // TODO 改为数据库生成的
  createTime?: number // TODO 改为数据库生成的
  done?: boolean
  status?: ThreadMessageStatus
}
