export default class ChatScrollTracker {
  container: HTMLElement
  parent: HTMLElement
  isAutoScrolling: boolean
  isUserActive: boolean
  rafId: unknown
  debounceTimer: unknown
  intersectionObserver: IntersectionObserver
  resizeObserver: ResizeObserver
  mutationObserver: MutationObserver

  public isActive: boolean = false

  constructor(container: HTMLElement | null) {
    const parentContainer = container?.parentElement

    if (!container || !parent) throw new Error('no dom found')

    this.container = container as HTMLElement
    this.parent = parentContainer!

    this.isAutoScrolling = false
    this.isUserActive = false
    this.rafId = null
    this.debounceTimer = null

    this.onParentScroll = this.onParentScroll.bind(this)
    this.adjust = this.adjust.bind(this)

    this.intersectionObserver = new IntersectionObserver(this.adjust, {
      root: this.parent,
      threshold: 1.0
    })

    this.resizeObserver = new ResizeObserver(() => {
      if (!this.isUserActive) this.requestAdjust()
    })

    this.mutationObserver = new MutationObserver(() => {
      if (!this.isUserActive) this.requestAdjust()
    })
  }

  start() {
    this.intersectionObserver.observe(this.container)
    this.resizeObserver.observe(this.container)
    this.resizeObserver.observe(this.parent)
    this.mutationObserver.observe(this.container, {
      childList: true,
      subtree: true
    })
    this.parent.addEventListener('scroll', this.onParentScroll)
    this.isActive = true
  }

  stop() {
    this.isActive = false
    cancelAnimationFrame(this.rafId as number)
    clearTimeout(this.debounceTimer as number)
    this.intersectionObserver.disconnect()
    this.resizeObserver.disconnect()
    this.mutationObserver.disconnect()
    this.parent.removeEventListener('scroll', this.onParentScroll)
  }

  adjust() {
    if (this.isUserActive) return

    const containerRect = this.container.getBoundingClientRect()
    const parentRect = this.parent.getBoundingClientRect()

    if (containerRect.bottom + 200 > parentRect.bottom) {
      this.isAutoScrolling = true
      this.parent.scrollTo({
        top:
          this.container.offsetTop + this.container.offsetHeight - this.parent.clientHeight + 200,
        behavior: 'smooth'
      })
      this.isAutoScrolling = false
    }
  }

  // 用户停止操作后恢复自动滚动
  onParentScroll() {
    if (this.isAutoScrolling) return
    this.isUserActive = true
    clearTimeout(this.debounceTimer as number)
    this.stop()
    this.debounceTimer = setTimeout(() => {
      this.isUserActive = false
      // this.requestAdjust()
    }, 1000)
  }

  requestAdjust() {
    if (this.rafId) return
    this.rafId = requestAnimationFrame(() => {
      this.adjust()
      this.rafId = null
    })
  }
}
