// chatService.ts
import { useLLMStore } from '../stores/llm'
import {
  userThreadMessageStore,
  ThreadMessageStatus,
  ThreadMessageUI
} from '../stores/threadMessage'
import {
  sessionType,
  CLOUD_MODEL,
  PROVIDER,
  IChatParams,
  IASKModel,
  CHAT_TYPE,
  ASK_TYPE,
  TYPE,
  ipcfunc
} from '@ainow/types/index'
import { WebApiService } from '@renderer/api/web'
import { RES_STREAM_EVENT } from './chatEvents'

const llmStore = useLLMStore()
const chatsStore = userThreadMessageStore()
const api = new WebApiService()
let lastMessageId = ''

// 发送云端消息
// 小窗时 sessionType为AINowCloud
const sendCloudMessage = async (
  messageId: string,
  callback: Function,
  dynamicParams: { data?: object; ipcfunc: string }
) => {
  // todo 待验证
  // 参数类似，暂时使用本地消息发送
  sendLocalMessage(messageId, callback, dynamicParams)
}
// 发送ainow事件交互消息
const sendAiNowActionMessage = async ({ dynamicParams, messageId, callback }) => {
  const chatParams: IChatParams = {
    accountId: llmStore.currentAccount?.id,
    threadId: llmStore.currentThread?.id,
    provider: llmStore.currentProvider,
    model: llmStore.currentModel,
    messageId
  }
  lastMessageId = messageId
  chatParams.ainowParams = {
    data: {
      data: {
        content: null,
        vantageType: ''
      },
      base64Image: null,
      type: null,
      language: 'EN',
      msg: null,
      channel: 'AIGCChat',
      lidToken: null,
      ainowToken: null,
      realm: null,

      ...dynamicParams.data
    },
    ipcfunc: dynamicParams.ipcfunc
  }
  sendMessage(chatParams, callback)
}
// 发送本地消息
// 小窗时 sessionType为AINowMini
const sendLocalMessage = async (
  messageId: string,
  callback: Function,
  dynamicParams?: { data?: object; ipcfunc: string }
) => {
  lastMessageId = messageId
  const chatParams: IChatParams = {
    accountId: llmStore.currentAccount?.id,
    threadId: llmStore.currentThread?.id,
    provider: llmStore.currentProvider,
    model: llmStore.currentModel,
    message: (dynamicParams?.data as { msg?: string }).msg || '',
    messageId
  }
  // 如果当前是ainow供应商 则需要追加传参
  if (llmStore.currentProvider === PROVIDER.AINOW) {
    // 大窗小窗 不同的聊天意图参数不同 todo
    chatParams.ainowParams = {
      data: {
        markingWords: null,
        fileList: [],
        type: '',
        roundContent: null,
        usingLVC: false,
        base64Image: null,
        action: null,
        cloud: false,
        regen: false,
        sessionId: null,
        allTypeList: null,
        sessionType: sessionType.AINowGeneral, //设置默认值
        historyMessageList: null,
        modelCode: null,
        translate: { dest: '', source: '' },
        lidToken: null,
        ainowToken: null,
        realm: null,
        ...dynamicParams!.data
      },
      ipcfunc: dynamicParams!.ipcfunc
    }
  }
  console.log('chatParams---local:', chatParams)
  sendMessage(chatParams, callback)
}

const appendQustion = (
  msg,
  messageId,
  additionalWords?,
  status: ThreadMessageStatus = ThreadMessageStatus.PENDING,
  response?
) => {
  // 发送后先把问题和空白答案展示到页面中
  const question: ThreadMessageUI = {
    role: 'user',
    threadId: llmStore.currentThread?.id,
    message: msg,
    additionalWords: additionalWords,
    promptId: 'qs:' + messageId,
    updateTime: Date.now()
  }
  console.log('-----appendQustion--question-', question)
  chatsStore.appendChat(question)
  const preAnswer: ThreadMessageUI = {
    role: 'assistant',
    provider: llmStore.currentProvider,
    model: llmStore.currentModel,
    status: status,
    threadId: llmStore.currentThread?.id,
    prompt: msg,
    promptId: messageId,
    response: response || '',
    updateTime: Date.now()
  }
  chatsStore.appendChat(preAnswer)
  // 发送消息
  console.log('---send message: messageId, ', messageId)
}

const chatCallback = (id: string, chunk: unknown) => {
  console.log('回复的answerid', id, lastMessageId)
  if (id !== lastMessageId) {
    console.warn('---chatCallback -id !== lastMessageId, return .', id, lastMessageId)
    return
  }
  // console.log('chatCallback', id, chunk)
  try {
    const data = typeof chunk === 'string' ? JSON.parse(chunk) : chunk
    if (data.type === 'abort') {
      lastMessageId = ''
      return
    }
    const record: ThreadMessageUI = {
      threadId: data.threadId,
      // prompt: data.textResponse || JSON.stringify(data.data), // todo 删除
      promptId: id,
      provider: data.provider,
      model: data.model,
      status: ThreadMessageStatus.STREAMING,
      response: data.textResponse || JSON.stringify(data.data), // ainow把整个返回传过去 datahere,
      updateTime: Date.now(),
      done: data.close || data.data.done // ainow的done是data.data.done
    }
    if (data.provider === 'ainow') {
      // ainow的回答不需要拼接
      chatsStore.appendChat(record, false)
    } else {
      chatsStore.appendChat(record)
    }
    // console.log('append chat', record)
    window.dispatchEvent(
      new CustomEvent(RES_STREAM_EVENT, {
        detail: record
      })
    )
  } catch (ex) {
    console.error(chunk, '-->', ex)
  }
}

const receiveMessageFromMinibar = ({ askType, additionalWords }) => {
  console.log('receiveMessageFromMinibar', askType, additionalWords)
  additionalWords =
    additionalWords?.Length >= 2000
      ? additionalWords?.Substring(0, 2000)?.ToString()
      : additionalWords
  const askModel: IASKModel = {
    msg: '',
    markingWords: {
      content: null
    },
    ChatText: null,
    // askType: ASK_TYPE,
    sessionType: sessionType.AINowMini,
    translate: null,
    type: null,
    chatType: CHAT_TYPE.CHAT
    // modelCode:string,
  }
  switch (askType) {
    case ASK_TYPE.EXPLAIN:
      askModel.msg = 'Explain the selected text:' + additionalWords
      askModel.sessionType = sessionType.AINowCloud
      askModel.modelCode = CLOUD_MODEL.GPT_40_MINI
      askModel.ChatText = 'Explain the selected text'
      break
    case ASK_TYPE.CLOUD_ASK:
      // 不直接发送，需输入框输入问题后，点击发送
      askModel.markingWords.content = additionalWords
      askModel.sessionType = sessionType.AINowCloud
      askModel.modelCode = CLOUD_MODEL.GPT_40_MINI
      break
    case ASK_TYPE.TRANSLATE:
      askModel.markingWords.content = additionalWords
      // 设置的翻译语言，默认英语
      askModel.translate = {
        dest: 'English',
        source: ''
      }
      askModel.type = TYPE.WORK_ASSISTANT_TRANSLATION
      break
    case ASK_TYPE.SUMMARIZE:
      askModel.markingWords.content = additionalWords
      askModel.type = TYPE.WORK_ASSISTANT_DOCUMENT_SUMMARY
      askModel.ChatText = 'Summarize the selected text'
      break
    case ASK_TYPE.POLISH:
      askModel.markingWords.content = additionalWords
      askModel.type = TYPE.WORK_ASSISTANT_DOCUMENT_POLISHING
      askModel.ChatText = 'Refine the selected text'
      break
    case ASK_TYPE.ASK:
      // todo 需手动输入
      askModel.markingWords.content = additionalWords
      askModel.type = TYPE.GENERAL_GENERATION
      askModel.ChatText = 'Get answer in local knowledge base'
      break
    case ASK_TYPE.SEARCH:
      askModel.markingWords.content = additionalWords
      askModel.type = TYPE.WORK_ASSISTANT_DOCUMENT_SEARCH
      askModel.ChatText = 'Search files in local knowledge base'
      break
    case ASK_TYPE.PCASSISTANT: //todo
      askModel.msg = additionalWords
      askModel.type = undefined
      askModel.ChatText = additionalWords
      break
    default:
      break
  }
  return askModel
}

/**
 * 对话框中数据，重新获取answer
 * @param question
 * @param answer
 * @param params
 */
const retryMessage = async (question: any, answer: any, params: any) => {
  console.log('retryMessage', question, answer, params)
  if (answer.provider == PROVIDER.AINOW && answer.description) {
    try {
      const obj = JSON.parse(answer.description)
      const type = obj.type
      const messageId = question?.promptId.split(':')[1]

      const askModel: IASKModel = {
        msg: null,
        markingWords: {
          content: null
        },
        translate: params.translate,
        type: obj.type,
        sessionType: obj.sessionType
      }

      switch (type) {
        case TYPE.WORK_ASSISTANT_TRANSLATION:
          askModel.msg = question?.additionalWords
          askModel.translate = params.translate
          break
        default:
          break
      }
      console.log('retryMessage', askModel)
      // todo 存储、区分大小窗类型，即sessionType
      sendLocalMessage(messageId, chatCallback, { data: askModel, ipcfunc: ipcfunc.AIGCChat })
    } catch (e) {}
  } else {
  }
}

const stop = (promptId?: string) => {
  if (__ELECTRON__) {
    window.api.abortChat(promptId)
  } else {
    api.abortChat(promptId)
  }
}
// 发送消息
const sendMessage = async (chatParams: IChatParams, callback: Function) => {
  if (__ELECTRON__) {
    window.api.streamChat(chatParams)
  } else {
    const reader = await api.streamChat(chatParams)
    while (true) {
      const { value, done } = await reader.read()
      if (done) break
      // dfai 等可能一次返回多个数据段
      const datas = value.replace(/^data:\s*/, '').split(/data:\s*/g)
      for (const dataStr of datas) {
        const v = dataStr
          .replace(/[\r\n]*$/, '')
          .replace(/(\\n)*$/, '\n')
          .replace(/[\n]*$/, '')
          .replace(/!\[(.*?)\]\((.*?)\s*(?:["'](.+?)["'])?\)/, '')
        try {
          const { id, chunk } = JSON.parse(v)
          callback(id, chunk)
        } catch (e) {
          console.error('local 解析事件数据失败:', e, dataStr)
        }
      }
    }
  }
}

export const chatService = {
  sendAiNowActionMessage,
  sendCloudMessage,
  sendLocalMessage,
  chatCallback,
  appendQustion,
  receiveMessageFromMinibar,
  retryMessage,
  stop
}
