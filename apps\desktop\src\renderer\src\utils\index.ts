import { WebApiService } from '@renderer/api/web'
import { Prisma } from '@prisma/client'
import sharedFacade, { type ISharedFacade } from '@ainow/shared'

const api = new WebApiService()

// 简化持久化模型的操作
export function getDBModelProxy<T extends Prisma.ModelName>(modelName: Prisma.ModelName) {
  return new Proxy(
    {},
    {
      get(_, action: string) {
        return async function (...args) {
          const response = __ELECTRON__
            ? // @see apps\desktop\src\main\utils\db.ts
              await window.api.persistenceAction(modelName, action, ...args)
            : // @see apps\backend\src\db\index.ts
              await api.persistenceAction(modelName, action, args)
          return response
        }
      }
    }
  ) as ReturnType<ISharedFacade['DBModels'][`get${T}Model`]>
}

// 省略对象中的某些键
export function omit(obj, keysToOmit) {
  const result = { ...obj }
  keysToOmit.forEach((key) => {
    if (key in result) {
      delete result[key]
    }
  })
  return result
}

// 节流函数
export function throttle<T>(func: (arg: T) => void, delay = 500) {
  let lastCallTime = 0

  return function (arg: T) {
    const currentTime = Date.now()
    if (currentTime - lastCallTime >= delay) {
      func(arg)
      lastCallTime = currentTime
    }
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => void>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout | null = null

  const debounced: T = ((...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }) as T

  return debounced
}
/**
 *
 * @returns 返回base64 编码的随机数
 */
export async function generateCodeVerifier(): Promise<string> {
  const array = new Uint8Array(32) // 32 字节随机数
  window.crypto.getRandomValues(array) // 使用加密安全的随机数生成器
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-') // URL-safe 字符替换
    .replace(/\//g, '_')
    .replace(/=+$/, '') // 移除末尾的填充等号
}

/**
 * 生成基于 SHA-256 的 code_challenge
 * @param verifier 通过 generateCodeVerifier() 生成的验证码
 * @returns 返回 43 字符的 URL-safe 字符串
 */
export async function generateCodeChallenge(verifier: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(verifier)
  const digest = await window.crypto.subtle.digest('SHA-256', data)
  return btoa(String.fromCharCode(...new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '')
}

export async function getAccessToken(tokenUrl: string): Promise<object | null> {
  return await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  })
}

export const ProtocolSchemeRegex = /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\//
