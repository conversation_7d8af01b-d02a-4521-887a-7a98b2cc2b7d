import markdownIt from 'markdown-it'
import copyPlugin from 'markdown-it-code-copy'
// @ts-ignore
import markdownItKatex from 'markdown-it-katex'
import hljs from 'highlight.js'
import he from 'he'
import { v4 as uuidv4 } from 'uuid'
import '@renderer/assets/katex.min.css'

const markdown = markdownIt({
  breaks: true, // 启用软换行支持
  html: false,
  typographer: true,
  highlight: function (code, lang) {
    const theme = window.localStorage.getItem('theme') === 'light' ? 'github' : 'github-dark'

    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<div class="code-block ${theme}">
            <div class="code-header">
              <span class="language">${lang}</span>
              <button class="copy-button" data-code-snippet data-code="code-${uuidv4()}">
                <svg class="icon"><use href="#copy-icon"></use></svg>
                <span>Copy code</span>
              </button>
            </div>
            <pre class="hljs">${hljs.highlight(code, { language: lang, ignoreIllegals: true }).value}</pre>
          </div>`
      } catch (__) {}
    }

    return `<div class="code-block ${theme}">
        <div class="code-header">
          <button class="copy-button" data-code-snippet data-code="code-${uuidv4()}">
            <svg class="icon"><use href="#copy-icon"></use></svg>
            <span>Copy code</span>
          </button>
        </div>
        <pre class="hljs">${he.encode(code)}</pre>
      </div>`
  }
})

markdown.renderer.rules.strong_open = () => '<strong>'
markdown.renderer.rules.strong_close = () => '</strong>'

markdown.renderer.rules.image = function (tokens, idx) {
  const token = tokens[idx]
  const srcIndex = token.attrIndex('src')
  const src = token.attrs[srcIndex][1]
  const alt = token.content || ''

  return `<div class="image-container"><img src="${src}" alt="${alt}" class="responsive-image"/></div>`
}

markdown.use(copyPlugin, {
  buttonStyle:
    'top:20px;right:16px;width:80px;height:20px;opacity:0.01;position: absolute;cursor:pointer;outline:none'
})
markdown.use(markdownItKatex, { throwOnError: false, errorColor: ' #cc0000' })

export function renderMarkdown(text = '') {
  return markdown.render(text)
}
