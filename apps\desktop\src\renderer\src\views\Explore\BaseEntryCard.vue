<template>
  <div class="entry-card">
    <div v-if="title" class="entry-card-header">
      <h3 class="entry-card-title">{{ title }}</h3>
      <p v-if="description" class="entry-card-desc">{{ description }}</p>
    </div>

    <div class="entry-card-content">
      <PEEntry
        v-for="entry in visibleEntries"
        :key="entry.icon + entry.description"
        v-bind="entry"
        :style="{ background: backgroundColor }"
        class="entry-item"
        @click="handleEntryClick(entry)"
      />
    </div>

    <button
      v-if="entries.length > visibleCount"
      class="more-button"
      @click="handleMoreClick"
      aria-label="More prompt template"
    >
      <SvgIcon name="ExploreMorePE" :size="'11'" />
      <span>More</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { EntryCardProps, EntryItem } from '@renderer/types/Explore'
import PEEntry from './PEEntry.vue'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'

const props = withDefaults(defineProps<EntryCardProps>(), {
  backgroundColor: 'transparent'
})

const emit = defineEmits<{
  (e: 'select', entry: EntryItem): void
  (e: 'clickMore'): void
}>()

const currentIndex = ref(0)
const visibleEntries = computed(() =>
  props.entries.slice(currentIndex.value, currentIndex.value + props.visibleCount)
)

const handleMoreClick = () => {
  currentIndex.value = (currentIndex.value + props.visibleCount) % props.entries.length
  emit('clickMore')
}

const handleEntryClick = (entry: EntryItem) => {
  emit('select', entry)
}
</script>

<style lang="less" scoped>
@import './variables.less';

.entry-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  background: #ffffff;

  &-header {
    margin-bottom: 20px;
    flex: none;
  }

  &-title {
    font-family: Lato;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0%;
    color: #18181b;
  }

  &-desc {
    margin-top: 4px;
    font-family: Lato;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0%;
    color: #52525b;
    width: 100%;
    word-wrap: break-word;
    white-space: normal;
  }

  &-content {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: space-between;
  }
}

.more-button {
  flex: none;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 4px 0 17px 0;

  padding: 4px 8px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  color: #52525b;
  line-height: 16px;
  border: 1px solid transparent;
  border-radius: 6px;

  &:hover {
    color: @primary-color;
  }
}
</style>
