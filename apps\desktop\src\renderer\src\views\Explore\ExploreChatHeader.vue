<template>
  <header class="explore-chat-header">
    <span class="explore-chat-header-icon" :style="{ backgroundImage: `url(${iconUrl})` }"></span>
    <div class="explore-chat-header-content">
      <SvgIcon class="explore-chat-header-content-left" :name="icon" :size="'88'"></SvgIcon>
      <div class="explore-chat-header-content-right">
        <h2 class="explore-chat-header-content-title">{{ title }}</h2>
        <p class="explore-chat-header-content-desc">{{ desc }}</p>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import SvgIcon from '@renderer/components/SvgIcon'
import { computed } from 'vue'

interface Props {
  icon: string
  title: string
  desc: string
}

const props = defineProps<Props>()
const iconUrl = computed(() => {
  return `/src/assets/icons/${props.icon}Id.svg`
})
</script>

<style lang="less" scoped>
.explore-chat-header {
  min-height: 149px;
  width: 100%;
  border-bottom: 1px solid #e4e4e7;
  position: relative;
  overflow: hidden;

  &-icon {
    background-repeat: no-repeat;
    background-position: top;
    background-size: contain;
    border-top-right-radius: 8px;
    width: 131px;
    height: 39px;
    position: absolute;
    right: 0;
    top: 0;
  }

  &-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    margin: 30px 0 17px 0;

    &-left {
      flex: none;
    }

    &-right {
      margin-left: 16px;
    }

    &-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 28px;
    }

    &-desc {
      margin-top: 8px;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
</style>
