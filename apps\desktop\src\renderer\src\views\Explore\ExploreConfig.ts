const pcEntries = [
  {
    icon: '&#xe6a4;',
    description: 'Turn on Eye Care Mode',
    promptText: '',
    hintText: '',
    actionType: 'Send',
    intentionOp: 'DEVICE_VANTAGE_DISPLAY_EYE_CARE_MODE_SET_ENABLE'
  },
  {
    icon: '&#xe6c1;',
    description: 'Turn on Night Mode',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6ab;',
    description: 'Turn on Adaptive Power Mode',
    promptText: '',
    hintText: '',
    actionType: 'Send',
    intentionOp: 'DEVICE_VANTAGE_POWER_POWER_MODES_SET_INTELLIGENT'
  },
  {
    icon: '&#xe6a6;',
    description: 'Turn on Battery Saver Mode',
    promptText: '',
    hintText: '',
    actionType: 'Send',
    intentionOp: 'DEVICE_VANTAGE_POWER_POWER_MODES_SET_ENERGY_SAVING'
  },
  {
    icon: '&#xe6a9;',
    description: 'Turn on Performance Mode',
    promptText: '',
    hintText: '',
    actionType: 'Send',
    intentionOp: 'DEVICE_VANTAGE_POWER_POWER_MODES_SET_BEAST'
  },
  {
    icon: '&#xe6aa;',
    description: 'Turn on Dolby Mode',
    promptText: '',
    hintText: '',
    actionType: 'Send',
    intentionOp: 'DEVICE_VANTAGE_SOUND_DOLBY_ATMOS_GET_NULL'
  },
  {
    icon: '&#xe6ad;',
    description: 'Set up a background',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6b9;',
    description: "What are my current PC's configuration?",
    promptText: '',
    hintText: '',
    actionType: 'Send',
    intentionOp: 'DEVICE_VANTAGE_SYSTEM_GET_HARDWARE_INFORMATION'
  },
  {
    icon: '&#xe6be;',
    description: 'When does the warranty expire?',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6d4;',
    description: 'Turn on do not disturb',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6d3;',
    description: 'Show me all the installed apps',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6d0;',
    description: 'How to set multi-tasking',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6c7;',
    description: "My mic doesn't work",
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6d2;',
    description: 'What is remote desktop',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6cf;',
    description: 'Change a warm system theme',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6d1;',
    description: 'Set my layout of start menu',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  }
]
const pkbEntries = [
  {
    icon: '&#xe6ac;',
    description: 'Find the documents that contain',
    promptText: 'mention file name, author, type, key words…',
    hintText: '',
    actionType: 'Input'
  },
  {
    icon: '&#xe6b0;',
    description: 'Search for data about',
    promptText: 'topic',
    hintText: 'in the knowledge base',
    actionType: 'Input'
  },
  {
    icon: '&#xe6ac;',
    description: 'Summarize the document',
    promptText: '',
    hintText: '',
    actionType: 'Input'
  },
  {
    icon: '&#xe6ac;',
    description: 'Find the images that contain',
    promptText: 'mention file name or description',
    hintText: '',
    actionType: 'Input'
  }
]
const cloudEntries = [
  {
    icon: '&#xe6a8;',
    description: 'How to build effective professional relationships with colleagues?',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6a8;',
    description: 'What are the challenges of working in a virtual team?',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6a8;',
    description: 'What are the most effective ways to ask for a raise or promotion?',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6a8;',
    description: 'Generate a market analysis report of Gen AI',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6a8;',
    description: 'Generate a Twitter about',
    promptText: 'input the theme',
    hintText: '',
    actionType: 'Send'
  },
  {
    icon: '&#xe6a8;',
    description: 'Help me generate a PPT outline on "The development of AI"',
    promptText: '',
    hintText: '',
    actionType: 'Send'
  }
]

export const pkbEntryInfo = {
  title: 'Knowledge Assistant',
  description:
    'It helps you boost your producity at work or for studying, all based on your personal, and private Knowledge Base.',
  entries: pkbEntries,
  visibleCount: 2
}
export const pcEntryInfo = {
  title: 'PC Assistant',
  description: 'It can help you easily understand, troubleshoot and get the most out of your PC.',
  entries: pcEntries,
  visibleCount: 2
}
export const cloudEntryInfo = {
  entries: cloudEntries,
  visibleCount: 3
}
export const partnerCardInfo = [
  {
    icon: 'ExploreLSM',
    title: 'Lenovo Smart Meeting',
    description:
      'An application for camera effects setting, especially for video conferencing scenarios, can intelligently set personal image and background replacement and other related functions.',
    status: ''
  }
]
export const cloudChatHeaderInfo = {
  icon: 'ExploreCloud',
  title: 'Cloud Chat',
  description:
    'Connecting to the cloud, I can support you with your curiosity about the whole world.'
}
export const localChatHeaderInfo = {
  icon: 'ExploreLocal',
  title: 'Local Chat',
  description:
    'Lenovo AI Agent is your personal offline intelligent assistant. I can help with inspiration, writing, summarizing, quick settings in PC, and more. I ensure data security, offering the safest AI experience.'
}
export const exploreHeaderInfo = {
  title: 'Explore what Lenovo AI Agent can do for you',
  description: 'Discover new ideas, practical features or actions that Lenovo AI Agent can do.'
}
export const partnerHeaderInfo = {
  title: 'More AI Partners are coming soon...',
  description:
    'Explore more AI applications and services to expand your horizons and enhance your AI experience in Lenovo AI Agent.'
}
