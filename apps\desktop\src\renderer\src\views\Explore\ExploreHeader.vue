<template>
  <header class="explore-header">
    <h1 class="explore-header-title">{{ title }}</h1>
    <p class="explore-header-desc">{{ description }}</p>
  </header>
</template>

<script setup lang="ts">
defineProps<{
  title: string
  description: string
}>()
</script>

<style lang="less" scoped>
@import './variables.less';
.explore-header {
  width: 100%;
  height: 184px;
  background: linear-gradient(
    270.94deg,
    #baa2ff 0.81%,
    #5ba6fd 39.76%,
    #5197ff 68.44%,
    #5c59ff 111.79%
  );
  font-family: Lato;
  overflow: hidden;
  color: #ffffff;

  &-title {
    margin-top: 50px;
    margin-left: 35px;
    font-weight: 800;
    font-size: 24px;
    line-height: 34px;
    letter-spacing: 0%;
  }

  &-desc {
    margin-top: 13px;
    margin-left: 35px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
  }
}
</style>
