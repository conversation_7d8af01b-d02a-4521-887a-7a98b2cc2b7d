<template>
  <section class="explore-partners">
    <header class="explore-partners-header">
      <h2>{{ headerInfo.title }}</h2>
      <p>{{ headerInfo.description }}</p>
    </header>
    <div class="explore-partners-container">
      <PartnerCard
        v-for="(partner, index) in partners"
        :key="index"
        :icon="partner.icon"
        :title="partner.title"
        :description="partner.description"
        :status="partner.status"
        @click="() => $emit('partnerClick', partner)"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
import PartnerCard from './PartnerCard.vue'
import { type PartnerCardProps } from '@renderer/types/Explore'

defineProps<{
  headerInfo: {
    title: string
    description: string
  }
  partners: PartnerCardProps[]
}>()

defineEmits<{
  (e: 'partnerClick', partner: PartnerCardProps): void
}>()
</script>

<style lang="less" scoped>
.explore-partners {
  margin: 0 35px 28px 35px;
  border-radius: 5px;
  border: 1.5px solid transparent;
  background: #ffffffbf;
  backdrop-filter: blur(6.19041633605957px);
  overflow: hidden;

  &-header {
    margin-top: 23px;
    border-bottom: 1px solid #e4e4e7;
    padding-left: 27px;

    h2 {
      font-family: Lato;
      font-weight: 700;
      font-size: 20px;
      line-height: 28px;
      letter-spacing: 0%;
      color: #18181b;
    }

    p {
      font-family: Lato;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0%;
      color: #52525b;
      margin: 8px 0;
    }
  }

  &-container {
    display: flex;
    flex-direction: column;
    align-items: space-between;
  }
}
</style>
