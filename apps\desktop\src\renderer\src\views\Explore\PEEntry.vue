<template>
  <button class="pe-entry" @click="$emit('click')" :aria-label="ariaLabel">
    <div class="pe-entry-content">
      <span class="iconfont-wrapper" aria-hidden="true">
        <span class="iconfont" v-html="icon"></span>
      </span>
      <div class="pe-entry-text">
        <span>{{ description }}</span>
        <template v-if="promptText">
          <span> [</span>
          <span class="text-primary">{{ promptText }}</span>
          <span>] </span>
        </template>
        <span v-if="hintText">{{ hintText }}</span>
      </div>
    </div>
  </button>
</template>

<script setup lang="ts">
import { type EntryItem } from '@renderer/types/Explore'
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps<EntryItem>()
defineEmits<{
  (e: 'click'): void
}>()
const ariaLabel = computed(() => {
  return props.promptText
    ? `${props.description}[${props.promptText}]${props.hintText}`
    : `${props.description}${props.hintText}`
})
</script>

<style lang="less" scoped>
@import './variables.less';

.pe-entry {
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 68px;
  cursor: pointer;
  border-radius: 6px;
  border: 1px solid transparent;

  &:hover {
    outline: 2px solid var(--primary-focus-color);
  }

  &-content {
    margin: 14px 10px;
    display: flex;
    align-items: flex-start;
    gap: 8px;

    .iconfont-wrapper {
      flex: none;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      border-radius: 10px;
      background-image: linear-gradient(141.42deg, #706dff 0%, #50abff 103.86%);
    }

    .iconfont {
      font-family: 'iconfont';
      font-size: 10px;
      color: #ffffff;
    }
  }

  &-text {
    font-size: 14px;
    line-height: 18px;
    color: @text-primary;
    font-weight: 400;
    text-align: left;

    .text-primary {
      color: @primary-color;
    }
  }
}
</style>
