<template>
  <div class="partner-card">
    <SvgIcon :name="icon" :size="'52'" />
    <div class="partner-card-text">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
    </div>
    <button
      :aria-label="`${status}, Button, Click to ${status === 'Get' ? 'download' : 'Open'} ${title}`"
      v-if="status"
      class="partner-card-button"
      @click="$emit('click')"
    >
      {{ status }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { type PartnerCardProps } from '@renderer/types/Explore'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'

defineProps<PartnerCardProps>()
defineEmits<{
  (e: 'click'): PartnerCardProps
}>()
</script>

<style lang="less" scoped>
@import './variables.less';

.partner-card {
  display: flex;
  align-items: center;
  padding: 20px 30px;

  &-text {
    margin-left: 20px;
    flex: 1;

    h3 {
      margin-bottom: 4px;
      font-family: Lato;
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0%;
      color: #18181b;
    }

    p {
      max-width: 731px;
      font-family: Lato;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0%;
      color: #52525b;
      word-wrap: break-word;
      white-space: normal;
    }
  }

  &-button {
    margin-left: 16px;
    padding: 4px 8px;
    border: 1px solid transparent;
    border-radius: 4px;
    background: #4663ff;
    color: #ffffff;
    cursor: pointer;
    font-family: Lato;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: 0%;
    text-align: center;
  }
}
</style>
