<template>
  <div class="explore">
    <ExploreHeader :title="exploreHeaderInfo.title" :description="exploreHeaderInfo.description" />
    <main class="explore-main">
      <article class="local-chat">
        <ExploreChatHeader
          :icon="localChatHeaderInfo.icon"
          :title="localChatHeaderInfo.title"
          :desc="localChatHeaderInfo.description"
        >
        </ExploreChatHeader>
        <div class="local-chat-entry-container">
          <BaseEntryCard
            :title="pkbEntryInfo.title"
            :description="pkbEntryInfo.description"
            :entries="pkbEntryInfo.entries"
            :visibleCount="pkbEntryInfo.visibleCount"
            backgroundColor="linear-gradient(90deg, #E6EAFF 0%, #ECF4FF 100%)"
          />

          <BaseEntryCard
            :title="pcEntryInfo.title"
            :description="pcEntryInfo.description"
            :entries="pcEntryInfo.entries"
            :visibleCount="2"
            backgroundColor="linear-gradient(90deg, #E6F2FF 0%, #EBF8FE 100%)"
          />
        </div>
      </article>
      <section class="cloud-chat">
        <ExploreChatHeader
          :icon="cloudChatHeaderInfo.icon"
          :title="cloudChatHeaderInfo.title"
          :desc="cloudChatHeaderInfo.description"
        >
        </ExploreChatHeader>
        <div class="cloud-chat-entry-container">
          <BaseEntryCard
            :entries="cloudEntryInfo.entries"
            :visibleCount="cloudEntryInfo.visibleCount"
            backgroundColor="linear-gradient(272.64deg, #F2F3FF 8.68%, #EBE9FF 147.1%)"
          />
        </div>
      </section>
    </main>
    <ExplorePartners
      v-if="isConsumerMachine"
      :headerInfo="partnerHeaderInfo"
      :partners="partnersInfo"
    />
  </div>
</template>

<script setup lang="ts">
import ExploreHeader from './ExploreHeader.vue'
import ExploreChatHeader from './ExploreChatHeader.vue'
import BaseEntryCard from './BaseEntryCard.vue'
import ExplorePartners from './ExplorePartners.vue'

import { type EntryItem, FeatureActionNameType, PartnerCardProps } from '@renderer/types/Explore'
import {
  pkbEntryInfo,
  pcEntryInfo,
  cloudEntryInfo,
  partnerCardInfo,
  cloudChatHeaderInfo,
  localChatHeaderInfo,
  exploreHeaderInfo,
  partnerHeaderInfo
} from './ExploreConfig'
import { ExploreLudpActionType, ShellExchangeFunctionType } from '@renderer/types/ShellExchange'
// import {
//   getInfo,
//   sendWebClickPE,
//   sendWebLoadCompleted,
//   sendWebClickMoreLUDP,
//   sendWebClickPartnerApp,
// } from "@/hooks/ShellExchange";
import { onMounted, ref } from 'vue'
const isConsumerMachine = ref(false)
const partnersInfo = ref(partnerCardInfo)
// getInfo(data => {
//   const {
//     action,
//     data: clientData
//   } = data;

//   if (action === ShellExchangeFunctionType.C_EXPLORE_PARTNERS_INFO) {
//     console.log('data', clientData);
//     const {
//       isConsumer,
//       partners
//     } = clientData;
//     isConsumerMachine.value = Boolean(isConsumer);
//     partnersInfo.value.forEach((item) => {
//       const matchedPartner = partners.find(
//         (partner: {
//           appName: string; appStatus: string
//         }) => partner.appName === item.title
//       );
//       if (matchedPartner) {
//         item.status = matchedPartner.appStatus;
//       }
//     });

//   }
// });
// onMounted(() => {
//   sendWebLoadCompleted(ShellExchangeFunctionType.W_EXPLORE_LOAD_COMPLETED);
// })

// const handleClickAPP = (partner: PartnerCardProps) => {
//   sendWebClickPartnerApp({
//     appName: partner.title,
//     appStatus: partner.status
//   });
// }
// const handleBaseSelect = (actionNameType: FeatureActionNameType, entry: EntryItem) => {
//   const data = {
//     actionName: actionNameType,
//     actionType: entry.actionType,
//     peName: entry.promptText ? `${entry.description}[${entry.promptText}]${entry.hintText}` : `${entry.description}${entry.hintText}`,
//     intentionOp: entry.intentionOp || '',
//   };
//   sendWebClickPE(data);
// }
// const handleBaseMore = (type: ExploreLudpActionType) => {
//   sendWebClickMoreLUDP(type);
// }
</script>

<style lang="less" scoped>
.explore {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  overflow: auto;
  color: #52525b;
}

.explore-main {
  margin: -40px 35px 25px 35px;
  display: flex;
  align-items: space-between;
  gap: 19px;

  .local-chat {
    flex: 1.8;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px #00000014;
    border: 1px solid transparent;

    .explore-header {
      flex: none;
    }

    &-entry-container {
      flex: 1;
      display: flex;
      gap: 20px;
      margin: 30px 22px 0 22px;
    }
  }

  .cloud-chat {
    flex: 1;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px #00000014;
    border: 1px solid transparent;

    .explore-header {
      flex: none;
    }
    &-entry-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin: 30px 22px 0 20px;
    }
  }
}
@media screen and (max-width: 720px) {
  .explore-main {
    flex-direction: column;
    gap: 16px;

    .local-chat,
    .cloud-chat {
      width: 100%;
      min-width: 0;
    }
  }
}
</style>
