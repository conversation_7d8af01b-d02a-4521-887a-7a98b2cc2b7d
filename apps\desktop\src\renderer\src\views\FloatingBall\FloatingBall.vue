<template>
  <div class="floating-ball">
    <!-- ICON部分 -->
    <div
      ref="floatingView"
      class="floating-ball_icon-wrapper"
      @contextmenu.prevent="onRightClick"
      @mousedown="onMouseDown"
    >
      <SvgIcon name="file-ainow" class="floating-ball_icon-wrapper_icon" size="32px"></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from '@renderer/components/SvgIcon'
import { electronHooks } from '../../electron'
import { ref, onMounted, nextTick } from 'vue'
import { IpcRendererEvent } from 'electron'

const electronApi = {
  ...electronHooks(),
  sendRightClick: () => {
    // Implement the sendRightClick method here
    console.log('Right click sent')
  },
  openSecondaryWindow: () => {
    // Implement the openSecondaryWindow method here
    console.log('Secondary window opened')
  },
  windowMoveDrag: (isDragging: boolean) => {
    // Implement the windowMoveDrag method here
    console.log(`Window move drag: ${isDragging}`)
  }
}
const floatingView = ref<HTMLElement>()
const isShowSearchBar = ref(false) // 控制搜索栏的显示状态

// 状态变量
let isDragging = false // 标识是否正在拖动
let mouseDown = false // 标识鼠标是否按下
const clickThreshold = 5 // 移动阈值，单位：px
let startX = 0
let startY = 0

// 右键菜单事件
const onRightClick = () => {
  electronApi?.sendRightClick()
}

// 单击事件：打开新窗口
const onClickHandler = () => {
  console.log('单击事件触发：打开新窗口', isShowSearchBar.value)
  if (isShowSearchBar.value) {
    isShowSearchBar.value = false
    return
  }
  isShowSearchBar.value = true
  electronApi?.openSecondaryWindow()
}

// 鼠标按下事件：开始拖动或准备点击
const onMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return // 只处理左键

  // 初始化状态
  mouseDown = true
  isDragging = false
  startX = e.clientX
  startY = e.clientY

  // 开始拖动窗口
  electronApi?.windowMoveDrag(true)

  // 监听鼠标移动和松开事件
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 鼠标移动事件：检测是否发生拖动
const onMouseMove = (e: MouseEvent) => {
  if (!mouseDown) return

  const deltaX = Math.abs(e.clientX - startX)
  const deltaY = Math.abs(e.clientY - startY)

  // 判断是否超出拖动阈值
  if (deltaX > clickThreshold || deltaY > clickThreshold) {
    isDragging = true
  }
}

// 右键菜单中隐藏悬浮球
const hideFloatingBall = () => {
  console.log('隐藏悬浮球')
  electronApi?.hideWindow() // 调用隐藏窗口的 API
}

// 打开 AI Now
const openAinow = () => {
  electronApi?.openAinow() // 调用打开 AI Now 的 API
}

// 鼠标释放事件：停止拖动并判断是否为单击
const onMouseUp = (e: MouseEvent) => {
  if (e.button !== 0) return // 只处理左键

  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
  // 判断是否为单击
  if (!isDragging) {
    onClickHandler()
  }
  // 停止拖动窗口
  electronApi?.windowMoveDrag(false)

  // 重置状态
  mouseDown = false
  isDragging = false
}

// 初始化事件监听
onMounted(async () => {
  await nextTick()
  electronApi?.selectMuenItem((args: IpcRendererEvent, param: string) => {
    console.log('悬浮球右键菜单点击：', args, param)
    if (param === 'Hide Widget') {
      hideFloatingBall() // 隐藏悬浮球
    } else if (param === 'Open AI Now') {
      openAinow() // 打开 AI Now
    }
  })
})
</script>

<style scoped lang="less">
.floating-ball {
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  position: relative;
  transform: none;
  margin-top: 5px;

  &_icon-wrapper {
    width: 50px;
    height: 50px;
    pointer-events: auto;
    // display: grid;
    place-items: center;
    border-radius: 50%;
    background: #ffffff;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 4px 12px 2px rgba(0, 0, 0, 0.2);

    &_icon {
      user-select: none;
    }
  }
}
</style>
