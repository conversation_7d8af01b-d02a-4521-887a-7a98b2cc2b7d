<template>
  <div class="kb-file-container">
    <header>
      <p class="title">{{ props.kbinfo?.title }}</p>
      <p class="file-count">322 files <SwapOutlined style="transform: rotate(90deg)" /></p>
    </header>
    <a-flex wrap="wrap" gap="12" class="kb-file-container_tool">
      <a-button type="primary" @click="handleUpload">+Import</a-button>
      <a-input v-model:value="filter" placeholder="Basic usage" style="width: 168px">
        <template #suffix>
          <a-tooltip title="Extra information">
            <SearchOutlined style="color: rgba(0, 0, 0, 0.45)" />
          </a-tooltip>
        </template>
      </a-input>
      <DeleteOutlined style="color: rgba(0, 0, 0, 0.45)" v-if="hasSelected" />
      <RedoOutlined style="color: rgba(0, 0, 0, 0.45)" />
    </a-flex>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'lastModifyTime'">
          <div>{{ dayjs(record.lastModifyTime).format('YYYY-MM-DD  HH:mm:ss') }}</div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, watch } from 'vue'
// import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
//   import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  kbinfo: Object
})

const defaultParams = {
  pageIndex: 1,
  pageSize: 20,
  filter: '',
  sorter: {
    field: 'lastModifyTime',
    order: 'descend'
  }
}

const filter = ref('')
interface dataItem {
  name: string
  owner: string
  tag: string
  lastModifyTime: Date
}
const dataSource = ref<dataItem[]>([])
const notPreviewFiles = ['docx']
const columns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: 'Owner',
    dataIndex: 'owner',
    key: 'owner'
  },
  {
    title: 'Tag',
    dataIndex: 'tag',
    key: 'tag',
    sorter: {
      compare: (a: { lastModifyTime: Date }, b: { lastModifyTime: Date }) => {
        return dayjs(a.lastModifyTime).valueOf() - dayjs(b.lastModifyTime).valueOf()
      }
    }
  },
  {
    title: 'Modified',
    dataIndex: 'lastModifyTime',
    key: 'lastModifyTime'
  }
]
type Key = string | number

const state = reactive<{
  selectedRowKeys: Key[]
  loading: boolean
}>({
  selectedRowKeys: [], // Check here to configure the default column
  loading: false
})

const onSelectChange = (selectedRowKeys: Key[]) => {
  state.selectedRowKeys = selectedRowKeys
}

const hasSelected = computed(() => state.selectedRowKeys.length > 0)

const handleKBChange = async (kbinfo: Object) => {
  // 清空filter，重置参数
  filter.value = ''
  // @ts-ignore
  defaultParams = filter.value
  // getDataSource()
}

const getFileList = async () => {
  state.loading = true
}

watch(() => props.kbinfo, handleKBChange, { immediate: true, deep: true })
const handleUpload = () => {
  console.log('handleUpload')
}
onMounted(() => {
  dataSource.value = [
    {
      name: 'test.txt',
      owner: 'admin',
      tag: 'test',
      lastModifyTime: new Date()
    },
    {
      name: 'test.txt',
      owner: 'admin',
      tag: 'test',
      lastModifyTime: new Date()
    }
  ]
  // getDataSource()
})
</script>
<style lang="less" scoped>
.kb-file-container {
  header {
    height: 38px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #000000;
    }
    .file-count {
      font-size: 14px;
      line-height: 22px;
      color: #6441ab;
    }
  }
  &_tool {
    margin-top: 8px;
    margin-bottom: 10px;
    height: 32px;
  }
}
</style>
