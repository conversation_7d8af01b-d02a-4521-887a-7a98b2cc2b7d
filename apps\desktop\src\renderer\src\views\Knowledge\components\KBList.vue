<template class="kb-list">
  <p class="kb-list_title">Knowledge Base</p>
  <ul class="kb-list_ul" v-for="item in menus" :key="item.type">
    <li class="kb-list_ul_title">{{ item.type + '(' + item.data.length + ')' }}</li>
    <li
      v-for="menu in item.data"
      :key="menu.path"
      class="kb-list_ul_item"
      :class="{ selected: menu.key == activeItem?.key }"
      @click="handleKBClick(menu)"
    >
      {{ menu.title }}
      <LoadingOutlined class="loading-icon" v-if="menu.loading" />
      <a-popover trigger="click" placement="rightBottom" overlayClassName="kb-detail">
        <template #title>
          <p>{{ menu.title }}</p>
        </template>
        <template #content>
          <p>Content</p>
          <p>Content</p>
        </template>
        <MoreOutlined class="more-icon" @click.stop="handleMoreClick(menu)" />
      </a-popover>
    </li>
  </ul>
  <!-- <a-menu
          id="dddddd"
          v-model:selectedKeys="selectedKeys"
          mode="inline"
          style="border: none;margin-top: 4px;"
          :items="items"
          @click="handleKBClick"
        ></a-menu> -->
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'

// const selectedKeys = ref<string[]>(['1']);
const emit = defineEmits(['selectChange'])
const activeItem = ref<TMenuItem>()

type TMenuItem = {
  key: string
  type: string | 'share' | 'private'
  title: string
  path?: string
  description?: string
  owner?: string
  file?: string
  loading?: boolean
}
type KBItem = {
  type: string
  data: TMenuItem[]
}

const menusPrivate: TMenuItem[] = reactive([
  { key: '11', type: 'item', name: 'home', title: 'Private KB1', path: '/home', loading: true }
])
const menusShare: TMenuItem[] = reactive([
  { key: '1', type: 'item', name: 'home', title: 'Home', path: '/home' },
  { key: '2', type: 'item', name: 'dashboard', title: 'Dashboard', path: '/dashboard' },
  {
    key: '3',
    type: 'item',
    name: 'knowledge',
    title: 'Knowledge Base',
    path: '/knowledge',
    loading: true
  },
  { key: '4', type: 'item', name: 'activity', title: 'Activity', path: '/activity' }
])
const menus: KBItem[] = reactive([
  { type: 'private', data: menusPrivate },
  { type: 'share', data: menusShare }
])
//   function getItem(
//     label: VueElement | string,
//     key: string,
//     icon?: any,
//     children?: ItemType[],
//     type?: 'group',
//   ): ItemType {
//     return {
//       key,
//       icon,
//       children,
//       label,
//       type,
//     } as ItemType;
//   }
//   const items: ItemType[] = reactive([
//    { type: 'divider' },
//     getItem('Group1', 'grp', null, [getItem('Option 13', '13'), getItem('Option 14', '14')], 'group'),
//     { type: 'divider' },
//     getItem('Group2', 'grp', null, [getItem('Option 13', '13'), getItem('Option 14', '14')], 'group'),
//   ]);

const handleKBClick = (e: any) => {
  console.log('handleKBClick--', e)
  activeItem.value = e
  emit('selectChange', e)
}
const handleMoreClick = (e: any) => {
  console.log('handleMoreClick--', e)
}
onMounted(() => {
  activeItem.value = menusPrivate[0]
  emit('selectChange', activeItem.value)
})
</script>

<style lang="less" scoped>
.kb-list {
  width: 200px;
  height: auto;
  border-right: 1px solid #e5e7ec;
  padding: 0 4px;
  &_title {
    height: 40px;
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    display: table-cell;
    vertical-align: middle;
    padding: 8px 16px;
  }
  &_ul {
    color: #000000;
    padding: 0;
    &_item {
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
      height: 40px;
      padding: 8px 16px;
      margin: 4px 0;
      display: flex;
      justify-content: space-between;
      .more-icon {
        transform: rotate(90deg);
        display: none;
      }
      &:hover {
        background: #f5f2fe;
        border-radius: 2px;
        .more-icon {
          display: inline-block;
        }
        .loading-icon {
          display: none;
        }
      }
      &.selected {
        color: #6441ab;
        font-weight: 600;
      }
    }
    &_title {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      line-height: 22px;
      color: #696969;
      height: 40px;
      padding: 8px 16px;
      margin: 4px 0;
      border-top: 1px solid #e5e7ec;
    }
  }
}
.kb-detail {
  padding: 12px 24px;
  width: 410px;
  margin-right: 12px;
  :deep(.ant-popover-content) {
    padding: 12px 24px;
    width: 410px;
  }
}
</style>
