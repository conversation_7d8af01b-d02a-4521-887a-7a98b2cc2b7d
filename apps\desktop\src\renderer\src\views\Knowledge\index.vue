<template>
  <a-table :dataSource="dataSource" :columns="columns">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'lastModifyTime'">
        <div>{{ dayjs(record.lastModifyTime).format('YYYY-MM-DD') }}</div>
      </template>
      <template v-if="column.key === 'targetName'">
        <a-button type="link" @click="previewFile(record.targetName, record.targetPath)">{{
          record.targetName
        }}</a-button>
      </template>
    </template>
  </a-table>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { v4 as uuidv4 } from 'uuid'
const dataSource = ref([])
const notPreviewFiles = ['docx']
const columns = [
  {
    title: 'Name',
    dataIndex: 'targetName',
    key: 'targetName',
    width: 200
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: 'Last Modified',
    dataIndex: 'lastModifyTime',
    key: 'lastModifyTime',
    sorter: {
      compare: (a: { lastModifyTime: Date }, b: { lastModifyTime: Date }) => {
        return dayjs(a.lastModifyTime).valueOf() - dayjs(b.lastModifyTime).valueOf()
      }
    }
  },
  {
    title: 'Action',
    dataIndex: 'action',
    key: 'action'
  }
]
onMounted(() => {
  getDataSource()
})
const previewFile = (targetName: string, previewPath: string) => {
  const fileType = targetName.substring(targetName.lastIndexOf('.') + 1)
  if (!notPreviewFiles.includes(fileType)) {
    window.api.previewTargetFile(previewPath)
  } else {
    message.warning('Sorry,the preview file is not ready,plase try again later.')
  }
}
const getDataSource = async () => {
  const messageId = uuidv4()
  const res = await window.api.getPKBFileList({
    targetPath: null,
    fileStatus: [
      'SUCCESSED',
      'FAILED',
      'IMPORTING',
      'WAITTING',
      'ILLEGAL',
      'ADDED',
      'SUSPEND',
      'REMOVING'
    ],
    fileType: 0,
    searchType: 0,
    messageId
  })
  try {
    const { fileList } = JSON.parse(res)
    dataSource.value = fileList
  } catch (error) {
    console.error(error)
  }
}
</script>
