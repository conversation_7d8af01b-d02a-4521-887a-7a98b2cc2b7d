<template>
  <a-row class="kb-container">
    <a-col class="kb-list">
      <KBList @select-change="handleKBChange"></KBList>
    </a-col>
    <a-col class="kb-file-list">
      <KBFileList :kbinfo="slectedKB"></KBFileList>
    </a-col>
  </a-row>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import KBList from './components/KBList.vue'
import KBFileList from './components/KBFileList.vue'

const slectedKB = ref()
const handleKBChange = (item: any) => {
  console.log('indexedge--handleKBChange---', item)
  slectedKB.value = item
}
onMounted(() => {
  // activeItem.value = menusPrivate[0];
})
</script>
<style lang="less" scoped>
.kb-container {
  background-color: #ffffff;
  height: 100%;
  .kb-list {
    width: 200px;
    height: auto;
    border-right: 1px solid #e5e7ec;
    padding: 0 4px;
  }
  .kb-file-list {
    width: calc(100% - 212px);
    margin-left: 12px;
  }
}
</style>
