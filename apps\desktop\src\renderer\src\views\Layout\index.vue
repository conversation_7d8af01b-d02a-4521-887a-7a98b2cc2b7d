<template>
  <a-config-provider :theme="currentTheme">
    <div class="responsive-layout">
      <div class="test" @click="test">test dark mode</div>
      <div class="test" @click="test2">test sys mode</div>
      <div class="layout-sider">
        <ul class="menu-list">
          <li
            v-for="menu in menus"
            :key="menu.path"
            class="menu-item"
            :class="{ selected: menu.key == activeMenu?.key }"
            @click="handleClick(menu)"
          >
            <router-link :to="menu.path!" :disabled="!menu.path">
              <img
                :src="menu.key == activeMenu?.key ? menu.iconActive : menu.icon"
                :alt="menu.title"
                style="width: 48px"
              />
              <h2>{{ menu.title }}</h2>
            </router-link>
          </li>
        </ul>
        <ul class="menu-list-bottom">
          <li class="hidden">
            <HistoryOutlined />
          </li>
          <li class="hidden">
            <MailOutlined />
          </li>
          <li class="fold">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <MoreOutlined rotate="90" />
              <template #overlay>
                <a-menu>
                  <a-menu-item>History</a-menu-item>
                  <a-menu-item>Feedback</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </li>
          <li>
            <a-dropdown placement="topRight" :trigger="['click']">
              <img :src="avatarIcon" />
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="getSettingConfig"> Settings </a-menu-item>
                  <a-menu-item> MyAccount </a-menu-item>
                  <a-menu-item>Help Center</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </li>
        </ul>
      </div>
      <div class="layout-content">
        <RouterView></RouterView>
      </div>
      <SettingsModal
        :is-open="isOpen"
        :settingConfig="settingConfig"
        v-on:handle-is-open="handleIsOpen"
        v-on:change-dark-mode="changeDarkMode"
      />
    </div>
  </a-config-provider>
</template>

<script lang="ts" setup>
import MenuLocalchat from '../../assets/navi/localchat_default.png'
import MenuKnowledge from '../../assets/navi/knowledge_default.png'
import MenuPc from '../../assets/navi/pc_default.png'
import MenuCloudchat from '../../assets/navi/cloudchat_default.png'
import MenuExplore from '../../assets/navi/explore_default.png'
import MenuLocalchatActive from '../../assets/navi/localchat_selected.png'
import MenuKnowledgeActive from '../../assets/navi/knowledge_selected.png'
import MenuPcActive from '../../assets/navi/pc_selected.png'
import MenuCloudchatActive from '../../assets/navi/cloudchat_selected.png'
import MenuExploreActive from '../../assets/navi/explore_selected.png'
import avatarIcon from '../../assets/navi/avatar.png'
import SettingsModal from '../../components/SettingsModal/index.vue'
import { ref, onMounted } from 'vue'
import { useTheme } from '@renderer/hooks/useTheme'
// 主题相关
const { currentTheme, toggleTheme } = useTheme()
const test = () => {
  window.api.toggle()
  console.log('test')
}
const test2 = () => {
  window.api.system()
  console.log('test')
}
// import { useRouter } from 'vue-router'

// const router = useRouter()
// const activeMenu = ref('')

type TMenuItem = {
  key: string
  icon: string
  iconActive: string
  title: string
  path?: string
}
const isOpen = ref<boolean>(false)
const activeMenu = ref<TMenuItem | null>(null)
const menus: TMenuItem[] = [
  {
    key: 'localchat',
    icon: MenuLocalchat,
    iconActive: MenuLocalchatActive,
    title: 'Local Chat',
    path: '/localchat'
  },
  {
    key: 'knowledgeAssistant',
    icon: MenuKnowledge,
    iconActive: MenuKnowledgeActive,
    title: 'Knowledge Assistant',
    path: '/knowledge'
  },
  {
    key: 'pcAssistant',
    icon: MenuPc,
    iconActive: MenuPcActive,
    title: 'PC Assistant',
    path: '/localchat'
  },
  {
    key: 'cloudChat',
    icon: MenuCloudchat,
    iconActive: MenuCloudchatActive,
    title: 'Cloud Chat',
    path: '/localchat'
  },
  {
    key: 'explore',
    icon: MenuExplore,
    iconActive: MenuExploreActive,
    title: 'Explore',
    path: '/explore'
  }
]
const settingConfig = ref()
async function getSettingConfig() {
  const res = await window.api.openSettingModal()
  console.log('getSettingConfig-------', res)
  if (res) {
    toggleTheme(res.darkMode.mode)
    settingConfig.value = res
    handleIsOpen()
  }
}
function changeDarkMode(mode: string) {
  toggleTheme(mode)
}
function handleIsOpen() {
  isOpen.value = !isOpen.value
}
function handleClick(info: TMenuItem) {
  console.log('click', info)
  console.log('click', typeof info.path)
  activeMenu.value = info
  // router.push({ name: info.path })
}

onMounted(() => {
  activeMenu.value = menus[0]
})
</script>

<style scoped lang="less">
@import '@renderer/assets/global.less';

.responsive-layout {
  display: flex;
  min-height: 100vh;
}
.test {
  display: none;
}
.responsive-layout .layout-sider {
  width: var(--app-sidebar-width);
  background-color: #fff;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
}

.responsive-layout .layout-sider ul {
  padding: 0;
  margin: 0;

  .menu-item {
    min-height: 86px;
    padding: 12px 7.5px;
    box-sizing: border-box;

    a {
      display: flex;
      text-decoration: none;
      flex-direction: column;
      align-items: center;
      img {
        width: 48px;
        height: 48px;
      }

      h2 {
        margin: 0 auto;
        font-family: Lato;
        font-weight: 400;
        font-size: 11px;
        line-height: 14px;
        letter-spacing: 0%;
        // width: 60px;
        text-align: center;
        color: var(--text-icon-gray-3-explain, rgba(82, 82, 91, 1));
      }
    }
    &:hover {
      background-color: #f7f7f8;
      a h2 {
        color: #4663ff;
      }
    }
    &:first-child:hover {
      a img {
        content: url('../../assets/navi/localchat_hover.png');
      }
    }
    &:nth-child(2):hover {
      a img {
        content: url('../../assets/navi/knowledge_hover.png');
      }
    }
    &:nth-child(3):hover {
      a img {
        content: url('../../assets/navi/pc_hover.png');
      }
    }
    &:nth-child(4):hover {
      a img {
        content: url('../../assets/navi/cloudchat_hover.png');
      }
    }
    &:nth-child(5):hover {
      a img {
        content: url('../../assets/navi/explore_hover.png');
      }
    }
  }
  .selected {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      bottom: 16px;
      left: 1px;
      width: 3px;
      height: 54px;
      background-color: #4663ff;
      display: block;
      border-radius: 3px;
    }
    a h2 {
      color: #4663ff;
      font-weight: 800;
    }
  }
}
.responsive-layout .layout-sider .menu-list-bottom {
  position: absolute;
  bottom: 24px;
  width: 88px;
  li {
    min-height: unset;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    &:not(:last-child) {
      margin: 12px 0;
      height: 32px;
    }
    &.fold {
      height: auto;
    }
    img {
      cursor: pointer;
    }
    .anticon {
      font-size: 22px;
      cursor: pointer;
      color: #949494;
      stroke: #949494;
      stroke-width: 5px;
      &:hover {
        color: #4663ff;
      }
    }
  }
}

.responsive-layout .layout-content {
  flex: 1;
  height: 100vh;
  padding: 10px 10px 10px 0;
  box-sizing: border-box;
  background: var(--color-app-background);
}

.mini-hide {
  .responsive-layout {
    display: block;
  }
  .responsive-layout .layout-sider {
    display: none;
  }
  :deep(.ant-list .ant-list-item) {
    padding-left: 0;
    padding-right: 0;
  }
}

.is-web.is-mobile {
  .mini-hide;
}
@media @app-mini-width-query {
  .mini-hide;
}

@import '@renderer/assets/global.less';
.is-web.is-mobile {
  .mini-hide;
}
@media @app-mini-width-query {
  .mini-hide;
}
@media (max-height: 650px) {
  .responsive-layout .layout-sider .menu-list-bottom .hidden {
    display: none;
  }
}
@media (min-height: 651px) {
  .responsive-layout .layout-sider .menu-list-bottom .fold {
    display: none;
  }
}
</style>
