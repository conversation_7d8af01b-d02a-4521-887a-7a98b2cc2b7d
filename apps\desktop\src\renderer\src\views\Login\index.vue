<template>
  <div>
    <h1>Login</h1>
    <button @click="openLoginPage">open new window</button>
    <button @click="openLoginPage2">open in this window</button>
    <div>
      {{ urldata }}
      <br />
      {{ loginStatus }}
      <br />
      {{ userInfo }}
    </div>
  </div>
</template>

<script setup>
// 这里可以添加组件逻辑
import { onMounted } from 'vue'
import crypto from 'crypto'
import { generateCodeChallenge, generateCodeVerifier, getAccessToken } from '../../utils'

//测试地址 https://global-test.lenovomm.cn，
// 正式地址 https://passport.lenovo.com.cn
const baseUrlTest = 'https://global-test.lenovomm.cn' //测试地址该clientId无法使用
const baseUrl = 'https://passport.lenovo.com'
const clientId = '043af4c56d82afaa21e0cc34b64cea38de110e4effb64b9db480423b032a2ac6'
const redirectUrl = 'ainow.row://' //'ainow.row://'
const redirectUrlWeb = 'http://localhost:5173/#/login'
const urldata = ref('')
const userInfo = ref({})
const loginTimeout = ref({} | null)
const loginStatus = ref('')

let state = '' //'s7XSqRNy-DFK7t0thNYeUccZHW8uFFlTA5hmNlATkJY'
let code_verifier = ''
let code_challenge = '' //'gCnotYn_kEKvDNEygUPZuhqt9yNJwlanPWUM5wW286U'

/**新窗口打开 */
const openLoginPage = () => {
  console.log('----openLoginPage---enter')
  if (loginTimeout.value) {
    clearTimeout()
    loginTimeout.value = null
  }
  loginStatus.value = 'waiting'
  const url = `${baseUrl}/v1.0/utility/lenovoid/oauth2/authorize?client_id=${clientId}&response_type=code&redirect_uri=${redirectUrl}&scope=openid&state=${state}&code_challenge=${code_challenge}&code_challenge_method=S256`
  console.log('----openLoginPage---url', url, state)
  window.open(url, '_blank')
  loginTimeout.value = setTimeout(() => {
    console.log('openLoginPage登录超时，清空')
    init() // 重新生成state
    loginTimeout.value = null
    loginStatus.value = 'timeout'
  }, 60000)
}
/** 原窗口打开 */
const openLoginPage2 = () => {
  const url = `${baseUrl}/v1.0/utility/lenovoid/oauth2/authorize?client_id=${clientId}&response_type=code&redirect_uri=${redirectUrl}&scope=openid&state=${state}&code_challenge=${code_challenge}&code_challenge_method=S256`
  console.log('----openLoginPage2---url', url)
  window.location.href = url
  // 测试地址
  // window.location.href = 'https://global-test.lenovomm.cn/v1.0/utility/lenovoid/oauth2/authorize?client_id=2a6341a3ca15629478975c8e0d36140f7dd5fe6d45a8f3d718189c666ff2d8da&response_type=code&redirect_uri=http://127.0.0.1:5173/#/oauthdemo/home&state=mytest&scope=openid&lenovoid.lang=zh_CN'
}

/**收到认证口令 */
const handleAuthToken = (param) => {
  console.log('-------login page handleAuthToken enter------', param)
  // 超时处理
  if (loginStatus.value != 'waiting') {
    console.log('认证时间超时，忽略。')
    return false
  }
  urldata.value = param
  let obj = new URL(param)
  const _code = obj.searchParams.get('code')
  const _state = obj.searchParams.get('state')
  console.log('-------login page handleAuthToken- 222-----', obj, _code, _state)
  if (!_code || !_state) {
    console.log('无效url,code or state is null, will not response.')
    return
  }
  if (_state !== state) {
    console.log(
      '无效state, the state is not the expected, mebe is not original request, will not response.'
    )
    return
  }
  // 调用后台接口获取token
  getAccessTokenByAuthCode(_code)
}
const getAccessTokenByAuthCode = async (code) => {
  const tokenUrl = `${baseUrl}/v1.0/utility/lenovoid/oauth2/token?grant_type=authorization_code&redirect_uri=${redirectUrl}&code=${code}&client_id=${clientId}&code_verifier=${code_verifier}`
  const params = new URLSearchParams({
    client_id: clientId,
    code,
    redirect_uri: redirectUrl,
    grant_type: 'authorization_code',
    code_verifier: code_verifier
  })
  // 前端获取
  // const url = `/v1.0/utility/lenovoid/oauth2/token?grant_type=authorization_code&redirect_uri=${redirectUrl}&code=${code}&client_id=${clientId}&code_verifier=${code_verifier}`;
  // getAccessToken(url).then((res)=>{
  //   console.log('getAccessToken---',res)
  // }).catch((error)=>{
  //   console.log('getAccessToken---error',error)
  // })
  console.log('-------getUserInfoByToken -tokenUrl -----', tokenUrl)
  // 后端获取 调用主窗口api 方法
  window.api.getAccessToken(tokenUrl)
}
/**收到用户信息 */
const handleUserInfo = (params) => {
  console.log('-------login page handleUserInfo------', params)
  userInfo.value = params
  loginStatus.value = '登录成功，点击跳转到主页面'

  // todo 存储用户数据
}
const init = async () => {
  state = await generateCodeVerifier() //'s7XSqRNy-DFK7t0thNYeUccZHW8uFFlTA5hmNlATkJY'
  code_verifier = await generateCodeVerifier()
  code_challenge = await generateCodeChallenge(code_verifier) //'gCnotYn_kEKvDNEygUPZuhqt9yNJwlanPWUM5wW286U'
}

onMounted(() => {
  console.log('----onMounted---url', window.location.href)
  urldata.value = window.location.href
  init()
  if (__ELECTRON__) {
    console.log('----onMounted---__ELECTRON__111')
    window.api.onAuthToken(handleAuthToken)
    window.api.onUpdateUserInfo(handleUserInfo)
  }
})
</script>

<style scoped>
/* 这里可以添加样式 */
</style>
