<template>
  <div class="chat">
    <div class="conner-notice">Local AI mode</div>

    <Tabs
      v-model:active-key="activeKey"
      tab-position="left"
      :style="{ height: '100%' }"
      @tab-scroll="handleTabsScroll"
      @change="handleTabChange"
    >
      <TabPane key="current" :tab="`${$t('current-conversation')} ${llmStore.currentThread?.name}`">
        <div class="chat-new">
          <ChatContent />
          <footer>
            <ModelSelect class="model-select hidden" @new-thread="handleNewThread"></ModelSelect>
            <div class="input-wrapper">
              <ChatInput />
              <Button
                class="new-conv-button"
                shape="circle"
                :title="$t('new-conversation')"
                @click="handleNewThread"
              ></Button>
            </div>
            <div class="ai-mistakes hidden">AI can make mistakes.</div>
          </footer>
        </div>
      </TabPane>
      <TabPane
        key="history"
        :tab="$t('conversation-history')"
        style="height: 100vh; overflow-y: auto"
      >
        <HistoryThreads :list="historyThreads" @change="handleHistoryThreadChange" />
      </TabPane>
    </Tabs>

    <!-- TODO 暂时隐藏了设置弹窗 -->
    <!-- <FloatButton @click="handleSettingClick" /> -->
    <!-- <SettingModal :open="open" @update-open="(e) => (open = e)" /> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { Tabs, TabPane, /*FloatButton,*/ type TabsProps, Button } from 'ant-design-vue'
import ChatInput from '@components/ChatInput.vue'
import ModelSelect from '@components/ModelSelect.vue'
import ChatContent from '@components/ChatContent.vue'
// import SettingModal from '@components/SettingModal.vue'
import HistoryThreads from '@components/HistoryThreads.vue'
import { useLLMStore } from '../../stores/llm'
import useDB from '../../hooks/useDB'
import '@renderer/assets/global.less'
import { useTheme } from '@renderer/hooks/useTheme'
import { Thread } from '@prisma/client'

// 主题相关
const { isDark, currentTheme, toggleTheme } = useTheme()
console.log(isDark, currentTheme, toggleTheme, 'themmmm')
const llmStore = useLLMStore()
const { createNewThread, listThreads, getAccount } = useDB()
const historyThreads = ref<Thread[]>([])
const handleNewThread = () => {
  const account = llmStore.currentAccount
  if (!account) {
    console.error('no account')
    return
  }
  createNewThread(account)
}

const activeKey = ref('current')

const handleTabsScroll: TabsProps['onTabScroll'] = (val) => {
  console.log(val)
}
const handleTabChange: any = async (key: string) => {
  if (key !== 'history') return

  const account = llmStore.currentAccount
  if (!account) {
    console.error('no account')
    return
  }

  const threads = await listThreads(account)
  historyThreads.value = threads
}

const handleHistoryThreadChange = (thread: Thread) => {
  llmStore.setThread(thread)
  activeKey.value = 'current'
}

// const setMiniPage = async (
//   params: { target: string; AskType: ASK_TYPE; AdditionalWords: string } | any
// ) => {
//   // 切换到mini模式，创建新的会话id
//   llmStore.setProvider('ainow')
//   llmStore.setModel('ainow.chat')
//   // await handleNewThread()
//   winType.value = sessionType.AINowMini //表示小窗口模式
//   askType.value = (+params.AskType as ASK_TYPE) || ASK_TYPE.ASK
//   AdditionalWords.value = params.AdditionalWords || ''
//   console.log('切换到mini模式，创建新的会话id')
// }

// const open = ref(false)
// const handleSettingClick = () => {
//   open.value = true
// }

// // 监听channel消息
// if (__ELECTRON__) {
//   window.api.listenerMsg((msg) => {
//     console.log('监听到的channel消息---', msg)
//     if (msg && msg.target == 'mini') {
//       setMiniPage(msg)
//     } else if (msg && winType.value == sessionType.AINowMini) {
//       winType.value == null
//     }
//   })
// }

onMounted(async () => {
  // TODO 持久化真实用户
  try {
    const userName = 'FAKE_USER'
    const account = await getAccount(userName)
    if (!account) throw Error('no account')
    llmStore.setAccount(account)
    console.log('init account', account)
  } catch (error) {
    console.log('init account error', error)
  }
})
</script>

<style scoped lang="less">
@import '@renderer/assets/global.less';

.chat {
  height: 100%;
  body.is-electron & {
    height: calc(100% - var(--app-header-height));
  }
  position: relative;

  .conner-notice {
    position: fixed;
    top: 0;
    body.is-electron & {
      top: var(--app-header-height);
    }
    right: 0;
    z-index: 1;
    line-height: 32px;
    padding: 0 16px 0 40px;
    background: var(--AI-NOW-Theme-Blue-100, rgba(229, 239, 255, 1));
    border-radius: 0 0 0 20px;
    font-family: Lato; // TODO 字体
    font-weight: 400;
    font-size: 12px;
    background-image: url('@renderer/assets/ConnerNotice.svg');
    background-repeat: no-repeat;
    background-position: left 20px top 50%;
  }

  :deep(.ant-tabs-content-holder) {
    border: none;
    .ant-tabs-content {
      height: 100%;
    }
  }
  .ant-float-btn {
    left: 10px;
  }

  &-new {
    @footer-height: 160px;
    padding-bottom: @footer-height;
    height: 100%;
    box-sizing: border-box;
    overflow-y: auto;

    .chat-content {
    }
    footer {
      height: @footer-height;
      position: fixed;
      bottom: 0;
      left: var(--app-sidebar-width);
      right: 0;
      padding: 0 0.5rem;
      background: var(--color-app-background);

      .input-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        // align-items: center;
        align-items: flex-end;
        gap: 10px;
        & > :first-child {
          flex: 1;
        }
        .new-conv-button {
          width: 40px;
          height: 40px;
          border: none;
          margin-bottom: 6px;
          background: no-repeat url('@renderer/assets/new-conv-button.svg') 50% 50%;
        }
      }

      .ai-mistakes {
        width: 100%;
        height: 35px;
        line-height: 35px;
        text-align: center;
        font-size: 12px;
        color: rgba(115, 115, 115, 1);
      }

      .model-select {
        margin: 0.5rem 0;
      }
    }
  }

  // 适应主板样式，暂时隐藏对话选择
  :deep(.ant-tabs-nav) {
    display: none;
  }
  @media @app-mini-width-query {
    .chat-new footer {
      left: 0;
      height: 80px;
      .hidden {
        display: none;
      }
    }

    :deep(.ant-tabs-nav) {
      display: none;
    }
  }
  .is-web.is-mobile {
    :deep(.ant-tabs-nav) {
      display: none;
    }
  }
}
</style>
