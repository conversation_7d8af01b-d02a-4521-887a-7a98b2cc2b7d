<template>
  <div ref="view" class="search-bar-view">
    <!-- <div class="search-bar-view_top" data-ignore="1"></div> -->
    <div class="search-bar-view_bar">
      <SearchBarInput ref="searchInput" @on-change="onChange"></SearchBarInput>
    </div>
    <div class="search-bar-view_bottom" data-ignore="1">
      <SearchList v-show="search" @empty-input="onEmptyInput"></SearchList>
    </div>
  </div>
</template>
<script setup lang="ts">
// darkmode 跟随系统 暗黑模式可以修改默认样式 使用软件（c#读文件 electron系统另一种了）的lightdark模式 dark模式自己写样式 不受系统模式影响
// antd组件主题？
// 公共的 其他也能用
import SearchList from './comps/SearchList'
import { electronHooks } from '../../electron'
import { onMounted, ref, provide } from 'vue'
import SearchBarInput from './comps/SearchBarInput'
import { useTheme } from '@renderer/hooks/useTheme'
// 主题相关
const { isDark, currentTheme, toggleTheme } = useTheme()
console.log(isDark, currentTheme, toggleTheme, 'themmmm')
const view = ref()
const searchInput = ref()
const electronApi = electronHooks()
onMounted(() => {
  if (electronApi) {
    const { handleIgnoreMouseEvents } = electronApi
    handleIgnoreMouseEvents(view.value)
    if (view.value) {
      console.log('add event', view.value)

      view.value.onmousedown = () => {
        console.log('onmousedown', view.value)
        console.dir(view.value)
        electronApi.windowMove(true)
      }
      view.value.onmouseup = () => {
        electronApi.windowMove(false)
      }
      view.value.ondragend = (e) => {
        electronApi.windowMove(false)
        console.log('drop', e)
      }
    }
    // if (view.value) {
    //   view.value.onmousedown = (e) => {
    //     const { x, y } = e
    //     document.onmousemove = (ev) => {
    //       const appX = ev.screenX - x
    //       const appY = ev.screenY - y
    //       //给主进程传入坐标
    //       const data = {
    //         x: appX,
    //         y: appY
    //         // width: view.value.offsetWidth
    //         // height: search.value ? view.value.offsetHeight : 80
    //         // 相对窗口左上角的坐标
    //       }
    //       console.log(data)
    //       electronApi.handleMoveWin(data)
    //     }
    //   }
    //   view.value.onmouseup = () => {
    //     document.onmousemove = null
    //   }
    // }
  }
  // view.value?.addEventListener('mouse', (e) => {})
})

const search = ref('')
provide('keyword', search)
const onChange = (text: string) => {
  search.value = text
}
const onEmptyInput = () => {
  searchInput.value.emptyInput()
}
</script>
<style lang="less" scoped>
.search-bar-view {
  //width: 500px; // 不能写死宽度 因为出现垂直滚动条时也占用宽度 会导致出现横向滚动条
  // box-shadow: 0px 2px 10.67px 0px #00000026;

  // box-shadow: 0px 16px 32px 0px #00000030;
  &_bar {
    position: relative;
  }
  &_bottom {
    // height: 450px;
  }
}
</style>
