<template>
  <div :class="{ 'search-bar-input': true, 'search-bar-input-inputted': !!text?.length }">
    <SvgIcon name="file-ainow" :class="{ 'search-bar-input_icon': true }" size="32px"></SvgIcon>
    <span class="search-bar-input_text">
      <span>{{ text }}</span>
      <input
        ref="icon"
        v-model="text"
        tabindex="-1"
        spellcheck="false"
        autofocus
        :placeholder="placeholder"
        @input="handleChange"
        @keydown="
          (e) => {
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'Tab') {
              const target = e.target as HTMLElement | null
              if (target && 'blur' in target) {
                target.blur()
              }
              e.preventDefault()
            }
          }
        "
        @mousedown="
          (e) => {
            e.stopPropagation()
          }
        "
      />
    </span>
    <SvgIcon
      v-show="text"
      name="icon-send"
      size="32px"
      class="sendIcon"
      @click="triggerClick"
    ></SvgIcon>
  </div>
</template>

<script setup lang="ts">
// import { Input } from 'ant-design-vue'
import SvgIcon from '@renderer/components/SvgIcon'
import { onMounted, ref } from 'vue'
import { emitter } from '@main/utils/EventBus'
interface Props {
  placeholder?: string
}
const emit = defineEmits<{
  (e: 'onChange', text: string): void
}>()
const icon = ref<HTMLElement>()
withDefaults(defineProps<Props>(), {
  placeholder: 'Ask me anything'
})
const triggerClick = () => {
  emitter.emit('enterKey')
}
const text = ref('')
const emptyInput = () => {
  // 清空输入框
  text.value = ''
  handleChange()
}
defineExpose({ emptyInput })
onMounted(() => {
  window.api?.onVisibleFocus(() => {
    icon.value?.focus()
  })
})
const handleChange = () => {
  console.log(text.value)

  emit('onChange', text.value)
}
// onMounted(() => {
//   console.log(icon.value)
// })
</script>

<style lang="less" scoped>
.search-bar-drag {
  // -webkit-app-region: drag;
}

.search-bar-input {
  display: flex;
  height: 56px;
  padding: 13px;
  padding-right: 0;
  background: var(--bg-color-searchbar1);
  box-sizing: border-box;
  border-radius: 8px;
  .sendIcon {
    position: absolute;
    right: 12px;
    color: #fff;
  }
  &_icon {
    // cursor: grab;
    margin-right: 12px;
  }

  &_text {
    display: inline-block;
    min-width: 120px;
    max-width: calc(100% - 58px);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    padding-right: 10px;
    box-sizing: border-box;

    span {
      display: inline-block;
      height: 100%;
      background: inherit;
      /**继承父级**/
      padding: 0;
      white-space: nowrap;
      opacity: 0;
    }

    input {
      display: inline-block;
      height: 100%;
      width: 100%;
      background: inherit;
      /**继承父级**/
      position: absolute;
      left: 0;
      top: 0;
      padding: 0;
      opacity: 1;
      min-width: 20px;
      border: none;

      &:focus {
        outline: 0;
      }
    }
  }

  &-inputted {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}
</style>
