<template>
  <div :class="'search-collection' + ' ' + props.type" tabindex="-1">
    <ul>
      <li
        v-for="(item, index) in props.collection"
        :key="item.id || item.command"
        :class="{ active: index + additionIndex === props.activeIndex }"
        @click="(e) => handleMenuClick(e, item, index + additionIndex)"
      >
        <SearchItem :item="item" :type="props.type"></SearchItem>
      </li>
    </ul>
    <div v-show="more" class="search-collection_more" @click="() => emit('moreToggle', !more)">
      <SvgIcon name="PE-more" size="24px"></SvgIcon><span>More AI skills</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import SearchItem from '../SearchItem'
import { ListItem } from '@/types'
import SvgIcon from '@renderer/components/SvgIcon'
const props = defineProps({
  collection: {
    type: Array<ListItem>,
    required: true
  },
  type: {
    type: String,
    required: true
  },
  activeIndex: {
    type: Number,
    required: true
  },
  additionIndex: {
    type: Number,
    required: false,
    default: 0
  },
  more: {
    type: Boolean,
    required: false,
    default: false
  }
})
const emit = defineEmits<{
  (e: 'menuClick', item: ListItem, index: number): void
  (e: 'moreToggle', status: boolean): void
}>()
const handleMenuClick = (e: MouseEvent, item: ListItem, index: number) => {
  e.stopPropagation()
  setTimeout(() => {
    if (e.currentTarget) {
      ;(e.currentTarget as HTMLElement).blur()
    }
  }, 0)
  emit('menuClick', item, index)
}
</script>
<style lang="less" scoped>
.search-collection {
  &.ai {
    max-height: 255px;
    overflow-y: auto;
  }
  &_more {
    padding-left: 26px;
    padding-top: 6px;
    padding-bottom: 6px;
    span {
      margin-left: 8px;
      vertical-align: middle;
      color: #3f3f46;
    }
    svg {
      vertical-align: middle;
    }
  }
  ul {
    padding: 0 6px;
    background-color: transparent;
    &.recommend {
      max-height: 210px;
      overflow-y: auto;

      .search-list_title {
        max-width: 280px;
      }
    }
  }
  li {
    padding: 6px 20px;
    position: relative;
    //margin-bottom: 10px;
    border-radius: 3px;
    font-size: 14px;
    color: #444444;
    line-height: 30px;
    &:hover {
      background-color: #e6e6e6;
      transition: 0.2s;
    }
  }

  :deep(.active) {
    background-color: rgba(70, 99, 255, 0.1) !important;
    .enter {
      display: inline;
    }
  }
}
</style>
