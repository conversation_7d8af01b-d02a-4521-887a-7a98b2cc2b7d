<template>
  <div class="wrapdiv">
    <SvgIcon v-if="props.item.icon" :name="props.item.icon" size="24px"></SvgIcon>
    <Image v-else :data-src="props.item.getIcon"></Image>
    <div v-if="props.type === CollectionType.AI" class="search-list_title">
      <span>{{ item.name }}</span>
      <label>{{ item.subName }}</label>
      <!-- ‘<span class="ellipsis">{{ keyword }}</span
      >’ -->
    </div>
    <template v-else>
      <div class="search-list_title ellipsis">{{ props.item.name }}</div>
      <label class="search-list_subtitle" :title="item.command">{{ props.item.type }}</label>
    </template>

    <SvgIcon name="enter" class="enter" color="#737373" size="24px"></SvgIcon>
  </div>
</template>
<script setup lang="ts">
import Image from '@/renderer/src/components/Image'
import SvgIcon from '@renderer/components/SvgIcon'
import { CollectionType } from '@/types'
// const keyword = inject('keyword')
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    required: true
  }
})
</script>
<style scoped lang="less">
.search-list {
  &_title {
    margin-left: 12px;
    color: #444444;
    font-size: 14px;
    position: relative;
    span {
      display: inline-block;
      max-width: 200px;
      vertical-align: middle;
      color: #3f3f46;
      font-size: 15px;
      margin-right: 8px;
    }
    label {
      vertical-align: middle;
      color: #52525b;
      font-size: 12px;
    }
  }
  &_subtitle {
    margin-left: 12px;
    color: #737373;
    font-size: 12px;
  }
  .enter {
    position: absolute;
    right: 32px;
    display: none;
  }
}
.wrapdiv {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
