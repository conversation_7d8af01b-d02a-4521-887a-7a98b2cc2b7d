<template>
  <div ref="searchRef" class="search-list">
    <SearchCollection
      :more="more"
      :collection="_aiSkill"
      :type="CollectionType.AI"
      :active-index="activeIndex"
      @more-toggle="more = $event"
      @menu-click="handleMenuClick"
    ></SearchCollection>
    <div class="search-list_divider">
      <span>Quick action</span>
      <Divider></Divider>
    </div>
    <SearchCollection
      :collection="_recommend"
      :type="CollectionType.RECOMMEND"
      :active-index="activeIndex"
      :addition-index="additionIndex"
      @menu-click="handleMenuClick"
    ></SearchCollection>
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  inject,
  Ref,
  computed,
  ComputedRef
} from 'vue'
import { Divider } from 'ant-design-vue'
import { ACTION_TYPE, ListItem, CollectionType, AISKILL, ASK_TYPE } from '@/types'
import { throttle, debounce, keyBy } from 'lodash'
import { emitter } from '@main/utils/EventBus'
import SearchCollection from '../SearchCollection'
const more = ref(true)
const _recommend: Ref<ListItem[]> = ref([])
const _aiSkill: ComputedRef<ListItem[]> = computed(() => {
  if (more.value) {
    return AISKILL.slice(0, 5)
  } else {
    return AISKILL
  }
})

// 下拉菜单激活项
const activeIndex = ref(0)

// 对aiskill依照输入词的长度 增加两外两条条目 todo
const hasApp = ref(false)
const additionIndex = computed(() => {
  return _aiSkill.value.length
})
// 空格不会搜出结果
const searchCallback = debounce((newK) => {
  console.log('搜索关键字', newK)

  window.api?.search(newK).then((res) => {
    console.log(res, '搜索结果')
    _recommend.value = res
    activeIndex.value = 0
    nextTick(() => {
      initBounding()
    })
    const apps = res.filter((item) => item.type === ACTION_TYPE.APP)

    if (apps.length) {
      //如果有应用
      //hasApp.value = true

      window.api?.searchIcon(apps).then((iconRes) => {
        const map = keyBy(iconRes, 'id')
        _recommend.value = _recommend.value.map((rec) => {
          if (map[rec.id]) {
            return { ...rec, getIcon: map[rec.id].getIcon }
          } else {
            return rec
          }
        })
      })
    } else {
      //hasApp.value = false
    }
  })
}, 200)
const keyword = inject<Ref<string>>('keyword', ref(''))

watch(
  () => keyword.value,
  (newK) => {
    searchCallback(newK)
    if (!newK) {
      // 只有完全空了 就重设置高度
      window.api?.setBounds({ height: 56 })
    }
  },
  { immediate: true }
)
const lock = ref(true) // 是否可打开交互
const openExternalHandle = async (item: ListItem): Promise<unknown> => {
  const { type, command, args, askType, isUWP } = item

  switch (type) {
    case ACTION_TYPE.APP:
      if (isUWP) {
        return window.api?.openUWP(item.appId)
      }
      if (command?.includes('cmd.exe') || !args) {
        // 系统cmd 用node的打不开 使用electron方式打开
        return window.api?.openApp(command)
      } else {
        // 命令带参数的 electron的打不开 使用node方式
        return window.api?.openProgram(command, (args as string).trim())
      }

    case ACTION_TYPE.MINIASK: // 打开mini小窗问答
    case ACTION_TYPE.MINITRANSLATE:
    case ACTION_TYPE.CLOUDEXPLAIN:
    case ACTION_TYPE.CLOUD_ASK:
    case ACTION_TYPE.LOCALQA:
    case ACTION_TYPE.PCASSISTANT:
    case ACTION_TYPE.POLISH:
    case ACTION_TYPE.SEARCHFILE:
    case ACTION_TYPE.SUMMARY:
      return window.open(
        `ainow://open?target=mini&AdditionalWords=${keyword.value}&AskType=${askType}&coord={"x":${window.screenX},"y":${window.screenY}}`
      )
    // return window.api?.sendMiniMsg({
    //   ChatText: keyword.value,
    //   type
    // })

    // case ACTION_TYPE.MINITRANSLATE: // 打开mini小窗翻译
    //   return window.api?.translateMiniMsg({
    //     ChatText: keyword.value
    //   })

    case ACTION_TYPE.SETTING: // 打开系统设置
      return window.api?.openSystemSettings(command)

    case ACTION_TYPE.BING:
      return window.api?.openWebSite(command + keyword.value)
    case ACTION_TYPE.URL:
      return window.api?.openWebSite(command)
  }
  // 清空输入框内容
  return
}
const emit = defineEmits<{
  (e: 'emptyInput'): void
}>()
// 打开外部应用
const openExternal = (item: ListItem) => {
  if (!lock.value) return
  lock.value = false // 不可点

  openExternalHandle(item)
    .then(() => {
      emit('emptyInput')
      window.api.setWindowBlur()
      console.log('触发了blur')
      activeIndex.value = 0
    })
    .finally(() => {
      lock.value = true // 可点
    })
}

// 点击菜单项
const handleMenuClick = (item: ListItem, i: number) => {
  activeIndex.value = i
  openExternal(item)
}

const searchRef = ref()
const initBounding = () => {
  // 搜索的时候也要动态改高度
  // 如果快触底了 则往上移动
  const h = (searchRef.value as HTMLElement).offsetHeight
  const y = window.screenY || window.screenTop

  window.api?.setBounds({ height: h + 56 })
  const availHeight = window.screen.availHeight - (h + 56)
  if (y > availHeight) {
    window.api?.setBounds({ y: availHeight })
  }
}

const preTab = () => {
  //向上切换列表
  const LENGTH = _aiSkill.value.length + _recommend.value.length //下拉菜单组件的列表总长度
  activeIndex.value = (activeIndex.value + 1) % LENGTH
}
const nextTab = () => {
  //向下切换列表
  const LENGTH = _aiSkill.value.length + _recommend.value.length
  activeIndex.value = (activeIndex.value - 1 + LENGTH) % LENGTH
}
// 监听整体组件事件 鼠标滚轮切换菜单项
const handleWheel = throttle(
  (e: WheelEvent) => {
    if (e.deltaY > 0) {
      preTab()
    } else {
      nextTab()
    }
  },
  400,
  { trailing: false, leading: true }
)
const indexToItem = () => {
  let temp
  if (hasApp.value) {
    temp = _recommend.value.concat(_aiSkill.value)
  } else {
    temp = _aiSkill.value.concat(_recommend.value)
  }
  return temp[activeIndex.value]
}
// 监听整体组件事件 键盘上下键切换菜单项 回车执行
const handleKeyPress = (e: KeyboardEvent) => {
  e.preventDefault()
  switch (e.key) {
    case 'Tab':
    case 'ArrowDown':
      preTab()
      break
    case 'ArrowUp':
      nextTab()
      break
    case 'Enter': {
      e.stopPropagation()
      e.stopImmediatePropagation()
      const item = indexToItem()
      openExternal(item)

      break
    }
  }
} //在window上注册监听上下键 防止焦点不在dom上触发不了
onMounted(() => {
  emitter.on('enterKey', () => {
    const item = indexToItem()
    openExternal(item)
  })
  initBounding()
  activeIndex.value = 0

  window.addEventListener('keyup', handleKeyPress)
  window.addEventListener('wheel', handleWheel)
  window.api?.onVisibleFocus(() => {
    more.value = true
  })
})
onUnmounted(() => {
  window.api?.setBounds({ height: 56 })
  window.removeEventListener('keyup', handleKeyPress)
  window.removeEventListener('wheel', handleWheel)
})
</script>
<style scoped lang="less">
.search-list {
  background-color: var(--bg-color-searchbar2);
  border-radius: 0 0px 8px 8px;
  -webkit-user-select: none;
  user-select: none;
  * {
    color: var(--text-color-searchbar);
  }
  &_divider {
    margin-top: 10px;

    span {
      color: var(--text-color-searchbar);
      font-size: 12px;
      padding: 0 20px;
    }
    :deep(.ant-divider) {
      margin: 15px 0 5px;
    }
  }
  .search-collection:nth-of-type(1) {
    :deep(.search-collection_divider) {
      display: none;
    }
  }
}
</style>
