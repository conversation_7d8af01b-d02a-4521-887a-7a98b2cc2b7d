<template>
  <a-config-provider :theme="currentTheme">
    <!-- <a-config-provider
      :theme="{
        algorithm: theme.darkAlgorithm
      }"
    > -->
    <div :class="{ 'app-dark': isDark }">
      <!-- 头部导航栏 -->
      <a-layout-header class="header">
        <div class="header-content">
          <h1 class="title">Vue3 + Electron + Ant Design</h1>
          <div class="header-right">
            <!-- 主题切换按钮 -->
            <a-tooltip :title="isDark ? '切换到亮色模式' : '切换到暗色模式'">
              <a-button type="primary" @click="toggleTheme"> 切换 </a-button>
            </a-tooltip>
          </div>
        </div>
      </a-layout-header>
      <div>
        <a-button type="primary">Primary Button</a-button>
        <a-button>Default Button</a-button>
        <a-button type="dashed">Dashed Button</a-button>
        <a-button type="text">Text Button</a-button>
        <a-button type="link">Link Button</a-button>
      </div>
    </div>
  </a-config-provider>
</template>

<script setup lang="ts">
import { useTheme } from '@renderer/hooks/useTheme'

// 主题相关
const { isDark, currentTheme, toggleTheme } = useTheme()

// 当前时间
</script>

<style scoped>
.app {
  min-height: 100vh;
  background-color: var(--bg-color-base);
}

/* 头部样式 */
.header {
  padding: 0 24px;
  background: var(--bg-color-container);
  border-bottom: 1px solid var(--border-color-split);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.title {
  color: var(--text-color-primary);
  margin: 0;
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 主内容区域样式 */
.main-content {
  padding: 24px;
  background: var(--bg-color-base);
  min-height: calc(100vh - 64px);
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--bg-color-container);
  padding: 24px;
  border-radius: 8px;
}

.demo-card {
  margin-bottom: 24px;
}

/* 主题切换按钮样式 */
.theme-toggle-btn {
  color: var(--text-color-primary);
}

/* 暗色模式特殊处理 */
.app-dark {
  background: var(--bg-color-base);
}

/* 过渡动画 */
.app,
.header,
.content-container,
.theme-toggle-btn {
  transition: all 0.3s;
}
</style>
