<template>
  <div class="selection-bar">
    <div class="logo-wrapper">
      <img class="logo-img" src="../../assets/word/logo.svg" />
    </div>
    <div class="item-wrapper" @click="onCloudChatClick">
      <img src="../../assets/word/cloud.svg" />
      <div>Cloud Chat</div>
    </div>
    <div class="item-wrapper" @click="onLocalChatClick">
      <img src="../../assets/word/local.svg" />
      <div>Local Chat</div>
    </div>
    <div class="item-wrapper" @click="onTranslateClick">
      <img src="../../assets/word/translate.svg" />
      <div>Translate</div>
    </div>
    <div class="item-wrapper" @click="onPolishClick">
      <img src="../../assets/word/polish.svg" />
      <div>Polish</div>
    </div>
    <div class="item-wrapper more-item" @click="onMoreClick">
      <img src="../../assets/word/more.svg" />
    </div>
    <!-- <div class="selected-text">{{ selectedText }}</div> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

enum ASK_TYPE {
  SEARCH = 0,
  ASK,
  EXPLAIN,
  TRANSLATE,
  POLISH,
  SUMMARIZE,
  CLOUD_ASK,
  PCASSISTANT
}

const selectedText = ref('demo')
onMounted(() => {
  window.api.onSelectText((event, text) => {
    console.log('selectedText', event, text)
    selectedText.value = text
  })

  window.api.onMenuAction((action) => {
    console.log('Menu action:', action)
  })
})

const onCloudChatClick = () => {
  window.api.hideWordSniffer()
  window.open(
    `ainow://open?target=mini&AdditionalWords=${selectedText.value}&AskType=${ASK_TYPE.CLOUD_ASK}`
  )
}

const onLocalChatClick = () => {
  window.api.hideWordSniffer()
  window.open(
    `ainow://open?target=mini&AdditionalWords=${selectedText.value}&AskType=${ASK_TYPE.ASK}`
  )
}

const onTranslateClick = () => {
  window.api.hideWordSniffer()
  window.open(
    `ainow://open?target=mini&AdditionalWords=${selectedText.value}&AskType=${ASK_TYPE.TRANSLATE}`
  )
}

const onPolishClick = () => {
  window.api.hideWordSniffer()
  window.open(
    `ainow://open?target=mini&AdditionalWords=${selectedText.value}&AskType=${ASK_TYPE.POLISH}`
  )
}
const onMoreClick = () => {
  window.api.showContextMenu()
  console.log('onMoreClick')
}
</script>

<style scoped>
.selection-bar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: white;
  border-radius: 8px;
  color: rgba(63, 63, 70, 1);
  box-shadow:
    inset 0px -2px 4px rgba(0, 0, 0, 0.1),
    inset 0px 2px 4px rgba(0, 0, 0, 0.1);

  img {
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }

  .logo-wrapper {
    cursor: move;
    -webkit-app-region: drag;
    display: flex;
    align-items: center;
    margin-right: 10px;
  }

  .logo-img {
    width: 20px;
    height: 20px;
    user-select: none;
  }

  .item-wrapper {
    display: flex;
    align-items: center;
    margin-right: 8px;

    height: 24px;
    /* padding: 0 1px; */
    border-radius: 6px;

    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    cursor: pointer;

    &:hover {
      background-color: rgba(236, 244, 255, 1);
    }

    img {
      width: 20px;
      height: 20px;
      margin-right: 2px;
    }
  }
}

.selected-text {
  margin-right: 10px;
  font-size: 14px;
  width: 80px;
  color: red;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

button {
  padding: 2px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #fff;
  cursor: pointer;
}

button:hover {
  background: #f5f5f5;
}
</style>
