import { BroadcastMsg } from '../main/SearchBar/AINowService/PipeClient'

export type BroadcastChannel = keyof BroadcastCallbackMapping
export type BroadcastCallbackMapping = {
  // eslint-disable-next-line @typescript-eslint/ban-types
  4096: (msg: BroadcastMsg<{ IsActive: boolean }>) => void //更改 SearchBar 的 Active 状态
  4097: (msg: BroadcastMsg<{ IsActive: boolean }>) => void //更改 悬浮球 的 Active 状态
  4098: (msg: BroadcastMsg<string>) => void //更新配置
  4099: (msg: BroadcastMsg<{ Status: boolean }>) => void //更新悬浮球状态
  4100: (msg: BroadcastMsg<{ LoginStatus: boolean }>) => void //更新登陆状态
}
export type BroadcastCB<T extends BroadcastChannel> = BroadcastCallbackMapping[T]
