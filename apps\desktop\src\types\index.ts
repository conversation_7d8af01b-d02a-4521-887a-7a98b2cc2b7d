// TODO 不要和 main/preload/renderer并列，是否并入 @ainow/types
import { IpcRendererEvent } from 'electron'

// import { Electron } from 'electron'
export interface IPCRes<T = undefined> {
  success: boolean
  res?: T
  timestamp: number
}
export interface Bounds {
  x: number
  y: number
  width: number
  height: number
}
export interface Rect {
  originX: number
  originY: number
  maxX: number
  maxY: number
}
export type Size = Pick<Bounds, 'width' | 'height'>
export type Pos = Pick<Bounds, 'x' | 'y'>
export interface CustomApi {
  setIgnoreMouseEvents: (ignore: boolean, option?: { forward: boolean }) => void
  openWebSite: (url: string) => Promise<boolean>
  handleMoveWin: (bounds: Bounds) => void
  sendMiniMsg: (param: Record<string, unknown>) => Promise<boolean>
  translateMiniMsg: (param: Record<string, unknown>) => Promise<boolean>
  openSystemSettings: (param: string) => Promise<boolean>
  openApp: (path: string) => Promise<boolean>
  openUWP: (appId: string) => Promise<boolean>
  openProgram: (path: string, args: string) => Promise<boolean>
  setBounds: (bounds: Partial<Bounds>) => void
  windowMove: (canMoving: boolean) => void
  search: (keyword: string) => Promise<ListItem[]>
  searchIcon: (data: ListItem[]) => Promise<ListItem[]>
  setWindowBlur: () => void
  onVisibleFocus: (arg) => void
}

export interface FloatingApi {
  sendRightClick: () => void
  openSecondaryWindow: () => void
  windowMoveDrag: (param: boolean) => void
  hideWindow: () => void
  selectMuenItem: (args: IpcRendererEvent, param?: string) => void
  openAinow: () => void
}

declare global {
  interface Window {
    api: CustomApi & FloatingApi & any
  }
}
// 菜单列表可以执行的操作类型
export enum ACTION_TYPE {
  URL = 'Website',
  MINIASK = 'miniask',
  CLOUDEXPLAIN = 'cloudexplain',
  MINITRANSLATE = 'minitranslate',
  POLISH = 'polish',
  LOCALQA = 'localqa',
  SEARCHFILE = 'searchfile',
  CLOUD_ASK = 'cloudask',
  SUMMARY = 'summary',
  PCASSISTANT = 'pcassistant',
  APP = 'app',
  SETTING = 'setting',
  BING = 'bing',
  UWP = 'uwp'
}
// 搜索列表每条数据的结构
export interface ListItem {
  id: string
  icon?: string
  getIcon?: () => Promise<Electron.NativeImage> | string
  command: string
  name: string
  type: ACTION_TYPE
  [key: string]: unknown
}

//向ainow小窗提问的类型  本地search--0 本地ask--1 Explain--2  Translate--3 Polish--4 Summarize--5 Cloud提问--6
export enum ASK_TYPE {
  SEARCH = 0,
  ASK,
  EXPLAIN,
  TRANSLATE,
  POLISH,
  SUMMARIZE,
  CLOUD_ASK,
  PCASSISTANT
}
export const map = new Map<string, { AskType: ASK_TYPE; Intention: string; ChatType: number }>([
  [ACTION_TYPE.MINIASK, { AskType: ASK_TYPE.ASK, Intention: '', ChatType: 0 }],
  [ACTION_TYPE.CLOUDEXPLAIN, { AskType: ASK_TYPE.EXPLAIN, Intention: '', ChatType: 32 }],
  [ACTION_TYPE.CLOUD_ASK, { AskType: ASK_TYPE.CLOUD_ASK, Intention: '', ChatType: 32 }],
  [ACTION_TYPE.LOCALQA, { AskType: ASK_TYPE.ASK, Intention: 'GENERAL_GENERATION', ChatType: 0 }],
  [
    ACTION_TYPE.MINITRANSLATE,
    { AskType: ASK_TYPE.TRANSLATE, Intention: 'WORK_ASSISTANT_TRANSLATION', ChatType: 0 }
  ],
  [ACTION_TYPE.PCASSISTANT, { AskType: ASK_TYPE.PCASSISTANT, Intention: '', ChatType: 0 }],
  [
    ACTION_TYPE.POLISH,
    { AskType: ASK_TYPE.POLISH, Intention: 'WORK_ASSISTANT_DOCUMENT_POLISHING', ChatType: 0 }
  ],
  [
    ACTION_TYPE.SEARCHFILE,
    { AskType: ASK_TYPE.SEARCH, Intention: 'WORK_ASSISTANT_DOCUMENT_SEARCH', ChatType: 0 }
  ],
  [
    ACTION_TYPE.SUMMARY,
    { AskType: ASK_TYPE.SUMMARIZE, Intention: 'WORK_ASSISTANT_DOCUMENT_SUMMARY', ChatType: 0 }
  ]
])
// export enum SearchType {
//   APP = 'app',
//   webSite = 'web-site',
//   config = 'config'
// }
export enum CollectionType {
  AI = 'ai',
  RECOMMEND = 'recommend'
}

export interface SearchItem extends ListItem {
  id: string
  getIcon?: () => Promise<Electron.NativeImage>
  keywords: string[]
}
export const bing = {
  id: ACTION_TYPE.BING,
  icon: 'PE-search',
  name: 'Search Bing',
  type: ACTION_TYPE.BING,
  command: 'https://www.bing.com/search?q='
}
export const AISKILL = [
  {
    id: ACTION_TYPE.MINIASK,
    icon: 'PE-insight',
    name: 'Ask AI Now ',
    command: '',
    type: ACTION_TYPE.MINIASK,
    askType: ASK_TYPE.ASK
  },
  {
    id: ACTION_TYPE.CLOUDEXPLAIN,
    icon: 'PE-explain',
    name: 'Cloud Explain ',
    subName: '(Cloud AI) Explain the text',
    command: '',
    type: ACTION_TYPE.CLOUDEXPLAIN,
    askType: ASK_TYPE.EXPLAIN
  },
  {
    id: ACTION_TYPE.MINITRANSLATE,
    icon: 'PE-Translate',
    name: 'Translate ',
    subName: '(Local AI) Translate the text',
    command: '',
    type: ACTION_TYPE.MINITRANSLATE,
    askType: ASK_TYPE.TRANSLATE
  },
  {
    id: ACTION_TYPE.POLISH,
    icon: 'PE-polish',
    name: 'Polish',
    subName: '(Local AI) Enhance the text',
    command: '',
    type: ACTION_TYPE.POLISH,
    askType: ASK_TYPE.POLISH
  },
  {
    id: ACTION_TYPE.LOCALQA,
    icon: 'PE-localchat',
    name: 'Local Q&A',
    subName: '(Local AI) Answer questions from knowledge base',
    command: '',
    type: ACTION_TYPE.LOCALQA,
    askType: ASK_TYPE.ASK
  },
  {
    id: ACTION_TYPE.SEARCHFILE,
    icon: 'bitcoin-icons_search-filled',
    name: 'Search Files',
    subName: '(Local AI) Search in the knowledge base',
    command: '',
    type: ACTION_TYPE.SEARCHFILE,
    askType: ASK_TYPE.SEARCH
  },
  {
    id: ACTION_TYPE.CLOUD_ASK,
    icon: 'PE-cloudask',
    name: 'Cloud Ask',
    subName: '(Cloud AI) Answer your questions',
    command: '',
    type: ACTION_TYPE.CLOUD_ASK,
    askType: ASK_TYPE.CLOUD_ASK
  },
  {
    id: ACTION_TYPE.SUMMARY,
    icon: 'PE-summary',
    name: 'Summary',
    subName: '(Local AI) Summarize the text',
    command: '',
    type: ACTION_TYPE.SUMMARY,
    askType: ASK_TYPE.SUMMARIZE
  },
  {
    id: ACTION_TYPE.PCASSISTANT,
    icon: 'pcass',
    name: 'PC assistant',
    subName: '(Local AI) Get help for PC control or service',
    command: '',
    type: ACTION_TYPE.PCASSISTANT,
    askType: ASK_TYPE.PCASSISTANT
  }
]
export const translate = {
  id: ACTION_TYPE.MINITRANSLATE,
  icon: 'PE-Translate',
  name: 'Translate for ',
  command: '',
  type: ACTION_TYPE.MINITRANSLATE
}
export const summary = {
  id: ACTION_TYPE.SUMMARY,
  icon: 'PE-Translate',
  name: 'summary',
  command: '',
  type: ACTION_TYPE.SUMMARY
}
export const polish = {
  id: ACTION_TYPE.POLISH,
  icon: 'PE-Translate',
  name: 'polish ',
  command: '',
  type: ACTION_TYPE.POLISH
}
export const ask = {
  id: ACTION_TYPE.MINIASK,
  icon: 'PE-insight',
  name: 'Ask AI Now for ',
  command: '',
  type: ACTION_TYPE.MINIASK
}
