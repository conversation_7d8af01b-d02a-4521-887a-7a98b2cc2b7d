{
  "files": [],
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "baseUrl": ".",
    "types": ["vite-plugin-svg-icons/client","node"],
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
  
    "jsx": "preserve",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@renderer/*": [
          "src/renderer/src/*"
      ],
      "@main/*": [
        "src/main/*"
      ]
   
    },

    /* Linting */
    "strict": true,
    // "noUnusedLocals": false,
    // "noUnusedParameters": true,
    "noUnusedParameters": false,
    "noUnusedLocals": false,
    "noFallthroughCasesInSwitch": true,
    // "noDebugger": false,
    "noImplicitAny": true
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }, { "path": "./tsconfig.web.json" }]
}
