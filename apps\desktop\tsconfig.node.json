{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*", "../../packages/**/*","src/config/**/*","src/types/**/*","src/main/SearchBar/services/**/*"],
  "exclude": ["../../packages/msix/**/*", "../../packages/@libs/**/*"],
  "compilerOptions": {
    "composite": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "types": ["electron-vite/node"]
  },
  "paths": {
      "@/*": [
        "src/*"
      ],
      "@renderer/*": [
          "src/renderer/src/*"
      ],
      "@main/*": [
        "src/main/*"
      ],
    }
  
}
