{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.vue",
    "src/preload/*.d.ts",
    "src/main/**/*",
    "src/types/*.ts",
    "../../packages/**/*"
  ],
  "exclude": [
    "../../packages/msix/**/*",
    "../../packages/@libs/**/*"
  ],
  "compilerOptions": {
    "composite": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@renderer/*": ["src/renderer/src/*"],
      "@components": ["src/renderer/src/components/*"],
      "@main/*": [
        "src/main/*"
      ],
    }
  }
}
