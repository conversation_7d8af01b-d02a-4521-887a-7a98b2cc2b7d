import { defineConfig, type Plugin } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'

const svgIconVirtualModule = (): Plugin => ({
  name: 'svg-icon-virtual-module',
  enforce: 'pre',
  resolveId(id) {
    return id === 'virtual:svg-icons-register' ? id : null
  },
  load(id) {
    if (id === 'virtual:svg-icons-register') {
      return 'export default {}'
    }
  }
})

// edge/web 版本的打包配置
export default defineConfig({
  root: resolve(__dirname, 'src/renderer'),
  resolve: {
    alias: {
      '@': resolve('src'),
      '@renderer': resolve('src/renderer/src'),
      '@components': resolve('src/renderer/src/components')
    }
  },
  define: {
    __ELECTRON__: false,
    __WEB__: true
  },
  css: {
    preprocessorOptions: {
      less: {
        charset: false,
        // TODO electron 上无效
        additionalData: '@import "@renderer/assets/global.less";'
      }
    }
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      '/v1': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: resolve(__dirname, 'dist/web'),
    emptyOutDir: true,
    rollupOptions: {
      input: resolve(__dirname, 'src/renderer/index.html')
    }
  },
  plugins: [
    vue(),
    svgIconVirtualModule(),
    AutoImport({
      imports: ['vue', 'vue-router'],
      dts: 'src/auto-import.d.ts'
    }),
    Components({
      // dts: true,
      // dirs: ['src/render/components'], // 指定组件所在位置
      // extensions: ['vue'],
      // dts: 'src/components.d.ts',
      resolvers: [
        AntDesignVueResolver({
          importStyle: false,
          resolveIcons: true
        })
      ]
    })
  ]
})
