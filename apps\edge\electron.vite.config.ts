/*
 * @Description:
 * @FilePath: \ainow-ui\apps\edge\electron.vite.config.ts
 */
import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@': resolve('src'),
        '@renderer': resolve('src/renderer/src'),
        '@main': resolve('src/main')
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@': resolve('src'),
        '@renderer': resolve('src/renderer/src'),
        '@main': resolve('src/main')
      }
    },
    build: {
      lib: {
        entry: ['src/preload/index.ts']
      }
    }
  },
  renderer: {
    resolve: {
      alias: {
        '@': resolve('src'),
        '@renderer': resolve('src/renderer/src'),
        '@main': resolve('src/main')
      }
    },
    plugins: [
      vue(),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [resolve(process.cwd(), 'src/renderer/src/assets/icons')],
        // 指定symbolId格式
        symbolId: 'icon-[name]'
      })
    ],
    server: {
      host: 'localhost',
      // 跨域
      cors: false,
      proxy: {
        // 知识库相关接口
        /*
        '/aclapi': { 
          target: 'http://*************:8090',
          // changeOrigin: true,
          // logLevel: 'debug',
          secure: false,
          // pathRewrite: ''
        },
        '/api': {  //知识库内文件相关接口
          target: 'http://*************:8081',
          // changeOrigin: true,
          // logLevel: 'debug',
          secure: false,
          // pathRewrite: ''
        },*/
        '^/clientLog': {
          target: 'https://ainowopstest.lenovo.com/',
          // target: 'http://**************:8082',
          changeOrigin: true
          // rewrite: (path: string) => path.replace(/^\/api/, '')
        }
      }
    }
  }
})
