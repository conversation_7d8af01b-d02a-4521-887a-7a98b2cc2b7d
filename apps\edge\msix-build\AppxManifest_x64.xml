<?xml version="1.0" encoding="utf-8"?>
<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
  xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
  xmlns:desktop2="http://schemas.microsoft.com/appx/manifest/desktop/windows10/2"
  xmlns:desktop6="http://schemas.microsoft.com/appx/manifest/desktop/windows10/6"
  IgnorableNamespaces="uap uap3 desktop2">
<Identity Name="Lenovo.edge"
          ProcessorArchitecture="x64"
          Version="1.0.0.0"
          Publisher="CN=Lenovo (Beijing) Limited"/>
  <Properties>
    <desktop6:RegistryWriteVirtualization>disabled</desktop6:RegistryWriteVirtualization>
    <desktop6:FileSystemWriteVirtualization>disabled</desktop6:FileSystemWriteVirtualization>
    <DisplayName>Lenovo Team AI</DisplayName>
    <PublisherDisplayName>Lenovo (Beijing) Limited</PublisherDisplayName>
    <Logo>assets\icon.png</Logo>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.22000.0" MaxVersionTested="10.0.22000.0" />
  </Dependencies>
  <Resources>
    <Resource Language="en-US" />
  </Resources>
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
    <rescap:Capability Name="unvirtualizedResources"/>
    <Capability Name="internetClient" />

  </Capabilities>
  <Applications>
  <Application Id="edge" Executable="app\win-unpacked\Lenovo Team AI.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements
          DisplayName="Lenovo Team AI"
          Description="Lenovo Team AI"
          Square44x44Logo="assets\Square44x44Logo.png"
          Square150x150Logo="assets\Square150x150Logo.png"
          BackgroundColor="transparent">
      </uap:VisualElements>
      <Extensions>
        <uap:Extension Category="windows.protocol">
          <uap:Protocol Name="ainow.row">
            <uap:DisplayName>Lenovo Team AI</uap:DisplayName>
          </uap:Protocol>
        </uap:Extension>
      </Extensions>

    </Application>
  </Applications>
</Package>
