const path = require('path')
const { packageMSIX } = require('../../../packages/msix')
const { signExecutableFiles } = require('./tools.cjs')

packageMSIX({
  appDir: path.join(__dirname, '..\\dist'),
  appManifest: path.join(__dirname, '.\\AppxManifest_x64.xml'),
  packageAssets: path.join(__dirname, '.\\assets'),
  outputDir: path.join(__dirname, '..\\msix-out'),
  signParams: [
    'sign',
    '/fd',
    'sha256',
    '/a',
    '/f',
    path.resolve('C:\\temp\\LenovoTestCert.pfx'),
    '/p',
    'YourPassword'
  ],
  signCallback: signExecutableFiles
})

// TODO 指定安装路径 https://learn.microsoft.com/zh-cn/uwp/schemas/appxpackage/uapmanifestschema/element-desktop8-mutablepackagedirectory
