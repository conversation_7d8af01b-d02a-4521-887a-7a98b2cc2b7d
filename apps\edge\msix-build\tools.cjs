const { execSync } = require('child_process')
const path = require('path')
const fs = require('fs')

function scanExecutableFiles(dirPath) {
  let results = []
  const files = fs.readdirSync(dirPath)
  for (const file of files) {
    const fullPath = path.join(dirPath, file)
    const stat = fs.statSync(fullPath)
    if (stat.isDirectory()) {
      results = results.concat(scanExecutableFiles(fullPath))
    } else {
      const ext = path.extname(file).toLowerCase()
      if (['.exe', '.dll', '.ime', '.cat', '.deploy'].includes(ext)) {
        results.push(fullPath)
      }
    }
  }
  return results
}

function isFileSigned(signTool, filePath) {
  try {
    const command = `"${signTool}" verify /v /pa "${path.resolve(filePath)}"`
    const output = execSync(command, { stdio: 'pipe' }).toString()
    // console.log("check sign", filePath, output);
    return output.includes('Successfully verified')
  } catch (error) {
    // console.log(error)
    return false
  }
}

function signExecutableFiles(programOptions, runFn) {
  const { signTool, signParams } = programOptions

  const appDir = path.join(__dirname, '..\\dist')
  const files = scanExecutableFiles(appDir).filter((file) => !isFileSigned(signTool, file))

  if (!files.length) {
    console.log('no file need sign')
    return false
  }

  if (signParams[0] === 'sign') signParams.shift()

  return Promise.all(
    files.map((file) => {
      const args = ['sign', ...signParams, file]
      console.log('signing...', file)
      return runFn(signTool, args)
    })
  )
}

module.exports = {
  signExecutableFiles
}
