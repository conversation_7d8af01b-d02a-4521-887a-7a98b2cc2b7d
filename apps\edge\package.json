{"name": "@ainow/edge", "version": "1.0.0", "description": "Lenovo Team AI", "main": "./out/main/index.js", "author": "Lenovo Team AI", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "chcp 65001 && electron-vite dev -- --trace-warnings --sourcemap", "build:worker": "vite build --config vite.worker.config.ts", "build": "npm run build:worker && electron-vite build", "hot": "electron-vite dev --watch", "build-test": "electron-vite build --mode 'test'", "postinstall": "electron-builder install-app-deps", "build-cache:unpack": "electron-builder --dir", "build-test:unpack": "npm run build-test && electron-builder --dir", "build:unpack": "npm run build && electron-builder --win --dir", "build:installer": "electron-builder --win --prepackaged ./dist/win-unpacked", "build:unpack-arm": "npm run build && electron-builder --dir --arm64", "build:win": "npm run build && electron-builder --win", "msix": "node msix-build/build-msix.cjs", "build:msix": "rimraf msix-out && rimraf out && rimraf dist && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false pnpm run build && electron-builder --dir --win && pnpm msix", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@libs/a-comps": "workspace:*", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "electron-log": "^5.2.4", "electron-store": "^10.0.1", "form-data": "^4.0.2", "fuse.js": "^7.0.0", "less": "^4.2.1", "lodash": "^4.17.21", "node-fetch": "^3.3.2", "regedit": "^5.1.3", "reset-css": "^5.0.2", "tiny-pinyin": "^1.3.2", "uuid": "^11.0.3", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.30", "vue-router": "^4.5.0", "vue3-lottie": "^3.3.1"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.10.3", "@types/lodash": "^4.17.13", "@types/node": "^20.14.8", "@types/pinyin": "^2.10.2", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.3.2", "typescript": "^5.5.2", "vite": "^5.3.1", "vue-tsc": "^2.0.22"}}