const { parentPort, workerData } = require('worker_threads')
;(async () => {
  const results = []
  // 开始循环获取图标数据并发送请求
  for (let i = 0; i < workerData.length; i++) {
    const { target, type, ...rest } = workerData[i]
    if (!target) {
      console.error('Missing target in Worker data:', workerData[i])
      continue
    }

    if (type === 'Website') {
      results.push({ ...rest, type })
      continue
    }
    const requestId = `icon-${i}`

    parentPort.postMessage({ action: 'getIcon', target, id: requestId })

    const icon = await new Promise((resolve) => {
      // 开始监听一次主线程发过来的data
      parentPort.once('message', (message) => {
        // 匹配当前请求的 ID也就是requestId~
        if (message.id === requestId) {
          //一样的话 从主线程接收到图标数据
          resolve(message.base64Icon)
        }
      })
    })

    // 整合
    results.push({ ...rest, getIcon: icon, type })
  }

  parentPort.postMessage({ type: 'final', data: results })
})()
