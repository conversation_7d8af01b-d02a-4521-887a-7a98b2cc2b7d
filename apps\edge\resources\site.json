{"success": true, "data": [{"url": "fbpigeon.com", "siteName": "fbpigeon", "keywords": ["fbpigeon"], "getIcon": "https://fbpigeon.com/favicon.ico"}, {"url": "alibabadns.com", "siteName": "alibabadns", "keywords": ["DNS service by Alibaba", "alibabadns"], "getIcon": "https://alibabadns.com/favicon.ico"}, {"url": "utorrent.com", "siteName": "utorrent", "keywords": ["Torrent client for downloading and sharing files via BitTorrent protocol.", "utorrent"], "getIcon": "https://utorrent.com/favicon.ico"}, {"url": "microsoft.com", "siteName": "microsoft", "keywords": ["Microsoft's official website for software hardware and services.", "microsoft"], "getIcon": "https://microsoft.com/favicon.ico"}, {"url": "fbcdn.net", "siteName": "fbcdn", "keywords": ["Content delivery network for Facebook media.", "fbcdn"], "getIcon": "https://fbcdn.net/favicon.ico"}, {"url": "bugsnag.com", "siteName": "bugsnag", "keywords": ["Error monitoring and reporting platform", "bugsnag"], "getIcon": "https://bugsnag.com/favicon.ico"}, {"url": "shopifysvc.com", "siteName": "shopifysvc", "keywords": ["Related to Shopify's backend services and infrastructure.", "shopifysvc"], "getIcon": "https://shopifysvc.com/favicon.ico"}, {"url": "aliyun.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Cloud computing services by Alibaba Cloud", "<PERSON><PERSON><PERSON>"], "getIcon": "https://aliyun.com/favicon.ico"}, {"url": "adsrvr.org", "siteName": "adsrvr", "keywords": ["Ad serving and tracking platform", "adsrvr"], "getIcon": "https://adsrvr.org/favicon.ico"}, {"url": "applovin.com", "siteName": "applovin", "keywords": ["Mobile advertising and app monetization platform", "applovin"], "getIcon": "https://applovin.com/favicon.ico"}, {"url": "chatgpt.com", "siteName": "chatgpt", "keywords": ["AI-powered chatbot platform by OpenAI", "chatgpt"], "getIcon": "https://chatgpt.com/favicon.ico"}, {"url": "adsafeprotected.com", "siteName": "adsafeprotected", "keywords": ["Online ad safety and fraud protection", "adsafeprotected"], "getIcon": "https://adsafeprotected.com/favicon.ico"}, {"url": "branch.io", "siteName": "branch", "keywords": ["Mobile deep linking and attribution services", "branch"], "getIcon": "https://branch.io/favicon.ico"}, {"url": "adriver.ru", "siteName": "adriver", "keywords": ["Russian advertising network", "adriver"], "getIcon": "https://adriver.ru/favicon.ico"}, {"url": "azure-devices.net", "siteName": "azure-devices", "keywords": ["Microsoft Azure services for IoT devices", "azure-devices"], "getIcon": "https://azure-devices.net/favicon.ico"}, {"url": "adnxs-simple.com", "siteName": "adnxs-simple", "keywords": ["Simplified ad network services", "adnxs-simple"], "getIcon": "https://adnxs-simple.com/favicon.ico"}, {"url": "aiv-delivery.net", "siteName": "aiv-delivery", "keywords": ["AI-driven delivery network for content", "aiv-delivery"], "getIcon": "https://aiv-delivery.net/favicon.ico"}, {"url": "adaether.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Decentralized finance (DeFi) platform", "<PERSON><PERSON><PERSON>"], "getIcon": "https://adaether.com/favicon.ico"}, {"url": "amazon.com", "siteName": "amazon", "keywords": ["Global online retailer", "amazon"], "getIcon": "https://amazon.com/favicon.ico"}, {"url": "t.co", "siteName": "t", "keywords": ["URL shortening service used by Twitter.", "t"], "getIcon": "https://t.co/favicon.ico"}, {"url": "autodesk.com", "siteName": "autodesk", "keywords": ["Design and engineering software solutions", "autodesk"], "getIcon": "https://autodesk.com/favicon.ico"}, {"url": "jwpltx.com", "siteName": "jwpltx", "keywords": ["Related to JWPlayer's video hosting and streaming services.", "jwpltx"], "getIcon": "https://jwpltx.com/favicon.ico"}, {"url": "akaquill.net", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Microsoft Quill API platform", "<PERSON><PERSON><PERSON>"], "getIcon": "https://akaquill.net/favicon.ico"}, {"url": "discordapp.com", "siteName": "discordapp", "keywords": ["Discord's communication platform", "discordapp"], "getIcon": "https://discordapp.com/favicon.ico"}, {"url": "binance.com", "siteName": "binance", "keywords": ["Cryptocurrency exchange platform", "binance"], "getIcon": "https://binance.com/favicon.ico"}, {"url": "app-analytics-services.com", "siteName": "app-analytics-services", "keywords": ["App analytics solutions", "app-analytics-services"], "getIcon": "https://app-analytics-services.com/favicon.ico"}, {"url": "appspot.com", "siteName": "appspot", "keywords": ["Google's cloud platform for hosting applications", "appspot"], "getIcon": "https://appspot.com/favicon.ico"}, {"url": "google.de", "siteName": "google", "keywords": ["Google's German website.", "google"], "getIcon": "https://google.de/favicon.ico"}, {"url": "usgovcloudapi.net", "siteName": "usgov<PERSON>louda<PERSON>", "keywords": ["API services related to cloud infrastructure for U.S. government services.", "usgov<PERSON>louda<PERSON>"], "getIcon": "https://usgovcloudapi.net/favicon.ico"}, {"url": "fb.com", "siteName": "fb", "keywords": ["Shortened URL for Facebook.", "fb"], "getIcon": "https://fb.com/favicon.ico"}, {"url": "bttrack.com", "siteName": "bttrack", "keywords": ["Tracking and marketing services", "bttrack"], "getIcon": "https://bttrack.com/favicon.ico"}, {"url": "alibaba.com", "siteName": "alibaba", "keywords": ["Chinese multinational e-commerce retail and technology company", "alibaba"], "getIcon": "https://alibaba.com/favicon.ico"}, {"url": "alipay.com", "siteName": "alipay", "keywords": ["Online payment platform by Alibaba Group", "alipay"], "getIcon": "https://alipay.com/favicon.ico"}, {"url": "mi.com", "siteName": "mi", "keywords": ["Xiaomi's e-commerce and device sales platform.", "mi"], "getIcon": "https://mi.com/favicon.ico"}, {"url": "apple.news", "siteName": "apple", "keywords": ["News aggregation service by Apple", "apple"], "getIcon": "https://apple.news/favicon.ico"}, {"url": "ads-twitter.com", "siteName": "ads-twitter", "keywords": ["Twitter advertising network", "ads-twitter"], "getIcon": "https://ads-twitter.com/favicon.ico"}, {"url": "shopify.com", "siteName": "shopify", "keywords": ["E-commerce platform enabling online stores and retail point-of-sale systems.", "shopify"], "getIcon": "https://shopify.com/favicon.ico"}, {"url": "adtrafficquality.google", "siteName": "adtrafficquality", "keywords": ["Google's ad traffic quality management", "adtrafficquality"], "getIcon": "https://adtrafficquality.google/favicon.ico"}, {"url": "admanmedia.com", "siteName": "admanmedia", "keywords": ["Media advertising and marketing solutions", "admanmedia"], "getIcon": "https://admanmedia.com/favicon.ico"}, {"url": "ad.gt", "siteName": "ad", "keywords": ["Mobile advertising network", "ad"], "getIcon": "https://ad.gt/favicon.ico"}, {"url": "aiv-cdn.net", "siteName": "aiv-cdn", "keywords": ["AI-based content delivery network services", "aiv-cdn"], "getIcon": "https://aiv-cdn.net/favicon.ico"}, {"url": "jwplayer.com", "siteName": "jwplayer", "keywords": ["A video player platform for publishers and content creators.", "jwplayer"], "getIcon": "https://jwplayer.com/favicon.ico"}, {"url": "discord.media", "siteName": "discord", "keywords": ["Media service for Discord", "discord"], "getIcon": "https://discord.media/favicon.ico"}, {"url": "akamaized.net", "siteName": "akamaized", "keywords": ["Akamai's CDN for delivering web content", "akamaized"], "getIcon": "https://akamaized.net/favicon.ico"}, {"url": "bilibili.com", "siteName": "bilibili", "keywords": ["Chinese video sharing and streaming platform", "bilibili"], "getIcon": "https://bilibili.com/favicon.ico"}, {"url": "attn.tv", "siteName": "attn", "keywords": ["Digital media and content platform", "attn"], "getIcon": "https://attn.tv/favicon.ico"}, {"url": "usertrust.com", "siteName": "usertrust", "keywords": ["Digital certificate provider for SSL certificates and website security.", "usertrust"], "getIcon": "https://usertrust.com/favicon.ico"}, {"url": "appsflyersdk.com", "siteName": "appsflyersdk", "keywords": ["AppsFlyer SDK for mobile app analytics", "appsflyersdk"], "getIcon": "https://appsflyersdk.com/favicon.ico"}, {"url": "google.com.hk", "siteName": "google", "keywords": ["Google's Hong Kong website.", "google"], "getIcon": "https://google.com.hk/favicon.ico"}, {"url": "fastly.net", "siteName": "fastly", "keywords": ["Content delivery network (CDN) provider.", "fastly"], "getIcon": "https://fastly.net/favicon.ico"}, {"url": "aliexpress.com", "siteName": "aliexpress", "keywords": ["Global retail platform for online shopping", "aliexpress"], "getIcon": "https://aliexpress.com/favicon.ico"}, {"url": "btloader.com", "siteName": "b<PERSON><PERSON><PERSON>", "keywords": ["Software loader services", "b<PERSON><PERSON><PERSON>"], "getIcon": "https://btloader.com/favicon.ico"}, {"url": "apple.com", "siteName": "apple", "keywords": ["Official website for Apple Inc. providing products and services", "apple"], "getIcon": "https://apple.com/favicon.ico"}, {"url": "bounceexchange.com", "siteName": "bounceexchange", "keywords": ["Marketing and customer experience platform", "bounceexchange"], "getIcon": "https://bounceexchange.com/favicon.ico"}, {"url": "adrta.com", "siteName": "adrta", "keywords": ["Real-time advertising platform", "adrta"], "getIcon": "https://adrta.com/favicon.ico"}, {"url": "adsmoloco.com", "siteName": "adsmoloco", "keywords": ["Mobile ad network and services", "adsmoloco"], "getIcon": "https://adsmoloco.com/favicon.ico"}, {"url": "adkernel.com", "siteName": "adkernel", "keywords": ["Programmatic advertising technology", "adkernel"], "getIcon": "https://adkernel.com/favicon.ico"}, {"url": "ad-score.com", "siteName": "ad-score", "keywords": ["Digital advertising platform", "ad-score"], "getIcon": "https://ad-score.com/favicon.ico"}, {"url": "jtvnw.net", "siteName": "jtvnw", "keywords": ["jtvnw"], "getIcon": "https://jtvnw.net/favicon.ico"}, {"url": "aibixby.com", "siteName": "aibixby", "keywords": ["AI-powered assistant platform", "aibixby"], "getIcon": "https://aibixby.com/favicon.ico"}, {"url": "discord.gg", "siteName": "discord", "keywords": ["Discord's invitation links for joining servers", "discord"], "getIcon": "https://discord.gg/favicon.ico"}, {"url": "bigolive.tv", "siteName": "bigolive", "keywords": ["Live streaming platform by BIGO Technology", "bigolive"], "getIcon": "https://bigolive.tv/favicon.ico"}, {"url": "akamaihd.net", "siteName": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["Content delivery network (CDN) by Akamai", "<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://akamaihd.net/favicon.ico"}, {"url": "azure-api.net", "siteName": "azure-api", "keywords": ["Azure API services by Microsoft", "azure-api"], "getIcon": "https://azure-api.net/favicon.ico"}, {"url": "usercentrics.eu", "siteName": "usercentrics", "keywords": ["Provides consent management services for businesses to comply with privacy regulations.", "usercentrics"], "getIcon": "https://usercentrics.eu/favicon.ico"}, {"url": "appsflyer.com", "siteName": "appsflyer", "keywords": ["Mobile marketing attribution platform", "appsflyer"], "getIcon": "https://appsflyer.com/favicon.ico"}, {"url": "chartboost.com", "siteName": "chartboost", "keywords": ["Mobile ad network for game developers", "chartboost"], "getIcon": "https://chartboost.com/favicon.ico"}, {"url": "google.com.br", "siteName": "google", "keywords": ["Google's Brazilian website.", "google"], "getIcon": "https://google.com.br/favicon.ico"}, {"url": "atomile.com", "siteName": "atomile", "keywords": ["Mobile advertising and marketing services", "atomile"], "getIcon": "https://atomile.com/favicon.ico"}, {"url": "adobess.com", "siteName": "adobess", "keywords": ["Adobe Secure Services platform", "adobess"], "getIcon": "https://adobess.com/favicon.ico"}, {"url": "amazon.co.uk", "siteName": "amazon", "keywords": ["Amazon's UK-based online store", "amazon"], "getIcon": "https://amazon.co.uk/favicon.ico"}, {"url": "t-msedge.net", "siteName": "t-msedge", "keywords": ["Related to Microsoft's Edge browser services.", "t-msedge"], "getIcon": "https://t-msedge.net/favicon.ico"}, {"url": "alicdn.com", "siteName": "alic<PERSON>", "keywords": ["Content delivery network (CDN) by Alibaba Cloud", "alic<PERSON>"], "getIcon": "https://alicdn.com/favicon.ico"}, {"url": "fastly-edge.com", "siteName": "fastly-edge", "keywords": ["Content delivery network (CDN) provider.", "fastly-edge"], "getIcon": "https://fastly-edge.com/favicon.ico"}, {"url": "bt.co", "siteName": "bt", "keywords": ["British Telecommunications company", "bt"], "getIcon": "https://bt.co/favicon.ico"}, {"url": "apple-dns.net", "siteName": "apple-dns", "keywords": ["Apple DNS services for internet traffic management", "apple-dns"], "getIcon": "https://apple-dns.net/favicon.ico"}, {"url": "heytapmobi.com", "siteName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keywords": ["Provides mobile app services.", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://heytapmobi.com/favicon.ico"}, {"url": "shopeemobile.com", "siteName": "shopeemobile", "keywords": ["Mobile platform for Shopee's e-commerce services.", "shopeemobile"], "getIcon": "https://shopeemobile.com/favicon.ico"}, {"url": "mi-img.com", "siteName": "mi-img", "keywords": ["Related to <PERSON><PERSON>'s image services.", "mi-img"], "getIcon": "https://mi-img.com/favicon.ico"}, {"url": "adroll.com", "siteName": "adroll", "keywords": ["Digital advertising and retargeting services", "adroll"], "getIcon": "https://adroll.com/favicon.ico"}, {"url": "adjust.com", "siteName": "adjust", "keywords": ["Mobile analytics and attribution platform", "adjust"], "getIcon": "https://adjust.com/favicon.ico"}, {"url": "jsdelivr.net", "siteName": "js<PERSON><PERSON><PERSON>", "keywords": ["Content delivery network (CDN) for open-source projects.", "js<PERSON><PERSON><PERSON>"], "getIcon": "https://jsdelivr.net/favicon.ico"}, {"url": "ad-delivery.net", "siteName": "ad-delivery", "keywords": ["Ad delivery services for digital marketing", "ad-delivery"], "getIcon": "https://ad-delivery.net/favicon.ico"}, {"url": "agora.io", "siteName": "agora", "keywords": ["Real-time communications API and services", "agora"], "getIcon": "https://agora.io/favicon.ico"}, {"url": "discord.com", "siteName": "discord", "keywords": ["Online communication platform for gamers and communities", "discord"], "getIcon": "https://discord.com/favicon.ico"}, {"url": "app-analytics-services-att.com", "siteName": "app-analytics-services-att", "keywords": ["App analytics services", "app-analytics-services-att"], "getIcon": "https://app-analytics-services-att.com/favicon.ico"}, {"url": "bigo.sg", "siteName": "bigo", "keywords": ["Social media and live-streaming platform", "bigo"], "getIcon": "https://bigo.sg/favicon.ico"}, {"url": "akamaiedge.net", "siteName": "akamaiedge", "keywords": ["Edge computing services by Akamai", "akamaiedge"], "getIcon": "https://akamaiedge.net/favicon.ico"}, {"url": "applvn.com", "siteName": "applvn", "keywords": ["AppLovin's advertising network", "applvn"], "getIcon": "https://applvn.com/favicon.ico"}, {"url": "atlassian.net", "siteName": "atlassian", "keywords": ["Atlassian's cloud-based network and services", "atlassian"], "getIcon": "https://atlassian.net/favicon.ico"}, {"url": "fastclick.net", "siteName": "fastclick", "keywords": ["Ad delivery and tracking platform.", "fastclick"], "getIcon": "https://fastclick.net/favicon.ico"}, {"url": "algolia.net", "siteName": "algolia", "keywords": ["Search and discovery API service", "algolia"], "getIcon": "https://algolia.net/favicon.ico"}, {"url": "sogou.com", "siteName": "sogou", "keywords": ["Chinese search engine and technology company.", "sogou"], "getIcon": "https://sogou.com/favicon.ico"}, {"url": "google.com.au", "siteName": "google", "keywords": ["Google's Australian website.", "google"], "getIcon": "https://google.com.au/favicon.ico"}, {"url": "adtng.com", "siteName": "adtng", "keywords": ["Programmatic advertising services", "adtng"], "getIcon": "https://adtng.com/favicon.ico"}, {"url": "cloudsink.net", "siteName": "cloudsink", "keywords": ["Data storage and cloud computing platform", "cloudsink"], "getIcon": "https://cloudsink.net/favicon.ico"}, {"url": "shopee.io", "siteName": "shopee", "keywords": ["Related to <PERSON><PERSON>'s services or platform.", "shopee"], "getIcon": "https://shopee.io/favicon.ico"}, {"url": "heytapdownload.com", "siteName": "heytapdownload", "keywords": ["App data delivery platform.", "heytapdownload"], "getIcon": "https://heytapdownload.com/favicon.ico"}, {"url": "shopee.com.br", "siteName": "shopee", "keywords": ["E-commerce platform serving Brazil (part of Sea Group).", "shopee"], "getIcon": "https://shopee.com.br/favicon.ico"}, {"url": "heytapdl.com", "siteName": "heytapdl", "keywords": ["Used for app data delivery.", "heytapdl"], "getIcon": "https://heytapdl.com/favicon.ico"}, {"url": "shopee.co.id", "siteName": "shopee", "keywords": ["E-commerce platform serving Indonesia (part of Sea Group).", "shopee"], "getIcon": "https://shopee.co.id/favicon.ico"}, {"url": "herokuapp.com", "siteName": "herokuapp", "keywords": ["Cloud platform for building and deploying apps.", "herokuapp"], "getIcon": "https://herokuapp.com/favicon.ico"}, {"url": "shifen.com", "siteName": "shifen", "keywords": ["Likely related to Chinese platform services (Shifen).", "shifen"], "getIcon": "https://shifen.com/favicon.ico"}, {"url": "shein.com", "siteName": "shein", "keywords": ["Global e-commerce platform focused on fast fashion.", "shein"], "getIcon": "https://shein.com/favicon.ico"}, {"url": "hihonorcloud.com", "siteName": "hihonorcloud", "keywords": ["Huawei's cloud services platform for Honor brand.", "hihonorcloud"], "getIcon": "https://hihonorcloud.com/favicon.ico"}, {"url": "amemv.com", "siteName": "amemv", "keywords": ["TikTok's infrastructure and services", "amemv"], "getIcon": "https://amemv.com/favicon.ico"}, {"url": "hicloud.com", "siteName": "hicloud", "keywords": ["Huawei's cloud services platform.", "hicloud"], "getIcon": "https://hicloud.com/favicon.ico"}, {"url": "slackb.com", "siteName": "slackb", "keywords": ["Used for Slack's internal or backup services.", "slackb"], "getIcon": "https://slackb.com/favicon.ico"}, {"url": "amazonvideo.com", "siteName": "amazonvideo", "keywords": ["Amazon's video streaming service", "amazonvideo"], "getIcon": "https://amazonvideo.com/favicon.ico"}, {"url": "hhf123dcd.com", "siteName": "hhf123dcd", "keywords": ["hhf123dcd"], "getIcon": "https://hhf123dcd.com/favicon.ico"}, {"url": "slack.com", "siteName": "slack", "keywords": ["Collaboration and messaging platform for teams.", "slack"], "getIcon": "https://slack.com/favicon.ico"}, {"url": "amazontrust.com", "siteName": "amazontrust", "keywords": ["Amazon's security and trust services", "amazontrust"], "getIcon": "https://amazontrust.com/favicon.ico"}, {"url": "heytapmobile.com", "siteName": "heytapmobile", "keywords": ["Provides mobile app services.", "heytapmobile"], "getIcon": "https://heytapmobile.com/favicon.ico"}, {"url": "skype.com", "siteName": "skype", "keywords": ["Communication platform for voice and video calls messaging and collaboration (owned by Microsoft).", "skype"], "getIcon": "https://skype.com/favicon.ico"}, {"url": "sitescout.com", "siteName": "sitescout", "keywords": ["Programmatic advertising platform.", "sitescout"], "getIcon": "https://sitescout.com/favicon.ico"}, {"url": "amazonaws.com", "siteName": "amazonaws", "keywords": ["Cloud computing services by Amazon Web Services (AWS)", "amazonaws"], "getIcon": "https://amazonaws.com/favicon.ico"}, {"url": "gvt3.com", "siteName": "gvt3", "keywords": ["Google's content delivery network (CDN).", "gvt3"], "getIcon": "https://gvt3.com/favicon.ico"}, {"url": "singular.net", "siteName": "singular", "keywords": ["Provides marketing analytics and attribution solutions.", "singular"], "getIcon": "https://singular.net/favicon.ico"}, {"url": "amazonalexa.com", "siteName": "amazonalexa", "keywords": ["Amazon Alexa service for smart devices", "amazonalexa"], "getIcon": "https://amazonalexa.com/favicon.ico"}, {"url": "simpli.fi", "siteName": "simpli", "keywords": ["Advertising platform focused on programmatic and local advertising.", "simpli"], "getIcon": "https://simpli.fi/favicon.ico"}, {"url": "gvt2.com", "siteName": "gvt2", "keywords": ["Google's content delivery network (CDN).", "gvt2"], "getIcon": "https://gvt2.com/favicon.ico"}, {"url": "amazon.dev", "siteName": "amazon", "keywords": ["Amazon's developer platform", "amazon"], "getIcon": "https://amazon.dev/favicon.ico"}, {"url": "signal.org", "siteName": "signal", "keywords": ["Privacy-focused messaging platform.", "signal"], "getIcon": "https://signal.org/favicon.ico"}, {"url": "gvt1.com", "siteName": "gvt1", "keywords": ["Google's content delivery network (CDN).", "gvt1"], "getIcon": "https://gvt1.com/favicon.ico"}, {"url": "amazon.de", "siteName": "amazon", "keywords": ["Amazon's German-based online store", "amazon"], "getIcon": "https://amazon.de/favicon.ico"}, {"url": "gumgum.com", "siteName": "gumgum", "keywords": ["Provides contextual ad targeting and computer vision technology.", "gumgum"], "getIcon": "https://gumgum.com/favicon.ico"}, {"url": "sentry.io", "siteName": "sentry", "keywords": ["Provides real-time error tracking and performance monitoring for web apps.", "sentry"], "getIcon": "https://sentry.io/favicon.ico"}, {"url": "alphonso.tv", "siteName": "alphon<PERSON>", "keywords": ["TV data analytics platform", "alphon<PERSON>"], "getIcon": "https://alphonso.tv/favicon.ico"}, {"url": "here.com", "siteName": "here", "keywords": ["Location-based services mapping and navigation.", "here"], "getIcon": "https://here.com/favicon.ico"}, {"url": "sentinelone.net", "siteName": "sentinelone", "keywords": ["Cybersecurity company offering endpoint protection and threat detection.", "sentinelone"], "getIcon": "https://sentinelone.net/favicon.ico"}, {"url": "he.net", "siteName": "he", "keywords": ["Hurricane Electric a global internet service provider.", "he"], "getIcon": "https://he.net/favicon.ico"}, {"url": "allawntech.com", "siteName": "allawntech", "keywords": ["Technology services provider", "allawntech"], "getIcon": "https://allawntech.com/favicon.ico"}, {"url": "sendgrid.net", "siteName": "sendgrid", "keywords": ["Email delivery platform providing email marketing and transactional email services.", "sendgrid"], "getIcon": "https://sendgrid.net/favicon.ico"}, {"url": "hcaptcha.com", "siteName": "h<PERSON><PERSON>a", "keywords": ["Privacy-focused CAPTCHA service.", "h<PERSON><PERSON>a"], "getIcon": "https://hcaptcha.com/favicon.ico"}, {"url": "allawnos.com", "siteName": "allawnos", "keywords": ["Technology solutions provider", "allawnos"], "getIcon": "https://allawnos.com/favicon.ico"}, {"url": "aliyuncs.com", "siteName": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["Cloud services by Alibaba Cloud", "<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://aliyuncs.com/favicon.ico"}, {"url": "segment.io", "siteName": "segment", "keywords": ["Customer data platform (same as segment.com).", "segment"], "getIcon": "https://segment.io/favicon.ico"}, {"url": "harman.com", "siteName": "harman", "keywords": ["Audio and connected car technology company.", "harman"], "getIcon": "https://harman.com/favicon.ico"}, {"url": "segment.com", "siteName": "segment", "keywords": ["Customer data platform providing analytics and segmentation tools.", "segment"], "getIcon": "https://segment.com/favicon.ico"}, {"url": "cdn-apple.com", "siteName": "cdn-apple", "keywords": ["Apple's content delivery network services", "cdn-apple"], "getIcon": "https://cdn-apple.com/favicon.ico"}, {"url": "seedtag.com", "siteName": "seedtag", "keywords": ["Provides programmatic advertising and content recommendations.", "seedtag"], "getIcon": "https://seedtag.com/favicon.ico"}, {"url": "hp.com", "siteName": "hp", "keywords": ["Computer and printer manufacturer.", "hp"], "getIcon": "https://hp.com/favicon.ico"}, {"url": "casalemedia.com", "siteName": "casalemedia", "keywords": ["Digital advertising and marketing solutions", "casalemedia"], "getIcon": "https://casalemedia.com/favicon.ico"}, {"url": "secu100.net", "siteName": "secu100", "keywords": ["secu100"], "getIcon": "https://secu100.net/favicon.ico"}, {"url": "hoyoverse.com", "siteName": "hoyoverse", "keywords": ["Creator of Genshin Impact and other video games.", "hoyoverse"], "getIcon": "https://hoyoverse.com/favicon.ico"}, {"url": "capcutapi.com", "siteName": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["CapCut video editing API services", "<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://capcutapi.com/favicon.ico"}, {"url": "hotmail.com", "siteName": "hotmail", "keywords": ["Microsoft's free email service.", "hotmail"], "getIcon": "https://hotmail.com/favicon.ico"}, {"url": "sectigo.com", "siteName": "sectigo", "keywords": ["Cybersecurity company offering SSL/TLS certificates and digital security solutions.", "sectigo"], "getIcon": "https://sectigo.com/favicon.ico"}, {"url": "capcut.com", "siteName": "capcut", "keywords": ["Video editing app by ByteDance", "capcut"], "getIcon": "https://capcut.com/favicon.ico"}, {"url": "hotjar.io", "siteName": "hotjar", "keywords": ["Provides user behavior analytics and feedback tools.", "hotjar"], "getIcon": "https://hotjar.io/favicon.ico"}, {"url": "canva.com", "siteName": "canva", "keywords": ["Graphic design platform", "canva"], "getIcon": "https://canva.com/favicon.ico"}, {"url": "sgsnssdk.com", "siteName": "sgsnssdk", "keywords": ["Related to SDK services for mobile app development and monetization.", "sgsnssdk"], "getIcon": "https://sgsnssdk.com/favicon.ico"}, {"url": "hotjar.com", "siteName": "hotjar", "keywords": ["Provides user behavior analytics and feedback tools.", "hotjar"], "getIcon": "https://hotjar.com/favicon.ico"}, {"url": "sfx.ms", "siteName": "sfx", "keywords": ["Related to Microsoft services (likely for media or distribution).", "sfx"], "getIcon": "https://sfx.ms/favicon.ico"}, {"url": "camelopardalisbee.com", "siteName": "camelopardalisbee", "keywords": ["Website services", "camelopardalisbee"], "getIcon": "https://camelopardalisbee.com/favicon.ico"}, {"url": "home-assistant.io", "siteName": "home-assistant", "keywords": ["Open-source platform for smart home automation.", "home-assistant"], "getIcon": "https://home-assistant.io/favicon.ico"}, {"url": "service-now.com", "siteName": "service-now", "keywords": ["Cloud-based platform for IT service management and automation.", "service-now"], "getIcon": "https://service-now.com/favicon.ico"}, {"url": "caelumbee.co.uk", "siteName": "caelumbee", "keywords": ["Web development and hosting services", "caelumbee"], "getIcon": "https://caelumbee.co.uk/favicon.ico"}, {"url": "servenobid.com", "siteName": "serve<PERSON>id", "keywords": ["serve<PERSON>id"], "getIcon": "https://servenobid.com/favicon.ico"}, {"url": "hisavana.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["<PERSON><PERSON><PERSON>"], "getIcon": "https://hisavana.com/favicon.ico"}, {"url": "bytetcdn.com", "siteName": "bytetcdn", "keywords": ["ByteDance's CDN services", "bytetcdn"], "getIcon": "https://bytetcdn.com/favicon.ico"}, {"url": "hik-connect.com", "siteName": "hik-connect", "keywords": ["Provides services for Hikvision cloud-connected devices.", "hik-connect"], "getIcon": "https://hik-connect.com/favicon.ico"}, {"url": "cdngslb.com", "siteName": "cdngslb", "keywords": ["CDN services for large-scale content delivery", "cdngslb"], "getIcon": "https://cdngslb.com/favicon.ico"}, {"url": "sharethrough.com", "siteName": "sharethrough", "keywords": ["Provides native advertising and content monetization services.", "sharethrough"], "getIcon": "https://sharethrough.com/favicon.ico"}, {"url": "cdn77.org", "siteName": "cdn77", "keywords": ["CDN services", "cdn77"], "getIcon": "https://cdn77.org/favicon.ico"}, {"url": "sharethis.com", "siteName": "sharethis", "keywords": ["Social sharing platform for websites and apps.", "sharethis"], "getIcon": "https://sharethis.com/favicon.ico"}, {"url": "ibyteimg.com", "siteName": "ibyteimg", "keywords": ["Domains used by ByteDance for image hosting.", "ibyteimg"], "getIcon": "https://ibyteimg.com/favicon.ico"}, {"url": "sharepoint.com", "siteName": "sharepoint", "keywords": ["Microsoft's web-based collaboration and document management platform.", "sharepoint"], "getIcon": "https://sharepoint.com/favicon.ico"}, {"url": "ibytedtos.com", "siteName": "iby<PERSON><PERSON>", "keywords": ["Domains used by ByteDance the parent company of TikTok.", "iby<PERSON><PERSON>"], "getIcon": "https://ibytedtos.com/favicon.ico"}, {"url": "cdn20.com", "siteName": "cdn20", "keywords": ["Content delivery network services", "cdn20"], "getIcon": "https://cdn20.com/favicon.ico"}, {"url": "shalltry.com", "siteName": "shalltry", "keywords": ["shalltry"], "getIcon": "https://shalltry.com/favicon.ico"}, {"url": "i18n-pglstatp.com", "siteName": "i18n-pglstatp", "keywords": ["i18n-pglstatp"], "getIcon": "https://i18n-pglstatp.com/favicon.ico"}, {"url": "cdn.house", "siteName": "cdn", "keywords": ["Content delivery network services", "cdn"], "getIcon": "https://cdn.house/favicon.ico"}, {"url": "tiktokcdn-eu.com", "siteName": "tiktokcdn-eu", "keywords": ["Content delivery network (CDN) used for TikTok's services in Europe.", "tiktokcdn-eu"], "getIcon": "https://tiktokcdn-eu.com/favicon.ico"}, {"url": "cedexis-radar.net", "siteName": "cedexis-radar", "keywords": ["Internet traffic management and optimization", "cedexis-radar"], "getIcon": "https://cedexis-radar.net/favicon.ico"}, {"url": "huntress.io", "siteName": "huntress", "keywords": ["Cybersecurity platform for threat detection and response.", "huntress"], "getIcon": "https://huntress.io/favicon.ico"}, {"url": "cdninstagram.com", "siteName": "cdninstagram", "keywords": ["Content delivery network for Instagram", "cdninstagram"], "getIcon": "https://cdninstagram.com/favicon.ico"}, {"url": "hulu.com", "siteName": "hulu", "keywords": ["Streaming service offering TV shows movies and original content.", "hulu"], "getIcon": "https://hulu.com/favicon.ico"}, {"url": "tiktok.com", "siteName": "tiktok", "keywords": ["Popular social media platform for short videos owned by ByteDance.", "tiktok"], "getIcon": "https://tiktok.com/favicon.ico"}, {"url": "cdnhwc3.com", "siteName": "cdnhwc3", "keywords": ["CDN services", "cdnhwc3"], "getIcon": "https://cdnhwc3.com/favicon.ico"}, {"url": "hubspot.com", "siteName": "hubspot", "keywords": ["Inbound marketing sales and service software.", "hubspot"], "getIcon": "https://hubspot.com/favicon.ico"}, {"url": "acronis.com", "siteName": "acronis", "keywords": ["Backup recovery and data protection solutions", "acronis"], "getIcon": "https://acronis.com/favicon.ico"}, {"url": "keplr.app", "siteName": "keplr", "keywords": ["A wallet app for the Cosmos blockchain ecosystem.", "keplr"], "getIcon": "https://keplr.app/favicon.ico"}, {"url": "cdnhwc1.com", "siteName": "cdnhwc1", "keywords": ["CDN services", "cdnhwc1"], "getIcon": "https://cdnhwc1.com/favicon.ico"}, {"url": "themoviedb.org", "siteName": "themoviedb", "keywords": ["Online database for movies and TV shows offering metadata and user reviews.", "themoviedb"], "getIcon": "https://themoviedb.org/favicon.ico"}, {"url": "huawei.com", "siteName": "hua<PERSON>", "keywords": ["Chinese multinational technology company.", "hua<PERSON>"], "getIcon": "https://huawei.com/favicon.ico"}, {"url": "thejeu.com", "siteName": "thejeu", "keywords": ["thejeu"], "getIcon": "https://thejeu.com/favicon.ico"}, {"url": "kaspersky.com", "siteName": "ka<PERSON>sky", "keywords": ["Cybersecurity company known for its antivirus software.", "ka<PERSON>sky"], "getIcon": "https://kaspersky.com/favicon.ico"}, {"url": "cloud.microsoft", "siteName": "cloud", "keywords": ["Microsoft's cloud computing services platform", "cloud"], "getIcon": "https://cloud.microsoft/favicon.ico"}, {"url": "acrobat.com", "siteName": "acrobat", "keywords": ["Online services for Adobe Acrobat PDF tools", "acrobat"], "getIcon": "https://acrobat.com/favicon.ico"}, {"url": "clevertap-prod.com", "siteName": "clevertap-prod", "keywords": ["Mobile marketing and customer engagement platform", "clevertap-prod"], "getIcon": "https://clevertap-prod.com/favicon.ico"}, {"url": "kaspersky-labs.com", "siteName": "kaspersky-labs", "keywords": ["Developer of Kaspersky security software solutions.", "kaspersky-labs"], "getIcon": "https://kaspersky-labs.com/favicon.ico"}, {"url": "accuweather.com", "siteName": "a<PERSON><PERSON><PERSON><PERSON>", "keywords": ["Weather forecasting service", "a<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://accuweather.com/favicon.ico"}, {"url": "gamepass.com", "siteName": "gamepass", "keywords": ["Online subscription service for video games (Microsoft).", "gamepass"], "getIcon": "https://gamepass.com/favicon.ico"}, {"url": "adobedc.net", "siteName": "adobedc", "keywords": ["Adobe Digital Cloud services", "adobedc"], "getIcon": "https://adobedc.net/favicon.ico"}, {"url": "clarium.io", "siteName": "clarium", "keywords": ["   ", "clarium"], "getIcon": "https://clarium.io/favicon.ico"}, {"url": "aaplimg.com", "siteName": "aaplimg", "keywords": ["Apple image content service", "aaplimg"], "getIcon": "https://aaplimg.com/favicon.ico"}, {"url": "tiktokv.com", "siteName": "tiktokv", "keywords": ["Domain associated with TikTok's video streaming service.", "tiktokv"], "getIcon": "https://tiktokv.com/favicon.ico"}, {"url": "kargo.com", "siteName": "kargo", "keywords": ["Digital advertising platform focusing on mobile and display ads.", "kargo"], "getIcon": "https://kargo.com/favicon.ico"}, {"url": "gameanalytics.com", "siteName": "gameanalytics", "keywords": ["Provides analytics tools for mobile and web games.", "gameanalytics"], "getIcon": "https://gameanalytics.com/favicon.ico"}, {"url": "tiktokcdn.com", "siteName": "tiktokcdn", "keywords": ["Content delivery network (CDN) used for TikTok globally.", "tiktokcdn"], "getIcon": "https://tiktokcdn.com/favicon.ico"}, {"url": "clarity.ms", "siteName": "clarity", "keywords": ["Microsoft Clarity for web analytics and insights", "clarity"], "getIcon": "https://clarity.ms/favicon.ico"}, {"url": "adition.com", "siteName": "adition", "keywords": ["Online advertising solutions", "adition"], "getIcon": "https://adition.com/favicon.ico"}, {"url": "adobe.io", "siteName": "adobe", "keywords": ["Adobe's API platform for cloud services", "adobe"], "getIcon": "https://adobe.io/favicon.ico"}, {"url": "game-sdk.com", "siteName": "game-sdk", "keywords": ["Software development kits (SDK) for game developers.", "game-sdk"], "getIcon": "https://game-sdk.com/favicon.ico"}, {"url": "tiktokcdn-us.com", "siteName": "tiktokcdn-us", "keywords": ["Content delivery network (CDN) used for TikTok's services in the U.S.", "tiktokcdn-us"], "getIcon": "https://tiktokcdn-us.com/favicon.ico"}, {"url": "ixigua.com", "siteName": "ixigua", "keywords": ["Video sharing platform part of ByteDance (similar to TikTok).", "ixigua"], "getIcon": "https://ixigua.com/favicon.ico"}, {"url": "cisco.com", "siteName": "cisco", "keywords": ["Networking hardware and software solutions", "cisco"], "getIcon": "https://cisco.com/favicon.ico"}, {"url": "adobe.com", "siteName": "adobe", "keywords": ["Adobe creative software solutions", "adobe"], "getIcon": "https://adobe.com/favicon.ico"}, {"url": "adingo.jp", "siteName": "adingo", "keywords": ["Japanese mobile ad network", "adingo"], "getIcon": "https://adingo.jp/favicon.ico"}, {"url": "fyber.com", "siteName": "fyber", "keywords": ["Mobile ad network for app developers.", "fyber"], "getIcon": "https://fyber.com/favicon.ico"}, {"url": "iterable.com", "siteName": "iterable", "keywords": ["Marketing automation platform focused on email SMS and mobile marketing.", "iterable"], "getIcon": "https://iterable.com/favicon.ico"}, {"url": "adnxs.com", "siteName": "adnxs", "keywords": ["Advertising technology and marketplace", "adnxs"], "getIcon": "https://adnxs.com/favicon.ico"}, {"url": "chocopinglate.org", "siteName": "chocopinglate", "keywords": ["   ", "chocopinglate"], "getIcon": "https://chocopinglate.org/favicon.ico"}, {"url": "tencent-cloud.net", "siteName": "tencent-cloud", "keywords": ["Cloud computing platform by Tencent.", "tencent-cloud"], "getIcon": "https://tencent-cloud.net/favicon.ico"}, {"url": "adguard.com", "siteName": "adguard", "keywords": ["Ad-blocking and privacy protection software", "adguard"], "getIcon": "https://adguard.com/favicon.ico"}, {"url": "geoedge.be", "siteName": "geoedge", "keywords": ["Provides ad verification and fraud prevention services.", "geoedge"], "getIcon": "https://geoedge.be/favicon.ico"}, {"url": "isnssdk.com", "siteName": "isnssdk", "keywords": ["isnssdk"], "getIcon": "https://isnssdk.com/favicon.ico"}, {"url": "checkpoint.com", "siteName": "checkpoint", "keywords": ["Cybersecurity software and solutions", "checkpoint"], "getIcon": "https://checkpoint.com/favicon.ico"}, {"url": "adgrx.com", "siteName": "adgrx", "keywords": ["Ad tech services", "adgrx"], "getIcon": "https://adgrx.com/favicon.ico"}, {"url": "temu.com", "siteName": "temu", "keywords": ["E-commerce platform offering a wide range of consumer goods.", "temu"], "getIcon": "https://temu.com/favicon.ico"}, {"url": "isappcloud.com", "siteName": "isappcloud", "keywords": ["isappcloud"], "getIcon": "https://isappcloud.com/favicon.ico"}, {"url": "gcdn.co", "siteName": "gcdn", "keywords": ["gcdn"], "getIcon": "https://gcdn.co/favicon.ico"}, {"url": "adform.net", "siteName": "adform", "keywords": ["Digital advertising platform", "adform"], "getIcon": "https://adform.net/favicon.ico"}, {"url": "telephony.goog", "siteName": "telephony", "keywords": ["Related to Google's telephony services.", "telephony"], "getIcon": "https://telephony.goog/favicon.ico"}, {"url": "tplinknbu.com", "siteName": "tplinknbu", "keywords": ["TP-Link's backup and storage-related services.", "tplinknbu"], "getIcon": "https://tplinknbu.com/favicon.ico"}, {"url": "centrastage.net", "siteName": "centrastage", "keywords": ["Managed IT and cloud services", "centrastage"], "getIcon": "https://centrastage.net/favicon.ico"}, {"url": "jquery.com", "siteName": "j<PERSON>y", "keywords": ["A popular JavaScript library for simplifying HTML DOM manipulation.", "j<PERSON>y"], "getIcon": "https://jquery.com/favicon.ico"}, {"url": "cedexis.net", "siteName": "cedexis", "keywords": ["Internet traffic optimization and management", "cedexis"], "getIcon": "https://cedexis.net/favicon.ico"}, {"url": "gccdn.net", "siteName": "gccdn", "keywords": ["gccdn"], "getIcon": "https://gccdn.net/favicon.ico"}, {"url": "joinhoney.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Provides a browser extension that finds online coupons and discounts.", "<PERSON><PERSON><PERSON>"], "getIcon": "https://joinhoney.com/favicon.ico"}, {"url": "telegram.org", "siteName": "telegram", "keywords": ["Messaging app focused on privacy and security.", "telegram"], "getIcon": "https://telegram.org/favicon.ico"}, {"url": "adentifi.com", "siteName": "adent<PERSON>i", "keywords": ["Digital advertising solution", "adent<PERSON>i"], "getIcon": "https://adentifi.com/favicon.ico"}, {"url": "garena.com", "siteName": "garena", "keywords": ["Online game and social platform creator of Free Fire.", "garena"], "getIcon": "https://garena.com/favicon.ico"}, {"url": "cedexis.com", "siteName": "cedexis", "keywords": ["Internet traffic management platform", "cedexis"], "getIcon": "https://cedexis.com/favicon.ico"}, {"url": "bitdefender.net", "siteName": "bitdefender", "keywords": ["Bitdefender's network services", "bitdefender"], "getIcon": "https://bitdefender.net/favicon.ico"}, {"url": "jhkkjkj.com", "siteName": "jhkkjkj", "keywords": ["jhkkjkj"], "getIcon": "https://jhkkjkj.com/favicon.ico"}, {"url": "adblockplus.org", "siteName": "adblockplus", "keywords": ["Browser extension for ad blocking", "adblockplus"], "getIcon": "https://adblockplus.org/favicon.ico"}, {"url": "terabox.com", "siteName": "terabox", "keywords": ["Cloud storage service provider.", "terabox"], "getIcon": "https://terabox.com/favicon.ico"}, {"url": "tplinkcloud.com", "siteName": "tplinkcloud", "keywords": ["Cloud service provider for TP-Link's smart home devices.", "tplinkcloud"], "getIcon": "https://tplinkcloud.com/favicon.ico"}, {"url": "cedexis-test.com", "siteName": "cedexis-test", "keywords": ["Internet traffic test services", "cedexis-test"], "getIcon": "https://cedexis-test.com/favicon.ico"}, {"url": "force.com", "siteName": "force", "keywords": ["Platform-as-a-service (PaaS) for developing and deploying apps (Salesforce).", "force"], "getIcon": "https://force.com/favicon.ico"}, {"url": "jetbrains.com", "siteName": "jetbrains", "keywords": ["Developer of integrated development environments (IDEs) and software development tools.", "jetbrains"], "getIcon": "https://jetbrains.com/favicon.ico"}, {"url": "tenor.com", "siteName": "tenor", "keywords": ["GIF search engine and platform (acquired by Google).", "tenor"], "getIcon": "https://tenor.com/favicon.ico"}, {"url": "bitdefender.com", "siteName": "bitdefender", "keywords": ["Cybersecurity and antivirus software", "bitdefender"], "getIcon": "https://bitdefender.com/favicon.ico"}, {"url": "footprintdns.com", "siteName": "footprintdns", "keywords": ["footprintdns"], "getIcon": "https://footprintdns.com/favicon.ico"}, {"url": "bing.net", "siteName": "bing", "keywords": ["Microsoft Bing's network services", "bing"], "getIcon": "https://bing.net/favicon.ico"}, {"url": "tenda.com.cn", "siteName": "tenda", "keywords": ["Chinese manufacturer of networking hardware and devices.", "tenda"], "getIcon": "https://tenda.com.cn/favicon.ico"}, {"url": "360safe.com", "siteName": "360safe", "keywords": ["Internet security software by Qihoo 360", "360safe"], "getIcon": "https://360safe.com/favicon.ico"}, {"url": "kwaicdn.com", "siteName": "kwaicdn", "keywords": ["Content delivery network (CDN) for Kwai services.", "kwaicdn"], "getIcon": "https://kwaicdn.com/favicon.ico"}, {"url": "bing.com", "siteName": "bing", "keywords": ["Microsoft's search engine", "bing"], "getIcon": "https://bing.com/favicon.ico"}, {"url": "fontawesome.com", "siteName": "fontawesome", "keywords": ["Provides vector icons and social logos for web development.", "fontawesome"], "getIcon": "https://fontawesome.com/favicon.ico"}, {"url": "wsdvs.com", "siteName": "wsdvs", "keywords": ["wsdvs"], "getIcon": "https://wsdvs.com/favicon.ico"}, {"url": "tencent.com", "siteName": "tencent", "keywords": ["Chinese multinational conglomerate involved in various tech services.", "tencent"], "getIcon": "https://tencent.com/favicon.ico"}, {"url": "kwai.net", "siteName": "kwai", "keywords": ["Domain related to Kwai services.", "kwai"], "getIcon": "https://kwai.net/favicon.ico"}, {"url": "360.cn", "siteName": "360", "keywords": ["Chinese internet security and software provider", "360"], "getIcon": "https://360.cn/favicon.ico"}, {"url": "flurry.com", "siteName": "flurry", "keywords": ["Mobile analytics and advertising platform.", "flurry"], "getIcon": "https://flurry.com/favicon.ico"}, {"url": "wps.com", "siteName": "wps", "keywords": ["Likely related to WPS Office a productivity software suite.", "wps"], "getIcon": "https://wps.com/favicon.ico"}, {"url": "kwai.com", "siteName": "kwai", "keywords": ["Shortened URL for Kwai (Kuaishou) a Chinese video sharing platform.", "kwai"], "getIcon": "https://kwai.com/favicon.ico"}, {"url": "33across.com", "siteName": "33across", "keywords": ["Data-driven advertising technology platform", "33across"], "getIcon": "https://33across.com/favicon.ico"}, {"url": "leiniao.com", "siteName": "leiniao", "keywords": ["Chinese website focused on technology and lifestyle.", "leiniao"], "getIcon": "https://leiniao.com/favicon.ico"}, {"url": "fwmrm.net", "siteName": "fwmrm", "keywords": ["Ad network for digital marketing.", "fwmrm"], "getIcon": "https://fwmrm.net/favicon.ico"}, {"url": "steamstatic.com", "siteName": "steamstatic", "keywords": ["Content delivery network (CDN) for static assets on Steam.", "steamstatic"], "getIcon": "https://steamstatic.com/favicon.ico"}, {"url": "wpadmngr.com", "siteName": "wpadmngr", "keywords": ["wpadmngr"], "getIcon": "https://wpadmngr.com/favicon.ico"}, {"url": "kwai-pro.com", "siteName": "kwai-pro", "keywords": ["Advertising platform related to Kwai (Kuaishou).", "kwai-pro"], "getIcon": "https://kwai-pro.com/favicon.ico"}, {"url": "launchdarkly.com", "siteName": "launchdarkly", "keywords": ["Feature management platform for software teams.", "launchdarkly"], "getIcon": "https://launchdarkly.com/favicon.ico"}, {"url": "2miners.com", "siteName": "2miners", "keywords": ["Cryptocurrency mining pool", "2miners"], "getIcon": "https://2miners.com/favicon.ico"}, {"url": "steamserver.net", "siteName": "steamserver", "keywords": ["Used for Steam's server-related services.", "steamserver"], "getIcon": "https://steamserver.net/favicon.ico"}, {"url": "wp.com", "siteName": "wp", "keywords": ["Used for hosting WordPress sites.", "wp"], "getIcon": "https://wp.com/favicon.ico"}, {"url": "fullstory.com", "siteName": "fullstory", "keywords": ["Provides session replay and analytics services for websites.", "fullstory"], "getIcon": "https://fullstory.com/favicon.ico"}, {"url": "larkplayerapp.com", "siteName": "lark<PERSON><PERSON><PERSON>", "keywords": ["A media player app for Android and other platforms.", "lark<PERSON><PERSON><PERSON>"], "getIcon": "https://larkplayerapp.com/favicon.ico"}, {"url": "kueezrtb.com", "siteName": "kueezrtb", "keywords": ["kueezrtb"], "getIcon": "https://kueezrtb.com/favicon.ico"}, {"url": "steampowered.com", "siteName": "steampowered", "keywords": ["Official website for Steam the gaming platform and storefront.", "steampowered"], "getIcon": "https://steampowered.com/favicon.ico"}, {"url": "worldfcdn2.com", "siteName": "worldfcdn2", "keywords": ["worldfcdn2"], "getIcon": "https://worldfcdn2.com/favicon.ico"}, {"url": "2mdn.net", "siteName": "2mdn", "keywords": ["Google's advertising network for media delivery", "2mdn"], "getIcon": "https://2mdn.net/favicon.ico"}, {"url": "freefiremobile.com", "siteName": "freefiremobile", "keywords": ["Mobile version of the Free Fire game.", "freefiremobile"], "getIcon": "https://freefiremobile.com/favicon.ico"}, {"url": "wordpress.com", "siteName": "wordpress", "keywords": ["Platform for creating and hosting websites and blogs.", "wordpress"], "getIcon": "https://wordpress.com/favicon.ico"}, {"url": "l-msedge.net", "siteName": "l-msedge", "keywords": ["Related to Microsoft Edge browser.", "l-msedge"], "getIcon": "https://l-msedge.net/favicon.ico"}, {"url": "kuaishou.com", "siteName": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["Chinese video sharing platform competitor to TikTok.", "<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://kuaishou.com/favicon.ico"}, {"url": "steamcontent.com", "siteName": "steamcontent", "keywords": ["Content delivery network (CDN) used for Steam's gaming services.", "steamcontent"], "getIcon": "https://steamcontent.com/favicon.ico"}, {"url": "forter.com", "siteName": "forter", "keywords": ["Fraud prevention service for eCommerce businesses.", "forter"], "getIcon": "https://forter.com/favicon.ico"}, {"url": "1rx.io", "siteName": "1rx", "keywords": ["Blockchain-related service", "1rx"], "getIcon": "https://1rx.io/favicon.ico"}, {"url": "withgoogle.com", "siteName": "withgoogle", "keywords": ["Likely a domain related to Google services or projects.", "withgoogle"], "getIcon": "https://withgoogle.com/favicon.ico"}, {"url": "ks-cdn.com", "siteName": "ks-cdn", "keywords": ["Content delivery network (CDN) for various services.", "ks-cdn"], "getIcon": "https://ks-cdn.com/favicon.ico"}, {"url": "kwimgs.com", "siteName": "kwimgs", "keywords": ["Image hosting service for Kwai.", "kwimgs"], "getIcon": "https://kwimgs.com/favicon.ico"}, {"url": "steamcommunity.com", "siteName": "steamcommunity", "keywords": ["Community platform for Steam gamers featuring forums guides and group interactions.", "steamcommunity"], "getIcon": "https://steamcommunity.com/favicon.ico"}, {"url": "1drv.com", "siteName": "1drv", "keywords": ["Microsoft OneDrive cloud storage service", "1drv"], "getIcon": "https://1drv.com/favicon.ico"}, {"url": "kwcdn.com", "siteName": "kwcdn", "keywords": ["CDN for Kwai services.", "kwcdn"], "getIcon": "https://kwcdn.com/favicon.ico"}, {"url": "glance-cdn.com", "siteName": "glance-cdn", "keywords": ["Provides content delivery network services.", "glance-cdn"], "getIcon": "https://glance-cdn.com/favicon.ico"}, {"url": "klaviyo.com", "siteName": "klaviyo", "keywords": ["Marketing automation platform primarily focused on email marketing.", "klaviyo"], "getIcon": "https://klaviyo.com/favicon.ico"}, {"url": "windowsupdate.com", "siteName": "windowsupdate", "keywords": ["Domain associated with Microsoft's update services for Windows OS.", "windowsupdate"], "getIcon": "https://windowsupdate.com/favicon.ico"}, {"url": "statuspage.io", "siteName": "statuspage", "keywords": ["Service status page for tracking uptime and incidents for tech services.", "statuspage"], "getIcon": "https://statuspage.io/favicon.ico"}, {"url": "163.com", "siteName": "163", "keywords": ["Chinese web portal providing news email and e-commerce services", "163"], "getIcon": "https://163.com/favicon.ico"}, {"url": "tp-link.com", "siteName": "tp-link", "keywords": ["Manufacturer of networking hardware including routers and Wi-Fi systems.", "tp-link"], "getIcon": "https://tp-link.com/favicon.ico"}, {"url": "githubusercontent.com", "siteName": "githubusercontent", "keywords": ["Used to serve raw files hosted on GitHub.", "githubusercontent"], "getIcon": "https://githubusercontent.com/favicon.ico"}, {"url": "kwaipros.com", "siteName": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["Advertising and video services platform for Kwai.", "<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://kwaipros.com/favicon.ico"}, {"url": "xiaohongshu.com", "siteName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keywords": ["Chinese social media platform for lifestyle content also known as Little Red Book.", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://xiaohongshu.com/favicon.ico"}, {"url": "static.microsoft", "siteName": "static", "keywords": ["Static content hosting for Microsoft's services.", "static"], "getIcon": "https://static.microsoft/favicon.ico"}, {"url": "igodigital.com", "siteName": "igodigital", "keywords": ["Digital marketing services platform.", "igodigital"], "getIcon": "https://igodigital.com/favicon.ico"}, {"url": "4dex.io", "siteName": "4dex", "keywords": ["Decentralized exchange for trading cryptocurrency", "4dex"], "getIcon": "https://4dex.io/favicon.ico"}, {"url": "xboxlive.com", "siteName": "xboxlive", "keywords": ["Online gaming and digital media delivery service for Xbox consoles.", "xboxlive"], "getIcon": "https://xboxlive.com/favicon.ico"}, {"url": "github.io", "siteName": "github", "keywords": ["GitHub Pages a static site hosting service.", "github"], "getIcon": "https://github.io/favicon.ico"}, {"url": "startappservice.com", "siteName": "startappservice", "keywords": ["Mobile advertising and monetization service provider.", "startappservice"], "getIcon": "https://startappservice.com/favicon.ico"}, {"url": "igamecj.com", "siteName": "igamecj", "keywords": ["igamecj"], "getIcon": "https://igamecj.com/favicon.ico"}, {"url": "x.com", "siteName": "x", "keywords": ["Historically a domain associated with PayPal currently used by X (formerly Twitter).", "x"], "getIcon": "https://x.com/favicon.ico"}, {"url": "3lift.com", "siteName": "3lift", "keywords": ["Data privacy solutions", "3lift"], "getIcon": "https://3lift.com/favicon.ico"}, {"url": "imrworldwide.com", "siteName": "imrworldwide", "keywords": ["Advertising and marketing technology company.", "imrworldwide"], "getIcon": "https://imrworldwide.com/favicon.ico"}, {"url": "github.com", "siteName": "github", "keywords": ["A platform for version control and collaboration using Git.", "github"], "getIcon": "https://github.com/favicon.ico"}, {"url": "wyzecam.com", "siteName": "wyzecam", "keywords": ["Smart home products particularly cameras and related devices.", "wyzecam"], "getIcon": "https://wyzecam.com/favicon.ico"}, {"url": "ieee.org", "siteName": "ieee", "keywords": ["Institute of Electrical and Electronics Engineers professional association for electronic engineering.", "ieee"], "getIcon": "https://ieee.org/favicon.ico"}, {"url": "3gppnetwork.org", "siteName": "3gppnetwork", "keywords": ["Organization defining standards for mobile telecommunications", "3gppnetwork"], "getIcon": "https://3gppnetwork.org/favicon.ico"}, {"url": "sunnypingdrink.com", "siteName": "sunnypingdrink", "keywords": ["sunnypingdrink"], "getIcon": "https://sunnypingdrink.com/favicon.ico"}, {"url": "imolive2.com", "siteName": "imolive2", "keywords": ["imolive2"], "getIcon": "https://imolive2.com/favicon.ico"}, {"url": "wynd.network", "siteName": "wynd", "keywords": ["wynd"], "getIcon": "https://wynd.network/favicon.ico"}, {"url": "360yield.com", "siteName": "360yield", "keywords": ["Online advertising platform by Qihoo 360", "360yield"], "getIcon": "https://360yield.com/favicon.ico"}, {"url": "id5-sync.com", "siteName": "id5-sync", "keywords": ["Provides ad tech services for user data synchronization.", "id5-sync"], "getIcon": "https://id5-sync.com/favicon.ico"}, {"url": "strpst.com", "siteName": "strpst", "keywords": ["Related to <PERSON><PERSON>'s services or resources.", "strpst"], "getIcon": "https://strpst.com/favicon.ico"}, {"url": "giphy.com", "siteName": "giphy", "keywords": ["A platform for searching and sharing GIFs.", "giphy"], "getIcon": "https://giphy.com/favicon.ico"}, {"url": "intelbras.com.br", "siteName": "intelbras", "keywords": ["Brazilian technology company specializing in security solutions networking and telecommunications.", "intelbras"], "getIcon": "https://intelbras.com.br/favicon.ico"}, {"url": "imoim.net", "siteName": "imoim", "keywords": ["Provides video and messaging services via the IMO app.", "imoim"], "getIcon": "https://imoim.net/favicon.ico"}, {"url": "chartbeat.net", "siteName": "chartbeat", "keywords": ["Web analytics and real-time tracking", "chartbeat"], "getIcon": "https://chartbeat.net/favicon.ico"}, {"url": "wuporg.com", "siteName": "wuporg", "keywords": ["Related to Windows Update or patch services.", "wuporg"], "getIcon": "https://wuporg.com/favicon.ico"}, {"url": "stripe.com", "siteName": "stripe", "keywords": ["Online payment processing for e-commerce and subscription services.", "stripe"], "getIcon": "https://stripe.com/favicon.ico"}, {"url": "icloud.com.cn", "siteName": "icloud", "keywords": ["Apple's cloud service for Chinese users.", "icloud"], "getIcon": "https://icloud.com.cn/favicon.ico"}, {"url": "imkirh.com", "siteName": "imkirh", "keywords": ["imkirh"], "getIcon": "https://imkirh.com/favicon.ico"}, {"url": "ggpht.com", "siteName": "ggpht", "keywords": ["Used by Google to serve images.", "ggpht"], "getIcon": "https://ggpht.com/favicon.ico"}, {"url": "aadrm.com", "siteName": "aadrm", "keywords": ["Digital rights management solutions", "aadrm"], "getIcon": "https://aadrm.com/favicon.ico"}, {"url": "wshareit.com", "siteName": "wshareit", "keywords": ["Platform for file sharing and media exchange.", "wshareit"], "getIcon": "https://wshareit.com/favicon.ico"}, {"url": "icloud.com", "siteName": "icloud", "keywords": ["Apple's cloud storage service.", "icloud"], "getIcon": "https://icloud.com/favicon.ico"}, {"url": "stickyadstv.com", "siteName": "stickyadstv", "keywords": ["Digital advertising platform specializing in video ads.", "stickyadstv"], "getIcon": "https://stickyadstv.com/favicon.ico"}, {"url": "a2z.com", "siteName": "a2z", "keywords": ["E-commerce platform for various industries", "a2z"], "getIcon": "https://a2z.com/favicon.ico"}, {"url": "ggblueshark.com", "siteName": "ggblueshark", "keywords": ["ggblueshark"], "getIcon": "https://ggblueshark.com/favicon.ico"}, {"url": "inmobi.com", "siteName": "inmobi", "keywords": ["Mobile advertising network and platform.", "inmobi"], "getIcon": "https://inmobi.com/favicon.ico"}, {"url": "intel.com", "siteName": "intel", "keywords": ["Leading technology company known for microprocessors and chipsets.", "intel"], "getIcon": "https://intel.com/favicon.ico"}, {"url": "icloud-content.com", "siteName": "icloud-content", "keywords": ["Used for iCloud media content delivery.", "icloud-content"], "getIcon": "https://icloud-content.com/favicon.ico"}, {"url": "susercontent.com", "siteName": "susercontent", "keywords": ["Likely related to Shopee's content delivery services.", "susercontent"], "getIcon": "https://susercontent.com/favicon.ico"}, {"url": "wechat.com", "siteName": "wechat", "keywords": ["Chinese messaging social media and mobile payment app developed by Tencent.", "wechat"], "getIcon": "https://wechat.com/favicon.ico"}, {"url": "a-msedge.net", "siteName": "a-msedge", "keywords": ["Microsoft Edge browser network services", "a-msedge"], "getIcon": "https://a-msedge.net/favicon.ico"}, {"url": "geotrust.com", "siteName": "geotrust", "keywords": ["Digital certificate and security provider.", "geotrust"], "getIcon": "https://geotrust.com/favicon.ico"}, {"url": "inkuai.com", "siteName": "inkuai", "keywords": ["inkuai"], "getIcon": "https://inkuai.com/favicon.ico"}, {"url": "int08h.com", "siteName": "int08h", "keywords": ["int08h"], "getIcon": "https://int08h.com/favicon.ico"}, {"url": "icanhazip.com", "siteName": "icanhazip", "keywords": ["A service that provides the user's public IP address.", "icanhazip"], "getIcon": "https://icanhazip.com/favicon.ico"}, {"url": "surfshark.com", "siteName": "surfshark", "keywords": ["VPN service provider focused on privacy and security.", "surfshark"], "getIcon": "https://surfshark.com/favicon.ico"}, {"url": "webrootcloudav.com", "siteName": "webrootcloudav", "keywords": ["Cloud-based antivirus and cybersecurity solutions from Webroot.", "webrootcloudav"], "getIcon": "https://webrootcloudav.com/favicon.ico"}, {"url": "ingage.tech", "siteName": "ingage", "keywords": ["Provides mobile and digital marketing solutions.", "ingage"], "getIcon": "https://ingage.tech/favicon.ico"}, {"url": "chartbeat.com", "siteName": "chartbeat", "keywords": ["Web analytics and real-time data monitoring", "chartbeat"], "getIcon": "https://chartbeat.com/favicon.ico"}, {"url": "ampproject.org", "siteName": "ampproject", "keywords": ["Open-source project for Accelerated Mobile Pages (AMP)", "ampproject"], "getIcon": "https://ampproject.org/favicon.ico"}, {"url": "a-mo.net", "siteName": "a-mo", "keywords": ["Web hosting services", "a-mo"], "getIcon": "https://a-mo.net/favicon.ico"}, {"url": "godaddy.com", "siteName": "<PERSON><PERSON>dy", "keywords": ["Domain registrar and web hosting company.", "<PERSON><PERSON>dy"], "getIcon": "https://godaddy.com/favicon.ico"}, {"url": "supersonicads.com", "siteName": "supersonicads", "keywords": ["Mobile ad network and monetization platform.", "supersonicads"], "getIcon": "https://supersonicads.com/favicon.ico"}, {"url": "indexww.com", "siteName": "indexww", "keywords": ["indexww"], "getIcon": "https://indexww.com/favicon.ico"}, {"url": "webex.com", "siteName": "webex", "keywords": ["Cisco's video conferencing and collaboration platform.", "webex"], "getIcon": "https://webex.com/favicon.ico"}, {"url": "ipredictive.com", "siteName": "ipredictive", "keywords": ["Provides predictive analytics and data science solutions.", "ipredictive"], "getIcon": "https://ipredictive.com/favicon.ico"}, {"url": "go-mpulse.net", "siteName": "go-mpulse", "keywords": ["Used by Akamai for analytics and monitoring services.", "go-mpulse"], "getIcon": "https://go-mpulse.net/favicon.ico"}, {"url": "amplitude.com", "siteName": "amplitude", "keywords": ["Product analytics platform", "amplitude"], "getIcon": "https://amplitude.com/favicon.ico"}, {"url": "supercell.com", "siteName": "supercell", "keywords": ["Game development company known for mobile games like Clash of Clans.", "supercell"], "getIcon": "https://supercell.com/favicon.ico"}, {"url": "anydesk.com", "siteName": "anydesk", "keywords": ["Remote desktop software and support service", "anydesk"], "getIcon": "https://anydesk.com/favicon.ico"}, {"url": "ipinfo.io", "siteName": "ipinfo", "keywords": ["Provides IP geolocation data and services.", "ipinfo"], "getIcon": "https://ipinfo.io/favicon.ico"}, {"url": "weather.com", "siteName": "weather", "keywords": ["Website providing weather forecasts news and data (owned by The Weather Company).", "weather"], "getIcon": "https://weather.com/favicon.ico"}, {"url": "instabug.com", "siteName": "instabug", "keywords": ["Provides bug and crash reporting tools for mobile apps.", "instabug"], "getIcon": "https://instabug.com/favicon.ico"}, {"url": "amp-endpoint3.com", "siteName": "amp-endpoint3", "keywords": ["AMP service endpoint for mobile performance", "amp-endpoint3"], "getIcon": "https://amp-endpoint3.com/favicon.ico"}, {"url": "aniview.com", "siteName": "an<PERSON><PERSON><PERSON>", "keywords": ["Video ad network for monetization", "an<PERSON><PERSON><PERSON>"], "getIcon": "https://aniview.com/favicon.ico"}, {"url": "gmail.com", "siteName": "gmail", "keywords": ["Google's email service.", "gmail"], "getIcon": "https://gmail.com/favicon.ico"}, {"url": "yimg.com", "siteName": "yimg", "keywords": ["Domain used for hosting Yahoo's images and media.", "yimg"], "getIcon": "https://yimg.com/favicon.ico"}, {"url": "ipify.org", "siteName": "ipify", "keywords": ["A simple public IP address API service.", "ipify"], "getIcon": "https://ipify.org/favicon.ico"}, {"url": "teamviewer.com", "siteName": "teamviewer", "keywords": ["Remote access and remote desktop software.", "teamviewer"], "getIcon": "https://teamviewer.com/favicon.ico"}, {"url": "amp-endpoint2.com", "siteName": "amp-endpoint2", "keywords": ["AMP (Accelerated Mobile Pages) service endpoint", "amp-endpoint2"], "getIcon": "https://amp-endpoint2.com/favicon.ico"}, {"url": "wcdnga.com", "siteName": "wcdnga", "keywords": ["wcdnga"], "getIcon": "https://wcdnga.com/favicon.ico"}, {"url": "innovid.com", "siteName": "innovid", "keywords": ["Video advertising platform for marketers and advertisers.", "innovid"], "getIcon": "https://innovid.com/favicon.ico"}, {"url": "android.com", "siteName": "android", "keywords": ["Official website for the Android operating system", "android"], "getIcon": "https://android.com/favicon.ico"}, {"url": "spotify.com", "siteName": "spotify", "keywords": ["Music streaming service offering personalized playlists and podcasts.", "spotify"], "getIcon": "https://spotify.com/favicon.ico"}, {"url": "yieldmo.com", "siteName": "yieldmo", "keywords": ["Advertising technology company focused on mobile and display ads.", "yieldmo"], "getIcon": "https://yieldmo.com/favicon.ico"}, {"url": "glpals.com", "siteName": "glpals", "keywords": ["glpals"], "getIcon": "https://glpals.com/favicon.ico"}, {"url": "tealiumiq.com", "siteName": "tealiumiq", "keywords": ["Customer data platform (CDP) for marketing data collection and management.", "tealiumiq"], "getIcon": "https://tealiumiq.com/favicon.ico"}, {"url": "ip-api.com", "siteName": "ip-api", "keywords": ["A service that provides IP geolocation data.", "ip-api"], "getIcon": "https://ip-api.com/favicon.ico"}, {"url": "wbx2.com", "siteName": "wbx2", "keywords": ["Likely related to Webex Cisco's video conferencing service.", "wbx2"], "getIcon": "https://wbx2.com/favicon.ico"}, {"url": "amung.us", "siteName": "amung", "keywords": ["User engagement and web analytics service", "amung"], "getIcon": "https://amung.us/favicon.ico"}, {"url": "inner-active.mobi", "siteName": "inner-active", "keywords": ["Mobile ad network.", "inner-active"], "getIcon": "https://inner-active.mobi/favicon.ico"}, {"url": "globo.com", "siteName": "globo", "keywords": ["Brazilian multimedia corporation operating TV network and online news.", "globo"], "getIcon": "https://globo.com/favicon.ico"}, {"url": "intuit.com", "siteName": "intuit", "keywords": ["Financial software company known for products like TurboTax and QuickBooks.", "intuit"], "getIcon": "https://intuit.com/favicon.ico"}, {"url": "teads.tv", "siteName": "teads", "keywords": ["Video advertising platform offering native ad formats.", "teads"], "getIcon": "https://teads.tv/favicon.ico"}, {"url": "spot.im", "siteName": "spot", "keywords": ["Provides engagement tools for online communities and websites.", "spot"], "getIcon": "https://spot.im/favicon.ico"}, {"url": "apple-cloudkit.com", "siteName": "apple-cloudkit", "keywords": ["Apple's CloudKit services for iOS developers", "apple-cloudkit"], "getIcon": "https://apple-cloudkit.com/favicon.ico"}, {"url": "yellowblue.io", "siteName": "yellowblue", "keywords": ["yellowblue"], "getIcon": "https://yellowblue.io/favicon.ico"}, {"url": "globalsign.com", "siteName": "globalsign", "keywords": ["Digital certificate authority offering SSL certificates and other security solutions.", "globalsign"], "getIcon": "https://globalsign.com/favicon.ico"}, {"url": "wattpad.com", "siteName": "wattpad", "keywords": ["Platform for reading and writing stories focused on user-generated content.", "wattpad"], "getIcon": "https://wattpad.com/favicon.ico"}, {"url": "intercom.io", "siteName": "intercom", "keywords": ["Customer messaging platform for sales marketing and support.", "intercom"], "getIcon": "https://intercom.io/favicon.ico"}, {"url": "discomax.com", "siteName": "discomax", "keywords": ["   ", "discomax"], "getIcon": "https://discomax.com/favicon.ico"}, {"url": "inmobicdn.net", "siteName": "inmobicdn", "keywords": ["Content delivery network (CDN) for InMobi services.", "inmobicdn"], "getIcon": "https://inmobicdn.net/favicon.ico"}, {"url": "toobit.com", "siteName": "toobit", "keywords": ["toobit"], "getIcon": "https://toobit.com/favicon.ico"}, {"url": "blogspot.com", "siteName": "blogspot", "keywords": ["Blogging platform owned by Google", "blogspot"], "getIcon": "https://blogspot.com/favicon.ico"}, {"url": "appier.net", "siteName": "appier", "keywords": ["Marketing automation and AI platform", "appier"], "getIcon": "https://appier.net/favicon.ico"}, {"url": "spo-msedge.net", "siteName": "spo-msedge", "keywords": ["Related to Microsoft Edge services and content delivery.", "spo-msedge"], "getIcon": "https://spo-msedge.net/favicon.ico"}, {"url": "yastatic.net", "siteName": "yastatic", "keywords": ["Static content hosting related to Yandex's services.", "yastatic"], "getIcon": "https://yastatic.net/favicon.ico"}, {"url": "glanceapis.com", "siteName": "glanceapis", "keywords": ["API platform for Glance media solutions.", "glanceapis"], "getIcon": "https://glanceapis.com/favicon.ico"}, {"url": "walmart.com", "siteName": "walmart", "keywords": ["E-commerce giant and retail store providing a wide range of products.", "walmart"], "getIcon": "https://walmart.com/favicon.ico"}, {"url": "cootlogix.com", "siteName": "cootlogix", "keywords": ["Web services provider", "cootlogix"], "getIcon": "https://cootlogix.com/favicon.ico"}, {"url": "intentiq.com", "siteName": "intentiq", "keywords": ["Provides intent-based marketing and advertising technology.", "intentiq"], "getIcon": "https://intentiq.com/favicon.ico"}, {"url": "tapad.com", "siteName": "tapad", "keywords": ["Provides marketing and advertising solutions based on cross-device tracking.", "tapad"], "getIcon": "https://tapad.com/favicon.ico"}, {"url": "yandexadexchange.net", "siteName": "yandexadexchange", "keywords": ["Yandex's ad exchange network.", "yandexadexchange"], "getIcon": "https://yandexadexchange.net/favicon.ico"}, {"url": "split.io", "siteName": "split", "keywords": ["Feature flagging and experimentation platform for developers.", "split"], "getIcon": "https://split.io/favicon.ico"}, {"url": "appcenter.ms", "siteName": "appcenter", "keywords": ["Microsoft's mobile app lifecycle management platform", "appcenter"], "getIcon": "https://appcenter.ms/favicon.ico"}, {"url": "digitaloceanspaces.com", "siteName": "digitaloceanspaces", "keywords": ["Cloud storage services by DigitalOcean", "digitaloceanspaces"], "getIcon": "https://digitaloceanspaces.com/favicon.ico"}, {"url": "cookielaw.org", "siteName": "cookielaw", "keywords": ["Cookie law and consent management platform", "cookielaw"], "getIcon": "https://cookielaw.org/favicon.ico"}, {"url": "taobao.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Chinese e-commerce platform owned by Alibaba.", "<PERSON><PERSON><PERSON>"], "getIcon": "https://taobao.com/favicon.ico"}, {"url": "agkn.com", "siteName": "agkn", "keywords": ["Audience measurement and targeting solutions", "agkn"], "getIcon": "https://agkn.com/favicon.ico"}, {"url": "whecloud.com", "siteName": "whecloud", "keywords": ["whecloud"], "getIcon": "https://whecloud.com/favicon.ico"}, {"url": "yandex.ru", "siteName": "yandex", "keywords": ["Russian domain for Yandex's main search engine services.", "yandex"], "getIcon": "https://yandex.ru/favicon.ico"}, {"url": "app-measurement.com", "siteName": "app-measurement", "keywords": ["Google Analytics and measurement services", "app-measurement"], "getIcon": "https://app-measurement.com/favicon.ico"}, {"url": "digicert.com", "siteName": "digicert", "keywords": ["Digital security and certificate services", "digicert"], "getIcon": "https://digicert.com/favicon.ico"}, {"url": "cookiebot.com", "siteName": "cookiebot", "keywords": ["Cookie consent management platform", "cookiebot"], "getIcon": "https://cookiebot.com/favicon.ico"}, {"url": "everesttech.net", "siteName": "everesttech", "keywords": ["Provides advertising technology services.", "everesttech"], "getIcon": "https://everesttech.net/favicon.ico"}, {"url": "cloudflare.net", "siteName": "cloudflare", "keywords": ["Cloudflare's network services", "cloudflare"], "getIcon": "https://cloudflare.net/favicon.ico"}, {"url": "byteoversea.net", "siteName": "byteoversea", "keywords": ["ByteDance's international CDN services", "byteoversea"], "getIcon": "https://byteoversea.net/favicon.ico"}, {"url": "speedtest.net", "siteName": "speedtest", "keywords": ["Popular website for testing internet connection speed.", "speedtest"], "getIcon": "https://speedtest.net/favicon.ico"}, {"url": "taboola.com", "siteName": "taboola", "keywords": ["Content discovery platform providing recommendations for websites.", "taboola"], "getIcon": "https://taboola.com/favicon.ico"}, {"url": "whatsapp.net", "siteName": "whatsapp", "keywords": ["Related to WhatsApp's network services.", "whatsapp"], "getIcon": "https://whatsapp.net/favicon.ico"}, {"url": "afcdn.net", "siteName": "afcdn", "keywords": ["Content delivery network (CDN) for media and ads", "afcdn"], "getIcon": "https://afcdn.net/favicon.ico"}, {"url": "yandex.net", "siteName": "yandex", "keywords": ["Domain used for Yandex's network services.", "yandex"], "getIcon": "https://yandex.net/favicon.ico"}, {"url": "conviva.com", "siteName": "conviva", "keywords": ["Streaming video performance analytics", "conviva"], "getIcon": "https://conviva.com/favicon.ico"}, {"url": "cloudflare.com", "siteName": "cloudflare", "keywords": ["Web performance and security company", "cloudflare"], "getIcon": "https://cloudflare.com/favicon.ico"}, {"url": "spbycdn.com", "siteName": "spbycdn", "keywords": ["Content delivery network (CDN) for video and streaming services.", "spbycdn"], "getIcon": "https://spbycdn.com/favicon.ico"}, {"url": "byteoversea.com", "siteName": "byteoversea", "keywords": ["ByteDance's services for international markets", "byteoversea"], "getIcon": "https://byteoversea.com/favicon.ico"}, {"url": "demdex.net", "siteName": "demdex", "keywords": ["Adobe demographic data platform", "demdex"], "getIcon": "https://demdex.net/favicon.ico"}, {"url": "eum-appdynamics.com", "siteName": "eum-appdynamics", "keywords": ["Provides application performance management and IT monitoring.", "eum-appdynamics"], "getIcon": "https://eum-appdynamics.com/favicon.ico"}, {"url": "whatsapp.com", "siteName": "whatsapp", "keywords": ["Messaging platform offering text voice and video services owned by Meta.", "whatsapp"], "getIcon": "https://whatsapp.com/favicon.ico"}, {"url": "afafb.com", "siteName": "afafb", "keywords": ["Digital advertising platform", "afafb"], "getIcon": "https://afafb.com/favicon.ico"}, {"url": "swishapps.ai", "siteName": "swisha<PERSON>", "keywords": ["AI-based mobile app development platform.", "swisha<PERSON>"], "getIcon": "https://swishapps.ai/favicon.ico"}, {"url": "cloudflare-dns.com", "siteName": "cloudflare-dns", "keywords": ["DNS resolution service by Cloudflare", "cloudflare-dns"], "getIcon": "https://cloudflare-dns.com/favicon.ico"}, {"url": "spamhaus.org", "siteName": "spamhaus", "keywords": ["Internet security organization focusing on preventing spam and malicious online activity.", "spamhaus"], "getIcon": "https://spamhaus.org/favicon.ico"}, {"url": "contextweb.com", "siteName": "contextweb", "keywords": ["Digital advertising solutions", "contextweb"], "getIcon": "https://contextweb.com/favicon.ico"}, {"url": "eu.org", "siteName": "eu", "keywords": ["A free domain registration service focused on nonprofit organizations.", "eu"], "getIcon": "https://eu.org/favicon.ico"}, {"url": "byteintl.com", "siteName": "by<PERSON><PERSON>", "keywords": ["Byte International services", "by<PERSON><PERSON>"], "getIcon": "https://byteintl.com/favicon.ico"}, {"url": "rings.solutions", "siteName": "rings", "keywords": ["Related to Ring's products and solutions for home security.", "rings"], "getIcon": "https://rings.solutions/favicon.ico"}, {"url": "yahoo.co.jp", "siteName": "yahoo", "keywords": ["Japanese version of Yahoo providing search and online services.", "yahoo"], "getIcon": "https://yahoo.co.jp/favicon.ico"}, {"url": "weerrhoop.cc", "siteName": "weerrhoop", "keywords": ["weerrhoop"], "getIcon": "https://weerrhoop.cc/favicon.ico"}, {"url": "bootstrapcdn.com", "siteName": "bootstrapcdn", "keywords": ["Content delivery network for Bootstrap framework", "bootstrapcdn"], "getIcon": "https://bootstrapcdn.com/favicon.ico"}, {"url": "contentsquare.net", "siteName": "contentsquare", "keywords": ["Customer experience analytics platform", "contentsquare"], "getIcon": "https://contentsquare.net/favicon.ico"}, {"url": "cloudapp.net", "siteName": "cloudapp", "keywords": ["Cloud application services", "cloudapp"], "getIcon": "https://cloudapp.net/favicon.ico"}, {"url": "dotomi.com", "siteName": "dotomi", "keywords": ["Digital advertising network", "dotomi"], "getIcon": "https://dotomi.com/favicon.ico"}, {"url": "swiftkey.com", "siteName": "swiftkey", "keywords": ["Keyboard app for mobile devices (owned by Microsoft).", "swiftkey"], "getIcon": "https://swiftkey.com/favicon.ico"}, {"url": "soundcloud.com", "siteName": "soundcloud", "keywords": ["Music streaming platform focused on independent artists and content.", "soundcloud"], "getIcon": "https://soundcloud.com/favicon.ico"}, {"url": "ring.com", "siteName": "ring", "keywords": ["Home security and smart home products and services owned by Amazon.", "ring"], "getIcon": "https://ring.com/favicon.ico"}, {"url": "byteglb.com", "siteName": "byteglb", "keywords": ["ByteGlobal platform for global data services", "byteglb"], "getIcon": "https://byteglb.com/favicon.ico"}, {"url": "ya.ru", "siteName": "ya", "keywords": ["Russian search engine part of Yandex.", "ya"], "getIcon": "https://ya.ru/favicon.ico"}, {"url": "akamai.net", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Akamai's cloud and network services", "<PERSON><PERSON><PERSON>"], "getIcon": "https://akamai.net/favicon.ico"}, {"url": "eu-1-id5-sync.com", "siteName": "eu-1-id5-sync", "keywords": ["Used for identity management and ad targeting.", "eu-1-id5-sync"], "getIcon": "https://eu-1-id5-sync.com/favicon.ico"}, {"url": "docker.io", "siteName": "docker", "keywords": ["Docker containerization platform", "docker"], "getIcon": "https://docker.io/favicon.ico"}, {"url": "svcmot.com", "siteName": "svcmot", "keywords": ["svcmot"], "getIcon": "https://svcmot.com/favicon.ico"}, {"url": "zdassets.com", "siteName": "zdassets", "keywords": ["Domain related to ZDNet a technology news website.", "zdassets"], "getIcon": "https://zdassets.com/favicon.ico"}, {"url": "cloudfront.net", "siteName": "cloudfront", "keywords": ["Content delivery network (CDN) by Amazon Web Services (AWS)", "cloudfront"], "getIcon": "https://cloudfront.net/favicon.ico"}, {"url": "connatix.com", "siteName": "connatix", "keywords": ["Video ad tech platform", "connatix"], "getIcon": "https://connatix.com/favicon.ico"}, {"url": "xiaomi.net", "siteName": "xia<PERSON>", "keywords": ["Related to Xiaomi's network services and products.", "xia<PERSON>"], "getIcon": "https://xiaomi.net/favicon.ico"}, {"url": "richaudience.com", "siteName": "richaudience", "keywords": ["Audience measurement and targeting platform.", "richaudience"], "getIcon": "https://richaudience.com/favicon.ico"}, {"url": "starmakerstudios.com", "siteName": "starmakerstudios", "keywords": ["Developer of the StarMaker app for music and video creation.", "starmakerstudios"], "getIcon": "https://starmakerstudios.com/favicon.ico"}, {"url": "bytefcdn-ttpeu.com", "siteName": "bytefcdn-ttpeu", "keywords": ["ByteDance's European CDN services", "bytefcdn-ttpeu"], "getIcon": "https://bytefcdn-ttpeu.com/favicon.ico"}, {"url": "booking.com", "siteName": "booking", "keywords": ["Online travel booking platform", "booking"], "getIcon": "https://booking.com/favicon.ico"}, {"url": "etoote.com", "siteName": "etoote", "keywords": ["A travel website offering hotel bookings and related services.", "etoote"], "getIcon": "https://etoote.com/favicon.ico"}, {"url": "akamai.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Cloud services and content delivery network (CDN) provider", "<PERSON><PERSON><PERSON>"], "getIcon": "https://akamai.com/favicon.ico"}, {"url": "company-target.com", "siteName": "company-target", "keywords": ["Ad targeting and audience measurement", "company-target"], "getIcon": "https://company-target.com/favicon.ico"}, {"url": "windows.com", "siteName": "windows", "keywords": ["Official website for Microsoft's Windows operating system and services.", "windows"], "getIcon": "https://windows.com/favicon.ico"}, {"url": "atlassian.com", "siteName": "atlassian", "keywords": ["Collaboration software company (Jira Confluence)", "atlassian"], "getIcon": "https://atlassian.com/favicon.ico"}, {"url": "cloudflarestatus.com", "siteName": "cloudflar<PERSON><PERSON>", "keywords": ["Cloudflare status and monitoring services", "cloudflar<PERSON><PERSON>"], "getIcon": "https://cloudflarestatus.com/favicon.ico"}, {"url": "svc.ms", "siteName": "svc", "keywords": ["Related to Microsoft's service infrastructure (uncertain).", "svc"], "getIcon": "https://svc.ms/favicon.ico"}, {"url": "yximgs.com", "siteName": "yximgs", "keywords": ["Likely related to image hosting or content delivery services.", "yximgs"], "getIcon": "https://yximgs.com/favicon.ico"}, {"url": "starlink.com", "siteName": "starlink", "keywords": ["Satellite internet service provided by SpaceX.", "starlink"], "getIcon": "https://starlink.com/favicon.ico"}, {"url": "docker.com", "siteName": "docker", "keywords": ["Platform for developing and deploying containerized applications", "docker"], "getIcon": "https://docker.com/favicon.ico"}, {"url": "xiaomi.com", "siteName": "xia<PERSON>", "keywords": ["Chinese electronics company known for smartphones and smart home products.", "xia<PERSON>"], "getIcon": "https://xiaomi.com/favicon.ico"}, {"url": "bytefcdn-oversea.com", "siteName": "bytefcdn-oversea", "keywords": ["ByteDance's overseas content delivery network", "bytefcdn-oversea"], "getIcon": "https://bytefcdn-oversea.com/favicon.ico"}, {"url": "rfihub.com", "siteName": "r<PERSON><PERSON>b", "keywords": ["Digital advertising network platform.", "r<PERSON><PERSON>b"], "getIcon": "https://rfihub.com/favicon.ico"}, {"url": "espncdn.com", "siteName": "espncdn", "keywords": ["Content delivery network for ESPN media.", "espncdn"], "getIcon": "https://espncdn.com/favicon.ico"}, {"url": "wikipedia.org", "siteName": "wikipedia", "keywords": ["Free online encyclopedia part of the Wikimedia Foundation.", "wikipedia"], "getIcon": "https://wikipedia.org/favicon.ico"}, {"url": "cloudflareinsights.com", "siteName": "cloudflareinsights", "keywords": ["Web performance insights by Cloudflare", "cloudflareinsights"], "getIcon": "https://cloudflareinsights.com/favicon.ico"}, {"url": "akadns.net", "siteName": "akadns", "keywords": ["DNS service provided by Akamai Technologies", "akadns"], "getIcon": "https://akadns.net/favicon.ico"}, {"url": "crwdcntrl.net", "siteName": "crwdcntrl", "keywords": ["Ad tech platform for targeting and audience data", "crwdcntrl"], "getIcon": "https://crwdcntrl.net/favicon.ico"}, {"url": "arubanetworks.com", "siteName": "arubanetworks", "keywords": ["Enterprise networking and wireless solutions", "arubanetworks"], "getIcon": "https://arubanetworks.com/favicon.ico"}, {"url": "dns.google", "siteName": "dns", "keywords": ["Google's public DNS resolver service", "dns"], "getIcon": "https://dns.google/favicon.ico"}, {"url": "yandex.com", "siteName": "yandex", "keywords": ["Russian search engine and technology company.", "yandex"], "getIcon": "https://yandex.com/favicon.ico"}, {"url": "bytedance.com", "siteName": "bytedance", "keywords": ["Chinese tech company behind TikTok and other apps", "bytedance"], "getIcon": "https://bytedance.com/favicon.ico"}, {"url": "wikimedia.org", "siteName": "wikimedia", "keywords": ["Organization that supports Wikimedia projects including Wikipedia.", "wikimedia"], "getIcon": "https://wikimedia.org/favicon.ico"}, {"url": "flashtalking.com", "siteName": "flashtalking", "keywords": ["Ad server platform for digital advertising.", "flashtalking"], "getIcon": "https://flashtalking.com/favicon.ico"}, {"url": "creativecdn.com", "siteName": "creativecdn", "keywords": ["Creative content and delivery network services", "creativecdn"], "getIcon": "https://creativecdn.com/favicon.ico"}, {"url": "socdm.com", "siteName": "socdm", "keywords": ["socdm"], "getIcon": "https://socdm.com/favicon.ico"}, {"url": "ssl-images-amazon.com", "siteName": "ssl-images-amazon", "keywords": ["Domain used by Amazon for serving SSL-secured image assets.", "ssl-images-amazon"], "getIcon": "https://ssl-images-amazon.com/favicon.ico"}, {"url": "facebook.net", "siteName": "facebook", "keywords": ["A subdomain used for Facebook services.", "facebook"], "getIcon": "https://facebook.net/favicon.ico"}, {"url": "firefox.com", "siteName": "firefox", "keywords": ["Web browser developed by Mozilla.", "firefox"], "getIcon": "https://firefox.com/favicon.ico"}, {"url": "youtube.com", "siteName": "youtube", "keywords": ["Video-sharing platform for user-generated content.", "youtube"], "getIcon": "https://youtube.com/favicon.ico"}, {"url": "duckdns.org", "siteName": "duckdns", "keywords": ["Free dynamic DNS service", "duckdns"], "getIcon": "https://duckdns.org/favicon.ico"}, {"url": "braze.eu", "siteName": "braze", "keywords": ["Braze services for Europe", "braze"], "getIcon": "https://braze.eu/favicon.ico"}, {"url": "redditmedia.com", "siteName": "redditmedia", "keywords": ["Related to Reddit's media and content services.", "redditmedia"], "getIcon": "https://redditmedia.com/favicon.ico"}, {"url": "comcast.net", "siteName": "comcast", "keywords": ["Telecommunications and media services provider", "comcast"], "getIcon": "https://comcast.net/favicon.ico"}, {"url": "ttvnw.net", "siteName": "ttvnw", "keywords": ["Network services associated with Twitch.", "ttvnw"], "getIcon": "https://ttvnw.net/favicon.ico"}, {"url": "discordapp.net", "siteName": "discordapp", "keywords": ["Discord's network services", "discordapp"], "getIcon": "https://discordapp.net/favicon.ico"}, {"url": "crazyegg.com", "siteName": "<PERSON><PERSON>g", "keywords": ["Website optimization and user analytics tool", "<PERSON><PERSON>g"], "getIcon": "https://crazyegg.com/favicon.ico"}, {"url": "exelator.com", "siteName": "exelator", "keywords": ["Provides advertising technology solutions.", "exelator"], "getIcon": "https://exelator.com/favicon.ico"}, {"url": "yahoo.com", "siteName": "yahoo", "keywords": ["Popular search engine and web portal.", "yahoo"], "getIcon": "https://yahoo.com/favicon.ico"}, {"url": "springserve.com", "siteName": "springserve", "keywords": ["Advertising technology platform for video and display ads.", "springserve"], "getIcon": "https://springserve.com/favicon.ico"}, {"url": "samsungapps.com", "siteName": "samsungapps", "keywords": ["Used for Samsung's app store and app distribution services.", "samsungapps"], "getIcon": "https://samsungapps.com/favicon.ico"}, {"url": "dual-s-msedge.net", "siteName": "dual-s-msedge", "keywords": ["Microsoft Edge network services", "dual-s-msedge"], "getIcon": "https://dual-s-msedge.net/favicon.ico"}, {"url": "snssdk.com", "siteName": "snssdk", "keywords": ["Related to ByteDance's SDK for mobile apps.", "snssdk"], "getIcon": "https://snssdk.com/favicon.ico"}, {"url": "firebaseio.com", "siteName": "firebaseio", "keywords": ["Real-time database service by Firebase (Google).", "firebaseio"], "getIcon": "https://firebaseio.com/favicon.ico"}, {"url": "ttoverseaus.net", "siteName": "t<PERSON><PERSON><PERSON>", "keywords": ["t<PERSON><PERSON><PERSON>"], "getIcon": "https://ttoverseaus.net/favicon.ico"}, {"url": "opera-api2.com", "siteName": "opera-api2", "keywords": ["Used for API services related to the Opera browser.", "opera-api2"], "getIcon": "https://opera-api2.com/favicon.ico"}, {"url": "youtube-nocookie.com", "siteName": "youtube-nocookie", "keywords": ["YouTube domain used for embedding videos without tracking cookies.", "youtube-nocookie"], "getIcon": "https://youtube-nocookie.com/favicon.ico"}, {"url": "coloros.com", "siteName": "coloros", "keywords": ["Mobile operating system by Oppo", "coloros"], "getIcon": "https://coloros.com/favicon.ico"}, {"url": "crashlytics.com", "siteName": "crashlytics", "keywords": ["App crash reporting and analytics platform", "crashlytics"], "getIcon": "https://crashlytics.com/favicon.ico"}, {"url": "braze.com", "siteName": "braze", "keywords": ["Customer engagement and marketing automation platform", "braze"], "getIcon": "https://braze.com/favicon.ico"}, {"url": "reddit.com", "siteName": "reddit", "keywords": ["Popular social media platform focused on news aggregation and community discussion.", "reddit"], "getIcon": "https://reddit.com/favicon.ico"}, {"url": "spotifycdn.com", "siteName": "spotifycdn", "keywords": ["Content delivery network (CDN) for streaming music and podcasts via Spotify.", "spotifycdn"], "getIcon": "https://spotifycdn.com/favicon.ico"}, {"url": "example.org", "siteName": "example", "keywords": ["A domain used for documentation and testing purposes.", "example"], "getIcon": "https://example.org/favicon.ico"}, {"url": "ttlivecdn.com", "siteName": "ttlivecdn", "keywords": ["Content delivery network (CDN) for live streaming services.", "ttlivecdn"], "getIcon": "https://ttlivecdn.com/favicon.ico"}, {"url": "samsungacr.com", "siteName": "samsungacr", "keywords": ["Related to Samsung's appliance and consumer electronics products.", "samsungacr"], "getIcon": "https://samsungacr.com/favicon.ico"}, {"url": "snaptube.app", "siteName": "snaptube", "keywords": ["App for downloading videos from various platforms.", "snaptube"], "getIcon": "https://snaptube.app/favicon.ico"}, {"url": "ubuntu.com", "siteName": "ubuntu", "keywords": ["Open-source Linux operating system popular for cloud server and desktop computing.", "ubuntu"], "getIcon": "https://ubuntu.com/favicon.ico"}, {"url": "coinbase.com", "siteName": "coinbase", "keywords": ["Cryptocurrency exchange platform", "coinbase"], "getIcon": "https://coinbase.com/favicon.ico"}, {"url": "fengpongshu.com", "siteName": "fengpongshu", "keywords": ["fengpongshu"], "getIcon": "https://fengpongshu.com/favicon.ico"}, {"url": "opera-api.com", "siteName": "opera-api", "keywords": ["Used for API services related to the Opera browser.", "opera-api"], "getIcon": "https://opera-api.com/favicon.ico"}, {"url": "cqloud.com", "siteName": "cqloud", "keywords": ["Cloud services", "cqloud"], "getIcon": "https://cqloud.com/favicon.ico"}, {"url": "yotpo.com", "siteName": "yotpo", "keywords": ["E-commerce marketing platform focused on reviews loyalty and customer engagement.", "yotpo"], "getIcon": "https://yotpo.com/favicon.ico"}, {"url": "brave.com", "siteName": "brave", "keywords": ["Privacy-focused web browser", "brave"], "getIcon": "https://brave.com/favicon.ico"}, {"url": "dbankcdn.cn", "siteName": "dbankcdn", "keywords": ["Chinese CDN services", "dbankcdn"], "getIcon": "https://dbankcdn.cn/favicon.ico"}, {"url": "example.com", "siteName": "example", "keywords": ["A domain used for documentation and testing purposes.", "example"], "getIcon": "https://example.com/favicon.ico"}, {"url": "dtscout.com", "siteName": "dtscout", "keywords": ["Digital marketing and customer insights platform", "dtscout"], "getIcon": "https://dtscout.com/favicon.ico"}, {"url": "twitch.tv", "siteName": "twitch", "keywords": ["Live streaming platform focused on video game content now part of Amazon.", "twitch"], "getIcon": "https://twitch.tv/favicon.ico"}, {"url": "cnn.com", "siteName": "cnn", "keywords": ["News website", "cnn"], "getIcon": "https://cnn.com/favicon.ico"}, {"url": "ubnt.com", "siteName": "ubnt", "keywords": ["Ubiquiti Networks provider of networking hardware and technology.", "ubnt"], "getIcon": "https://ubnt.com/favicon.ico"}, {"url": "samsung.com", "siteName": "samsung", "keywords": ["South Korean multinational electronics company known for smartphones and other tech.", "samsung"], "getIcon": "https://samsung.com/favicon.ico"}, {"url": "googlezip.net", "siteName": "googlezip", "keywords": ["Used for Google web performance services.", "googlezip"], "getIcon": "https://googlezip.net/favicon.ico"}, {"url": "feednews.com", "siteName": "feednews", "keywords": ["Provides news aggregation services.", "feednews"], "getIcon": "https://feednews.com/favicon.ico"}, {"url": "openx.net", "siteName": "openx", "keywords": ["Programmatic advertising exchange platform.", "openx"], "getIcon": "https://openx.net/favicon.ico"}, {"url": "yimg.jp", "siteName": "yimg", "keywords": ["Yahoo Japan's image hosting domain.", "yimg"], "getIcon": "https://yimg.jp/favicon.ico"}, {"url": "sophosxl.net", "siteName": "sophosxl", "keywords": ["Related to <PERSON><PERSON><PERSON>' extended security services.", "sophosxl"], "getIcon": "https://sophosxl.net/favicon.ico"}, {"url": "smartthings.com", "siteName": "smartthings", "keywords": ["Samsung's platform for smart home products and services.", "smartthings"], "getIcon": "https://smartthings.com/favicon.ico"}, {"url": "datto.com", "siteName": "datto", "keywords": ["Data backup and recovery solutions", "datto"], "getIcon": "https://datto.com/favicon.ico"}, {"url": "roku.com", "siteName": "roku", "keywords": ["Streaming platform providing media players and content services.", "roku"], "getIcon": "https://roku.com/favicon.ico"}, {"url": "evergage.com", "siteName": "evergage", "keywords": ["Offers real-time personalization and customer engagement solutions.", "evergage"], "getIcon": "https://evergage.com/favicon.ico"}, {"url": "facebook.com", "siteName": "facebook", "keywords": ["Social networking platform.", "facebook"], "getIcon": "https://facebook.com/favicon.ico"}, {"url": "quickconnect.to", "siteName": "quickconnect", "keywords": ["Likely a service related to fast connections or instant messaging.", "quickconnect"], "getIcon": "https://quickconnect.to/favicon.ico"}, {"url": "twimg.com", "siteName": "twimg", "keywords": ["Domain used by Twitter for hosting images and media.", "twimg"], "getIcon": "https://twimg.com/favicon.ico"}, {"url": "googlevideo.com", "siteName": "googlevideo", "keywords": ["Used to serve video content on Google platforms.", "googlevideo"], "getIcon": "https://googlevideo.com/favicon.ico"}, {"url": "ubi.com", "siteName": "ubi", "keywords": ["Ubisoft video game company known for franchises like Assassin's Creed and Rainbow Six.", "ubi"], "getIcon": "https://ubi.com/favicon.ico"}, {"url": "samba.tv", "siteName": "samba", "keywords": ["Provides TV data analytics and smart TV advertising solutions.", "samba"], "getIcon": "https://samba.tv/favicon.ico"}, {"url": "dynamicyield.com", "siteName": "dynamicyield", "keywords": ["Personalization and optimization platform", "dynamicyield"], "getIcon": "https://dynamicyield.com/favicon.ico"}, {"url": "fbsbx.com", "siteName": "fbsbx", "keywords": ["Facebook server for handling media files.", "fbsbx"], "getIcon": "https://fbsbx.com/favicon.ico"}, {"url": "openwebmp.com", "siteName": "openwebmp", "keywords": ["Related to open web advertising and media platform.", "openwebmp"], "getIcon": "https://openwebmp.com/favicon.ico"}, {"url": "smartadserver.com", "siteName": "smartadserver", "keywords": ["Digital advertising platform offering programmatic and direct sales solutions.", "smartadserver"], "getIcon": "https://smartadserver.com/favicon.ico"}, {"url": "sophos.com", "siteName": "sophos", "keywords": ["Cybersecurity company offering endpoint protection firewalls and security services.", "sophos"], "getIcon": "https://sophos.com/favicon.ico"}, {"url": "datadome.co", "siteName": "datadome", "keywords": ["Bot protection and cybersecurity service", "datadome"], "getIcon": "https://datadome.co/favicon.ico"}, {"url": "quantummetric.com", "siteName": "quantummetric", "keywords": ["Provides data analytics and experience optimization tools.", "quantummetric"], "getIcon": "https://quantummetric.com/favicon.ico"}, {"url": "rocket-cdn.com", "siteName": "rocket-cdn", "keywords": ["Content delivery network (CDN) used for media and game services.", "rocket-cdn"], "getIcon": "https://rocket-cdn.com/favicon.ico"}, {"url": "ezvizlife.com", "siteName": "ezvizlife", "keywords": ["Connected camera systems for home security.", "ezvizlife"], "getIcon": "https://ezvizlife.com/favicon.ico"}, {"url": "dv.tech", "siteName": "dv", "keywords": ["Digital marketing technology platform", "dv"], "getIcon": "https://dv.tech/favicon.ico"}, {"url": "gstatic.com", "siteName": "gstatic", "keywords": ["Google-hosted static content for various services.", "gstatic"], "getIcon": "https://gstatic.com/favicon.ico"}, {"url": "googleusercontent.com", "siteName": "googleusercontent", "keywords": ["Serves user-uploaded content on Google platforms.", "googleusercontent"], "getIcon": "https://googleusercontent.com/favicon.ico"}, {"url": "uber.com", "siteName": "uber", "keywords": ["Ride-sharing and transportation services platform.", "uber"], "getIcon": "https://uber.com/favicon.ico"}, {"url": "smadex.com", "siteName": "smadex", "keywords": ["Mobile and display advertising network.", "smadex"], "getIcon": "https://smadex.com/favicon.ico"}, {"url": "quantserve.com", "siteName": "quantserve", "keywords": ["Offers website analytics and online advertising services.", "quantserve"], "getIcon": "https://quantserve.com/favicon.ico"}, {"url": "googleadservices.com", "siteName": "googleadservices", "keywords": ["Used for Google ad services and ad tracking.", "googleadservices"], "getIcon": "https://googleadservices.com/favicon.ico"}, {"url": "sonobi.com", "siteName": "sonobi", "keywords": ["Advertising technology company providing programmatic ad services.", "sonobi"], "getIcon": "https://sonobi.com/favicon.ico"}, {"url": "samsungcloudsolution.net", "siteName": "samsungcloudsolution", "keywords": ["Offers cloud solutions related to Samsung's ecosystem.", "samsungcloudsolution"], "getIcon": "https://samsungcloudsolution.net/favicon.ico"}, {"url": "googletagmanager.com", "siteName": "googletagmanager", "keywords": ["Google's tag management system.", "googletagmanager"], "getIcon": "https://googletagmanager.com/favicon.ico"}, {"url": "datadoghq.com", "siteName": "datadoghq", "keywords": ["Cloud monitoring and analytics platform", "datadoghq"], "getIcon": "https://datadoghq.com/favicon.ico"}, {"url": "optimizely.com", "siteName": "optimizely", "keywords": ["Provides experimentation and optimization tools for websites and apps.", "optimizely"], "getIcon": "https://optimizely.com/favicon.ico"}, {"url": "tribalfusion.com", "siteName": "tribalfusion", "keywords": ["Online advertising company providing display and video ad services.", "tribalfusion"], "getIcon": "https://tribalfusion.com/favicon.ico"}, {"url": "roblox.com", "siteName": "roblo<PERSON>", "keywords": ["Online platform for creating and playing user-generated video games.", "roblo<PERSON>"], "getIcon": "https://roblox.com/favicon.ico"}, {"url": "dutils.com", "siteName": "dutils", "keywords": ["Utilities for web and mobile platforms", "dutils"], "getIcon": "https://dutils.com/favicon.ico"}, {"url": "eyeota.net", "siteName": "<PERSON><PERSON>", "keywords": ["Data-driven marketing platform focused on audience targeting.", "<PERSON><PERSON>"], "getIcon": "https://eyeota.net/favicon.ico"}, {"url": "turn.com", "siteName": "turn", "keywords": ["Provides services for data delivery privacy and audience targeting in advertising.", "turn"], "getIcon": "https://turn.com/favicon.ico"}, {"url": "quantcount.com", "siteName": "quantcount", "keywords": ["Provides data-driven advertising solutions.", "quantcount"], "getIcon": "https://quantcount.com/favicon.ico"}, {"url": "googletagservices.com", "siteName": "googletagservices", "keywords": ["Services related to Google Ads and analytics.", "googletagservices"], "getIcon": "https://googletagservices.com/favicon.ico"}, {"url": "gravatar.com", "siteName": "gravatar", "keywords": ["Provides avatars for use on websites.", "gravatar"], "getIcon": "https://gravatar.com/favicon.ico"}, {"url": "typekit.net", "siteName": "typekit", "keywords": ["Domain used for Typekit's web font services.", "typekit"], "getIcon": "https://typekit.net/favicon.ico"}, {"url": "samsungcloudsolution.com", "siteName": "samsungcloudsolution", "keywords": ["Offers cloud solutions related to Samsung's ecosystem.", "samsungcloudsolution"], "getIcon": "https://samsungcloudsolution.com/favicon.ico"}, {"url": "sonicwall.com", "siteName": "sonicwall", "keywords": ["Cybersecurity company offering firewall and security services.", "sonicwall"], "getIcon": "https://sonicwall.com/favicon.ico"}, {"url": "googlesyndication.com", "siteName": "googlesyndication", "keywords": ["Provides Google ad network services.", "googlesyndication"], "getIcon": "https://googlesyndication.com/favicon.ico"}, {"url": "oppomobile.com", "siteName": "oppomobile", "keywords": ["Chinese smartphone manufacturer known for Oppo devices.", "oppomobile"], "getIcon": "https://oppomobile.com/favicon.ico"}, {"url": "google.us", "siteName": "google", "keywords": ["Google's US website.", "google"], "getIcon": "https://google.us/favicon.ico"}, {"url": "smaato.net", "siteName": "sma<PERSON>", "keywords": ["Mobile advertising platform providing real-time monetization solutions.", "sma<PERSON>"], "getIcon": "https://smaato.net/favicon.ico"}, {"url": "zoho.com", "siteName": "zoho", "keywords": ["Cloud-based software suite offering CRM productivity and business tools.", "zoho"], "getIcon": "https://zoho.com/favicon.ico"}, {"url": "rlcdn.com", "siteName": "rlcdn", "keywords": ["Content delivery network (CDN) used for Riot Games services.", "rlcdn"], "getIcon": "https://rlcdn.com/favicon.ico"}, {"url": "exp-tas.com", "siteName": "exp-tas", "keywords": ["exp-tas"], "getIcon": "https://exp-tas.com/favicon.ico"}, {"url": "qualtrics.com", "siteName": "qualtrics", "keywords": ["Experience management platform providing tools for market research and customer feedback.", "qualtrics"], "getIcon": "https://qualtrics.com/favicon.ico"}, {"url": "dalyfeds.com", "siteName": "dalyfeds", "keywords": ["   ", "dalyfeds"], "getIcon": "https://dalyfeds.com/favicon.ico"}, {"url": "grammarly.io", "siteName": "grammarly", "keywords": ["Platform for developers to integrate Grammarly services.", "grammarly"], "getIcon": "https://grammarly.io/favicon.ico"}, {"url": "typekit.com", "siteName": "typekit", "keywords": ["Adobe's web font service for websites (now integrated into Adobe Fonts).", "typekit"], "getIcon": "https://typekit.com/favicon.ico"}, {"url": "cloudlinks.cn", "siteName": "cloudlinks", "keywords": ["CDN services by Alibaba Cloud", "cloudlinks"], "getIcon": "https://cloudlinks.cn/favicon.ico"}, {"url": "vivoglobal.com", "siteName": "vivoglobal", "keywords": ["Likely related to VIVO's global services or operations.", "vivoglobal"], "getIcon": "https://vivoglobal.com/favicon.ico"}, {"url": "samsungcloud.tv", "siteName": "samsungcloud", "keywords": ["Samsung's cloud-based TV services and apps.", "samsungcloud"], "getIcon": "https://samsungcloud.tv/favicon.ico"}, {"url": "google.co.id", "siteName": "google", "keywords": ["Google's Indonesian website.", "google"], "getIcon": "https://google.co.id/favicon.ico"}, {"url": "googleapis.com", "siteName": "googlea<PERSON>", "keywords": ["Google's general API service platform.", "googlea<PERSON>"], "getIcon": "https://googleapis.com/favicon.ico"}, {"url": "google.ru", "siteName": "google", "keywords": ["Google's Russian website.", "google"], "getIcon": "https://google.ru/favicon.ico"}, {"url": "opera.software", "siteName": "opera", "keywords": ["Developer of the Opera web browser.", "opera"], "getIcon": "https://opera.software/favicon.ico"}, {"url": "zijieapi.com", "siteName": "<PERSON><PERSON><PERSON><PERSON>", "keywords": ["<PERSON><PERSON><PERSON><PERSON>"], "getIcon": "https://zijieapi.com/favicon.ico"}, {"url": "riotgames.com", "siteName": "riotgames", "keywords": ["Game development company known for titles like League of Legends and Valorant.", "riotgames"], "getIcon": "https://riotgames.com/favicon.ico"}, {"url": "qq.com", "siteName": "qq", "keywords": ["Chinese internet company and the owner of the QQ messaging platform.", "qq"], "getIcon": "https://qq.com/favicon.ico"}, {"url": "tremorhub.com", "siteName": "tremorhub", "keywords": ["Ad tech platform offering video and display advertising solutions.", "tremorhub"], "getIcon": "https://tremorhub.com/favicon.ico"}, {"url": "cxense.com", "siteName": "cxense", "keywords": ["Data management and personalized advertising services", "cxense"], "getIcon": "https://cxense.com/favicon.ico"}, {"url": "grammarly.com", "siteName": "grammarly", "keywords": ["Online writing assistant that helps with grammar and spelling.", "grammarly"], "getIcon": "https://grammarly.com/favicon.ico"}, {"url": "douyinliving.com", "siteName": "douyinliving", "keywords": ["E-commerce services by <PERSON><PERSON><PERSON> (TikTok)", "douyinliving"], "getIcon": "https://douyinliving.com/favicon.ico"}, {"url": "snapkit.com", "siteName": "snapkit", "keywords": ["Snapchat's developer platform for integrating Snap features into apps.", "snapkit"], "getIcon": "https://snapkit.com/favicon.ico"}, {"url": "tynt.com", "siteName": "tynt", "keywords": ["Provides content analytics and social media tracking tools.", "tynt"], "getIcon": "https://tynt.com/favicon.ico"}, {"url": "google.cn", "siteName": "google", "keywords": ["Google's Chinese website.", "google"], "getIcon": "https://google.cn/favicon.ico"}, {"url": "samsungcloud.com", "siteName": "samsungcloud", "keywords": ["Samsung's cloud storage and syncing services.", "samsungcloud"], "getIcon": "https://samsungcloud.com/favicon.ico"}, {"url": "vivo.com.cn", "siteName": "vivo", "keywords": ["Chinese telecommunications company and smartphone manufacturer.", "vivo"], "getIcon": "https://vivo.com.cn/favicon.ico"}, {"url": "cloudinary.com", "siteName": "cloudinary", "keywords": ["Cloud-based image and video management service", "cloudinary"], "getIcon": "https://cloudinary.com/favicon.ico"}, {"url": "naver.com", "siteName": "naver", "keywords": ["South Korean search engine and online services platform.", "naver"], "getIcon": "https://naver.com/favicon.ico"}, {"url": "googleapis.cn", "siteName": "googlea<PERSON>", "keywords": ["Google's API services for China.", "googlea<PERSON>"], "getIcon": "https://googleapis.cn/favicon.ico"}, {"url": "ovscdns.com", "siteName": "ovscdns", "keywords": ["Related to OVSC's DNS services.", "ovscdns"], "getIcon": "https://ovscdns.com/favicon.ico"}, {"url": "zeotap.com", "siteName": "zeotap", "keywords": ["Customer data platform (CDP) for advertisers and marketers.", "zeotap"], "getIcon": "https://zeotap.com/favicon.ico"}, {"url": "opera.com", "siteName": "opera", "keywords": ["Official website for Opera web browser.", "opera"], "getIcon": "https://opera.com/favicon.ico"}, {"url": "google.fr", "siteName": "google", "keywords": ["Google's French website.", "google"], "getIcon": "https://google.fr/favicon.ico"}, {"url": "qlivecdn.com", "siteName": "qlivecdn", "keywords": ["Content delivery network (CDN) for live streaming services.", "qlivecdn"], "getIcon": "https://qlivecdn.com/favicon.ico"}, {"url": "cursor.sh", "siteName": "cursor", "keywords": ["Web-based services platform", "cursor"], "getIcon": "https://cursor.sh/favicon.ico"}, {"url": "adobelogin.com", "siteName": "adobelogin", "keywords": ["Adobe login service for accessing Adobe products", "adobelogin"], "getIcon": "https://adobelogin.com/favicon.ico"}, {"url": "grafana.com", "siteName": "grafana", "keywords": ["Open-source analytics and monitoring platform.", "grafana"], "getIcon": "https://grafana.com/favicon.ico"}, {"url": "snapchat.com", "siteName": "snapchat", "keywords": ["Social media app for sharing ephemeral photos and videos.", "snapchat"], "getIcon": "https://snapchat.com/favicon.ico"}, {"url": "google.ca", "siteName": "google", "keywords": ["Google's Canadian website.", "google"], "getIcon": "https://google.ca/favicon.ico"}, {"url": "axon.ai", "siteName": "axon", "keywords": ["AI-based data and analytics services", "axon"], "getIcon": "https://axon.ai/favicon.ico"}, {"url": "ttdns2.com", "siteName": "ttdns2", "keywords": ["Likely related to DNS services used by a specific network or provider.", "ttdns2"], "getIcon": "https://ttdns2.com/favicon.ico"}, {"url": "twitter.com", "siteName": "twitter", "keywords": ["Social media platform for short-form content primarily text-based posts.", "twitter"], "getIcon": "https://twitter.com/favicon.ico"}, {"url": "visualstudio.com", "siteName": "visualstudio", "keywords": ["Microsoft's integrated development environment (IDE) and developer tools platform.", "visualstudio"], "getIcon": "https://visualstudio.com/favicon.ico"}, {"url": "cloudfunctions.net", "siteName": "cloudfunctions", "keywords": ["Serverless computing platform by Google Cloud", "cloudfunctions"], "getIcon": "https://cloudfunctions.net/favicon.ico"}, {"url": "nakheelteam.cc", "siteName": "nakheelteam", "keywords": ["nakheelteam"], "getIcon": "https://nakheelteam.cc/favicon.ico"}, {"url": "zendesk.com", "siteName": "zendesk", "keywords": ["Customer service platform offering ticketing support and engagement tools.", "zendesk"], "getIcon": "https://zendesk.com/favicon.ico"}, {"url": "overwolf.com", "siteName": "overwolf", "keywords": ["A platform for creating in-game apps and mods.", "overwolf"], "getIcon": "https://overwolf.com/favicon.ico"}, {"url": "pvp.net", "siteName": "pvp", "keywords": ["Related to Riot Games used for game and player-related services (e.g. League of Legends).", "pvp"], "getIcon": "https://pvp.net/favicon.ico"}, {"url": "cspserver.net", "siteName": "cspserver", "keywords": ["Web server solutions", "cspserver"], "getIcon": "https://cspserver.net/favicon.ico"}, {"url": "sacdnssedge.com", "siteName": "sacdnssedge", "keywords": ["Related to Microsoft Edge's content delivery network and services.", "sacdnssedge"], "getIcon": "https://sacdnssedge.com/favicon.ico"}, {"url": "google.com", "siteName": "google", "keywords": ["Google's global website for search news maps and more.", "google"], "getIcon": "https://google.com/favicon.ico"}, {"url": "goskope.com", "siteName": "goskope", "keywords": ["goskope"], "getIcon": "https://goskope.com/favicon.ico"}, {"url": "tsyndicate.com", "siteName": "tsyndicate", "keywords": ["tsyndicate"], "getIcon": "https://tsyndicate.com/favicon.ico"}, {"url": "doubleverify.com", "siteName": "doubleverify", "keywords": ["Ad verification and fraud detection services", "doubleverify"], "getIcon": "https://doubleverify.com/favicon.ico"}, {"url": "snackvideo.in", "siteName": "snackvideo", "keywords": ["Short-video platform (primarily in India owned by <PERSON><PERSON><PERSON>).", "snackvideo"], "getIcon": "https://snackvideo.in/favicon.ico"}, {"url": "onetrust.io", "siteName": "onetrust", "keywords": ["Provides privacy management and compliance solutions.", "onetrust"], "getIcon": "https://onetrust.io/favicon.ico"}, {"url": "virtualearth.net", "siteName": "virtualearth", "keywords": ["Microsoft's service for mapping and geographic information part of Bing Maps.", "virtualearth"], "getIcon": "https://virtualearth.net/favicon.ico"}, {"url": "n-able.com", "siteName": "n-able", "keywords": ["Provides IT management and security solutions.", "n-able"], "getIcon": "https://n-able.com/favicon.ico"}, {"url": "adobedtm.com", "siteName": "adobedtm", "keywords": ["Adobe Digital Marketing services", "adobedtm"], "getIcon": "https://adobedtm.com/favicon.ico"}, {"url": "unifi-ai.com", "siteName": "unifi-ai", "keywords": ["Likely related to AI-based services or products.", "unifi-ai"], "getIcon": "https://unifi-ai.com/favicon.ico"}, {"url": "outlook.com", "siteName": "outlook", "keywords": ["Microsoft's web-based email service.", "outlook"], "getIcon": "https://outlook.com/favicon.ico"}, {"url": "ax-msedge.net", "siteName": "ax-msedge", "keywords": ["Microsoft Edge network infrastructure", "ax-msedge"], "getIcon": "https://ax-msedge.net/favicon.ico"}, {"url": "unrulymedia.com", "siteName": "unrulymedia", "keywords": ["Video advertising platform focused on interactive and video ads.", "unrulymedia"], "getIcon": "https://unrulymedia.com/favicon.ico"}, {"url": "s-msedge.net", "siteName": "s-msedge", "keywords": ["Related to Microsoft's Edge browser and associated services.", "s-msedge"], "getIcon": "https://s-msedge.net/favicon.ico"}, {"url": "truste.com", "siteName": "truste", "keywords": ["Privacy compliance and certification service for websites.", "truste"], "getIcon": "https://truste.com/favicon.ico"}, {"url": "gos-gsp.io", "siteName": "gos-gsp", "keywords": ["gos-gsp"], "getIcon": "https://gos-gsp.io/favicon.ico"}, {"url": "smilewanted.com", "siteName": "smilewanted", "keywords": ["smilewanted"], "getIcon": "https://smilewanted.com/favicon.ico"}, {"url": "google.co.uk", "siteName": "google", "keywords": ["Google's UK website.", "google"], "getIcon": "https://google.co.uk/favicon.ico"}, {"url": "onetrust.com", "siteName": "onetrust", "keywords": ["Provides privacy management and compliance solutions.", "onetrust"], "getIcon": "https://onetrust.com/favicon.ico"}, {"url": "rayjump.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["<PERSON><PERSON><PERSON>"], "getIcon": "https://rayjump.com/favicon.ico"}, {"url": "userapi.com", "siteName": "userapi", "keywords": ["Likely related to social media or messaging platforms for user APIs.", "userapi"], "getIcon": "https://userapi.com/favicon.ico"}, {"url": "undertone.com", "siteName": "undertone", "keywords": ["Digital advertising company providing display and video ad solutions.", "undertone"], "getIcon": "https://undertone.com/favicon.ico"}, {"url": "mzstatic.com", "siteName": "mzstatic", "keywords": ["Used for serving static content in mobile apps (often related to Mi services).", "mzstatic"], "getIcon": "https://mzstatic.com/favicon.ico"}, {"url": "ddns.net", "siteName": "ddns", "keywords": ["Dynamic DNS services", "ddns"], "getIcon": "https://ddns.net/favicon.ico"}, {"url": "permutive.app", "siteName": "permutive", "keywords": ["Data platform for publishers and advertisers to manage and activate data.", "permutive"], "getIcon": "https://permutive.app/favicon.ico"}, {"url": "vimeocdn.com", "siteName": "vimeocdn", "keywords": ["Content delivery network (CDN) used for serving Vimeo videos.", "vimeocdn"], "getIcon": "https://vimeocdn.com/favicon.ico"}, {"url": "doubleclick.net", "siteName": "doubleclick", "keywords": ["Google's digital advertising network", "doubleclick"], "getIcon": "https://doubleclick.net/favicon.ico"}, {"url": "trustarc.com", "siteName": "trustarc", "keywords": ["Privacy management software providing solutions for businesses to comply with global privacy regulations.", "trustarc"], "getIcon": "https://trustarc.com/favicon.ico"}, {"url": "outbrainimg.com", "siteName": "outbrainimg", "keywords": ["Related to Outbrain's content discovery and media services.", "outbrainimg"], "getIcon": "https://outbrainimg.com/favicon.ico"}, {"url": "pstatp.com", "siteName": "pstatp", "keywords": ["Used by ByteDance likely related to TikTok's data services.", "pstatp"], "getIcon": "https://pstatp.com/favicon.ico"}, {"url": "unpkg.com", "siteName": "unpkg", "keywords": ["Fast and free CDN for JavaScript and other web assets.", "unpkg"], "getIcon": "https://unpkg.com/favicon.ico"}, {"url": "rubiconproject.com", "siteName": "rubiconproject", "keywords": ["Advertising exchange platform providing programmatic advertising solutions.", "rubiconproject"], "getIcon": "https://rubiconproject.com/favicon.ico"}, {"url": "google.co.jp", "siteName": "google", "keywords": ["Google's Japanese website.", "google"], "getIcon": "https://google.co.jp/favicon.ico"}, {"url": "useinsider.com", "siteName": "useinsider", "keywords": ["Marketing platform focused on behavioral intelligence and customer experience optimization.", "useinsider"], "getIcon": "https://useinsider.com/favicon.ico"}, {"url": "ravm.tv", "siteName": "ravm", "keywords": ["ravm"], "getIcon": "https://ravm.tv/favicon.ico"}, {"url": "onetag-sys.com", "siteName": "onetag-sys", "keywords": ["Related to digital marketing technology.", "onetag-sys"], "getIcon": "https://onetag-sys.com/favicon.ico"}, {"url": "azurefd.net", "siteName": "azurefd", "keywords": ["Microsoft Azure Front Door service", "azurefd"], "getIcon": "https://azurefd.net/favicon.ico"}, {"url": "redd.it", "siteName": "redd", "keywords": ["URL shortening service used by Reddit.", "redd"], "getIcon": "https://redd.it/favicon.ico"}, {"url": "umeng.com", "siteName": "umeng", "keywords": ["Chinese analytics platform providing user insights and app analytics (now part of Alibaba).", "umeng"], "getIcon": "https://umeng.com/favicon.ico"}, {"url": "dbankcloud.com", "siteName": "dbankcloud", "keywords": ["Data Bank cloud computing services", "dbankcloud"], "getIcon": "https://dbankcloud.com/favicon.ico"}, {"url": "pendo.io", "siteName": "pendo", "keywords": ["Provides product experience and user analytics software for businesses.", "pendo"], "getIcon": "https://pendo.io/favicon.ico"}, {"url": "vimeo.com", "siteName": "vimeo", "keywords": ["Video hosting platform focused on high-quality content often used by creatives.", "vimeo"], "getIcon": "https://vimeo.com/favicon.ico"}, {"url": "blockdh100b.net", "siteName": "blockdh100b", "keywords": ["Security and network service", "blockdh100b"], "getIcon": "https://blockdh100b.net/favicon.ico"}, {"url": "mythad.com", "siteName": "mythad", "keywords": ["mythad"], "getIcon": "https://mythad.com/favicon.ico"}, {"url": "synology.com", "siteName": "synology", "keywords": ["Manufacturer of network-attached storage (NAS) devices and software solutions.", "synology"], "getIcon": "https://synology.com/favicon.ico"}, {"url": "amazon-adsystem.com", "siteName": "amazon-adsystem", "keywords": ["Amazon's advertising network", "amazon-adsystem"], "getIcon": "https://amazon-adsystem.com/favicon.ico"}, {"url": "rtmark.net", "siteName": "rtmark", "keywords": ["rtmark"], "getIcon": "https://rtmark.net/favicon.ico"}, {"url": "unmsapp.com", "siteName": "unmsapp", "keywords": ["Ubiquiti's app for managing and monitoring networking devices.", "unmsapp"], "getIcon": "https://unmsapp.com/favicon.ico"}, {"url": "urbanairship.com", "siteName": "urbanairship", "keywords": ["Customer engagement platform providing push notifications and mobile marketing tools.", "urbanairship"], "getIcon": "https://urbanairship.com/favicon.ico"}, {"url": "opendns.com", "siteName": "opendns", "keywords": ["DNS service provider (acquired by Cisco).", "opendns"], "getIcon": "https://opendns.com/favicon.ico"}, {"url": "prodregistryv2.org", "siteName": "prodregistryv2", "keywords": ["Related to production registration or registry services (uncertain).", "prodregistryv2"], "getIcon": "https://prodregistryv2.org/favicon.ico"}, {"url": "google.co.in", "siteName": "google", "keywords": ["Google's Indian website.", "google"], "getIcon": "https://google.co.in/favicon.ico"}, {"url": "dropboxapi.com", "siteName": "dropboxapi", "keywords": ["Dropbox API platform for integration", "dropboxapi"], "getIcon": "https://dropboxapi.com/favicon.ico"}, {"url": "ebay.com", "siteName": "ebay", "keywords": ["Online marketplace for buying and selling goods.", "ebay"], "getIcon": "https://ebay.com/favicon.ico"}, {"url": "wac-msedge.net", "siteName": "wac-msedge", "keywords": ["Related to Microsoft Edge's services and features.", "wac-msedge"], "getIcon": "https://wac-msedge.net/favicon.ico"}, {"url": "recaptcha.net", "siteName": "recaptcha", "keywords": ["Google's service for preventing bot interactions on websites via CAPTCHA.", "recaptcha"], "getIcon": "https://recaptcha.net/favicon.ico"}, {"url": "azureedge.net", "siteName": "azureedge", "keywords": ["Azure's edge services for content delivery", "azureedge"], "getIcon": "https://azureedge.net/favicon.ico"}, {"url": "rapid7.com", "siteName": "rapid7", "keywords": ["Cybersecurity company providing solutions for threat detection and vulnerability management.", "rapid7"], "getIcon": "https://rapid7.com/favicon.ico"}, {"url": "onesignal.com", "siteName": "onesignal", "keywords": ["Push notification service provider.", "onesignal"], "getIcon": "https://onesignal.com/favicon.ico"}, {"url": "uisp.com", "siteName": "uisp", "keywords": ["Ubiquiti's software platform for managing their networking devices.", "uisp"], "getIcon": "https://uisp.com/favicon.ico"}, {"url": "dbankcloud.cn", "siteName": "dbankcloud", "keywords": ["Cloud computing services in China", "dbankcloud"], "getIcon": "https://dbankcloud.cn/favicon.ico"}, {"url": "peacocktv.com", "siteName": "peacocktv", "keywords": ["Streaming service offering movies TV shows and original content (owned by NBCUniversal).", "peacocktv"], "getIcon": "https://peacocktv.com/favicon.ico"}, {"url": "vidoomy.com", "siteName": "vidoomy", "keywords": ["Likely related to video content or advertising services.", "vidoomy"], "getIcon": "https://vidoomy.com/favicon.ico"}, {"url": "myqcloud.com", "siteName": "myqcloud", "keywords": ["Related to QQ Cloud services.", "myqcloud"], "getIcon": "https://myqcloud.com/favicon.ico"}, {"url": "uol.com.br", "siteName": "uol", "keywords": ["Brazilian internet services company providing a variety of online products and services.", "uol"], "getIcon": "https://uol.com.br/favicon.ico"}, {"url": "symcb.com", "siteName": "symcb", "keywords": ["Related to Symantec's cybersecurity services.", "symcb"], "getIcon": "https://symcb.com/favicon.ico"}, {"url": "amap.com", "siteName": "amap", "keywords": ["Chinese mapping and navigation service", "amap"], "getIcon": "https://amap.com/favicon.ico"}, {"url": "dropbox.com", "siteName": "dropbox", "keywords": ["Cloud storage service", "dropbox"], "getIcon": "https://dropbox.com/favicon.ico"}, {"url": "openai.com", "siteName": "openai", "keywords": ["The domain for OpenAI the organization behind GPT models and other AI technologies.", "openai"], "getIcon": "https://openai.com/favicon.ico"}, {"url": "reasonsecurity.com", "siteName": "reasonsecurity", "keywords": ["Provides cybersecurity solutions.", "reasonsecurity"], "getIcon": "https://reasonsecurity.com/favicon.ico"}, {"url": "privacysandboxservices.com", "siteName": "privacysandboxservices", "keywords": ["Related to Google's Privacy Sandbox initiative for online privacy.", "privacysandboxservices"], "getIcon": "https://privacysandboxservices.com/favicon.ico"}, {"url": "w55c.net", "siteName": "w55c", "keywords": ["w55c"], "getIcon": "https://w55c.net/favicon.ico"}, {"url": "salesforceliveagent.com", "siteName": "salesforceliveagent", "keywords": ["Part of Salesforce's live chat and customer support services.", "salesforceliveagent"], "getIcon": "https://salesforceliveagent.com/favicon.ico"}, {"url": "easy4ipcloud.com", "siteName": "easy4ipcloud", "keywords": ["easy4ipcloud"], "getIcon": "https://easy4ipcloud.com/favicon.ico"}, {"url": "norton.com", "siteName": "norton", "keywords": ["Cybersecurity company offering antivirus and online protection services.", "norton"], "getIcon": "https://norton.com/favicon.ico"}, {"url": "orbsrv.com", "siteName": "orbsrv", "keywords": ["Related to Orbital Data solutions.", "orbsrv"], "getIcon": "https://orbsrv.com/favicon.ico"}, {"url": "azure.net", "siteName": "azure", "keywords": ["Network services by Microsoft Azure", "azure"], "getIcon": "https://azure.net/favicon.ico"}, {"url": "rainberrytv.com", "siteName": "rainberrytv", "keywords": ["Related to Rainberry TV a platform for streaming media content.", "rainberrytv"], "getIcon": "https://rainberrytv.com/favicon.ico"}, {"url": "blizzard.com", "siteName": "blizzard", "keywords": ["Video game and entertainment company (World of Warcraft)", "blizzard"], "getIcon": "https://blizzard.com/favicon.ico"}, {"url": "paypal.com", "siteName": "paypal", "keywords": ["Online payment platform facilitating e-commerce and digital payments.", "paypal"], "getIcon": "https://paypal.com/favicon.ico"}, {"url": "vidaahub.com", "siteName": "vidaahub", "keywords": ["vidaahub"], "getIcon": "https://vidaahub.com/favicon.ico"}, {"url": "dbankcdn.com", "siteName": "dbankcdn", "keywords": ["Data Bank CDN services", "dbankcdn"], "getIcon": "https://dbankcdn.com/favicon.ico"}, {"url": "mynetname.net", "siteName": "mynetname", "keywords": ["mynetname"], "getIcon": "https://mynetname.net/favicon.ico"}, {"url": "popmart.com", "siteName": "popmart", "keywords": ["E-commerce and retail platform for collectible toys and figures.", "popmart"], "getIcon": "https://popmart.com/favicon.ico"}, {"url": "bb.com.br", "siteName": "bb", "keywords": ["E-commerce platform in Brazil", "bb"], "getIcon": "https://bb.com.br/favicon.ico"}, {"url": "dropbox-dns.com", "siteName": "dropbox-dns", "keywords": ["DNS services for Dropbox", "dropbox-dns"], "getIcon": "https://dropbox-dns.com/favicon.ico"}, {"url": "w3.org", "siteName": "w3", "keywords": ["International organization that develops web standards and guidelines (e.g. HTML CSS).", "w3"], "getIcon": "https://w3.org/favicon.ico"}, {"url": "onmicrosoft.com", "siteName": "onmicrosoft", "keywords": ["Part of Microsoft's cloud and enterprise solutions.", "onmicrosoft"], "getIcon": "https://onmicrosoft.com/favicon.ico"}, {"url": "privacymanager.io", "siteName": "privacymanager", "keywords": ["Privacy management software for businesses and websites.", "privacymanager"], "getIcon": "https://privacymanager.io/favicon.ico"}, {"url": "no-ip.com", "siteName": "no-ip", "keywords": ["Dynamic DNS service provider.", "no-ip"], "getIcon": "https://no-ip.com/favicon.ico"}, {"url": "azure.com", "siteName": "azure", "keywords": ["Cloud computing platform by Microsoft Azure", "azure"], "getIcon": "https://azure.com/favicon.ico"}, {"url": "salesforce.com", "siteName": "salesforce", "keywords": ["Cloud-based customer relationship management (CRM) platform.", "salesforce"], "getIcon": "https://salesforce.com/favicon.ico"}, {"url": "oraclecloud.com", "siteName": "oraclecloud", "keywords": ["Oracle's cloud computing services.", "oraclecloud"], "getIcon": "https://oraclecloud.com/favicon.ico"}, {"url": "easebar.com", "siteName": "easebar", "keywords": ["easebar"], "getIcon": "https://easebar.com/favicon.ico"}, {"url": "parsely.com", "siteName": "parsely", "keywords": ["Web analytics company providing tools for tracking and analyzing online content.", "parsely"], "getIcon": "https://parsely.com/favicon.ico"}, {"url": "ueiwsp.com", "siteName": "ueiwsp", "keywords": ["ueiwsp"], "getIcon": "https://ueiwsp.com/favicon.ico"}, {"url": "deltaork.com", "siteName": "deltaork", "keywords": ["Web technology services", "deltaork"], "getIcon": "https://deltaork.com/favicon.ico"}, {"url": "pluto.tv", "siteName": "pluto", "keywords": ["Streaming service offering free TV shows movies and live TV channels.", "pluto"], "getIcon": "https://pluto.tv/favicon.ico"}, {"url": "rbxcdn.com", "siteName": "rbxcdn", "keywords": ["Content delivery network (CDN) used by Roblox for gaming and media services.", "rbxcdn"], "getIcon": "https://rbxcdn.com/favicon.ico"}, {"url": "douyinvod.com", "siteName": "douyinvod", "keywords": ["Video-on-demand service by Douyin (TikTok)", "douyinvod"], "getIcon": "https://douyinvod.com/favicon.ico"}, {"url": "pv-cdn.net", "siteName": "pv-cdn", "keywords": ["Content delivery network (CDN) used for serving videos.", "pv-cdn"], "getIcon": "https://pv-cdn.net/favicon.ico"}, {"url": "pinimg.com", "siteName": "pinimg", "keywords": ["Image hosting and content delivery domain used by Pinterest.", "pinimg"], "getIcon": "https://pinimg.com/favicon.ico"}, {"url": "online-metrix.net", "siteName": "online-metrix", "keywords": ["Related to online metrics and analytics services.", "online-metrix"], "getIcon": "https://online-metrix.net/favicon.ico"}, {"url": "vungle.com", "siteName": "vungle", "keywords": ["Mobile advertising network providing in-app video ads.", "vungle"], "getIcon": "https://vungle.com/favicon.ico"}, {"url": "tlivepush.com", "siteName": "tlivepush", "keywords": ["Likely related to live streaming push services.", "tlivepush"], "getIcon": "https://tlivepush.com/favicon.ico"}, {"url": "adsco.re", "siteName": "adsco", "keywords": ["URL shortening and ad-based services", "adsco"], "getIcon": "https://adsco.re/favicon.ico"}, {"url": "nist.gov", "siteName": "nist", "keywords": ["U.S. National Institute of Standards and Technology.", "nist"], "getIcon": "https://nist.gov/favicon.ico"}, {"url": "playstation.com", "siteName": "playstation", "keywords": ["Official website for Sony's PlayStation gaming consoles and services.", "playstation"], "getIcon": "https://playstation.com/favicon.ico"}, {"url": "oracle.com", "siteName": "oracle", "keywords": ["Provider of enterprise software database services and cloud computing.", "oracle"], "getIcon": "https://oracle.com/favicon.ico"}, {"url": "sbixby.com", "siteName": "sbixby", "keywords": ["Related to Samsung's Bixby voice assistant service.", "sbixby"], "getIcon": "https://sbixby.com/favicon.ico"}, {"url": "script.ac", "siteName": "script", "keywords": ["Likely related to JavaScript and web development resources.", "script"], "getIcon": "https://script.ac/favicon.ico"}, {"url": "pangle.io", "siteName": "pangle", "keywords": ["Advertising platform related to ByteDance providing monetization solutions for apps.", "pangle"], "getIcon": "https://pangle.io/favicon.ico"}, {"url": "ea.com", "siteName": "ea", "keywords": ["Electronic Arts a major video game publisher.", "ea"], "getIcon": "https://ea.com/favicon.ico"}, {"url": "nflximg.com", "siteName": "nflximg", "keywords": ["Netflix's content delivery network for images.", "nflximg"], "getIcon": "https://nflximg.com/favicon.ico"}, {"url": "safedk.com", "siteName": "safedk", "keywords": ["Mobile SDK offering app monetization and security features.", "safedk"], "getIcon": "https://safedk.com/favicon.ico"}, {"url": "volcfcdndvs.com", "siteName": "volcfcdndvs", "keywords": ["volcfcdndvs"], "getIcon": "https://volcfcdndvs.com/favicon.ico"}, {"url": "privacy-center.org", "siteName": "privacy-center", "keywords": ["Advocacy group focused on digital privacy and consumer rights.", "privacy-center"], "getIcon": "https://privacy-center.org/favicon.ico"}, {"url": "phicdn.net", "siteName": "p<PERSON><PERSON>", "keywords": ["Content delivery network (CDN) used by PHI-related services.", "p<PERSON><PERSON>"], "getIcon": "https://phicdn.net/favicon.ico"}, {"url": "dell.com", "siteName": "dell", "keywords": ["Computer hardware and technology company", "dell"], "getIcon": "https://dell.com/favicon.ico"}, {"url": "like.video", "siteName": "like", "keywords": ["A platform for short-form video content.", "like"], "getIcon": "https://like.video/favicon.ico"}, {"url": "tizen.org", "siteName": "tizen", "keywords": ["Open-source operating system based on Linux used in Samsung devices.", "tizen"], "getIcon": "https://tizen.org/favicon.ico"}, {"url": "pusher.com", "siteName": "pusher", "keywords": ["Provides real-time communication services for web and mobile apps.", "pusher"], "getIcon": "https://pusher.com/favicon.ico"}, {"url": "sascdn.com", "siteName": "sascdn", "keywords": ["Content delivery network (CDN) related to SAS services.", "sascdn"], "getIcon": "https://sascdn.com/favicon.ico"}, {"url": "netgear.com", "siteName": "netgear", "keywords": ["Manufacturer of networking hardware and software.", "netgear"], "getIcon": "https://netgear.com/favicon.ico"}, {"url": "nintendo.net", "siteName": "nintendo", "keywords": ["Official website for Nintendo.", "nintendo"], "getIcon": "https://nintendo.net/favicon.ico"}, {"url": "scorecardresearch.com", "siteName": "scorecardresearch", "keywords": ["Web analytics and advertising company.", "scorecardresearch"], "getIcon": "https://scorecardresearch.com/favicon.ico"}, {"url": "playstation.net", "siteName": "playstation", "keywords": ["Network services for PlayStation consoles including PlayStation Network.", "playstation"], "getIcon": "https://playstation.net/favicon.ico"}, {"url": "marketingcloudapis.com", "siteName": "marketingcloudapis", "keywords": ["Provides APIs for Salesforce's Marketing Cloud.", "marketingcloudapis"], "getIcon": "https://marketingcloudapis.com/favicon.ico"}, {"url": "pandora.com", "siteName": "pandora", "keywords": ["Music streaming service offering personalized radio stations.", "pandora"], "getIcon": "https://pandora.com/favicon.ico"}, {"url": "okx.com", "siteName": "okx", "keywords": ["Cryptocurrency exchange platform.", "okx"], "getIcon": "https://okx.com/favicon.ico"}, {"url": "ucweb.com", "siteName": "ucweb", "keywords": ["Mobile internet software company known for UC Browser primarily in Asia.", "ucweb"], "getIcon": "https://ucweb.com/favicon.ico"}, {"url": "nflxext.com", "siteName": "nflxext", "keywords": ["Related to Netflix's media services.", "nflxext"], "getIcon": "https://nflxext.com/favicon.ico"}, {"url": "primis.tech", "siteName": "primis", "keywords": ["Ad tech platform focused on video and monetization solutions.", "primis"], "getIcon": "https://primis.tech/favicon.ico"}, {"url": "phantom.app", "siteName": "phantom", "keywords": ["Likely related to Phantom a Web3 wallet for managing digital assets.", "phantom"], "getIcon": "https://phantom.app/favicon.ico"}, {"url": "vmware.com", "siteName": "vmware", "keywords": ["Provides cloud computing and virtualization technology solutions.", "vmware"], "getIcon": "https://vmware.com/favicon.ico"}, {"url": "safebrowsing.apple", "siteName": "safebrowsing", "keywords": ["Part of Apple's online security initiative offering safe browsing services.", "safebrowsing"], "getIcon": "https://safebrowsing.apple/favicon.ico"}, {"url": "e-planning.net", "siteName": "e-planning", "keywords": ["Provides digital advertising solutions and campaign management.", "e-planning"], "getIcon": "https://e-planning.net/favicon.ico"}, {"url": "deepl.com", "siteName": "deepl", "keywords": ["AI-powered language translation service", "deepl"], "getIcon": "https://deepl.com/favicon.ico"}, {"url": "mediatek.com", "siteName": "mediatek", "keywords": ["Taiwanese semiconductor company known for mobile processors.", "mediatek"], "getIcon": "https://mediatek.com/favicon.ico"}, {"url": "lijit.com", "siteName": "lijit", "keywords": ["Advertising and marketing solutions for publishers.", "lijit"], "getIcon": "https://lijit.com/favicon.ico"}, {"url": "scene7.com", "siteName": "scene7", "keywords": ["Media asset management and dynamic media delivery platform.", "scene7"], "getIcon": "https://scene7.com/favicon.ico"}, {"url": "netflix.net", "siteName": "netflix", "keywords": ["Content delivery network (CDN) for Netflix services.", "netflix"], "getIcon": "https://netflix.net/favicon.ico"}, {"url": "paloaltonetworks.com", "siteName": "paloaltonetworks", "keywords": ["Cybersecurity company offering firewall cloud and endpoint protection services.", "paloaltonetworks"], "getIcon": "https://paloaltonetworks.com/favicon.ico"}, {"url": "pullcm.com", "siteName": "pullcm", "keywords": ["pullcm"], "getIcon": "https://pullcm.com/favicon.ico"}, {"url": "nflxvideo.net", "siteName": "nflxvideo", "keywords": ["Netflix's content delivery network for video.", "nflxvideo"], "getIcon": "https://nflxvideo.net/favicon.ico"}, {"url": "vk.com", "siteName": "vk", "keywords": ["Russian social media platform similar to Facebook.", "vk"], "getIcon": "https://vk.com/favicon.ico"}, {"url": "nextmillmedia.com", "siteName": "nextmillmedia", "keywords": ["Digital media company.", "nextmillmedia"], "getIcon": "https://nextmillmedia.com/favicon.ico"}, {"url": "presage.io", "siteName": "presage", "keywords": ["Platform for customer data management and predictions.", "presage"], "getIcon": "https://presage.io/favicon.ico"}, {"url": "permutive.com", "siteName": "permutive", "keywords": ["Offers data solutions focused on privacy-first advertising and analytics.", "permutive"], "getIcon": "https://permutive.com/favicon.ico"}, {"url": "mgid.com", "siteName": "mgid", "keywords": ["Digital advertising network.", "mgid"], "getIcon": "https://mgid.com/favicon.ico"}, {"url": "e-msedge.net", "siteName": "e-msedge", "keywords": ["Microsoft Edge network services", "e-msedge"], "getIcon": "https://e-msedge.net/favicon.ico"}, {"url": "mmcdn.com", "siteName": "mmcdn", "keywords": ["Used by mobile ad networks and marketing platforms.", "mmcdn"], "getIcon": "https://mmcdn.com/favicon.ico"}, {"url": "transsion-os.com", "siteName": "transsion-os", "keywords": ["Operating system developed by Transsion for mobile devices.", "transsion-os"], "getIcon": "https://transsion-os.com/favicon.ico"}, {"url": "mapbox.com", "siteName": "mapbox", "keywords": ["Provides maps and location-based services for developers.", "mapbox"], "getIcon": "https://mapbox.com/favicon.ico"}, {"url": "samsungqbe.com", "siteName": "samsungqbe", "keywords": ["Related to Samsung's services or products (uncertain).", "samsungqbe"], "getIcon": "https://samsungqbe.com/favicon.ico"}, {"url": "scdn.co", "siteName": "scdn", "keywords": ["Content delivery network (CDN) used for streaming and web services.", "scdn"], "getIcon": "https://scdn.co/favicon.ico"}, {"url": "liftoff.io", "siteName": "liftoff", "keywords": ["Mobile app marketing and user acquisition platform.", "liftoff"], "getIcon": "https://liftoff.io/favicon.ico"}, {"url": "deepintent.com", "siteName": "deepintent", "keywords": ["Marketing technology platform for healthcare", "deepintent"], "getIcon": "https://deepintent.com/favicon.ico"}, {"url": "mediago.io", "siteName": "mediago", "keywords": ["Provides media delivery and hosting services.", "mediago"], "getIcon": "https://mediago.io/favicon.ico"}, {"url": "netflix.com", "siteName": "netflix", "keywords": ["Streaming service offering TV shows movies and original content.", "netflix"], "getIcon": "https://netflix.com/favicon.ico"}, {"url": "pullcf.com", "siteName": "pullcf", "keywords": ["pullcf"], "getIcon": "https://pullcf.com/favicon.ico"}, {"url": "palmplaystore.com", "siteName": "palmplaystore", "keywords": ["Likely related to Palm's app store (acquired by HP).", "palmplaystore"], "getIcon": "https://palmplaystore.com/favicon.ico"}, {"url": "bidswitch.net", "siteName": "bidswitch", "keywords": ["Programmatic ad buying technology", "bidswitch"], "getIcon": "https://bidswitch.net/favicon.ico"}, {"url": "msauth.net", "siteName": "msauth", "keywords": ["Used for Microsoft authentication services.", "msauth"], "getIcon": "https://msauth.net/favicon.ico"}, {"url": "okcdn.ru", "siteName": "okcdn", "keywords": ["Content delivery network (CDN) for Russian services.", "okcdn"], "getIcon": "https://okcdn.ru/favicon.ico"}, {"url": "nflxso.net", "siteName": "nflxso", "keywords": ["Netflix's content delivery network for streaming.", "nflxso"], "getIcon": "https://nflxso.net/favicon.ico"}, {"url": "mlstatic.com", "siteName": "mlstatic", "keywords": ["Used for serving static files in mobile apps (often related to Mi services).", "mlstatic"], "getIcon": "https://mlstatic.com/favicon.ico"}, {"url": "postrelease.com", "siteName": "postrelease", "keywords": ["Marketing agency providing services for mobile apps.", "postrelease"], "getIcon": "https://postrelease.com/favicon.ico"}, {"url": "newrelic.com", "siteName": "newrelic", "keywords": ["Provides performance monitoring and analytics for web and mobile applications.", "newrelic"], "getIcon": "https://newrelic.com/favicon.ico"}, {"url": "mail.ru", "siteName": "mail", "keywords": ["Russian email service and internet company.", "mail"], "getIcon": "https://mail.ru/favicon.ico"}, {"url": "dyndns.org", "siteName": "dyndns", "keywords": ["Dynamic DNS service", "dyndns"], "getIcon": "https://dyndns.org/favicon.ico"}, {"url": "samsungosp.com", "siteName": "samsungosp", "keywords": ["Related to Samsung's mobile operating system and devices.", "samsungosp"], "getIcon": "https://samsungosp.com/favicon.ico"}, {"url": "pki.goog", "siteName": "pki", "keywords": ["Related to Google's public key infrastructure services.", "pki"], "getIcon": "https://pki.goog/favicon.ico"}, {"url": "sc-static.net", "siteName": "sc-static", "keywords": ["Static content hosting domain often related to web services.", "sc-static"], "getIcon": "https://sc-static.net/favicon.ico"}, {"url": "liftoff-creatives.io", "siteName": "liftoff-creatives", "keywords": ["Advertising and marketing technology company.", "liftoff-creatives"], "getIcon": "https://liftoff-creatives.io/favicon.ico"}, {"url": "onenote.net", "siteName": "onenote", "keywords": ["Microsoft's note-taking app.", "onenote"], "getIcon": "https://onenote.net/favicon.ico"}, {"url": "media.net", "siteName": "media", "keywords": ["Digital advertising company focused on display and native ads.", "media"], "getIcon": "https://media.net/favicon.ico"}, {"url": "nest.com", "siteName": "nest", "keywords": ["Smart home products and services (acquired by Google).", "nest"], "getIcon": "https://nest.com/favicon.ico"}, {"url": "trafficmanager.net", "siteName": "trafficmanager", "keywords": ["Managed DNS and global traffic distribution service.", "trafficmanager"], "getIcon": "https://trafficmanager.net/favicon.ico"}, {"url": "pages.dev", "siteName": "pages", "keywords": ["Likely related to Google's Firebase Pages or static hosting service.", "pages"], "getIcon": "https://pages.dev/favicon.ico"}, {"url": "bidr.io", "siteName": "bidr", "keywords": ["Bid management platform", "bidr"], "getIcon": "https://bidr.io/favicon.ico"}, {"url": "office365.com", "siteName": "office365", "keywords": ["Microsoft's cloud-based office productivity suite.", "office365"], "getIcon": "https://office365.com/favicon.ico"}, {"url": "mparticle.com", "siteName": "mparticle", "keywords": ["Provides customer data platform and analytics services.", "mparticle"], "getIcon": "https://mparticle.com/favicon.ico"}, {"url": "microsoftpersonalcontent.com", "siteName": "microsoftpersonalcontent", "keywords": ["Part of Microsoft's personal services.", "microsoftpersonalcontent"], "getIcon": "https://microsoftpersonalcontent.com/favicon.ico"}, {"url": "mixpanel.com", "siteName": "mixpanel", "keywords": ["Provides product analytics and user behavior tracking services.", "mixpanel"], "getIcon": "https://mixpanel.com/favicon.ico"}, {"url": "sc-gw.com", "siteName": "sc-gw", "keywords": ["sc-gw"], "getIcon": "https://sc-gw.com/favicon.ico"}, {"url": "lgtvcommon.com", "siteName": "lgtvcommon", "keywords": ["Related to LG TV services and apps.", "lgtvcommon"], "getIcon": "https://lgtvcommon.com/favicon.ico"}, {"url": "nflximg.net", "siteName": "nflximg", "keywords": ["Netflix's content delivery network for images.", "nflximg"], "getIcon": "https://nflximg.net/favicon.ico"}, {"url": "awswaf.com", "siteName": "awswaf", "keywords": ["AWS Web Application Firewall service", "awswaf"], "getIcon": "https://awswaf.com/favicon.ico"}, {"url": "pubnative.net", "siteName": "pubnative", "keywords": ["Mobile advertising network providing monetization for apps.", "pubnative"], "getIcon": "https://pubnative.net/favicon.ico"}, {"url": "dynatrace.com", "siteName": "dynatrace", "keywords": ["Software intelligence platform for monitoring and analytics", "dynatrace"], "getIcon": "https://dynatrace.com/favicon.ico"}, {"url": "media-amazon.com", "siteName": "media-amazon", "keywords": ["Amazon's domain for media and assets delivery.", "media-amazon"], "getIcon": "https://media-amazon.com/favicon.ico"}, {"url": "onenote.com", "siteName": "onenote", "keywords": ["Microsoft's note-taking app.", "onenote"], "getIcon": "https://onenote.com/favicon.ico"}, {"url": "live.com", "siteName": "live", "keywords": ["Microsoft's web portal including email and other services.", "live"], "getIcon": "https://live.com/favicon.ico"}, {"url": "samsungnyc.com", "siteName": "samsungnyc", "keywords": ["Likely related to Samsung's New York City operations or events.", "samsungnyc"], "getIcon": "https://samsungnyc.com/favicon.ico"}, {"url": "bidmachine.io", "siteName": "bidmachine", "keywords": ["Programmatic advertising technology platform", "bidmachine"], "getIcon": "https://bidmachine.io/favicon.ico"}, {"url": "samsungiotcloud.com", "siteName": "samsungiotcloud", "keywords": ["Samsung's Internet of Things (IoT) cloud platform.", "samsungiotcloud"], "getIcon": "https://samsungiotcloud.com/favicon.ico"}, {"url": "onelink.me", "siteName": "onelink", "keywords": ["Mobile deep linking service for apps.", "onelink"], "getIcon": "https://onelink.me/favicon.ico"}, {"url": "ltwebstatic.com", "siteName": "ltwebstatic", "keywords": ["Static content for websites and apps.", "ltwebstatic"], "getIcon": "https://ltwebstatic.com/favicon.ico"}, {"url": "ap4r.com", "siteName": "ap4r", "keywords": ["Marketing automation services", "ap4r"], "getIcon": "https://ap4r.com/favicon.ico"}, {"url": "nel.goog", "siteName": "nel", "keywords": ["Google's network error logging service.", "nel"], "getIcon": "https://nel.goog/favicon.ico"}, {"url": "al-array.com", "siteName": "al-array", "keywords": ["Data management and analytics services", "al-array"], "getIcon": "https://al-array.com/favicon.ico"}, {"url": "microsoftapp.net", "siteName": "microsoftapp", "keywords": ["Related to Microsoft's application services.", "microsoftapp"], "getIcon": "https://microsoftapp.net/favicon.ico"}, {"url": "mozilla.net", "siteName": "mozilla", "keywords": ["Another domain related to Mozilla services.", "mozilla"], "getIcon": "https://mozilla.net/favicon.ico"}, {"url": "linkedin.com", "siteName": "linkedin", "keywords": ["Social networking site primarily for professional connections.", "linkedin"], "getIcon": "https://linkedin.com/favicon.ico"}, {"url": "epicgames.com", "siteName": "epicgames", "keywords": ["Video game development company creators of Fortnite and Unreal Engine.", "epicgames"], "getIcon": "https://epicgames.com/favicon.ico"}, {"url": "office.com", "siteName": "office", "keywords": ["Microsoft's office productivity suite online services.", "office"], "getIcon": "https://office.com/favicon.ico"}, {"url": "samsungelectronics.com", "siteName": "samsungelectronics", "keywords": ["Official domain for Samsung's electronics division.", "samsungelectronics"], "getIcon": "https://samsungelectronics.com/favicon.ico"}, {"url": "bfmio.com", "siteName": "bfmio", "keywords": ["Marketing technology platform", "bfmio"], "getIcon": "https://bfmio.com/favicon.ico"}, {"url": "onedrive.com", "siteName": "onedrive", "keywords": ["Microsoft's cloud storage service.", "onedrive"], "getIcon": "https://onedrive.com/favicon.ico"}, {"url": "mcafee.com", "siteName": "mcafee", "keywords": ["Cybersecurity company offering antivirus and security solutions.", "mcafee"], "getIcon": "https://mcafee.com/favicon.ico"}, {"url": "pubmatic.com", "siteName": "pubmatic", "keywords": ["Programmatic advertising company providing solutions for publishers.", "pubmatic"], "getIcon": "https://pubmatic.com/favicon.ico"}, {"url": "loopme.me", "siteName": "loopme", "keywords": ["Mobile advertising company focused on video and engagement.", "loopme"], "getIcon": "https://loopme.me/favicon.ico"}, {"url": "line-apps.com", "siteName": "line-apps", "keywords": ["Used for services related to the LINE messaging app.", "line-apps"], "getIcon": "https://line-apps.com/favicon.ico"}, {"url": "microsoft.us", "siteName": "microsoft", "keywords": ["Microsoft's domain for its US-based services.", "microsoft"], "getIcon": "https://microsoft.us/favicon.ico"}, {"url": "ne.jp", "siteName": "ne", "keywords": ["Japanese domain related to Nifty Corporation.", "ne"], "getIcon": "https://ne.jp/favicon.ico"}, {"url": "anythinktech.com", "siteName": "anythinktech", "keywords": ["Technology solutions and services", "anythinktech"], "getIcon": "https://anythinktech.com/favicon.ico"}, {"url": "lenovo.com", "siteName": "lenovo", "keywords": ["Chinese multinational technology company known for laptops and computers.", "lenovo"], "getIcon": "https://lenovo.com/favicon.ico"}, {"url": "oculus.com", "siteName": "oculus", "keywords": ["Virtual reality platform owned by Meta.", "oculus"], "getIcon": "https://oculus.com/favicon.ico"}, {"url": "adtelligent.com", "siteName": "adtelligent", "keywords": ["Ad tech platform for digital advertising", "adtelligent"], "getIcon": "https://adtelligent.com/favicon.ico"}, {"url": "piojm.tech", "siteName": "piojm", "keywords": ["piojm"], "getIcon": "https://piojm.tech/favicon.ico"}, {"url": "mozilla.com", "siteName": "mozilla", "keywords": ["Official website for Mozilla Corporation creator of Firefox.", "mozilla"], "getIcon": "https://mozilla.com/favicon.ico"}, {"url": "entrust.net", "siteName": "entrust", "keywords": ["Provides digital certificates and secure encryption solutions.", "entrust"], "getIcon": "https://entrust.net/favicon.ico"}, {"url": "one.one", "siteName": "one", "keywords": ["A domain associated with Cloudflare's DNS services.", "one"], "getIcon": "https://one.one/favicon.ico"}, {"url": "mookie1.com", "siteName": "mookie1", "keywords": ["Advertising network and ad serving platform.", "mookie1"], "getIcon": "https://mookie1.com/favicon.ico"}, {"url": "beyla.site", "siteName": "beyla", "keywords": ["Web-based services platform", "beyla"], "getIcon": "https://beyla.site/favicon.ico"}, {"url": "mathtag.com", "siteName": "mathtag", "keywords": ["Ad tech company providing solutions for digital marketing.", "mathtag"], "getIcon": "https://mathtag.com/favicon.ico"}, {"url": "livechatinc.com", "siteName": "livechatinc", "keywords": ["Provider of live chat software for customer service.", "livechatinc"], "getIcon": "https://livechatinc.com/favicon.ico"}, {"url": "mfadsrvr.com", "siteName": "mfadsrvr", "keywords": ["mfadsrvr"], "getIcon": "https://mfadsrvr.com/favicon.ico"}, {"url": "lencr.org", "siteName": "lencr", "keywords": ["lencr"], "getIcon": "https://lencr.org/favicon.ico"}, {"url": "nvidia.com", "siteName": "nvidia", "keywords": ["Technology company known for graphics processing units (GPUs).", "nvidia"], "getIcon": "https://nvidia.com/favicon.ico"}, {"url": "omtrdc.net", "siteName": "omtrdc", "keywords": ["Related to Omniture's marketing and analytics platform.", "omtrdc"], "getIcon": "https://omtrdc.net/favicon.ico"}, {"url": "msftconnecttest.com", "siteName": "msftconnecttest", "keywords": ["Used by Microsoft to check network connectivity.", "msftconnecttest"], "getIcon": "https://msftconnecttest.com/favicon.ico"}, {"url": "miui.com", "siteName": "miui", "keywords": ["Xiaomi's custom Android operating system.", "miui"], "getIcon": "https://miui.com/favicon.ico"}, {"url": "emxdgt.com", "siteName": "emxdgt", "keywords": ["emxdgt"], "getIcon": "https://emxdgt.com/favicon.ico"}, {"url": "pinterest.com", "siteName": "pinterest", "keywords": ["Social media platform focused on image sharing and idea curation.", "pinterest"], "getIcon": "https://pinterest.com/favicon.ico"}, {"url": "live.net", "siteName": "live", "keywords": ["Microsoft's web portal including email and other services.", "live"], "getIcon": "https://live.net/favicon.ico"}, {"url": "moloco.com", "siteName": "moloco", "keywords": ["Provides programmatic advertising technology.", "moloco"], "getIcon": "https://moloco.com/favicon.ico"}, {"url": "betweendigital.com", "siteName": "betweendigital", "keywords": ["Digital marketing services", "betweendigital"], "getIcon": "https://betweendigital.com/favicon.ico"}, {"url": "baidu.com", "siteName": "baidu", "keywords": ["Chinese search engine and technology company", "baidu"], "getIcon": "https://baidu.com/favicon.ico"}, {"url": "omnitagjs.com", "siteName": "omnitagjs", "keywords": ["Related to Omniture's analytics and marketing platform.", "omnitagjs"], "getIcon": "https://omnitagjs.com/favicon.ico"}, {"url": "mossru.com", "siteName": "mossru", "keywords": ["mossru"], "getIcon": "https://mossru.com/favicon.ico"}, {"url": "minutemedia-prebid.com", "siteName": "minutemedia-prebid", "keywords": ["Related to MinuteMedia's advertising and media platforms.", "minutemedia-prebid"], "getIcon": "https://minutemedia-prebid.com/favicon.ico"}, {"url": "msftauth.net", "siteName": "msftauth", "keywords": ["Used for Microsoft authentication and authorization.", "msftauth"], "getIcon": "https://msftauth.net/favicon.ico"}, {"url": "elasticbeanstalk.com", "siteName": "elasticbeanstalk", "keywords": ["Amazon Web Services (AWS) platform for deploying and managing applications.", "elasticbeanstalk"], "getIcon": "https://elasticbeanstalk.com/favicon.ico"}, {"url": "metamask.io", "siteName": "metamask", "keywords": ["A browser extension and mobile app for interacting with the Ethereum blockchain.", "metamask"], "getIcon": "https://metamask.io/favicon.ico"}, {"url": "belkin.com", "siteName": "belkin", "keywords": ["Electronics and networking solutions provider", "belkin"], "getIcon": "https://belkin.com/favicon.ico"}, {"url": "b-msedge.net", "siteName": "b-msedge", "keywords": ["Microsoft Edge services for browser connectivity", "b-msedge"], "getIcon": "https://b-msedge.net/favicon.ico"}, {"url": "moengage.com", "siteName": "moengage", "keywords": ["Provides customer engagement and marketing automation platform.", "moengage"], "getIcon": "https://moengage.com/favicon.ico"}, {"url": "mosspf.com", "siteName": "mosspf", "keywords": ["mosspf"], "getIcon": "https://mosspf.com/favicon.ico"}, {"url": "msedge.net", "siteName": "msedge", "keywords": ["Related to Microsoft Edge browser services.", "msedge"], "getIcon": "https://msedge.net/favicon.ico"}, {"url": "edgesuite.net", "siteName": "edgesuite", "keywords": ["Content delivery network (CDN) service by Akamai.", "edgesuite"], "getIcon": "https://edgesuite.net/favicon.ico"}, {"url": "trafficjunky.net", "siteName": "trafficjunky", "keywords": ["Ad network specializing in display and native advertising solutions.", "trafficjunky"], "getIcon": "https://trafficjunky.net/favicon.ico"}, {"url": "aws.dev", "siteName": "aws", "keywords": ["AWS (Amazon Web Services) developer platform", "aws"], "getIcon": "https://aws.dev/favicon.ico"}, {"url": "licdn.com", "siteName": "licdn", "keywords": ["LinkedIn's domain for professional networking.", "licdn"], "getIcon": "https://licdn.com/favicon.ico"}, {"url": "mercadolibre.com", "siteName": "mercadolibre", "keywords": ["Latin American e-commerce platform.", "mercadolibre"], "getIcon": "https://mercadolibre.com/favicon.ico"}, {"url": "b-cdn.net", "siteName": "b-cdn", "keywords": ["Content delivery network services", "b-cdn"], "getIcon": "https://b-cdn.net/favicon.ico"}, {"url": "mobiuspace.net", "siteName": "mobiuspace", "keywords": ["mobiuspace"], "getIcon": "https://mobiuspace.net/favicon.ico"}, {"url": "msecnd.net", "siteName": "msecnd", "keywords": ["Used by Microsoft for hosting content and software delivery.", "msecnd"], "getIcon": "https://msecnd.net/favicon.ico"}, {"url": "miniclippt.com", "siteName": "miniclippt", "keywords": ["Used by Miniclip for online gaming services.", "miniclippt"], "getIcon": "https://miniclippt.com/favicon.ico"}, {"url": "edgekey.net", "siteName": "edgekey", "keywords": ["Content delivery network (CDN) service by Akamai.", "edgekey"], "getIcon": "https://edgekey.net/favicon.ico"}, {"url": "mtgglobals.com", "siteName": "mtgglobals", "keywords": ["Related to Magic: The Gathering online services.", "mtgglobals"], "getIcon": "https://mtgglobals.com/favicon.ico"}, {"url": "adswizz.com", "siteName": "adswizz", "keywords": ["Audio advertising and monetization solutions", "adswizz"], "getIcon": "https://adswizz.com/favicon.ico"}, {"url": "awardpongsur.org", "siteName": "awardpongsur", "keywords": ["Non-profit organization", "awardpongsur"], "getIcon": "https://awardpongsur.org/favicon.ico"}, {"url": "mega.co.nz", "siteName": "mega", "keywords": ["Cloud storage and file hosting service.", "mega"], "getIcon": "https://mega.co.nz/favicon.ico"}, {"url": "tradplusad.com", "siteName": "tradplusad", "keywords": ["Ad tech platform providing solutions for mobile ads and monetization.", "tradplusad"], "getIcon": "https://tradplusad.com/favicon.ico"}, {"url": "viber.com", "siteName": "viber", "keywords": ["Messaging and VoIP service for text voice and video calls.", "viber"], "getIcon": "https://viber.com/favicon.ico"}, {"url": "meraki.com", "siteName": "me<PERSON>i", "keywords": ["Cisco's cloud-managed networking company.", "me<PERSON>i"], "getIcon": "https://meraki.com/favicon.ico"}, {"url": "liadm.com", "siteName": "liadm", "keywords": ["liadm"], "getIcon": "https://liadm.com/favicon.ico"}, {"url": "mob.com", "siteName": "mob", "keywords": ["mob"], "getIcon": "https://mob.com/favicon.ico"}, {"url": "azurewebsites.net", "siteName": "azurewebsites", "keywords": ["Microsoft Azure platform for hosting websites", "azurewebsites"], "getIcon": "https://azurewebsites.net/favicon.ico"}, {"url": "mikrotik.com", "siteName": "mikrotik", "keywords": ["Manufacturer of networking hardware and software.", "mikrotik"], "getIcon": "https://mikrotik.com/favicon.ico"}, {"url": "tiktokv.us", "siteName": "tiktokv", "keywords": ["TikTok's U.S. video delivery network.", "tiktokv"], "getIcon": "https://tiktokv.us/favicon.ico"}, {"url": "meethue.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Related to Philips Hue smart lighting platform.", "<PERSON><PERSON><PERSON>"], "getIcon": "https://meethue.com/favicon.ico"}, {"url": "lgtvsdp.com", "siteName": "lgtvsdp", "keywords": ["Related to LG Smart TV platform.", "lgtvsdp"], "getIcon": "https://lgtvsdp.com/favicon.ico"}, {"url": "samsungdm.com", "siteName": "samsungdm", "keywords": ["Related to Samsung's device management services.", "samsungdm"], "getIcon": "https://samsungdm.com/favicon.ico"}, {"url": "verkada.com", "siteName": "verkada", "keywords": ["Provides security solutions such as cameras and access control systems for businesses.", "verkada"], "getIcon": "https://verkada.com/favicon.ico"}, {"url": "mmstat.com", "siteName": "mmstat", "keywords": ["Used by mobile analytics and advertising platforms.", "mmstat"], "getIcon": "https://mmstat.com/favicon.ico"}, {"url": "msidentity.com", "siteName": "msidentity", "keywords": ["Microsoft's identity platform for developers.", "msidentity"], "getIcon": "https://msidentity.com/favicon.ico"}, {"url": "mediavine.com", "siteName": "mediavine", "keywords": ["Provides ad network services for content creators.", "mediavine"], "getIcon": "https://mediavine.com/favicon.ico"}, {"url": "verisign.com", "siteName": "verisign", "keywords": ["Provider of domain name registry services and internet security.", "verisign"], "getIcon": "https://verisign.com/favicon.ico"}, {"url": "mmechocaptiveportal.com", "siteName": "mmechocaptiveportal", "keywords": ["Used for captive portal redirection in networking.", "mmechocaptiveportal"], "getIcon": "https://mmechocaptiveportal.com/favicon.ico"}, {"url": "msftncsi.com", "siteName": "msftncsi", "keywords": ["Used by Microsoft to detect network status on Windows devices.", "msftncsi"], "getIcon": "https://msftncsi.com/favicon.ico"}, {"url": "v-videoapp.com", "siteName": "v-videoapp", "keywords": ["v-videoapp"], "getIcon": "https://v-videoapp.com/favicon.ico"}, {"url": "tradingview.com", "siteName": "tradingview", "keywords": ["Financial charting platform used for analyzing and tracking financial markets.", "tradingview"], "getIcon": "https://tradingview.com/favicon.ico"}, {"url": "miwifi.com", "siteName": "<PERSON><PERSON><PERSON>", "keywords": ["Xiaomi's router and networking hardware.", "<PERSON><PERSON><PERSON>"], "getIcon": "https://miwifi.com/favicon.ico"}, {"url": "tq-tungsten.com", "siteName": "tq-tungsten", "keywords": ["tq-tungsten"], "getIcon": "https://tq-tungsten.com/favicon.ico"}, {"url": "avira.com", "siteName": "avira", "keywords": ["Antivirus and security software", "avira"], "getIcon": "https://avira.com/favicon.ico"}, {"url": "nubank.com.br", "siteName": "nubank", "keywords": ["Brazilian digital bank.", "nubank"], "getIcon": "https://nubank.com.br/favicon.ico"}, {"url": "avcdn.net", "siteName": "avcdn", "keywords": ["Content delivery network by Avast", "avcdn"], "getIcon": "https://avcdn.net/favicon.ico"}, {"url": "ntppool.org", "siteName": "ntppool", "keywords": ["Global NTP server network for time synchronization.", "ntppool"], "getIcon": "https://ntppool.org/favicon.ico"}, {"url": "avast.com", "siteName": "avast", "keywords": ["Antivirus and cybersecurity software", "avast"], "getIcon": "https://avast.com/favicon.ico"}, {"url": "ntp.org", "siteName": "ntp", "keywords": ["Network Time Protocol used for time synchronization.", "ntp"], "getIcon": "https://ntp.org/favicon.ico"}, {"url": "av380.net", "siteName": "av380", "keywords": ["Video streaming platform", "av380"], "getIcon": "https://av380.net/favicon.ico"}, {"url": "nr-data.net", "siteName": "nr-data", "keywords": ["nr-data"], "getIcon": "https://nr-data.net/favicon.ico"}, {"url": "adscale.de", "siteName": "adscale", "keywords": ["German-based digital advertising network", "adscale"], "getIcon": "https://adscale.de/favicon.ico"}, {"url": "battle.net", "siteName": "battle", "keywords": ["Online gaming platform by Blizzard Entertainment", "battle"], "getIcon": "https://battle.net/favicon.ico"}, {"url": "bamgrid.com", "siteName": "bamgrid", "keywords": ["Ad tech services and solutions", "bamgrid"], "getIcon": "https://bamgrid.com/favicon.ico"}]}