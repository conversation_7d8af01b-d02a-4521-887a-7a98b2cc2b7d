"use strict";
const { parentPort } = require("node:worker_threads");
const fs = require("node:fs");
const http = require("node:http");
const path = require("node:path");
if (!parentPort) {
  throw new Error("必须在 Worker 线程中运行");
}
function generateBoundary() {
  return "------------------------" + Date.now().toString(16);
}
function createMultipartBody(source, filePath, boundary) {
  const fileName = path.basename(filePath);
  const fileStream = fs.createReadStream(filePath);
  const header = Buffer.from(
    [
      `--${boundary}`,
      `Content-Disposition: form-data; name="knowledgeId"`,
      "",
      source,
      `--${boundary}`,
      `Content-Disposition: form-data; name="file"; filename="${fileName}"`,
      "Content-Type: application/octet-stream",
      "",
      ""
    ].join("\r\n"),
    "utf-8"
  );
  const footer = Buffer.from(`\r
--${boundary}--\r
`, "utf-8");
  return { header, fileStream, footer };
}
const uploadRequests = /* @__PURE__ */ new Map();
async function uploadFile(source, fileId, filePath, token, userIdForUpload, uploadApiUrl, resourceId, resourceType) {
  try {
    const stats = await fs.promises.stat(filePath);
    const totalSize = stats.size;
    let uploadedSize = 0;
    const boundary = generateBoundary();
    const { header, fileStream, footer } = createMultipartBody(source, filePath, boundary);
    const controller = new AbortController();
    const urlObj = new URL(uploadApiUrl);
    const { hostname, port, pathname } = urlObj;
    console.log("Upload URL:", hostname, port, pathname);
    return new Promise((resolve, reject) => {
      const headers = {
        "Content-Type": `multipart/form-data; boundary=${boundary}`,
        userId: userIdForUpload,
        token
      };
      if (resourceId) {
        headers["resource-Id"] = resourceId;
        if (resourceType) {
          headers["resource-Type"] = resourceType;
        }
      }
      const request = http.request(
        {
          hostname,
          port,
          path: pathname,
          method: "POST",
          headers,
          signal: controller.signal
          // 添加 signal
        },
        (response) => {
          let data = "";
          response.on("data", (chunk) => {
            data += chunk;
          });
          response.on("end", () => {
            uploadRequests.delete(fileId);
            const result = JSON.parse(data);
            resolve(result);
          });
        }
      );
      uploadRequests.set(fileId, {
        request,
        fileStream,
        controller
      });
      request.on("error", (error) => {
        uploadRequests.delete(fileId);
        reject(error);
      });
      request.write(header);
      parentPort.postMessage({
        type: "open",
        fileId
      });
      fileStream.pipe(request, { end: false });
      fileStream.on("end", () => {
        request.end(footer);
        console.log("Request Headers:", request.getHeaders());
      });
    }).then((result) => {
      uploadRequests.delete(fileId);
      console.log("Upload Promise result:", result);
      if (result.code === 200) {
        parentPort.postMessage({
          type: "success",
          fileId,
          data: result.data,
          source,
          resourceId,
          resourceType
        });
      } else {
        parentPort.postMessage({
          type: "error",
          fileId,
          data: result
        });
      }
    }).catch((error) => {
      console.log("Upload Promise error:", error);
      uploadRequests.delete(fileId);
      parentPort.postMessage({
        type: "error",
        fileId,
        data: error
      });
    });
  } catch (error) {
    uploadRequests.delete(fileId);
    console.log("Upload error:", error);
    parentPort.postMessage({
      type: "error",
      fileId,
      error: error.message
    });
  }
}
parentPort.on("message", (message) => {
  const {
    type,
    source,
    fileId,
    filePath,
    token,
    userIdForUpload,
    uploadApiUrl,
    resourceId,
    resourceType
  } = message;
  parentPort.postMessage({
    type: "on worker message",
    uploadInfo: message
  });
  if (type === "upload") {
    uploadFile(
      source,
      fileId,
      filePath,
      token,
      userIdForUpload,
      uploadApiUrl,
      resourceId,
      resourceType
    );
  }
  if (type === "pause" || type === "delete") {
    const uploadRequest = uploadRequests.get(fileId);
    console.log("uploadRequest", uploadRequest);
    if (uploadRequest) {
      try {
        const { fileStream, controller, request } = uploadRequest;
        fileStream.removeAllListeners();
        request.removeAllListeners();
        fileStream.destroy();
        console.log("文件流已消毁", fileId);
        uploadRequests.delete(fileId);
        parentPort.postMessage({
          type,
          fileId
        });
      } catch (error) {
        console.error("pause error", error);
      }
    }
  }
});
//# sourceMappingURL=fileUpload.js.map
