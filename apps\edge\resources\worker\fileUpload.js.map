{"version": 3, "file": "fileUpload.js", "sources": ["../../src/main/Service/fileSync/core/FileUploadWorkerNative.ts"], "sourcesContent": ["/*\r\n * @Description:\r\n * @FilePath: \\ainow-ui\\apps\\edge\\src\\main\\Service\\fileSync\\core\\FileUploadWorkerNative.ts\r\n */\r\n\r\nconst { parentPort } = require('node:worker_threads')\r\nconst fs = require('node:fs')\r\nconst http = require('node:http')\r\nconst path = require('node:path')\r\n\r\n// 定义消息类型\r\ninterface UploadMessage {\r\n  type: 'upload' | 'pause' | 'delete'\r\n  source: string\r\n  fileId: string\r\n  filePath: string\r\n  token: string\r\n  userIdForUpload: string\r\n  uploadApiUrl: string\r\n  resourceId: string\r\n  resourceType: string\r\n}\r\n\r\ninterface ResultMessage {\r\n  type: 'success' | 'error' | 'open' | 'progress' | 'pause'\r\n  fileId: string\r\n  data?: any\r\n  error?: string\r\n  source?: string\r\n}\r\n\r\ninterface ApiResponse {\r\n  code: number\r\n  message?: string\r\n  data?: any\r\n}\r\n\r\nif (!parentPort) {\r\n  throw new Error('必须在 Worker 线程中运行')\r\n}\r\n\r\n// 生成随机边界字符串\r\nfunction generateBoundary() {\r\n  return '------------------------' + Date.now().toString(16)\r\n}\r\n\r\n// 构建 multipart/form-data 请求体\r\nfunction createMultipartBody(source: string, filePath: string, boundary: string) {\r\n  const fileName = path.basename(filePath)\r\n  const fileStream = fs.createReadStream(filePath)\r\n\r\n  const header = Buffer.from(\r\n    [\r\n      `--${boundary}`,\r\n      `Content-Disposition: form-data; name=\"knowledgeId\"`,\r\n      '',\r\n      source,\r\n      `--${boundary}`,\r\n      `Content-Disposition: form-data; name=\"file\"; filename=\"${fileName}\"`,\r\n      'Content-Type: application/octet-stream',\r\n      '',\r\n      ''\r\n    ].join('\\r\\n'),\r\n    'utf-8'\r\n  )\r\n\r\n  const footer = Buffer.from(`\\r\\n--${boundary}--\\r\\n`, 'utf-8')\r\n\r\n  return { header, fileStream, footer }\r\n}\r\n\r\n// 存储上传请求的 Map\r\nconst uploadRequests = new Map<\r\n  string,\r\n  {\r\n    request: http.ClientRequest\r\n    fileStream: fs.ReadStream\r\n    controller: AbortController\r\n  }\r\n>()\r\n\r\nasync function uploadFile(\r\n  source: string,\r\n  fileId: string,\r\n  filePath: string,\r\n  token: string,\r\n  userIdForUpload: string,\r\n  uploadApiUrl: string,\r\n  resourceId?: string,\r\n  resourceType?: string\r\n) {\r\n  try {\r\n    const stats = await fs.promises.stat(filePath)\r\n    const totalSize = stats.size\r\n    let uploadedSize = 0\r\n\r\n    const boundary = generateBoundary()\r\n    const { header, fileStream, footer } = createMultipartBody(source, filePath, boundary)\r\n    const controller = new AbortController()\r\n\r\n    const urlObj = new URL(uploadApiUrl)\r\n    const { hostname, port, pathname } = urlObj\r\n    console.log('Upload URL:', hostname, port, pathname)\r\n\r\n    return new Promise((resolve, reject) => {\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': `multipart/form-data; boundary=${boundary}`,\r\n        userId: userIdForUpload,\r\n        token\r\n      }\r\n      if (resourceId) {\r\n        headers['resource-Id'] = resourceId\r\n        if (resourceType) {\r\n          headers['resource-Type'] = resourceType\r\n        }\r\n      }\r\n\r\n      const request = http.request(\r\n        {\r\n          hostname,\r\n          port,\r\n          path: pathname,\r\n          method: 'POST',\r\n          headers,\r\n          signal: controller.signal // 添加 signal\r\n        },\r\n        (response) => {\r\n          let data = ''\r\n          response.on('data', (chunk) => {\r\n            data += chunk\r\n          })\r\n\r\n          response.on('end', () => {\r\n            uploadRequests.delete(fileId)\r\n            const result: ApiResponse = JSON.parse(data)\r\n            resolve(result)\r\n          })\r\n        }\r\n      )\r\n\r\n      // 存储请求实例\r\n      uploadRequests.set(fileId, {\r\n        request,\r\n        fileStream,\r\n        controller\r\n      })\r\n\r\n      request.on('error', (error) => {\r\n        uploadRequests.delete(fileId)\r\n        reject(error)\r\n      })\r\n\r\n      // 写入 header\r\n      request.write(header)\r\n\r\n      parentPort!.postMessage({\r\n        type: 'open',\r\n        fileId\r\n      } as ResultMessage)\r\n\r\n      // 监听文件流进度\r\n      // fileStream.on('data', (chunk: Buffer) => {\r\n      //   uploadedSize += chunk.length\r\n      //   const progress = Math.round((uploadedSize / totalSize) * 100)\r\n\r\n      //   parentPort!.postMessage({\r\n      //     type: 'progress',\r\n      //     fileId,\r\n      //     data: {\r\n      //       progress,\r\n      //       uploaded: uploadedSize,\r\n      //       total: totalSize\r\n      //     }\r\n      //   })\r\n      // })\r\n\r\n      // 通过管道传输文件数据\r\n      fileStream.pipe(request, { end: false })\r\n\r\n      // 文件传输完成后写入 footer 并结束请求\r\n      fileStream.on('end', () => {\r\n        request.end(footer)\r\n\r\n        // 打印请求信息\r\n        console.log('Request Headers:', request.getHeaders())\r\n      })\r\n    })\r\n      .then((result: ApiResponse) => {\r\n        uploadRequests.delete(fileId)\r\n        console.log('Upload Promise result:', result)\r\n        if (result.code === 200) {\r\n          parentPort!.postMessage({\r\n            type: 'success',\r\n            fileId,\r\n            data: result.data,\r\n            source,\r\n            resourceId,\r\n            resourceType\r\n          } as ResultMessage)\r\n        } else {\r\n          parentPort!.postMessage({\r\n            type: 'error',\r\n            fileId,\r\n            data: result\r\n          } as ResultMessage)\r\n        }\r\n      })\r\n      .catch((error: any) => {\r\n        console.log('Upload Promise error:', error)\r\n\r\n        uploadRequests.delete(fileId)\r\n        parentPort!.postMessage({\r\n          type: 'error',\r\n          fileId,\r\n          data: error\r\n        } as ResultMessage)\r\n      })\r\n  } catch (error: any) {\r\n    uploadRequests.delete(fileId)\r\n    console.log('Upload error:', error)\r\n    parentPort!.postMessage({\r\n      type: 'error',\r\n      fileId,\r\n      error: error.message\r\n    } as ResultMessage)\r\n  }\r\n}\r\n\r\n// 监听主线程消息\r\nparentPort.on('message', (message: UploadMessage) => {\r\n  const {\r\n    type,\r\n    source,\r\n    fileId,\r\n    filePath,\r\n    token,\r\n    userIdForUpload,\r\n    uploadApiUrl,\r\n    resourceId,\r\n    resourceType\r\n  } = message\r\n\r\n  parentPort!.postMessage({\r\n    type: 'on worker message',\r\n    uploadInfo: message\r\n  })\r\n  if (type === 'upload') {\r\n    uploadFile(\r\n      source,\r\n      fileId,\r\n      filePath,\r\n      token,\r\n      userIdForUpload,\r\n      uploadApiUrl,\r\n      resourceId,\r\n      resourceType\r\n    )\r\n  }\r\n\r\n  if (type === 'pause' || type === 'delete') {\r\n    const uploadRequest = uploadRequests.get(fileId)\r\n    console.log('uploadRequest', uploadRequest)\r\n    if (uploadRequest) {\r\n      try {\r\n        const { fileStream, controller, request } = uploadRequest\r\n\r\n        // 中止请求\r\n        // controller.abort()\r\n\r\n        // 移除所有事件监听器\r\n        fileStream.removeAllListeners()\r\n        request.removeAllListeners()\r\n\r\n        // 先中止请求\r\n        // request.abort()\r\n\r\n        // 销毁文件流\r\n        fileStream.destroy()\r\n        console.log('文件流已消毁', fileId)\r\n\r\n        // 销毁请求\r\n        // request.destroy()\r\n        // console.log('请求已销毁', fileId)\r\n\r\n        uploadRequests.delete(fileId)\r\n\r\n        parentPort!.postMessage({\r\n          type,\r\n          fileId\r\n        } as ResultMessage)\r\n      } catch (error) {\r\n        console.error('pause error', error)\r\n      }\r\n    }\r\n  }\r\n})\r\n"], "names": [], "mappings": ";AAKA,MAAM,EAAE,WAAA,IAAe,QAAQ,qBAAqB;AACpD,MAAM,KAAK,QAAQ,SAAS;AAC5B,MAAM,OAAO,QAAQ,WAAW;AAChC,MAAM,OAAO,QAAQ,WAAW;AA6BhC,IAAI,CAAC,YAAY;AACT,QAAA,IAAI,MAAM,kBAAkB;AACpC;AAGA,SAAS,mBAAmB;AAC1B,SAAO,6BAA6B,KAAK,IAAI,EAAE,SAAS,EAAE;AAC5D;AAGA,SAAS,oBAAoB,QAAgB,UAAkB,UAAkB;AACzE,QAAA,WAAW,KAAK,SAAS,QAAQ;AACjC,QAAA,aAAa,GAAG,iBAAiB,QAAQ;AAE/C,QAAM,SAAS,OAAO;AAAA,IACpB;AAAA,MACE,KAAK,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,QAAQ;AAAA,MACb,0DAA0D,QAAQ;AAAA,MAClE;AAAA,MACA;AAAA,MACA;AAAA,IAAA,EACA,KAAK,MAAM;AAAA,IACb;AAAA,EACF;AAEM,QAAA,SAAS,OAAO,KAAK;AAAA,IAAS,QAAQ;AAAA,GAAU,OAAO;AAEtD,SAAA,EAAE,QAAQ,YAAY,OAAO;AACtC;AAGA,MAAM,qCAAqB,IAOzB;AAEF,eAAe,WACb,QACA,QACA,UACA,OACA,iBACA,cACA,YACA,cACA;AACI,MAAA;AACF,UAAM,QAAQ,MAAM,GAAG,SAAS,KAAK,QAAQ;AAC7C,UAAM,YAAY,MAAM;AACxB,QAAI,eAAe;AAEnB,UAAM,WAAW,iBAAiB;AAC5B,UAAA,EAAE,QAAQ,YAAY,OAAA,IAAW,oBAAoB,QAAQ,UAAU,QAAQ;AAC/E,UAAA,aAAa,IAAI,gBAAgB;AAEjC,UAAA,SAAS,IAAI,IAAI,YAAY;AACnC,UAAM,EAAE,UAAU,MAAM,SAAa,IAAA;AACrC,YAAQ,IAAI,eAAe,UAAU,MAAM,QAAQ;AAEnD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,UAAkC;AAAA,QACtC,gBAAgB,iCAAiC,QAAQ;AAAA,QACzD,QAAQ;AAAA,QACR;AAAA,MACF;AACA,UAAI,YAAY;AACd,gBAAQ,aAAa,IAAI;AACzB,YAAI,cAAc;AAChB,kBAAQ,eAAe,IAAI;AAAA,QAAA;AAAA,MAC7B;AAGF,YAAM,UAAU,KAAK;AAAA,QACnB;AAAA,UACE;AAAA,UACA;AAAA,UACA,MAAM;AAAA,UACN,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ,WAAW;AAAA;AAAA,QACrB;AAAA,QACA,CAAC,aAAa;AACZ,cAAI,OAAO;AACF,mBAAA,GAAG,QAAQ,CAAC,UAAU;AACrB,oBAAA;AAAA,UAAA,CACT;AAEQ,mBAAA,GAAG,OAAO,MAAM;AACvB,2BAAe,OAAO,MAAM;AACtB,kBAAA,SAAsB,KAAK,MAAM,IAAI;AAC3C,oBAAQ,MAAM;AAAA,UAAA,CACf;AAAA,QAAA;AAAA,MAEL;AAGA,qBAAe,IAAI,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACD;AAEO,cAAA,GAAG,SAAS,CAAC,UAAU;AAC7B,uBAAe,OAAO,MAAM;AAC5B,eAAO,KAAK;AAAA,MAAA,CACb;AAGD,cAAQ,MAAM,MAAM;AAEpB,iBAAY,YAAY;AAAA,QACtB,MAAM;AAAA,QACN;AAAA,MAAA,CACgB;AAmBlB,iBAAW,KAAK,SAAS,EAAE,KAAK,OAAO;AAG5B,iBAAA,GAAG,OAAO,MAAM;AACzB,gBAAQ,IAAI,MAAM;AAGlB,gBAAQ,IAAI,oBAAoB,QAAQ,WAAA,CAAY;AAAA,MAAA,CACrD;AAAA,IAAA,CACF,EACE,KAAK,CAAC,WAAwB;AAC7B,qBAAe,OAAO,MAAM;AACpB,cAAA,IAAI,0BAA0B,MAAM;AACxC,UAAA,OAAO,SAAS,KAAK;AACvB,mBAAY,YAAY;AAAA,UACtB,MAAM;AAAA,UACN;AAAA,UACA,MAAM,OAAO;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QAAA,CACgB;AAAA,MAAA,OACb;AACL,mBAAY,YAAY;AAAA,UACtB,MAAM;AAAA,UACN;AAAA,UACA,MAAM;AAAA,QAAA,CACU;AAAA,MAAA;AAAA,IACpB,CACD,EACA,MAAM,CAAC,UAAe;AACb,cAAA,IAAI,yBAAyB,KAAK;AAE1C,qBAAe,OAAO,MAAM;AAC5B,iBAAY,YAAY;AAAA,QACtB,MAAM;AAAA,QACN;AAAA,QACA,MAAM;AAAA,MAAA,CACU;AAAA,IAAA,CACnB;AAAA,WACI,OAAY;AACnB,mBAAe,OAAO,MAAM;AACpB,YAAA,IAAI,iBAAiB,KAAK;AAClC,eAAY,YAAY;AAAA,MACtB,MAAM;AAAA,MACN;AAAA,MACA,OAAO,MAAM;AAAA,IAAA,CACG;AAAA,EAAA;AAEtB;AAGA,WAAW,GAAG,WAAW,CAAC,YAA2B;AAC7C,QAAA;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AAEJ,aAAY,YAAY;AAAA,IACtB,MAAM;AAAA,IACN,YAAY;AAAA,EAAA,CACb;AACD,MAAI,SAAS,UAAU;AACrB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAGE,MAAA,SAAS,WAAW,SAAS,UAAU;AACnC,UAAA,gBAAgB,eAAe,IAAI,MAAM;AACvC,YAAA,IAAI,iBAAiB,aAAa;AAC1C,QAAI,eAAe;AACb,UAAA;AACF,cAAM,EAAE,YAAY,YAAY,QAAY,IAAA;AAM5C,mBAAW,mBAAmB;AAC9B,gBAAQ,mBAAmB;AAM3B,mBAAW,QAAQ;AACX,gBAAA,IAAI,UAAU,MAAM;AAM5B,uBAAe,OAAO,MAAM;AAE5B,mBAAY,YAAY;AAAA,UACtB;AAAA,UACA;AAAA,QAAA,CACgB;AAAA,eACX,OAAO;AACN,gBAAA,MAAM,eAAe,KAAK;AAAA,MAAA;AAAA,IACpC;AAAA,EACF;AAEJ,CAAC;"}