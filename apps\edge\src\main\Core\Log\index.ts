import { app } from 'electron'
import Logger from './log'
export const Log = new Logger(app.getPath('logs'), 'main')
export const logCombine = (path: string, id?: string) => {
  const logger = new Logger(path, id)
  const { info, error, warn, silly, verbose, debug } = logger.logger.functions
  // console.log=logger.logger.log
  Object.assign(console, {
    info,
    error,
    warn,
    silly,
    verbose,
    debug
  })
  //   console.log('log path', path)
}
// export Logger
