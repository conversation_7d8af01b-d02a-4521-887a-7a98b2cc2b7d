import log from 'electron-log/main'
import { MainLogger, FileTransport, LogFunctions } from 'electron-log'
import path from 'path'
import { createFile, getCurrentUTCDateStr } from '../../../utils'
// const logInit=()=>{
//     const currentDate = new Date().toUTCString()
// log.transports.file.resolvePathFn = (vars)=>{
//     console.log('log vars',vars, path.join(vars.libraryDefaultDir,currentDate));

// return path.join(vars.libraryDefaultDir,currentDate)
// }

// }
// logInit()
export default class Logger {
  constructor(logPath: string, id = 'main') {
    log.initialize()
    this.logger = log.create({ logId: id })

    // console.log(this.logger)
    const filePath = path.join(logPath, this.logger.logId + getCurrentUTCDateStr() + '.log')
    createFile(filePath)
    if (this.logger.transports?.file) {
      this.handleFileTransport(filePath, this.logger.transports.file)
    }
  }
  logger: MainLogger

  createScope(scope: string) {
    return this.logger.scope(scope)
  }
  handleFileTransport(logPath: string, file: FileTransport) {
    file.resolvePathFn = () => {
      return logPath
    }
  }
}
