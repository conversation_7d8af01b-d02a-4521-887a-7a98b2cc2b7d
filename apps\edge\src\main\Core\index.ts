// import { GlobalConfig } from '@/config'
import { Bounds } from '@/types'
import { getFile } from '@/utils'
import { BrowserWindow, shell, screen, app, globalShortcut, Display } from 'electron'
import fs from 'fs'
import path from 'path'
export * from './Log'
import { exec, spawn } from 'child_process'
import { Log } from './Log'
// const electronStore = require('electron-store');
// electronStore.initRenderer()
export const startApp2 = (appPath: string) => {
  const app = exec(appPath, (error, stdout, stderr) => {
    console.log(error, stdout, stderr)
  })

  return app
}
export const startApp = (exePath: string, ...args) => {
  console.log(exePath, args, '打开app参数')
  // 假设这是Windows下的可执行文件路径，你需要根据实际情况修改
  // let exePath = 'C:\\Windows\\System32\\cmd.exe';
  // let exePath = 'C:\\Program Files (x86)\\Common Files\\LenovoAppStoreNotify\\LenovoAppStoreNotify.exe';
  // const exePath = 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe' // let args = ['shortcut_desktopxxxx'];
  // const args = ['www.baidu.com'] // let args = ['/k', 'color 4F'];
  // let args = [ ];
  const exeProcess = spawn(exePath, args) // 可以对exeProcess进行进一步的处理，比如监听它的事件等
  // 例如，监听进程的错误事件
  exeProcess.on('error', (err) => {
    console.log('启动可执行文件出错:', err)
  }) // 监听进程的退出事件，并获取退出码
  exeProcess.on('exit', (code) => {
    console.log('可执行文件已退出，退出码:', code)
  })
  return exeProcess
}
export function openWebSite(url: string) {
  return shell.openExternal(url)
}
export function openApp(url: string) {
  return shell.openPath(url)
}
export function openProgram(url: string, args) {
  console.log('startApp', url, args)
  return startApp(url, args)
}
export function testOpen2(url: string) {
  console.log('testOpen2', url)
  startApp2(url)
}
export function setBounds(win: BrowserWindow, bounds: Partial<Bounds>) {
  win.setBounds(bounds)
}

export function getMoveWinFunc(win: BrowserWindow) {
  let winStartPosition = { x: 0, y: 0 }
  let mouseStartPosition = { x: 0, y: 0 }
  let movingInterval: NodeJS.Timeout | null = null
  const displays = screen.getAllDisplays()

  return {
    curWin: win,
    execFunc: (canMoving: boolean) => {
      if (canMoving) {
        // 读取原位置
        const { x, y, width, height } = win.getContentBounds()
        winStartPosition = { x, y }
        mouseStartPosition = screen.getCursorScreenPoint()

        // 清除之前的定时器
        if (movingInterval) {
          clearInterval(movingInterval)
        }

        // 开启新的定时器
        movingInterval = setInterval(() => {
          // 实时更新位置
          const cursorPosition = screen.getCursorScreenPoint()
          const x = winStartPosition.x + cursorPosition.x - mouseStartPosition.x
          const y = winStartPosition.y + cursorPosition.y - mouseStartPosition.y

          // const windowSize = limitWindowPosition(win, x, y, displays)
          // if (windowSize) {
          //   win.setBounds({ x: windowSize.newX, y: windowSize.newY, width, height })
          // } else {
          // }
          win.setBounds({ x, y, width, height })
        }, 15)
      } else {
        movingInterval && clearInterval(movingInterval)
        movingInterval = null
      }
    }
  }
}
export function getFilesRecursively(dir) {
  // console.log(dir);

  const files = fs.readdirSync(dir)
  let fileList = [] as string[]

  files.forEach((file) => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)

    if (stat.isDirectory()) {
      // 递归获取子文件夹中的文件
      fileList = fileList.concat(getFilesRecursively(filePath))
    } else {
      fileList.push(filePath)
    }
  })

  return fileList
}
// export const getAinowConfig = async () => {
//   try {
//     const homePath = app.getPath('home')
//     console.log(homePath)
//     const logPath = path.join(homePath, '\\AppData\\Local\\Lenovo\\Lenovo AI Now\\client.conf')

//     const configStr = await getFile(logPath)
//     console.log('configStr', configStr)
//     const lines = configStr.split('\n')
//     const config = {}
//     lines.forEach((line) => {
//       const [key, value] = line.split('=')
//       if (key && value) {
//         config[key.trim()] = value.trim()
//       }
//     })

//     // 输出解析后的JSON对象
//     console.log(config)
//     return config as Record<string, string>
//   } catch (error) {
//     console.log(error)
//     return {}
//   }
// }

// export function getShortcutKeyFunc(shortCutKey: string) {
//   let curShortCut = ''
//   return async (cb: () => void) => {
//     try {
//       const config = await getAinowConfig()
//       curShortCut && globalShortcut.unregister(curShortCut)
//       globalShortcut.register(config[shortCutKey], cb)
//       curShortCut = config[shortCutKey]
//     } catch (error) {
//       console.log('getShortcutKeyFunc', error)
//     }
//     // console.log('setShortCut', config.SearchBarShortcutKey)
//   }
// }
export function handleWinHide(win: BrowserWindow) {
  win.hide()
  return win
}
export function handleWinShow(win: BrowserWindow) {
  // if (!GlobalConfig.isLogin) {
  //   console.log('is not login', GlobalConfig)

  //   return win
  // }
  if (win.isMinimized()) {
    win.restore()
  }

  win.show()
  return win
}
// export function handleWinState(win: BrowserWindow) {
//   if (!GlobalConfig.isLogin) {
//     console.log('is not login', GlobalConfig)

//     return handleWinHide(win)
//   }

//   if (win.isVisible()) {
//     return handleWinHide(win)
//   } else {
//     return handleWinShow(win)
//   }
// }
// export const updateGlobalConfig = async () => {
//   try {
//     const originConfig = await getAinowConfig()
//     // const Lid = getLid()
//     GlobalConfig.isLogin =
//       originConfig.CurrentMachineType === 'Commercial' ||
//       originConfig.IsLogin.toLowerCase() === 'true'

//     GlobalConfig.isTest = originConfig.IsUseTestEnvironment.toLowerCase() === 'true'

//     console.log('GlobalConfig', GlobalConfig)

//     return GlobalConfig
//   } catch (error) {
//     console.log('updateGlobalConfig', error)

//     return GlobalConfig
//   }
// }
// updateGlobalConfig()

// const limitWindowPosition = (
//   win: BrowserWindow,
//   cursorX: number,
//   cursorY: number,
//   displays: Display[]
// ): { newX: number; newY: number } | null => {
//   const { width: winWidth, height: winHeight } = win.getContentBounds()

//   // 使用即将移动到的新位置来确定当前屏幕
//   const currentScreen = screen.getDisplayNearestPoint({
//     x: cursorX + winWidth / 2,
//     y: cursorY + winHeight / 2
//   })
//   const { x, y, width: WAWidth, height: WAHeight } = currentScreen.workArea

//   let newX = cursorX
//   let newY = cursorY
//   const maxWinX = cursorX + winWidth
//   const maxWinY = cursorY + winHeight

//   // 限制窗口在当前屏幕的工作区域内
//   if (cursorX < x) newX = x // 限制左边界
//   if (cursorY < y) newY = y // 限制上边界
//   if (maxWinX > x + WAWidth) newX = x + WAWidth - winWidth // 限制右边界
//   if (maxWinY > y + WAHeight) newY = y + WAHeight - winHeight // 限制下边界

//   // 如果位置被调整，检查是否跨屏
//   if (newX !== cursorX || newY !== cursorY) {
//     const currentDisplays = getDisplaysBetween(
//       {
//         x: newX,
//         y: newY,
//         width: winWidth,
//         height: winHeight
//       },
//       displays
//     )
//     if (currentDisplays.length < 2) {
//       return { newX, newY }
//     }
//   }

//   return null
// }

// function getDisplaysBetween(windowBounds: Bounds, displays: Display[]): Display[] {
//   const crossingDisplays: Display[] = []

//   displays.forEach((display) => {
//     const { x, y, width, height } = display.workArea

//     const isOverlapping =
//       windowBounds.x < x + width &&
//       windowBounds.x + windowBounds.width > x &&
//       windowBounds.y < y + height &&
//       windowBounds.y + windowBounds.height > y

//     if (isOverlapping) {
//       crossingDisplays.push(display)
//     }
//   })

//   // 判断是否靠近主屏与分屏交接的边缘
//   if (crossingDisplays.length === 1) {
//     const [crossingDisplay] = crossingDisplays
//     const { x, y, width: workWidth, height: workHeight } = crossingDisplay.workArea
//     const windowCenterX = windowBounds.x + windowBounds.width / 2
//     const windowCenterY = windowBounds.y + windowBounds.height / 2

//     const nearThreshold = 100 // 调整此值以改变“靠近”边缘的判定

//     const isNearLeftEdge = windowCenterX < x + nearThreshold
//     const isNearRightEdge = windowCenterX > x + workWidth - nearThreshold
//     const isNearTopEdge = windowCenterY < y + nearThreshold / 2
//     const isNearBottomEdge = windowCenterY > y + workHeight - nearThreshold

//     if (isNearLeftEdge || isNearRightEdge || isNearTopEdge || isNearBottomEdge) {
//       const adjacentDisplay = displays.find(
//         (display) =>
//           display.id !== crossingDisplay.id &&
//           windowBounds.x + windowBounds.width > display.workArea.x &&
//           windowBounds.x < display.workArea.x + display.workArea.width
//       )
//       if (adjacentDisplay) {
//         crossingDisplays.push(adjacentDisplay)
//       }
//     }
//   }

//   return crossingDisplays
// }
export function broadcastToWindow(win: BrowserWindow, eventName, data) {
  win.webContents.send(eventName, data)
}
export const isMax = (win: BrowserWindow) => {
  const bounds = win.getBounds()
  const cur = screen.getDisplayNearestPoint({
    x: bounds.x + 8,
    y: bounds.y + 8
  })

  console.log('=============')
  console.log(bounds)
  console.log('=>>>>>>>>>>>>')

  console.log(cur)
  return (
    bounds.height >= cur.workArea.height &&
    bounds.width >= cur.workArea.width &&
    bounds.x === cur.workArea.x - 8 &&
    bounds.y === cur.workArea.y - 8
  )
}
