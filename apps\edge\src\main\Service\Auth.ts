// export function AuthService() {

import { app, BrowserWindow } from 'electron'
import { broadcastToWindow } from '../Core'
import { parseQuery } from '@/utils'
import path from 'path'

// import { log } from "console"

// }
const scheme = 'ainow.row'
export const toLoginWithCode = (win: BrowserWindow, argv: string[]) => {
  const codeStr = argv.find((arg) => arg.includes(scheme))

  console.info('process.argv', argv)

  if (codeStr) {
    const code = parseQuery(codeStr.slice(codeStr.indexOf('?') + 1))
    console.info('LID Code', code)
    broadcastToWindow(win, 'toLoginWithCode', code)
  }
  return codeStr
}
export const setProtocolScheme = () => {
  const args = [] as string[]
  if (!app.isPackaged) {
    //开发阶段调试阶段需要将运行程序的绝对路径加入启动参数
    args.push(path.resolve(process.argv[1]))
  }
  //添加--防御自定义协议漏洞，忽略后面追加参数
  args.push('--')
  try {
    app.setAsDefaultProtocolClient(scheme, process.execPath, args)
  } catch (error) {
    console.error(error)
  }
}
