import { promisified as regedit, RegistryPutItem } from 'regedit'
export class RegeditService {
  static async getVal(paths: string[]) {
    try {
      const listRes = await regedit.list(paths)
      return listRes
    } catch (error) {
      console.error(error)
      return []
    }
  }
  static async setVal(key: string, val: RegistryPutItem) {
    console.log('setVal')
    console.log(key)
    console.log(val)

    try {
      await regedit.createKey([key])
      await regedit.putValue({
        [key]: val
      })
      return true
    } catch (error) {
      console.error(error)
      return false
    }
  }
}
