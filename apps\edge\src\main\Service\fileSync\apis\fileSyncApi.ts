import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { IpcChannels } from '../constants/fileChannels'
import { FileSource, SyncStatus, ISyncProgress, IFileInfo } from '../constants/types'
import { SyncEvents } from '../constants/events'

interface SelectFilesOptions {
  source?: FileSource
  filters?: { name: string; extensions: string[] }[]
  dialogTitle?: string
}

interface UploadResult {
  success: boolean
  message?: string
  error?: string
}

interface StatusResult {
  status: SyncStatus
  progress: ISyncProgress
}

interface PendingFilesResult {
  pendingFiles: IFileInfo[]
  allStates: IFileInfo[]
}

export interface IFileSyncAPI {
  // 更新上传API地址
  updateUploadApiUrl: (uploadApiUrl: string) => Promise<void>
  updateUploadApiInfo: ({
    apiToken,
    uploadApiUrl
  }: {
    apiToken: string
    uploadApiUrl: string
  }) => Promise<void>

  // 文件选择
  selectFiles: (options?: SelectFilesOptions) => Promise<void>
  getPendingFiles: () => Promise<PendingFilesResult>
  selectCustomPathFilesUpload: (folderPath: string) => Promise<void>

  // 聊天文件选择
  chooseFiles: (options?: SelectFilesOptions) => Promise<UploadResult>
  dropFiles: (options?: SelectFilesOptions) => Promise<UploadResult>
  // 上传控制
  startUpload: (fileIds: string[]) => Promise<UploadResult>
  cancelUpload: (fileIds: string[]) => Promise<UploadResult>
  pauseUploadingTask: (fileIds: string[]) => Promise<UploadResult>
  deleteUploadingTask: (fileIds: string[]) => Promise<UploadResult>
  deleteFiles: (fileIds: string[]) => Promise<{ success: boolean; count: number }>

  // 事件监听
  onFileListUpdated: (callback: (files: IFileInfo[]) => void) => void
  onOneUploadCompleted: (callback: (data: { fileId: string; data: any }) => void) => void
  onProgressUpdate: (callback: (progress: ISyncProgress) => void) => void
  onFileProgressUpdate: (callback: (data: { fileId: string; progress: number }) => void) => void
  removeAllListeners: () => void

  onChatUploadCompleted: (callback: (data: { fileId: string; data: any }) => void) => void

  getFilesByPath: (folderPath: string) => Promise<IFileInfo[]>

  // START_UPLOAD_TO_WORKER
  onUploadToWorkerStart: (callback: (data: { isCurrentWorker; postMessageData }) => void) => void
  onWorkerMessageUpdate: (callback: (data: { messageEvent: any }) => void) => void
  onReceiveHeartbeat: (callback: (data: { heartbeatTimestamp: number }) => void) => void

  notifyRendererReady: () => Promise<void>
  checkRendererStatus: () => Promise<{ isReady: boolean; hasPendingFiles: boolean }>
}

// API 实现
export const fileSyncApi: IFileSyncAPI = {
  // 更新上传API地址
  updateUploadApiUrl: (uploadApiUrl: string) => {
    return ipcRenderer.invoke(IpcChannels.UPDATE_UPLOAD_API_URL, { uploadApiUrl })
  },

  updateUploadApiInfo: ({ apiToken, uploadApiUrl }: { apiToken: string; uploadApiUrl: string }) => {
    return ipcRenderer.invoke(IpcChannels.UPDATE_UPLOAD_API_INFO, { apiToken, uploadApiUrl })
  },

  selectFiles: (options = {}) => {
    return ipcRenderer.invoke(IpcChannels.SELECT_FILES, options)
  },

  chooseFiles: (options = {}) => {
    return ipcRenderer.invoke(IpcChannels.CHOOSE_FILES, { ...options })
  },
  dropFiles: (options = {}) => {
    return ipcRenderer.invoke(IpcChannels.DROP_FILES, { ...options })
  },
  getPendingFiles: () => {
    return ipcRenderer.invoke(IpcChannels.GET_FILES)
  },

  selectCustomPathFilesUpload: (folderPath: string) => {
    return ipcRenderer.invoke(IpcChannels.SELECT_CUSTOM_PATH_FILES_UPLOAD, { folderPath })
  },

  // 开始上传多个文件
  startUpload: (fileIds: string[]) => {
    return ipcRenderer.invoke(IpcChannels.START_UPLOAD, { fileIds })
  },

  pauseUploadingTask: (fileIds: string[]) => {
    return ipcRenderer.invoke(IpcChannels.PAUSE_UPLOADING_TASK, { fileIds })
  },

  cancelUpload: (fileIds: string[]) => {
    return ipcRenderer.invoke(IpcChannels.CANCEL_UPLOAD, { fileIds })
  },

  deleteUploadingTask: (fileIds: string[]) => {
    return ipcRenderer.invoke(IpcChannels.DELETE_UPLOADING_TASK, { fileIds })
  },

  deleteFiles: (fileIds: string[]) => {
    return ipcRenderer.invoke(IpcChannels.DELETE_FILES, { fileIds })
  },

  getFilesByPath: (folderPath: string) => {
    return ipcRenderer.invoke(IpcChannels.GET_FILES_BY_PATH, { folderPath })
  },

  // 事件监听
  onFileListUpdated: (callback) => {
    ipcRenderer.on(SyncEvents.FILE_LIST_UPDATED, (_, data) => callback(data))
  },

  onOneUploadCompleted: (callback) => {
    ipcRenderer.on(SyncEvents.ONE_UPLOAD_COMPLETED, (_, data) => callback(data))
  },

  onChatUploadCompleted: (callback) => {
    ipcRenderer.on(SyncEvents.CHAT_UPLOAD_COMPLETED, (_, data) => callback(data))
  },

  onProgressUpdate: (callback) => {
    ipcRenderer.on(SyncEvents.PROGRESS_UPDATE, (_, data) => callback(data))
  },

  onFileProgressUpdate: (callback) => {
    ipcRenderer.on(SyncEvents.FILE_PROGRESS_UPDATE, (_, data) => callback(data))
  },

  onUploadToWorkerStart: (callback) => {
    console.log('====== onUploadToWorkerStart ======')
    ipcRenderer.on(SyncEvents.START_UPLOAD_TO_WORKER, (_, data) => callback(data))
  },

  onWorkerMessageUpdate: (callback) => {
    ipcRenderer.on(SyncEvents.WORKER_MESSAGE, (_, data) => callback(data))
  },

  // 监听接收心跳
  onReceiveHeartbeat: (callback) => {
    ipcRenderer.on(SyncEvents.RECEIVE_HEARTBEAT, (_, data) => callback(data))
  },

  // 渲染进程就绪通知
  notifyRendererReady: () => {
    return ipcRenderer.invoke(IpcChannels.RENDERER_READY)
  },

  checkRendererStatus: () => {
    return ipcRenderer.invoke(IpcChannels.CHECK_RENDERER_STATUS)
  },

  removeAllListeners: () => {
    ipcRenderer.removeAllListeners(SyncEvents.FILE_LIST_UPDATED)
    ipcRenderer.removeAllListeners(SyncEvents.ONE_UPLOAD_COMPLETED)
    ipcRenderer.removeAllListeners(SyncEvents.PROGRESS_UPDATE)
    ipcRenderer.removeAllListeners(SyncEvents.FILE_PROGRESS_UPDATE)
    ipcRenderer.removeAllListeners(SyncEvents.CHAT_UPLOAD_COMPLETED)
    ipcRenderer.removeAllListeners(SyncEvents.START_UPLOAD_TO_WORKER)
    ipcRenderer.removeAllListeners(SyncEvents.WORKER_MESSAGE)
    ipcRenderer.removeAllListeners(SyncEvents.RECEIVE_HEARTBEAT)
  }
}

// 导出用于 preload.ts 的 expose 函数
export function exposeFileSyncApi() {
  return fileSyncApi
}
