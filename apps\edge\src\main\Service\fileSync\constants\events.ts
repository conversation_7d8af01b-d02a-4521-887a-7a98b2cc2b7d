/**
 * 文件同步相关事件常量定义
 */
export const SyncEvents = {
  // 同步生命周期事件
  SYNC_STARTED: 'sync-started',
  SYNC_COMPLETED: 'sync-completed',
  SYNC_FAILED: 'sync-failed',

  // 文件操作事件
  FILE_LIST_UPDATED: 'file-list-updated', // 更新列表

  // 上传事件
  ONE_UPLOAD_COMPLETED: 'one-upload-completed',
  ONE_UPLOAD_FAILED: 'one-upload-failed',

  CHAT_UPLOAD_COMPLETED: 'chat-upload-completed',
  CHAT_UPLOAD_FAILED: 'chat-upload-failed',

  // 状态事件
  PROGRESS_UPDATE: 'progress-update',
  CONFLICT_DETECTED: 'conflict-detected',
  FILE_PROGRESS_UPDATE: 'file-progress-update',

  // 接收到worker消息事件
  START_UPLOAD_TO_WORKER: 'start-upload-to-worker',
  WORKER_MESSAGE: 'worker-message',

  RECEIVE_HEARTBEAT: 'receive-heartbeat' // 接收到心跳消息
}
