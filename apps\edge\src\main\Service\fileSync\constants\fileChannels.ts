/**
 * 文件同步相关IPC通道名称常量定义
 */
export const IpcChannels = {
  // 更新上传API地址
  UPDATE_UPLOAD_API_URL: 'fileSync:updateUploadApiUrl',
  UPDATE_UPLOAD_API_INFO: 'fileSync:updateUploadApiInfo',

  // 文件选择相关
  SELECT_FILES: 'fileSync:selectFiles', // 统一的文件选择通道，支持指定source参数
  GET_FILES: 'fileSync:getFiles',
  GET_FILES_BY_PATH: 'file-sync:get-files-by-path',
  SELECT_CUSTOM_PATH_FILES_UPLOAD: 'fileSync:selectCustomPathFilesUpload',

  // 聊天文件选择相关
  CHOOSE_FILES: 'fileSync:chooseFiles',
  DROP_FILES: 'fileSync:dropFiles', // 聊天文件拖拽上传

  // 上传相关
  START_UPLOAD: 'fileSync:startUpload',
  CANCEL_UPLOAD: 'fileSync:cancelUpload',
  PAUSE_UPLOADING_TASK: 'fileSync:pauseUploadingTask',
  DELETE_UPLOADING_TASK: 'fileSync:deleteUploadingTask',
  DELETE_FILES: 'fileSync:deleteFiles',

  // 渲染进程状态相关
  RENDERER_READY: 'fileSync:rendererReady',
  CHECK_RENDERER_STATUS: 'fileSync:checkRendererStatus'
}
