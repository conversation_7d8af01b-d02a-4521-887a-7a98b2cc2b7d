/**
 * 文件信息接口
 */
export interface IFileInfo {
  path: string
  name: string
  size: number
  lastModified: number
  isDirectory: boolean
  source: FileSource
  fileId: string
  status: FileStatus
  resourceId: string
  resourceType: string
}

/**
 * 文件来源枚举
 */
// export enum FileSource {
//   PKB = 'pkb',
//   TKB = 'tkb'
//   // 可以在此处添加更多来源类型
// }
export type FileSource = string

export const PrivateKBSource = 'bccd276b-7b52-478c-a8b3-b9cb91012165' // 'PKB';

/**
 * 文件状态枚举
 */
export enum FileStatus {
  PENDING = 'Waiting',
  UPLOADING = 'Uploading',
  SUCCESS = 'success',
  FAILED = 'Failed',
  CONFLICT = 'conflict',
  PAUSED = 'Paused'
}

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  IDLE = 'idle',
  SYNCING = 'syncing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CONFLICT = 'conflict'
}

/**
 * 同步配置接口
 */
export interface ISyncConfig {
  sourcePath: string
  targetPath: string
  excludePatterns: string[]
  autoSync: boolean
  syncInterval?: number
  conflictResolution: 'newer' | 'source' | 'target' | 'manual'
}

/**
 * 同步进度接口
 */
export interface ISyncProgress {
  total: number
  processed: number
  deleted: number
  conflicts: number
}

/**
 * 上传令牌接口
 */
export interface IUploadToken {
  token: string
  expires: number
  config?: any
}
