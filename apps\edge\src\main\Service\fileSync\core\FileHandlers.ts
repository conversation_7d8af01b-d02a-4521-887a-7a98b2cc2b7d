import { ipcMain } from 'electron'
import { FileSyncManager } from './FileSyncManager'
import { PrivateKBSource } from '../constants/types'
import { IpcChannels } from '../constants/fileChannels'

export class FileHandlers {
  private syncManager: FileSyncManager

  constructor(_syncManager: FileSyncManager) {
    this.syncManager = _syncManager
    this.registerHandlers()
  }

  /**
   * 注册IPC处理函数
   */
  private registerHandlers(): void {
    // 更新上传API地址
    ipcMain.handle(IpcChannels.UPDATE_UPLOAD_API_URL, (_, { uploadApiUrl }) => {
      return this.syncManager.updateUploadApiUrl(uploadApiUrl)
    })

    ipcMain.handle(IpcChannels.UPDATE_UPLOAD_API_INFO, (_, { apiToken, uploadApiUrl }) => {
      return this.syncManager.updateUploadApiInfo({
        apiToken,
        uploadApiUrl
      })
    })

    // 统一的文件选择函数 - 支持不同来源
    ipcMain.handle(
      IpcChannels.SELECT_FILES,
      async (
        _,
        {
          source = PrivateKBSource,
          isLimitFileCount,
          userIdForUpload,
          resourceId,
          resourceType
        } = {}
      ) => {
        return await this.syncManager.selectFiles({
          source,
          isLimitFileCount,
          userIdForUpload,
          resourceId,
          resourceType
        })
      }
    )

    ipcMain.handle(
      IpcChannels.CHOOSE_FILES,
      async (_, { source, userIdForUpload, resourceId, resourceType, remainingSlots } = {}) => {
        return await this.syncManager.chooseFiles(
          source,
          userIdForUpload,
          resourceId,
          resourceType,
          undefined,
          remainingSlots
        )
      }
    )
    ipcMain.handle(IpcChannels.DROP_FILES, async (_, { source, result } = {}) => {
      return await this.syncManager.fileUpload(source, result)
    })
    // 获取待处理文件
    ipcMain.handle(IpcChannels.GET_FILES, () => {
      return this.syncManager.getPendingFiles()
    })

    // 开始上传
    ipcMain.handle(IpcChannels.START_UPLOAD, async (_, { fileIds }) => {
      try {
        const result = await this.syncManager.startUpload(fileIds)
        return result
      } catch (error: any) {
        return { success: false, error: error.message || String(error) }
      }
    })

    // 取消上传
    ipcMain.handle(IpcChannels.CANCEL_UPLOAD, async (_, { fileIds }) => {
      return this.syncManager.puauseUpload(fileIds)
    })

    // 暂停上传中任务
    ipcMain.handle(IpcChannels.PAUSE_UPLOADING_TASK, async (_, { fileIds }) => {
      return this.syncManager.puauseUploadingTask(fileIds)
    })

    ipcMain.handle(IpcChannels.DELETE_UPLOADING_TASK, async (_, { fileIds }) => {
      return this.syncManager.deleteUploadingTask(fileIds)
    })

    // 清除失败文件
    ipcMain.handle(IpcChannels.DELETE_FILES, async (_, { fileIds }) => {
      const count = this.syncManager.deleteFiles(fileIds)
      return { success: true, count: 0 }
    })

    // 通过路径获取文件列表
    ipcMain.handle(IpcChannels.GET_FILES_BY_PATH, async (_, { folderPath }) => {
      try {
        const files = await this.syncManager.getFilesByPath(folderPath)
        return files
      } catch (error: any) {
        return []
      }
    })

    // 获取自定义路径文件上传
    ipcMain.handle(
      IpcChannels.SELECT_CUSTOM_PATH_FILES_UPLOAD,
      async (_, { folderPath, source, resourceId, resourceType }) => {
        return await this.syncManager.selectCustomPathFilesUpload(
          folderPath,
          source,
          resourceId,
          resourceType
        )
      }
    )
  }

  /**
   * 清理IPC处理函数
   */
  public cleanup(): void {
    ipcMain.removeHandler(IpcChannels.START_UPLOAD)
    ipcMain.removeHandler(IpcChannels.GET_FILES)
    ipcMain.removeHandler(IpcChannels.CANCEL_UPLOAD)
    ipcMain.removeHandler(IpcChannels.DELETE_FILES)
    ipcMain.removeHandler(IpcChannels.GET_FILES_BY_PATH)
  }

  public async handleChooseFiles(source: string) {
    return await this.syncManager.chooseFiles(source)
  }
}
