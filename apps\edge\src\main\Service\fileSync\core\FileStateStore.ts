import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import { IFileInfo, FileStatus } from '../constants/types'

export class FileStateStore {
  private storePath: string

  constructor() {
    this.storePath = path.join(app.getPath('userData'), 'fileSync.json')
  }

  /**
   * 从文件加载状态
   * @returns 加载的文件列表
   */
  public loadStates(): IFileInfo[] {
    try {
      if (fs.existsSync(this.storePath)) {
        const data = fs.readFileSync(this.storePath, 'utf-8')
        const files = JSON.parse(data)
        console.log(`已加载 ${files.length} 个文件状态记录`)
        return files
      }
    } catch (error) {
      console.error('加载文件状态失败:', error)
    }
    return []
  }

  /**
   * 保存状态到文件
   * @param files 要保存的文件列表
   */
  public saveStates(files: IFileInfo[]): void {
    try {
      // 过滤掉已完成的文件
      const pendingFiles = files.filter((file) => file.status !== FileStatus.SUCCESS)
      fs.writeFileSync(this.storePath, JSON.stringify(pendingFiles, null, 2), 'utf-8')
    } catch (error) {
      console.error('保存文件状态失败:', error)
    }
  }
}
