/*
 * @Description:
 * @FilePath: \ainow-ui\apps\edge\src\main\Service\fileSync\core\FileSyncManager.ts
 */
import {
  IFileInfo,
  FileStatus,
  FileSource,
  PrivateKBSource,
  SyncStatus,
  ISyncProgress,
  IUploadToken
} from '../constants/types'
import { SyncEvents } from '../constants/events'
import { IpcChannels } from '../constants/fileChannels'
import { FileStateStore } from './FileStateStore'
import * as path from 'path'
import * as fs from 'fs'
import { ipcMain, dialog, BrowserWindow } from 'electron'
import { FileHandlers } from './FileHandlers'
import { Worker } from 'worker_threads'
import { getResourcesPath, getFilenameFromPath } from '@/utils'

// 记录上次文件选择时间
let lastFileSelectTime = 0

/**
 * 文件同步管理器
 * 负责管理文件选择、上传和状态跟踪
 */
export class FileSyncManager {
  // 添加图片扩展名数组
  private static readonly IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'bmp']

  // 添加默认过滤器作为类常量
  private static readonly DEFAULT_FILTERS = [
    {
      name: 'All Files',
      extensions: [
        'pdf',
        'ppt',
        'pptx',
        'doc',
        'docx',
        'txt',
        'xls',
        'xlsx',
        'jpg',
        'jpeg',
        'png',
        'bmp'
      ]
    },
    { name: 'PDF Files', extensions: ['pdf'] },
    { name: 'PowerPoint Presentations', extensions: ['ppt', 'pptx'] },
    { name: 'Word Documents', extensions: ['doc', 'docx'] },
    { name: 'Text Files', extensions: ['txt'] },
    { name: 'Excel', extensions: ['xls', 'xlsx'] },
    { name: 'Images', extensions: FileSyncManager.IMAGE_EXTENSIONS }
  ]

  private static readonly MAX_FILE_COUNT = 10 // 最大文件选择数量
  private static readonly MAX_IMAGE_SIZE = 50 * 1024 * 1024 // 图片最大50MB
  private static readonly MAX_OTHER_SIZE = 2 * 1024 * 1024 * 1024 // 其他文件最大2GB

  private status: SyncStatus = SyncStatus.IDLE
  private progress: ISyncProgress = {
    total: 0,
    processed: 0,
    deleted: 0,
    conflicts: 0
  }
  private pendingFiles: IFileInfo[] = [] // 待上传文件列表
  private fileStateStore: FileStateStore // 文件列表状态存储
  private currentWorker?: Worker

  private currentChatWorker?: Worker
  private currentFileId?: string
  private token?: string
  private userIdForUpload: string = ''
  private uploadApiUrl: string = '' // 上传API地址
  private handlers: FileHandlers
  private mainWindow?: BrowserWindow

  // 渲染进程就绪状态管理
  private isRendererReady: boolean = false
  private pendingAutoUpload: boolean = false
  private rendererReadyCheckInterval?: NodeJS.Timeout

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow
    this.fileStateStore = new FileStateStore()
    this.handlers = new FileHandlers(this)

    // 恢复历史状态
    this.loadHistoryState()

    this.setupIpcHandlers()

    // 定时发送心跳事件
    setInterval(() => {
      // 发送时间格式 XX月XX日 XX:XX:XX
      this.emitEvent(SyncEvents.RECEIVE_HEARTBEAT, {
        timestamp: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      })
    }, 5000)
  }

  /**
   * 加载历史状态
   */
  private loadHistoryState(): void {
    // 获取待上传文件
    this.pendingFiles = this.fileStateStore.loadStates()
    console.log(`加载历史文件状态: ${this.pendingFiles.length} 个上传文件`)
  }

  /**
   * 设置 IPC 监听器
   */
  private setupIpcHandlers(): void {
    // 监听渲染进程就绪通知
    ipcMain.handle(IpcChannels.RENDERER_READY, () => {
      // console.log('渲染进程已就绪')
      this.isRendererReady = true

      // 清除检查间隔
      if (this.rendererReadyCheckInterval) {
        clearInterval(this.rendererReadyCheckInterval)
        this.rendererReadyCheckInterval = undefined
      }

      // 如果有待处理的自动上传，立即执行
      if (this.pendingAutoUpload) {
        this.pendingAutoUpload = false
        this.executeAutoUpload()
      }

      return { success: true }
    })

    // 监听检查渲染进程状态请求
    ipcMain.handle(IpcChannels.CHECK_RENDERER_STATUS, () => {
      return {
        isReady: this.isRendererReady,
        hasPendingFiles: this.pendingFiles.length > 0
      }
    })
  }

  /**
   * 通过路径返回文件列表
   */
  public async getFilesByPath(
    folderPath: string,
    source: string,
    resourceId: string,
    resourceType: string
  ): Promise<IFileInfo[]> {
    const fileInfos: IFileInfo[] = []
    try {
      // 使用默认过滤器递归获取文件
      const filePaths = await this.getFilesRecursively(folderPath, FileSyncManager.DEFAULT_FILTERS)

      // 处理每个文件
      for (const filePath of filePaths) {
        const stats = await fs.promises.stat(filePath)
        const parsedPath = path.parse(filePath)

        const fileInfo: IFileInfo = {
          path: filePath,
          name: parsedPath.base,
          size: stats.size,
          lastModified: stats.mtimeMs,
          isDirectory: false, // 因为 getFilesRecursively 只返回文件
          source, // PrivateKBSource, // 默认为 PKB，可以根据需要修改
          fileId: `${source}_${parsedPath.base}_${Date.now()}`,
          status: FileStatus.PENDING,
          resourceId, // 需要使用PrivateKB 的knowledgeBaseId
          resourceType
        }

        fileInfos.push(fileInfo)
      }
    } catch (error) {
      console.error('获取文件列表错误:', error)
    }

    return fileInfos
  }

  /**
   * 检查文件是否符合过滤条件
   */
  private isFileMatchFilter(
    fileName: string,
    filters?: { name: string; extensions: string[] }[]
  ): boolean {
    if (!filters) return true
    const ext = path.extname(fileName).toLowerCase().slice(1)
    return filters.some(
      (filter) => filter.extensions.includes('*') || filter.extensions.includes(ext)
    )
  }

  /**
   * 递归获取指定目录下的所有文件
   */
  private async getFilesRecursively(
    dirPath: string,
    filters?: { name: string; extensions: string[] }[]
  ): Promise<string[]> {
    const files: string[] = []

    const that = this // 保持对 FileSyncManager 实例的引用
    async function traverse(currentPath: string) {
      const entries = await fs.promises.readdir(currentPath, { withFileTypes: true })

      for (const entry of entries) {
        const fullPath = path.join(currentPath, entry.name)
        if (entry.isDirectory()) {
          await traverse(fullPath)
        } else if (entry.isFile()) {
          if (!filters || that.isFileMatchFilter(entry.name, filters)) {
            files.push(fullPath)
          }
        }
      }
    }

    await traverse(dirPath)
    return files
  }
  public async fileUpload(source, result) {
    if (!result.canceled && result.filePaths.length > 0) {
      // 获取上传令牌
      // const token = await this.getUploadToken()
      // this.token = token.token
      console.log('拖拽上传文件---', result.filePaths, source)
      // 处理所有选择的文件
      for (const filePath of result.filePaths) {
        // 创建新worker并发送上传消息
        const worker = this.createChatWorker(filePath)
        const fileId = `${source}_${path.basename(filePath)}_${Date.now()}`

        worker.postMessage({
          type: 'upload',
          source: source,
          fileId: fileId,
          filePath: filePath,
          token: this.token,
          uploadApiUrl: this.uploadApiUrl
        })
      }
    }
  }
  public async chooseFiles(
    source: FileSource,
    userIdForUpload: string,
    resourceId?: string,
    resourceType?: string,
    filters?: { name: string; extensions: string[] }[],
    remainingSlots?: number
  ): Promise<any> {
    const title = '选择要上传的文件'
    try {
      // 添加2秒的节流逻辑
      const now = Date.now()
      if (now - lastFileSelectTime < 3000) {
        return { canceled: true, filePaths: [] }
      }
      lastFileSelectTime = now

      // 显示文件选择对话框
      const result = await dialog.showOpenDialog(global.mainWindow, {
        properties: ['openFile', 'multiSelections'],
        filters: filters || FileSyncManager.DEFAULT_FILTERS,
        title: title
      })

      if (!result.canceled && result.filePaths.length > 0) {
        const maxAllowed = remainingSlots || FileSyncManager.MAX_FILE_COUNT
        if (result.filePaths.length > maxAllowed) {
          await dialog.showMessageBox({
            type: 'warning',
            title: '警告',
            message: '文件超过10个，请重新选择'
            // message: `You can add at most ${maxAllowed} files.`
          })
          return { canceled: true, filePaths: [], error: 'FILE_LIMIT_EXCEEDED' }
        }

        // 检查每个文件大小
        for (const filePath of result.filePaths) {
          const stats = await fs.promises.stat(filePath)
          if (!this.isFileSizeValid(filePath, stats.size)) {
            return { canceled: true, filePaths: [], error: 'FILE_SIZE_EXCEEDED' }
          }
        }

        // 获取上传令牌
        // const token = await this.getUploadToken()
        // this.token = token.token
        console.log('上传文件---', result.filePaths, source)
        // 处理所有选择的文件
        for (const filePath of result.filePaths) {
          // 创建新worker并发送上传消息
          const worker = this.createChatWorker(filePath)
          const fileId = `${source}_${path.basename(filePath)}_${Date.now()}`

          worker.postMessage({
            type: 'upload',
            source: source,
            fileId: fileId,
            filePath: filePath,
            token: this.token,
            userIdForUpload: userIdForUpload,
            uploadApiUrl: this.uploadApiUrl,
            resourceId,
            resourceType
          })
        }

        // 返回成功结果
        return { canceled: false, filePaths: result.filePaths, success: true }
      }

      // 取消了文件选择
      return result
    } catch (error) {
      throw error
    }
  }

  /**
   * 检查文件大小是否超出限制
   */
  private isFileSizeValid(filePath: string, size: number): boolean {
    const ext = path.extname(filePath).toLowerCase().slice(1)
    const isImage = FileSyncManager.IMAGE_EXTENSIONS.includes(ext)
    const maxSize = isImage ? FileSyncManager.MAX_IMAGE_SIZE : FileSyncManager.MAX_OTHER_SIZE

    if (size > maxSize) {
      const sizeInMB = maxSize / (1024 * 1024)
      dialog.showMessageBox({
        type: 'error',
        title: 'Error',
        message: `File ${path.basename(filePath)} exceeds ${isImage ? 'image' : 'file'} size limit（${sizeInMB}MB）`
      })
      return false
    }
    return true
  }

  /**
   * 更新上传API地址
   * @param uploadApiUrl
   * @returns
   */
  public updateUploadApiUrl(uploadApiUrl: string): void {
    this.uploadApiUrl = uploadApiUrl
    console.log('更新上传API地址:', this.uploadApiUrl)
  }

  /**
   * 更新上传API信息
   * @param apiToken 上传令牌
   * @param uploadApiUrl 上传API地址
   */
  public updateUploadApiInfo({
    apiToken,
    uploadApiUrl
  }: {
    apiToken: string
    uploadApiUrl: string
  }): void {
    this.token = apiToken
    this.uploadApiUrl = uploadApiUrl
    console.log('更新上传API信息:', { apiToken, uploadApiUrl })
  }

  /**
   * 选择并处理文件
   * @param source 文件来源，PKB或TKB的knowledgeId
   * @param filters 可选，文件过滤器，默认为标准文档和图片格式
   * @param dialogTitle 可选，对话框标题，默认根据source生成
   * @returns 对话框结果，包含选中的文件路径和来源信息
   */
  public async selectFiles({
    source,
    filters,
    dialogTitle,
    isLimitFileCount = true,
    userIdForUpload,
    resourceId,
    resourceType
  }: {
    source: FileSource
    filters?: { name: string; extensions: string[] }[]
    dialogTitle?: string
    isLimitFileCount?: boolean
    userIdForUpload: string
    resourceId: string
    resourceType: string
  }): Promise<void> {
    // 使用类常量作为默认过滤器
    const actualFilters = filters || FileSyncManager.DEFAULT_FILTERS

    // 生成对话框标题
    let title = dialogTitle || '选择要上传的文件'
    // 显示文件选择对话框
    const result = await dialog.showOpenDialog(global.mainWindow, {
      properties: ['openFile', 'multiSelections'],
      filters: actualFilters,
      title: title
    })

    if (!result.canceled && result.filePaths.length > 0) {
      // 检查文件数量
      if (isLimitFileCount && result.filePaths.length > FileSyncManager.MAX_FILE_COUNT) {
        dialog.showMessageBox({
          type: 'error',
          title: 'Error',
          message: `Maximum ${FileSyncManager.MAX_FILE_COUNT} files can be selected`
        })
        return
      }

      // 检查每个文件大小
      for (const filePath of result.filePaths) {
        const stats = await fs.promises.stat(filePath)
        if (!this.isFileSizeValid(filePath, stats.size)) {
          return
        }
      }

      // 处理所选文件
      await this.processSelectedFiles(result.filePaths, source, resourceId, resourceType)
    }
    if (
      [SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) &&
      this.pendingFiles.length > 0
    ) {
      this.userIdForUpload = userIdForUpload
      this.autoUpload()
    }
  }

  /**
   * 处理选择的文件
   */
  private async processSelectedFiles(
    filePaths: string[],
    source: FileSource,
    resourceId: string,
    resourceType: string
  ): Promise<void> {
    const fileInfos: IFileInfo[] = []

    for (const filePath of filePaths) {
      try {
        const stats = await fs.promises.stat(filePath)
        const parsedPath = path.parse(filePath)

        const fileInfo: IFileInfo = {
          path: filePath,
          name: parsedPath.base,
          size: stats.size,
          lastModified: stats.mtimeMs,
          isDirectory: stats.isDirectory(),
          source: source,
          fileId: `${source}_${parsedPath.base}_${Date.now()}`, // 使用时间戳构建唯一文件ID
          status: FileStatus.PENDING,
          resourceId,
          resourceType
        }

        fileInfos.push(fileInfo)
      } catch (error) {
        console.error(`处理文件错误 ${filePath}:`, error)
      }
    }

    // 存储待同步文件
    this.pendingFiles = [...this.pendingFiles, ...fileInfos]
    this.fileStateStore.saveStates(this.pendingFiles)

    // 更新UI显示待同步文件列表
    this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
      source,
      files: this.getFilesBySource(source),
      pendingFiles: this.pendingFiles
    })
  }

  private createChatWorker(filePath: string): Worker {
    const worker = new Worker(
      getResourcesPath(path.join(__dirname, '../../resources/worker/fileUpload.js'))
    )
    worker.on('message', (event) => {
      const { type, fileId, data, error, source } = event

      if (type === 'success') {
        const filename = getFilenameFromPath(filePath)
        // 发送成功事件
        this.emitEvent(SyncEvents.CHAT_UPLOAD_COMPLETED, {
          fileId,
          source,
          data,
          filename,
          path: filePath
        })
      } else if (type === 'error') {
        // 发送失败事件
        this.emitEvent(SyncEvents.SYNC_FAILED, { fileId, source, error })
      }
    })

    return worker
  }

  /**
   * 创建Worker
   */
  private createWorker(): Worker {
    const worker = new Worker(
      getResourcesPath(path.join(__dirname, '../../resources/worker/fileUpload.js'))
    )

    worker.on('message', (event) => {
      const { type, fileId, data, error, source, resourceId, resourceType } = event

      console.log('Worker message received:', event)
      // 输出所有worker消息
      // this.emitEvent(SyncEvents.WORKER_MESSAGE, {
      //   messageEvent: event
      // })

      if (type === 'open') {
        // 更新文件状态为上传中
        this.changeFilesStatus([fileId], FileStatus.UPLOADING)
      } else if (type === 'progress') {
        // 处理进度事件
        this.emitEvent(SyncEvents.FILE_PROGRESS_UPDATE, {
          fileId,
          progress: data.progress,
          uploaded: data.uploaded,
          total: data.total
        })

        // 更新文件状态为上传中
        // this.changeFilesStatus([fileId], FileStatus.UPLOADING)
      } else if (type === 'success') {
        console.log('上传成功', fileId, data)
        const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId)
        if (fileIndex !== -1) {
          this.progress.processed++

          // 删除已上传的文件
          this.pendingFiles.splice(fileIndex, 1)

          // this.changeFilesStatus([fileId], FileStatus.SUCCESS)      // 无用
          // 写入缓存
          this.fileStateStore.saveStates(this.pendingFiles)

          // 更新当前知识库上传文件列表
          this.emitEvent(SyncEvents.ONE_UPLOAD_COMPLETED, {
            source,
            sourceFiles: this.getFilesBySource(source),
            pendingFiles: this.pendingFiles,
            resourceId,
            resourceType
          })
        }

        // this.emitEvent(SyncEvents.PROGRESS_UPDATE, this.progress)
        this.uploadNext() // 处理下一个文件
      } else if (type === 'error') {
        const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId)
        if (fileIndex !== -1) {
          this.progress.processed++
          this.progress.conflicts++
          this.changeFilesStatus([fileId], FileStatus.FAILED)
        }

        // this.emitEvent(SyncEvents.PROGRESS_UPDATE, this.progress)
        this.uploadNext() // 处理下一个文件
      } else if (type === 'pause') {
        this.changeFilesStatus([fileId], FileStatus.PAUSED)
        this.uploadNext()
      } else if (type === 'delete') {
        this.deleteFiles([fileId])
        this.uploadNext()
      }
    })

    return worker
  }

  /**
   * 上传下一个文件
   */
  private uploadNext() {
    // 清理当前worker
    if (this.currentWorker) {
      this.currentWorker.terminate()
      this.currentWorker = undefined
      this.currentFileId = undefined
    }

    // 获取下一个待上传文件
    const nextFile = this.pendingFiles.find((f) => f.status === FileStatus.PENDING)
    if (!nextFile) {
      this.status = SyncStatus.COMPLETED
      console.log('所有文件上传完成')
      return
    }

    // 创建新worker开始上传
    this.currentWorker = this.createWorker()
    this.currentFileId = nextFile.fileId

    console.log('this uploadApiUrl', this.uploadApiUrl)

    this.emitEvent(SyncEvents.START_UPLOAD_TO_WORKER, {
      isCurrentWorker: !!this.currentWorker,
      postMessageData: {
        type: 'postMessage upload to worker',
        source: nextFile.source,
        fileId: nextFile.fileId,
        filePath: nextFile.path,
        token: this.token,
        userIdForUpload: this.userIdForUpload,
        uploadApiUrl: this.uploadApiUrl,
        resourceId: nextFile.resourceId,
        resourceType: nextFile.resourceType
      }
    })

    this.currentWorker.postMessage({
      type: 'upload',
      source: nextFile.source,
      fileId: nextFile.fileId,
      filePath: nextFile.path,
      token: this.token,
      userIdForUpload: this.userIdForUpload,
      uploadApiUrl: this.uploadApiUrl,
      resourceId: nextFile.resourceId,
      resourceType: nextFile.resourceType
    })
  }

  public async autoUpload(): Promise<{ success: boolean; message?: string }> {
    try {
      if (this.pendingFiles.length === 0) {
        return { success: false, message: '没有待上传的文件' }
      }

      // 如果渲染进程已就绪，直接执行上传
      if (this.isRendererReady) {
        return this.executeAutoUpload()
      }

      // 如果渲染进程未就绪，设置待处理标志并开始检查
      // console.log('渲染进程未就绪，等待就绪后开始上传...')
      this.pendingAutoUpload = true

      // 开始检查渲染进程状态
      this.startRendererReadyCheck()

      return { success: true, message: '等待渲染进程就绪后开始上传' }
    } catch (error) {
      this.status = SyncStatus.FAILED
      this.emitEvent(SyncEvents.SYNC_FAILED, { error })
      return {
        success: false,
        message: error instanceof Error ? error.message : '上传过程中发生未知错误'
      }
    }
  }

  /**
   * 执行上传流程
   */
  private async executeAutoUpload(): Promise<{ success: boolean; message?: string }> {
    try {
      if (this.pendingFiles.length === 0) {
        return { success: false, message: '没有待上传的文件' }
      }

      this.emitEvent(SyncEvents.START_UPLOAD_TO_WORKER, { isReady: true })

      this.status = SyncStatus.SYNCING
      this.resetProgress()
      this.progress.total = this.pendingFiles.length

      // 获取上传令牌
      // const token = await this.getUploadToken()
      // this.token = token.token

      // 开始上传第一个文件
      this.uploadNext()

      return { success: true }
    } catch (error) {
      this.status = SyncStatus.FAILED
      this.emitEvent(SyncEvents.SYNC_FAILED, { error })
      return {
        success: false,
        message: error instanceof Error ? error.message : '上传过程中发生未知错误'
      }
    }
  }

  /**
   * 开始检查渲染进程就绪状态
   */
  private startRendererReadyCheck(): void {
    // 如果已经在检查，不再重复开始
    if (this.rendererReadyCheckInterval) {
      return
    }

    // 每500ms检查一次渲染进程状态
    this.rendererReadyCheckInterval = setInterval(() => {
      if (this.isRendererReady) {
        if (this.rendererReadyCheckInterval) {
          clearInterval(this.rendererReadyCheckInterval)
          this.rendererReadyCheckInterval = undefined
        }
        return
      }
    }, 500)
  }

  /**
   * 开始上传暂停和错误的文件
   */
  public startUpload(fileIds: string[]): { success: boolean; message?: string } {
    this.changeFilesStatus(fileIds, FileStatus.PENDING)

    // 如果上传完成，并且有待上传的文件，则自动上传
    if (
      [SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) &&
      this.pendingFiles.length > 0
    ) {
      this.autoUpload()
    }

    return { success: true, message: '已开启上传' }
  }

  // 暂停上传
  public puauseUpload(fileIds: string[]): { success: boolean; message?: string } {
    this.changeFilesStatus(fileIds, FileStatus.PAUSED)
    return { success: true, message: '已暂停上传' }
  }

  // 暂停上传中任务
  public puauseUploadingTask(fileIds: string[]): { success: boolean; message?: string } {
    if (this.currentWorker) {
      for (const fileId of fileIds) {
        this.currentWorker.postMessage({
          type: 'pause',
          fileId
        })
      }
    }

    return { success: true, message: '已暂停上传' }
  }

  /**
   * 更改文件状态
   * @param fileIds 要更改状态的文件ID数组
   * @param status
   */
  private changeFilesStatus(fileIds: string[], status: FileStatus): void {
    let source: FileSource | undefined
    for (const fileId of fileIds) {
      const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId)
      if (fileIndex !== -1) {
        // 如果状态不同则更新状态
        if (this.pendingFiles[fileIndex].status !== status) {
          this.pendingFiles[fileIndex].status = status
        }

        source = this.pendingFiles[fileIndex].source
      }
    }
    if (source) {
      this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
        source,
        files: this.getFilesBySource(source),
        pendingFiles: this.pendingFiles
      })
      this.fileStateStore.saveStates(this.pendingFiles)
    }
  }

  /**
   * 删除上传中的任务
   * @param fileIds 要删除的文件ID数组
   */
  public deleteUploadingTask(fileIds: string[]): { success: boolean; message?: string } {
    let source: FileSource | undefined
    let delFiles: IFileInfo[] = []

    if (this.currentWorker) {
      for (const fileId of fileIds) {
        this.currentWorker.postMessage({
          type: 'delete',
          fileId
        })
      }
    }

    // 同时从pendingFiles中删除文件，确保UI立即更新
    for (const fileId of fileIds) {
      const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId)
      if (fileIndex !== -1) {
        delFiles = this.pendingFiles.splice(fileIndex, 1)
        this.progress.deleted += 1
        this.progress.total -= 1
      }
    }

    if (delFiles.length > 0) {
      source = delFiles[0].source
      this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
        source,
        files: this.getFilesBySource(source),
        pendingFiles: this.pendingFiles
      })
      this.fileStateStore.saveStates(this.pendingFiles)
    }

    return { success: true, message: '已删除上传中的任务' }
  }

  /**
   * 删除指定文件ID列表中的文件
   * @param fileIds 要删除的文件ID数组
   */
  public deleteFiles(fileIds: string[]): void {
    let source: FileSource | undefined
    let delFiles: IFileInfo[] = []
    for (const fileId of fileIds) {
      const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId)
      if (fileIndex !== -1) {
        delFiles = this.pendingFiles.splice(fileIndex, 1)
        this.progress.deleted += 1
        this.progress.total -= 1
      }
    }

    if (delFiles.length > 0) {
      source = delFiles[0].source
      this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
        source,
        files: this.getFilesBySource(source),
        pendingFiles: this.pendingFiles
      })
      this.fileStateStore.saveStates(this.pendingFiles)
    }
  }

  /**
   * 获取上传令牌（模拟实现）
   */
  private async getUploadToken(): Promise<IUploadToken> {
    // 这里应该是实际获取上传令牌的API调用
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    return {
      token: `upload_token_${Date.now()}`,
      expires: Date.now() + 3600000 // 1小时后过期
    }
  }

  /**
   * 重置进度信息
   */
  private resetProgress(): void {
    this.progress = {
      total: 0,
      processed: 0,
      deleted: 0,
      conflicts: 0
    }
  }

  /**
   * 发送事件到渲染进程
   */
  private emitEvent(event: string, data: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      try {
        this.mainWindow.webContents.send(event, data)
        console.log(`webContents event ${event} sent successfully`, data)
      } catch (error) {
        console.error(`Failed to emit event ${event}:`, error)
      }
    } else {
      console.warn(`Cannot emit ${event}: mainWindow is not available`)
    }
  }

  /**
   * 获取当前同步状态
   */
  public getStatus(): SyncStatus {
    return this.status
  }

  /**
   * 获取当前同步进度
   */
  public getProgress(): ISyncProgress {
    return this.progress
  }

  /**
   * 获取待同步文件列表
   */
  public getPendingFiles(): IFileInfo[] {
    return this.pendingFiles
  }

  /**
   * 清除待同步文件列表
   */
  public clearFiles(source: FileSource): void {
    this.pendingFiles = this.pendingFiles.filter((f) => f.source !== source)
    this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
      source,
      files: [],
      pendingFiles: this.pendingFiles
    })
  }

  /**
   * 获取指定来源的文件
   */
  public getFilesBySource(source: FileSource): IFileInfo[] {
    return this.pendingFiles.filter((f) => f.source === source)
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.currentWorker) {
      this.currentWorker.terminate()
      this.currentWorker = undefined
    }
  }

  /**
   * 选择自定义路径文件上传
   * @param source 文件来源，PKB或TKB的knowledgeId
   * @returns
   */
  public async selectCustomPathFilesUpload(
    folderPath: string,
    source: string,
    resourceId: string,
    resourceType: string
  ): Promise<void> {
    const fileInfoArr = await this.getFilesByPath(folderPath, source, resourceId, resourceType)

    if (fileInfoArr.length > 0) {
      // 添加获取的文件
      await this.processSelectedFiles(
        fileInfoArr.map((fileInfoItem) => fileInfoItem.path),
        source, // PrivateKBSource, // 'PKB'
        resourceId,
        resourceType
      )
    }

    // 自动上传
    if (
      [SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) &&
      this.pendingFiles.length > 0
    ) {
      this.autoUpload()
    }
  }

  /**
   * 自动上传已有待上传文件
   * @param
   * @return
   */
  public async autoUploadPendingFiles(): Promise<void> {
    if (
      [SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) &&
      this.pendingFiles.length > 0
    ) {
      this.autoUpload()
    } else {
      console.log('没有待上传的文件')
    }
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    // 清理检查间隔
    if (this.rendererReadyCheckInterval) {
      clearInterval(this.rendererReadyCheckInterval)
      this.rendererReadyCheckInterval = undefined
    }

    // 清理当前 worker
    if (this.currentWorker) {
      this.currentWorker.terminate()
      this.currentWorker = undefined
    }

    if (this.currentChatWorker) {
      this.currentChatWorker.terminate()
      this.currentChatWorker = undefined
    }

    // console.log('FileSyncManager 资源已清理')
  }
}
