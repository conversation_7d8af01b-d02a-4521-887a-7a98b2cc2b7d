const { parentPort } = require('node:worker_threads')
const fs = require('node:fs')
import FormData from 'form-data' // 改用 ESM 方式导入以获得正确的类型定义

// 定义消息类型
interface UploadMessage {
  type: 'upload'
  source: string
  fileId: string
  filePath: string
  token: string
}

interface ResultMessage {
  type: 'success' | 'error' | 'progress'
  fileId: string
  data?: any
  error?: string
  source?: string
}

// 定义 API 返回数据结构
interface ApiResponse {
  code: number // 假设接口返回的 code 是 number 类型
  message?: string
  data?: any
}

if (!parentPort) {
  throw new Error('必须在 Worker 线程中运行')
}

// 处理上传请求
async function uploadFile(source: string, fileId: string, filePath: string, token: string) {
  try {
    const fetch = (await import('node-fetch')).default
    const stats = await fs.promises.stat(filePath)
    const totalSize = stats.size
    let uploadedSize = 0

    // 构建上传数据对象
    const file = fs.createReadStream(filePath)
    const dataObj = {
      knowledgeId: source,
      file: file
    }

    const formData = new FormData()
    for (const key in dataObj) {
      formData.append(key, dataObj[key])
    }

    // 监听文件流的数据传输
    file.on('data', (chunk: Buffer) => {
      uploadedSize += chunk.length
      const progress = Math.round((uploadedSize / totalSize) * 100)

      // 发送进度消息
      parentPort!.postMessage({
        type: 'progress',
        fileId,
        data: {
          progress,
          uploaded: uploadedSize,
          total: totalSize
        }
      })
    })

    // 简化请求配置
    const response = await fetch('http://10.176.14.223:8081/api/v1/document/upload', {
      method: 'POST',
      headers: formData.getHeaders(),
      body: formData
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`)
    }

    const result: ApiResponse = (await response.json()) as ApiResponse
    console.log('Upload success:', result)
    if (result.code === 200) {
      parentPort!.postMessage({
        type: 'success',
        fileId,
        data: result.data,
        source
      } as ResultMessage)
    }
    if (result.code !== 200) {
      parentPort!.postMessage({
        type: 'error',
        fileId,
        data: result
      } as ResultMessage)
    }
  } catch (error: any) {
    console.log('Upload error:', error)
    parentPort!.postMessage({
      type: 'error',
      fileId,
      error: error.message
    } as ResultMessage)
  }
}

// 监听主线程消息
parentPort.on('message', (message: UploadMessage) => {
  const { type, source, fileId, filePath, token } = message
  if (type === 'upload') {
    uploadFile(source, fileId, filePath, token)
  }
})
