/*
 * @Description:
 * @FilePath: \ainow-ui\apps\edge\src\main\Service\fileSync\core\FileUploadWorkerNative.ts
 */

const { parentPort } = require('node:worker_threads')
const fs = require('node:fs')
const http = require('node:http')
const path = require('node:path')

// 定义消息类型
interface UploadMessage {
  type: 'upload' | 'pause' | 'delete'
  source: string
  fileId: string
  filePath: string
  token: string
  userIdForUpload: string
  uploadApiUrl: string
  resourceId: string
  resourceType: string
}

interface ResultMessage {
  type: 'success' | 'error' | 'open' | 'progress' | 'pause'
  fileId: string
  data?: any
  error?: string
  source?: string
}

interface ApiResponse {
  code: number
  message?: string
  data?: any
}

if (!parentPort) {
  throw new Error('必须在 Worker 线程中运行')
}

// 生成随机边界字符串
function generateBoundary() {
  return '------------------------' + Date.now().toString(16)
}

// 构建 multipart/form-data 请求体
function createMultipartBody(source: string, filePath: string, boundary: string) {
  const fileName = path.basename(filePath)
  const fileStream = fs.createReadStream(filePath)

  const header = Buffer.from(
    [
      `--${boundary}`,
      `Content-Disposition: form-data; name="knowledgeId"`,
      '',
      source,
      `--${boundary}`,
      `Content-Disposition: form-data; name="file"; filename="${fileName}"`,
      'Content-Type: application/octet-stream',
      '',
      ''
    ].join('\r\n'),
    'utf-8'
  )

  const footer = Buffer.from(`\r\n--${boundary}--\r\n`, 'utf-8')

  return { header, fileStream, footer }
}

// 存储上传请求的 Map
const uploadRequests = new Map<
  string,
  {
    request: http.ClientRequest
    fileStream: fs.ReadStream
    controller: AbortController
  }
>()

async function uploadFile(
  source: string,
  fileId: string,
  filePath: string,
  token: string,
  userIdForUpload: string,
  uploadApiUrl: string,
  resourceId?: string,
  resourceType?: string
) {
  try {
    const stats = await fs.promises.stat(filePath)
    const totalSize = stats.size
    let uploadedSize = 0

    const boundary = generateBoundary()
    const { header, fileStream, footer } = createMultipartBody(source, filePath, boundary)
    const controller = new AbortController()

    const urlObj = new URL(uploadApiUrl)
    const { hostname, port, pathname } = urlObj
    console.log('Upload URL:', hostname, port, pathname)

    return new Promise((resolve, reject) => {
      const headers: Record<string, string> = {
        'Content-Type': `multipart/form-data; boundary=${boundary}`,
        userId: userIdForUpload,
        token
      }
      if (resourceId) {
        headers['resource-Id'] = resourceId
        if (resourceType) {
          headers['resource-Type'] = resourceType
        }
      }

      const request = http.request(
        {
          hostname,
          port,
          path: pathname,
          method: 'POST',
          headers,
          signal: controller.signal // 添加 signal
        },
        (response) => {
          let data = ''
          response.on('data', (chunk) => {
            data += chunk
          })

          response.on('end', () => {
            uploadRequests.delete(fileId)
            const result: ApiResponse = JSON.parse(data)
            resolve(result)
          })
        }
      )

      // 存储请求实例
      uploadRequests.set(fileId, {
        request,
        fileStream,
        controller
      })

      request.on('error', (error) => {
        uploadRequests.delete(fileId)
        reject(error)
      })

      // 写入 header
      request.write(header)

      parentPort!.postMessage({
        type: 'open',
        fileId
      } as ResultMessage)

      // 监听文件流进度
      // fileStream.on('data', (chunk: Buffer) => {
      //   uploadedSize += chunk.length
      //   const progress = Math.round((uploadedSize / totalSize) * 100)

      //   parentPort!.postMessage({
      //     type: 'progress',
      //     fileId,
      //     data: {
      //       progress,
      //       uploaded: uploadedSize,
      //       total: totalSize
      //     }
      //   })
      // })

      // 通过管道传输文件数据
      fileStream.pipe(request, { end: false })

      // 文件传输完成后写入 footer 并结束请求
      fileStream.on('end', () => {
        request.end(footer)

        // 打印请求信息
        console.log('Request Headers:', request.getHeaders())
      })
    })
      .then((result: ApiResponse) => {
        uploadRequests.delete(fileId)
        console.log('Upload Promise result:', result)
        if (result.code === 200) {
          parentPort!.postMessage({
            type: 'success',
            fileId,
            data: result.data,
            source,
            resourceId,
            resourceType
          } as ResultMessage)
        } else {
          parentPort!.postMessage({
            type: 'error',
            fileId,
            data: result
          } as ResultMessage)
        }
      })
      .catch((error: any) => {
        console.log('Upload Promise error:', error)

        uploadRequests.delete(fileId)
        parentPort!.postMessage({
          type: 'error',
          fileId,
          data: error
        } as ResultMessage)
      })
  } catch (error: any) {
    uploadRequests.delete(fileId)
    console.log('Upload error:', error)
    parentPort!.postMessage({
      type: 'error',
      fileId,
      error: error.message
    } as ResultMessage)
  }
}

// 监听主线程消息
parentPort.on('message', (message: UploadMessage) => {
  const {
    type,
    source,
    fileId,
    filePath,
    token,
    userIdForUpload,
    uploadApiUrl,
    resourceId,
    resourceType
  } = message

  parentPort!.postMessage({
    type: 'on worker message',
    uploadInfo: message
  })
  if (type === 'upload') {
    uploadFile(
      source,
      fileId,
      filePath,
      token,
      userIdForUpload,
      uploadApiUrl,
      resourceId,
      resourceType
    )
  }

  if (type === 'pause' || type === 'delete') {
    const uploadRequest = uploadRequests.get(fileId)
    console.log('uploadRequest', uploadRequest)
    if (uploadRequest) {
      try {
        const { fileStream, controller, request } = uploadRequest

        // 中止请求
        // controller.abort()

        // 移除所有事件监听器
        fileStream.removeAllListeners()
        request.removeAllListeners()

        // 先中止请求
        // request.abort()

        // 销毁文件流
        fileStream.destroy()
        console.log('文件流已消毁', fileId)

        // 销毁请求
        // request.destroy()
        // console.log('请求已销毁', fileId)

        uploadRequests.delete(fileId)

        parentPort!.postMessage({
          type,
          fileId
        } as ResultMessage)
      } catch (error) {
        console.error('pause error', error)
      }
    }
  }
})
