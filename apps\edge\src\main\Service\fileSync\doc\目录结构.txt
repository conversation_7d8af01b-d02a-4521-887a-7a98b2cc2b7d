src/
├── main/                                 # 主进程代码目录
│   ├── fileSync/                         # 文件同步模块目录 (改为fileSync更贴切实际功能)
│   │   ├── __tests__/                   # 测试文件目录
│   │   │   ├── FileSyncManager.test.ts       # 同步管理器测试
│   │   │   ├── FileStateStore.test.ts        # 文件状态存储测试
│   │   │   └── FileUploadWorker.test.ts      # 文件上传worker测试
│   │   ├── types/                       # 类型定义目录
│   │   │   ├── index.ts                 # 类型定义导出文件
│   │   │   ├── syncTypes.ts             # 同步相关类型定义(包含文件状态、来源等)
│   │   │   └── uploadTypes.ts           # 上传相关类型定义(上传配置、进度等)
│   │   ├── utils/                       # 工具函数目录
│   │   │   ├── index.ts                 # 工具函数导出文件
│   │   │   ├── fileProcessor.ts         # 文件处理器(文件操作相关功能)
│   │   │   └── syncValidator.ts         # 同步验证器(数据验证相关功能)
│   │   ├── syncConfig.ts                # 同步配置文件(配置参数、错误码等)
│   │   ├── FileStateStore.ts            # 文件状态存储类(替代Cache，更明确功能)
│   │   ├── FileSyncManager.ts           # 文件同步管理器(替代DocumentSyncModule，更清晰)
│   │   ├── FileUploadWorker.ts          # 文件上传Worker管理器
│   │   ├── uploadTask.ts                # 上传任务实现(替代uploadWorker，更符合功能)
│   │   └── index.ts                     # 模块导出文件
│   └── index.ts                         # 主进程入口文件
├── shared/                              # 共享代码目录
│   ├── types/                          # 共享类型定义目录
│   │   └── ipcEvents.ts                # IPC事件类型定义(改名更符合实际内容)
│   └── constants/                      # 共享常量目录
│       └── syncEvents.ts               # 同步事件常量(更明确表明是同步相关事件)
├── renderer/                           # 渲染进程代码目录
│   └── services/
│       └── FileSyncService.ts         # 文件同步服务(更明确功能定位)
└── package.json                        # 项目配置文件