@startuml
participant PKB order 1
participant TKB order 2
participant "文件同步管理器\n(FileSyncManager)" as FSM order 3
participant "文件上传web worker\n(FileUploadWorker)" as Worker order 4
participant "服务端\n(Server)" as Server order 5
database "文件状态存储\n(FileStateStore)" as Store order 6

== 读取历史阶段 (History Loading) ==
FSM -> Store: 读取历史文件状态
note right
获取缓存信息：
- 未完成的上传任务
- 上传失败的文件
- 最近上传成功的记录
end note

Store --> FSM: 返回缓存的文件状态
note left
返回状态信息：
- PENDING: 等待上传的文件
- UPLOADING: 中断的上传任务
- FAILED: 上传失败的文件
- SUCCESS: 最近上传成功的文件
end note

FSM -> FSM: 分析历史状态
note right
处理历史数据：
- 恢复中断的上传任务
- 重试失败的上传
- 过滤重复文件
end note

== 新的上传阶段 (Initialization) ==
PKB -> FSM: 获取本地文件列表
TKB -> FSM: 获取本地文件列表

FSM -> Store: 保存文件列表元数据和初始状态
note right
状态信息包括：
- fileId: 文件唯一标识
- status: 等待中(PENDING)
- source: 来源(PKB/TKB)
- timestamp: 时间戳
- metadata: {
    name: 文件名,
    type: 文件类型,
    size: 文件大小
  }
end note

== 文件去重和验证 (Deduplication & Validation) ==
FSM -> FSM: 文件列表处理
note right
- 验证文件格式
- 检查文件大小
- 过滤重复文件
end note

== 创建上传Worker (Create Upload Worker) ==
FSM -> Worker: 创建Worker实例
note right
初始化配置：
- 分片大小
- 重试次数
- 请求超时
end note

== 上传准备 (Upload Preparation) ==
FSM -> Server: 请求上传令牌
Server --> FSM: 返回上传令牌和配置

== 文件上传阶段 (File Upload) ==
loop 文件列表不为空
    FSM -> FSM: 获取待上传文件
    FSM -> Store: 更新文件状态为上传中(UPLOADING)
    
    FSM -> Worker: 发送上传任务
    note right
    上传任务信息：
    - fileId
    - 文件数据
    - 上传令牌
    - 分片配置
    end note
    
    Worker -> Worker: 文件分片处理(本次分片为1)
    note right
    - 计算分片
    - 准备上传数据
    end note
    
    loop 分片上传
        Worker -> Server: 上传文件分片
        
        alt 分片上传成功
            Server --> Worker: 200 OK
            Worker --> FSM: 分片完成通知
            FSM -> Store: 更新文件状态为上传中(SUCCESS)
            FSM -> FSM: 更新UI显示进度
        else 分片上传失败
            Server --> Worker: 错误响应
            Worker --> FSM: 分片失败通知
            FSM --> Store: 更新文件状态为失败(FAILED)
        end
    end
end

== 上传完成处理 (Upload Completion) ==
FSM -> PKB: 通知上传完成状态
FSM -> TKB: 通知上传完成状态
FSM -> Worker: 终止Worker

@enduml