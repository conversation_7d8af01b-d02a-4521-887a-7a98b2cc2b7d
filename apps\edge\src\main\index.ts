import { app, protocol } from 'electron'
import { electronApp, optimizer } from '@electron-toolkit/utils'
import { IpcRegister } from './ipc'
import { setProtocolScheme, toLoginWithCode } from './Service/Auth'
import { Config } from '../config'
import path from 'path'
import { readFile } from 'fs/promises'
import { createMainWin } from './win/MainWin'
import { logCombine } from './Core/Log'
import { handleWinShow } from './Core'
import { FileSyncManager } from './Service/fileSync'
// import { initConfig } from '@/config'

logCombine(app.getPath('logs'))
// initConfig()
const mainWinInit = () => {
  const win = createMainWin() // 搜索框创建
  // win.on('close', (e) => {
  //   console.log('close')
  //   e.preventDefault()
  //   win.hide()
  // })
  // const mainWin = mainWinInit()
  app.on('second-instance', (e, argv) => {
    console.info('second-instance', 'second instance detected.')
    toLoginWithCode(win, argv)
    win.isVisible() || handleWinShow(win)
  })

  // if (process.platform === 'win32') {
  //   win.setBackgroundMaterial('acrylic') // 可选值: 'mica', 'acrylic', 'tabbed'
  // }
  IpcRegister(win)
  const fileSyncManager = new FileSyncManager(win)
  // fileSyncManager.autoUploadPendingFiles()

  return win
}

app.whenReady().then(async () => {
  console.info('main mode=>', import.meta.env.MODE)
  console.info('env', import.meta.env.VITE_ENV)
  await Config.init()

  setProtocolScheme()
  const gotTheLock = app.requestSingleInstanceLock()

  // toLoginWithCode(w)
  // protocol.registerSchemesAsPrivileged([dddddd
  if (!gotTheLock) {
    console.info('Another instance is running.')
    app.quit()
  }
  electronApp.setAppUserModelId('com.electron')

  // RegeditService.setVal('HKCR\\ainow.row\\shell\\open', {
  //   'command': {
  //     value: app.getPath('exe'),
  //     type: 'REG_SZ',
  //   }
  // })

  protocol.handle('app', async (request) => {
    // 解析请求路径（例如 app://index.html → /index.html）
    const url = new URL(request.url) // 提取路径部分
    const filePath = path.join(__dirname, '../' + url.hostname, url.pathname)
    try {
      // 读取文件内容
      const data = await readFile(filePath)
      // 根据文件类型设置 MIME 类型（可选）
      let mimeType = 'text/html'
      if (filePath.endsWith('.js')) mimeType = 'text/javascript'
      else if (filePath.endsWith('.css')) mimeType = 'text/css'
      else if (filePath.endsWith('.png')) mimeType = 'image/png'

      // 返回 Response 对象
      return new Response(data, {
        headers: { 'Content-Type': mimeType }
      })
    } catch (error) {
      // 文件不存在时返回 404
      return new Response('Not Found', { status: 404 })
    }
  })
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })
  mainWinInit()
  // IPC test
  // ipcMain.on('ping', () => console.log('pong'))
  app.on('window-all-closed', () => {
    console.log('window-all-closed')

    if (process.platform !== 'darwin') {
      app.quit()
    }
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.

// function getResourcesPath(arg0: string) {
//   throw new Error('Function not implemented.')
// }
// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
