import { app, BrowserWindow, dialog, ipc<PERSON>ain, screen } from 'electron'
import { Bounds, IPCRes, WinState } from '@/types'
import { openWebSite, getMoveWinFunc, setBounds, isMax } from '../Core'
// import { toLoginWithCode } from '../Service/Auth'
import * as fs from 'fs'
import path from 'path'
// import { getFile, getResourcesPath, isMax } from '../../utils'
import { Config } from '@/config'
// import AINowService from '../AINowService'
// import { processIconsInMultipleWorkers, searchFn } from '../LocalDataService'

// import { logInit } from '../LogService'
// import { LogType } from 'vite'

export function IpcRegister(win: BrowserWindow) {
  const beforeMaxMounds = win.getBounds()
  function handleIpcResponse<T>(success: boolean, res?: T): IPCRes<T> {
    const timestamp = Date.now()
    return { success, res, timestamp }
  }

  const moveHandler = getMoveWinFunc(win)
  ipcMain.on('setWinBounds', (e, bound: Bounds) => {
    if (!win.isDestroyed()) {
      setBounds(win, bound)
    }
    // moveHandler.setCurWinSize({ width: bound.width, height: bound.height })
  })
  ipcMain.on('window:move', (e, canMoving) => {
    moveHandler.execFunc(canMoving)
  })

  ipcMain.handle('getBounds', () => {
    const res = win.getBounds()
    return handleIpcResponse(!!res, res)
  })
  ipcMain.on('setIgnoreMouseEvents', (e, ignore: boolean, option: { forward: boolean }) => {
    if (!win.isDestroyed()) {
      win.setIgnoreMouseEvents(ignore, option) //
    }
  })
  ipcMain.handle('openWebSite', (e, url: string) => {
    return openWebSite(url)
  })
  // //返回登录code
  // ipcMain.handle('handleLogin', (e,) => {
  //   return getAuthCode()
  // })

  ipcMain.on('setWindowBlur', () => {
    if (!win.isDestroyed()) {
      win.blur()
    }
  })
  ipcMain.on('winMax', () => {
    if (!win.isDestroyed()) {
      Object.assign(beforeMaxMounds, win.getBounds())
      win.maximize()
    }
  })
  ipcMain.on('winRestore', () => {
    console.log('winRestore')
    if (!win.isDestroyed()) {
      win.setBounds(beforeMaxMounds)
    }
  })
  ipcMain.on('winMin', () => {
    if (!win.isDestroyed()) {
      win.minimize()
    }
  })
  ipcMain.on('winClose', () => {
    win.close()
  })
  ipcMain.handle('getWinState', () => {
    let state = WinState.Normal

    if (isMax(win)) {
      state = WinState.Max
    } else if (win.isMinimized()) {
      state = WinState.Min
    }
    return state
  })
  // ipcMain.on('maximize', () => {
  //   win.close()
  // })
  ipcMain.handle('getConfig', (e, needUpDate?: boolean) => {
    // const configPath = getResourcesPath(path.join(__dirname, '../../../resources/config.json'))
    return Config.getConfig(needUpDate)
  })

  ipcMain.handle('print-to-pdf', async () => {
    if (!win || win.isDestroyed()) {
      throw new Error('Browser window is not available')
    }

    try {
      return await win.webContents.printToPDF({
        printBackground: true,
        pageSize: 'A4',
        landscape: false
      })
    } catch (err) {
      console.error('PDF生成失败:', err)
    }
  })

  ipcMain.handle('save-pdf', async (event, { buffer, path }) => {
    try {
      await fs.promises.writeFile(path, buffer) // 改用异步API
      return { success: true }
    } catch (err) {
      console.error('文件保存失败:', err)
      throw err
    }
  })
  ipcMain.handle('show-save-dialog', (_, options) => {
    return dialog.showSaveDialog(win, options)
  })

  ipcMain.handle('save-file', async (_, { fileName, data }) => {
    const downloadsPath = app.getPath('downloads')
    const filePath = path.join(downloadsPath, fileName)
    return new Promise((resolve, reject) => {
      fs.writeFile(filePath, Buffer.from(data), (err) => {
        if (err) {
          reject(`save file error: ${err.message}`)
        } else {
          resolve(filePath)
        }
      })
    })
  })
}
