import { BrowserWindow, shell, screen, Display } from 'electron'
import { join } from 'path'
import { is } from '@electron-toolkit/utils'
import { Bounds } from '@/types'
import { handleWinHide } from '../../Core'
import { IpcRegister } from '@/main/ipc'
// import { BroadcastMsg } from '../AINowService/PipeClient'
// import { log } from 'console'
// import icon from '../../resources/icon.ico?asset'
export function createMainWin(): BrowserWindow {
  // 创建窗口时先不设置位置
  const mainWindow = new BrowserWindow({
    width: 1100,
    height: 800,
    minHeight: 600,
    minWidth: 1100,
    frame: false,
    hasShadow: true,
    useContentSize: true,
    roundedCorners: true,
    transparent: true,
    resizable: true,
    show: false, // 先不显示窗口
    title: 'Lenovo Team AI',
    // backgroundColor: '#000000', // 必须为 HEX 透明色

    webPreferences: {
      webSecurity: false, // 关键配置：禁用同源策略
      zoomFactor: 1.0,
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })
  global.mainWindow = mainWindow
  // 手动居中窗口
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  const windowBounds = mainWindow.getBounds()

  // 计算居中位置
  const x = Math.floor((width - windowBounds.width) / 2)
  const y = Math.floor((height - windowBounds.height) / 2)

  // 设置窗口位置
  mainWindow.setBounds({ x, y, width: windowBounds.width, height: windowBounds.height })

  // 设置完位置后显示窗口
  mainWindow.show()

  // IpcRegister(mainWindow)
  mainWindow.on('will-resize', (e, newBounds) => {
    console.warn(e, newBounds, 'will-resize')
  })
  // mainWindow.shadow = true
  // mainWindow.setAlwaysOnTop(true, 'screen-saver')
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/#/main')
    // mainWindow.webContents.openDevTools()
  } else {
    // mainWindow.loadURL('app://../renderer/index.html' + '/#/main')
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'), { hash: 'main' })
    // mainWindow.loadFile('../renderer/index.html', { hash: 'main' })
    // mainWindow.webContents.openDevTools()
  }
  // function getDisplaysBetween(windowBounds: Bounds, displays: Display[]) {
  //   const crossingDisplays = [] as Display[]

  //   displays.forEach((display) => {
  //     const workArea = display.workArea

  //     // 判断窗体是否跨越当前显示器的水平范围（x坐标）和垂直范围（y坐标）
  //     const isCrossingHorizontal =
  //       windowBounds.x < workArea.x + workArea.width &&
  //       windowBounds.x + windowBounds.width > workArea.x
  //     const isCrossingVertical =
  //       windowBounds.y < workArea.y + workArea.height &&
  //       windowBounds.y + windowBounds.height > workArea.y

  //     // 如果窗体与当前显示器的范围相交，则记录该显示器
  //     if (isCrossingHorizontal && isCrossingVertical) {
  //       crossingDisplays.push(display)
  //     }
  //   })

  //   return crossingDisplays
  // }
  // const displays = screen.getAllDisplays() // 获取所有显示器的信息
  //console.log(displays, 'displays')
  // const limitWindowPosition = () => {
  //   const windowBounds = mainWindow.getBounds() // 获取窗体的位置和尺寸
  //   const { x: winX, y: winY, width: winWidth, height: winHeight } = mainWindow.getContentBounds() // 获取窗体位置
  //   // mainWindow.webContents
  //   const currentScreen = screen.getDisplayNearestPoint({ x: winX, y: winY }) // 获取窗体所在屏幕
  //   const { x, y, width: WAWidth, height: WAHeight } = currentScreen.workArea // 获取屏幕工作区大小

  //   let newX = winX
  //   let newY = winY
  //   const maxWinX = winX + winWidth
  //   const maxWinY = winY + winHeight
  //   if (winX < x) newX = x // 限制左边界
  //   if (winY < y) newY = y // 限制上边界
  //   if (maxWinX > x + WAWidth) newX = x + WAWidth - winWidth // 限制右边界
  //   if (maxWinY > y + WAHeight) newY = y + WAHeight - winHeight // 限制下边界

  //   // 如果位置被调整，设置新位置
  //   if (newX !== winX || newY !== winY) {
  //     const currentDisplays = getDisplaysBetween(windowBounds, displays)
  //     if (currentDisplays.length < 2) {
  //       mainWindow.setBounds({
  //         x: newX,
  //         y: newY,
  //         width: winWidth,
  //         height: winHeight
  //       })
  //     }
  //   }
  // }

  //   mainWindow.on('move', limitWindowPosition)
  screen.on('display-metrics-changed', () => {
    // limitWindowPosition() // 更新窗体位置，确保仍然在屏幕内
    if (!mainWindow.isDestroyed()) {
      const { width, height } = mainWindow.getBounds()
      console.log('Display metrics changed, updating window constraints...', width, height)
      mainWindow.setBounds({
        width,
        height
      })
    }
  })

  mainWindow.on('blur', () => {
    // handleWinHide(mainWindow)
  })
  mainWindow.on('show', () => {
    // handleWinHide(mainWindow)

    mainWindow.focus()
    mainWindow.webContents.send('visible-focus')
  })

  mainWindow.on('ready-to-show', () => {})
  // mainWindow.on('unmaximize', () => {
  //   mainWindow.webContents.send('onMaximize', false)
  // })
  mainWindow.on('maximize', () => {
    if (!mainWindow.isDestroyed()) {
      const bound = mainWindow.getBounds()
      mainWindow.setBounds({
        width: bound.width + 16,
        height: bound.height + 16,
        x: bound.x - 8,
        y: bound.y - 8
      })
    }
  })
  return mainWindow
}
