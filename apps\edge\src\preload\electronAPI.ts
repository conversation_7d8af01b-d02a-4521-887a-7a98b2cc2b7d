import { ipc<PERSON>ender<PERSON> } from 'electron'

// 公共Api方法
export const commonApi = {
  openApp: (path: string) => ipcRenderer.invoke('openApp', path),
  openWebSite: (url: string) => ipcRenderer.invoke('openWebSite', url),
  getConfig: (needUpdate?: Boolean) => ipcRenderer.invoke('getConfig', needUpdate),
  getWinState: () => ipcRenderer.invoke('getWinState'),
  windowMove: (canMoving: boolean) => {
    ipcRenderer.send('window:move', canMoving)
  },
  toLoginWithCode: (cb: (e: Electron.IpcRendererEvent, code: string) => void) => {
    ipcRenderer.on('toLoginWithCode', cb)
  },
  winMax: () => {
    ipcRenderer.send('winMax')
  },
  winMin: () => {
    ipcRenderer.send('winMin')
  },
  winRestore: () => {
    ipcRenderer.send('winRestore')
  },
  winClose: () => {
    ipcRenderer.send('winClose')
  },
  // onMaximize: (cb: (e: Electron.IpcRendererEvent, msg: boolean) => void) => {
  //   ipcRenderer.on('onMaximize', cb)
  // },
  windowMoveDrag: (canMoving: boolean) => {
    ipcRenderer.send('window:move-drag', canMoving)
  },
  setIgnoreMouseEvents: (ignore: boolean, option?: { forward: boolean }): void =>
    ipcRenderer.send('setIgnoreMouseEvents', ignore, option),
  // 导出相关API
  printToPdf: () => ipcRenderer.invoke('print-to-pdf'),
  showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
  savePdf: (data: { buffer: Buffer; path: string }) => ipcRenderer.invoke('save-pdf', data),
  saveFile: (fileName: string, blob) =>
    ipcRenderer.invoke('save-file', {
      fileName,
      data: Array.from(new Uint8Array(blob))
    })
}
