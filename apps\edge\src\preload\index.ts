import { contextBridge, ipc<PERSON><PERSON><PERSON>, web<PERSON>rame } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { Bounds, ListItem } from '@/types'
import { commonApi } from './electronAPI'
import { fileSyncApi } from '../main/Service/fileSync'

// Custom APIs for renderer
const api = {
  ...commonApi,
  ...fileSyncApi,
  openWebSite: (url: string) => ipcRenderer.invoke('openWebSite', url),
  openApp: (path: string) => ipcRenderer.invoke('openApp', path),
  openProgram: (path: string, args) => ipcRenderer.invoke('startApp', path, args),
  testOpen2: (path: string) => ipcRenderer.send('testOpen2', path),
  // handleMoveWin: (bounds: Bounds) => ipcRenderer.send('handleMoveWin', bounds),
  setIgnoreMouseEvents: (ignore: boolean, option?: { forward: boolean }): void =>
    ipcRenderer.send('setIgnoreMouseEvents', ignore, option),

  sendMiniMsg: (param: Record<string, unknown>): Promise<boolean> =>
    ipcRenderer.invoke('sendMiniMsg', param),
  translateMiniMsg: (param: Record<string, unknown>): Promise<boolean> =>
    ipcRenderer.invoke('translateMiniMsg', param),
  openSystemSettings: (param: string) => ipcRenderer.invoke('openSystemSettings', param),
  setBounds: (bounds: Partial<Bounds>) => {
    ipcRenderer.send('setWinBounds', bounds)
  },
  search: async (keyword: string): Promise<ListItem[]> =>
    await ipcRenderer.invoke('search', keyword),
  setWindowBlur: () => {
    //设置窗口失去焦点 多次打开mini小窗时 没有触发失去焦点 需手动设置
    ipcRenderer.send('setWindowBlur')
  },
  onVisibleFocus: (callback: (value) => void) =>
    ipcRenderer.on('visible-focus', (_event, value) => callback(value))
}

window.addEventListener('DOMContentLoaded', () => {
  webFrame.setZoomFactor(1) // 在 DOM 加载前设置
  console.log('设置 webFrame 缩放因子为 1.0', Date.now())
})
// 修复 localStorage 不可用的问题
if (location.href.startsWith('app://')) {
  const storage = new Map()

  Object.defineProperty(window, 'localStorage', {
    value: {
      getItem: (key) => storage.get(key),
      setItem: (key, value) => storage.set(key, value),
      removeItem: (key) => storage.delete(key),
      clear: () => storage.clear()
    },
    writable: false,
    configurable: false
  })
  console.log('localStorage', localStorage)
}
// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
