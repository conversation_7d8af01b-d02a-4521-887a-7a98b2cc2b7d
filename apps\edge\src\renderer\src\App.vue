<template>
  <WinBarBtns class="win-bar-tools"></WinBarBtns>
  <LoginModal @initSuccess="initSuccess"></LoginModal>
  <RouterView v-if="initType"></RouterView>
</template>
<script setup lang="ts">
// import Layout from './components/Layout'
// import { GlobalConfig } from './common'
// import { electronHooks } from './electron'
// import { loginHooks } from './hooks/Auth'
import { ref } from 'vue'
import LoginModal from './components/LoginModal'
import WinBarBtns from './components/WinBarBtns'

const initType = ref(false)
const initSuccess = (type: boolean) => {
  initType.value = type
}
</script>
<style>
#app {
  position: relative;
  margin: 8px;
  height: calc(100vh - 16px);
  border-radius: 10px;
  overflow: hidden;
  background: transparent;
  /* box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.5); */
  position: relative;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.25);
}

@media print {
  #app {
    overflow: visible;
  }
}

.win-bar-tools {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1000;
}

html,
body {
  margin: 0;
  overflow: visible !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.kb-modal-confirm .ant-modal-confirm-btns {
  display: flex;
  justify-content: center;
  flex-flow: row-reverse;
  gap: 10px;
}
</style>
