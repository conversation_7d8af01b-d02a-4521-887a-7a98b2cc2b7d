import http from '../../../services/http'
import { GlobalConfig } from '../common'
import { Res } from '@/types'
import { FetchResponse } from '@/services/Fetch'
type HttpRes<T> = Promise<FetchResponse<Res<T>>>

const chatBaseUrl = `${GlobalConfig.kbFileServer}/api/v1`
export function getFileStatus(params: {
  documentIds: any
  knowledgeBaseId: number | string
}): HttpRes<{}> {
  const { documentIds, knowledgeBaseId } = params
  const config: { headers?: Record<string, string> } = {}
  if (knowledgeBaseId) {
    config.headers = {
      'resource-Id': String(knowledgeBaseId),
      'resource-Type': '1'
    }
  }
  return http.post(`${chatBaseUrl}/document/status`, { documentIds }, config)
}

export const deleteKBFile = async (params: { documentId: string; knowledgeBaseId: string }) => {
  return http.post(
    `${chatBaseUrl}/document/delete`,
    {
      documentId: params.documentId
    },
    {
      headers: {
        'resource-Id': params.knowledgeBaseId,
        'resource-Type': '2'
      }
    }
  )
}

export const getKBName = async (params: { knowledgeIds: string }) => {
  return http.post(`${chatBaseUrl}/knowledge/list`, {
    knowledgeIds: [params.knowledgeIds]
  })
}

export const deleteKBFiles = async (params: {
  docIds: string[]
  knowledgeBaseId: string
}): HttpRes<null> => {
  return http.post(
    `${chatBaseUrl}/document/deletes`,
    {
      documentIds: params.docIds
    },
    {
      headers: {
        'resource-Id': params.knowledgeBaseId,
        'resource-Type': '2'
      }
    }
  )
}

export const getKBList = (): HttpRes<
  {
    id: string
    name: string
  }[]
> => {
  return http.get(`${chatBaseUrl}/knowledgebase/list`)
}

export const knowledgeBaseApi = {
  deleteKBFile,
  deleteKBFiles,
  getKBList,
  getKBName
}
