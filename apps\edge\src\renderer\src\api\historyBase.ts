import http from '../../../services/http'
import { GlobalConfig } from '../common'
import { Res } from '@/types'
import { FetchResponse } from '@/services/Fetch'
type HttpRes<T> = Promise<FetchResponse<Res<T>>>

const lrBaseUrl = GlobalConfig.historyServer

/**
 * 注册会话历史，并返回一个唯一的 sessionId
 * @returns Promise
 */
export function registHistory(userId): HttpRes<{}> {
  return http.post(`${lrBaseUrl}/user/${userId}/session`, {})
}

export function getHistoryList(userId): HttpRes<{}> {
  return http.get(`${lrBaseUrl}/user/${userId}/sessions`, {})
}

export function getHistoryConversation(userId, id): HttpRes<{}> {
  return http.get(`${lrBaseUrl}/user/${userId}/session/${id}`, {})
}

export function addHistoryConversation(userId, id, param) {
  return http.post(`${lrBaseUrl}/user/${userId}/session/${id}/conversation`, {
    ...param
  })
}

export const renameCurHistory = async (userId, id: string, name) => {
  return http.post(`${lrBaseUrl}/user/${userId}/session/${id}/rename`, {
    name
  })
}

export const deleteCurHistory = async (userId, id: string): HttpRes<null> => {
  return http.post(`${lrBaseUrl}/user/${userId}/session/${id}/delete`, {})
}

export function sendAnswerChunk(param) {
  return http.post(
    `${GlobalConfig.chunkServer}/flow/773b13b9-922c-4e87-a86e-6f734fd1df43/execute2`,
    {
      ...param
    }
  )
}

export const historyBaseApi = {
  registHistory,
  getHistoryList,
  getHistoryConversation,
  addHistoryConversation,
  renameCurHistory,
  deleteCurHistory,
  sendAnswerChunk
}
