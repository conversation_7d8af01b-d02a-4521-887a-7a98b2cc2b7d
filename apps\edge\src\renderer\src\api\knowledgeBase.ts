import http from '../../../services/http'
import { GlobalConfig } from '../common'
import { Res, CutingTask } from '@/types'
import { FetchResponse } from '@/services/Fetch'
import { CutErrorData } from '@/renderer/src/views/KnowledgeBase/type'
type HttpRes<T> = Promise<FetchResponse<Res<T>>>

const kbFileBaseUrl = GlobalConfig.kbFileServer + '/api'
const lrBaseUrl = `${GlobalConfig.kbFileServer}/api/v1`

const getKBList = () => {
  return http.get(`/knowledgebase/list`)
}

const getKBFileList = async (params: object) => {
  const { knowledgeBaseId, ...rest } = params as any
  const headers: Record<string, string> = {}
  if (knowledgeBaseId) {
    headers['resource-Id'] = knowledgeBaseId
    headers['resource-Type'] = '2'
  }
  return http.get(`${kbFileBaseUrl}/v1/knowledge/documentList`, {
    params: {
      ...rest,
      getType: 1
    },
    headers
  })
}

const deleteKBFile = async (params: { fileId: string; knowledgeBaseId: string }) => {
  const headers: Record<string, string> = {}
  if (params.knowledgeBaseId) {
    headers['resource-Id'] = params.knowledgeBaseId
    headers['resource-Type'] = '2'
  }
  return http.post(
    `${kbFileBaseUrl}/v1/document/delete`,
    {
      documentId: params.fileId
    },
    {
      headers
    }
  )
}

const deleteKBFiles = async (params: {
  fileIds: string[]
  knowledgeBaseId: string
}): HttpRes<null> => {
  const headers: Record<string, string> = {}
  if (params.knowledgeBaseId) {
    headers['resource-Id'] = params.knowledgeBaseId
    headers['resource-Type'] = '2'
  }
  return http.post(
    `${kbFileBaseUrl}/v1/document/deletes`,
    {
      documentIds: params.fileIds
    },
    {
      headers
    }
  )
}

// 获取切片任务列表数据
export function getCutTaskListData(params: {
  knowledgeId: string
  userId: string
  knowledgeBaseId: string
}): HttpRes<CutingTask[]> {
  const { knowledgeId, userId, knowledgeBaseId } = params
  const headers: Record<string, string> = { userId }
  if (knowledgeBaseId) {
    headers['resource-Id'] = knowledgeBaseId
    headers['resource-Type'] = '2'
  }
  return http.get(`${lrBaseUrl}/document/getCutTasks`, {
    headers,
    params: {
      knowledgeId
    }
  })
}

// 暂停切片任务
export function pauseCutTask(documentIds: string[]): HttpRes<null> {
  return http.post(`${lrBaseUrl}/document/pauseCutMany`, {
    documentIds
  })
}

// 开始切片任务
export function startCutTask(documentIds: string[]): HttpRes<null> {
  return http.post(`${lrBaseUrl}/document/resumeCutMany`, {
    documentIds
  })
}

// 取消切片任务
export function deleteCutTask(documentIds: string[]): HttpRes<null> {
  return http.post(`${lrBaseUrl}/document/cancelCutMany`, {
    documentIds
  })
}

// 获取切片错误文件列表
export function getCutErrorListData(params: {
  knowledgeId: string
  knowledgeBaseId: string
}): HttpRes<CutErrorData> {
  const { knowledgeId, knowledgeBaseId } = params
  const headers: Record<string, string> = {}
  if (knowledgeBaseId) {
    headers['resource-Id'] = knowledgeBaseId
    headers['resource-Type'] = '2'
  }
  return http.get(`${lrBaseUrl}/knowledge/documentList`, {
    params: {
      knowledgeId,
      getType: 2,
      pageIndex: 1,
      pageSize: 10000
    },
    headers
  })
}

// 重试切片错误任务
export function retryCutTask(documentIds: string[]): HttpRes<null> {
  return http.post(`${lrBaseUrl}/document/reChunk`, {
    documentIds
  })
}

export const knowledgeBaseApi = {
  getKBList,
  getKBFileList,
  deleteKBFile,
  deleteKBFiles
}
