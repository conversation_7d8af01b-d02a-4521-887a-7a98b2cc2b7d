import http from '../../../services/http'
import { Res, KnowledgeData, AuthorizedData, GroupListData, HttpRes } from '@/types'
import { FetchResponse } from '@/services/Fetch'

// 获取知识库列表
export const getKnowledgeList = (params: {
  knowledgeBaseName?: string
  groupId?: string
}): HttpRes<KnowledgeData> => {
  return http.get('/knowledgebase/list_by_group', {
    params
  })
}

// 获取分组列表
export const getGroupList = (): HttpRes<GroupListData> => {
  return http.get('/knowledgebase/group/list')
}

// 删除知识库
export const deleteKnowledge = (params: { knowledgeBaseIds: string[] }): HttpRes => {
  return http.post('/knowledgebase/delete', params)
}

// 分组创建
export const createGroup = (params: { groupName: string }) => {
  return http.post('/knowledgebase/group/add', params)
}

// 分组编辑
export const updateGroup = (params: { groupId: any; groupName: string }) => {
  return http.post('/knowledgebase/group/update', params)
}

// KB创建
export const createKB = (params: {
  knowledgeBaseName: string
  groupId: number | null
  knowledgeBaseDesc: string
}) => {
  return http.post('/knowledgebase/add', params)
}

// KB编辑
export const updateKB = (params: {
  knowledgeBaseName: string
  groupId: number | null
  knowledgeBaseDesc: string
  knowledgeBaseId: string
}) => {
  return http.post('/knowledgebase/update', params)
}

// 删除分组
export const deleteGroup = (params: { groupId: string }): HttpRes => {
  return http.post('/knowledgebase/group/delete', params)
}

// KB授权用户列表
export const fetchKBUserList = (params: {
  knowledgeBaseId?: string
  permission?: number
}): HttpRes<GroupListData> => {
  return http.get('/knowledgebase/authed_user_list', {
    params
  })
}

// KB授权用户更新
export const updateKBAuth = (params: {
  knowledgeBaseId: string
  authedUserList: {
    userId: string
    permission: number
  }[]
}): HttpRes => {
  return http.post('/knowledgebase/authed_user_update', params)
}
