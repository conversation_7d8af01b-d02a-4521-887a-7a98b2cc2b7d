/*
 * @Description:
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\api\userManagement.ts
 */
import http from '../../../services/http'
import { Res, AccountListData, AuthorizedData } from '@/types'
import { FetchResponse } from '@/services/Fetch'

type HttpRes<T = undefined> = Promise<FetchResponse<Res<T>>>
const userUrlPrefix =
  process.env.NODE_ENV === 'development' ? 'https://m1.apifoxmock.com/m1/6558758-0-default' : ''

// 获取账号列表
export const getAccountList = (params: {
  userName?: string
  role?: string
  current?: number
  size?: number
}): HttpRes<AccountListData> => {
  return http.get(`/admin/user/list`, {
    params
  })
}

// 删除账号
export const deleteAccount = (params: { userIds: string[] }): HttpRes => {
  return http.post(`/admin/user/delete`, params)
}

// 获取授权列表
export const getAuthorizedList = (params?: { toUserId: string }): HttpRes<AuthorizedData> => {
  return http.get(`/admin/user/can_auth_resource_list`, {
    params
  })
}

// 更新账号
export const updateAuthorisation = (params: {
  userId: string
  resourceList: AuthorizedData
}): HttpRes<{}> => {
  return http.post('/admin/user/authed_resource_update', params)
}

export const updateAccount = (params: {
  userId: string
  nickName: string
  role: string
  resourceList: AuthorizedData
}): HttpRes<{}> => {
  return http.post('/admin/user/update', params)
}

// 邀请账号
export const inviteAccount = (params: {
  userName: string
  role: string
  resourceList: AuthorizedData
}): HttpRes<{}> => {
  return http.post('/admin/user/invite', params)
}
