<svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1076_110914)">
<g clip-path="url(#paint0_angular_1076_110914_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.0125937 0.00775 0.00775 0.0125937 15.5 15.5)"><foreignObject x="-1535.1" y="-1535.1" width="3070.19" height="3070.19"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(0, 0, 0, 0.5561) 0deg,rgba(0, 0, 0, 0) 153.36deg,rgba(0, 0, 0, 1) 237.572deg,rgba(0, 0, 0, 0.5561) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path fill-rule="evenodd" clip-rule="evenodd" d="M16.9091 1.40909C16.9091 0.630871 16.2782 0 15.5 0C6.93958 0 0 6.93958 0 15.5C0 24.0604 6.93958 31 15.5 31C24.0604 31 31 24.0604 31 15.5C31 14.7218 30.3691 14.0909 29.5909 14.0909C28.8127 14.0909 28.1818 14.7218 28.1818 15.5C28.1818 22.504 22.504 28.1818 15.5 28.1818C8.49602 28.1818 2.81818 22.504 2.81818 15.5C2.81818 8.49602 8.49602 2.81818 15.5 2.81818C16.2782 2.81818 16.9091 2.18731 16.9091 1.40909Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:0.0},&#34;position&#34;:0.42599976062774658},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.65992361307144165}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:0.0},&#34;position&#34;:0.42599976062774658},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.65992361307144165}],&#34;transform&#34;:{&#34;m00&#34;:-25.18750,&#34;m01&#34;:15.499999046325684,&#34;m02&#34;:20.343750,&#34;m10&#34;:15.499999046325684,&#34;m11&#34;:25.18750,&#34;m12&#34;:-4.8437490463256836},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<clipPath id="paint0_angular_1076_110914_clip_path"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.9091 1.40909C16.9091 0.630871 16.2782 0 15.5 0C6.93958 0 0 6.93958 0 15.5C0 24.0604 6.93958 31 15.5 31C24.0604 31 31 24.0604 31 15.5C31 14.7218 30.3691 14.0909 29.5909 14.0909C28.8127 14.0909 28.1818 14.7218 28.1818 15.5C28.1818 22.504 22.504 28.1818 15.5 28.1818C8.49602 28.1818 2.81818 22.504 2.81818 15.5C2.81818 8.49602 8.49602 2.81818 15.5 2.81818C16.2782 2.81818 16.9091 2.18731 16.9091 1.40909Z"/></clipPath><clipPath id="clip0_1076_110914">
<rect width="31" height="31" fill="white"/>
</clipPath>
</defs>
</svg>
