<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_iiii_3597_51861)">
<rect width="56" height="56" rx="28" fill="white"/>
<g filter="url(#filter1_i_3597_51861)">
<path d="M18.1964 33.6501C15.6469 32.5887 12.5062 30.7091 11.891 27.8405C11.3202 24.8662 14.0864 22.7937 16.5394 21.7221C17.6628 21.2362 18.8272 20.8513 20.0188 20.5719C21.9932 20.1018 24.0742 20.8757 25.209 22.5582C25.786 23.4093 26.2124 24.4905 26.6045 25.9152C26.7505 26.4458 27.5639 26.4448 27.7102 25.9142C28.7843 22.0193 30.1146 20.6899 34.0074 19.6162C34.538 19.4698 34.5379 18.6557 34.0073 18.5094C30.111 17.4353 28.7827 16.1042 27.7089 12.2066C27.5627 11.6762 26.7502 11.6747 26.6039 12.205C25.9257 14.663 25.1487 16.1005 23.6894 17.0967C23.1653 17.4489 22.5728 17.6864 21.9505 17.7938C19.7723 18.1863 17.6366 18.8066 15.629 19.7853C13.147 21.0011 10.5327 23.0343 10.0694 25.9771C9.81344 27.4591 10.287 29.0385 11.1359 30.2364C12.728 32.4838 15.2963 33.7986 17.8261 34.6527C18.4839 34.8404 18.8184 33.9291 18.1964 33.6501Z" fill="url(#paint0_linear_3597_51861)"/>
</g>
<g filter="url(#filter2_i_3597_51861)">
<path d="M44.8668 26.3547C43.2746 24.1065 40.7064 22.7917 38.1766 21.9384C37.5188 21.7465 37.1843 22.6577 37.8055 22.9367C40.3558 23.999 43.4965 25.8778 44.1116 28.7472C44.6825 31.7206 41.9163 33.794 39.4633 34.8648C38.4505 35.3033 37.4039 35.6591 36.3337 35.9287C35.1535 36.2289 33.906 36.1126 32.8015 35.5997C31.6971 35.0867 30.8035 34.2085 30.2715 33.1131C29.9423 32.4318 29.6628 31.6298 29.3989 30.6731C29.2526 30.1425 28.4385 30.1438 28.2922 30.6744C27.2181 34.5686 25.8876 35.8974 21.9946 36.9709C21.464 37.1172 21.4641 37.9312 21.9946 38.0775C25.8897 39.1515 27.2187 40.4813 28.2923 44.3749C28.4386 44.9055 29.2526 44.9063 29.3989 44.3757C30.096 41.848 30.9013 40.4024 32.4447 39.4039C32.9187 39.1029 33.4481 38.8999 34.0018 38.8067C36.1971 38.4142 38.3532 37.7922 40.3737 36.8059C42.8557 35.5909 45.47 33.5568 45.9333 30.6174C46.185 29.1345 45.7157 27.5518 44.8668 26.3547Z" fill="url(#paint1_linear_3597_51861)"/>
</g>
</g>
<defs>
<filter id="filter0_iiii_3597_51861" x="-4" y="-4" width="64" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.418945 0 0 0 0 0.496419 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3597_51861"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.679842 0 0 0 0 0.531475 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3597_51861" result="effect2_innerShadow_3597_51861"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.936026 0 0 0 0 0.786753 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_3597_51861" result="effect3_innerShadow_3597_51861"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.857891 0 0 0 0 1 0 0 0 0 0.914735 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_3597_51861" result="effect4_innerShadow_3597_51861"/>
</filter>
<filter id="filter1_i_3597_51861" x="10" y="11.8076" width="24.4062" height="24.8701" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.95628 0 0 0 0 0.95628 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3597_51861"/>
</filter>
<filter id="filter2_i_3597_51861" x="21.5977" y="21.9121" width="24.4023" height="24.8613" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3597_51861"/>
</filter>
<linearGradient id="paint0_linear_3597_51861" x1="13" y1="32" x2="26.539" y2="14.9546" gradientUnits="userSpaceOnUse">
<stop stop-color="#6FA6FF"/>
<stop offset="0.505" stop-color="#6256FF"/>
<stop offset="1" stop-color="#8D64DD"/>
</linearGradient>
<linearGradient id="paint1_linear_3597_51861" x1="24.448" y1="38.1355" x2="43.5" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#6153FF"/>
<stop offset="0.654024" stop-color="#FF5956"/>
<stop offset="1" stop-color="#7D7DF6"/>
</linearGradient>
</defs>
</svg>
