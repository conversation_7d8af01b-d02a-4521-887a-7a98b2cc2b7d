// import Store from 'electron-store'
import { reactive } from 'vue'

const domain = 'http://10.103.62.109'

export const GlobalConfig = reactive({
  isEdge: false,
  localPKBPath: '',
  authServer: `${domain}:9080/auth`,
  edgeServer: `${domain}:9070/ainow-user-acl-api`,
  kbFileServer: `${domain}:9070/lr-kb-service-api`,
  modelServer: `${domain}:8099/api/v1/flow/c7667aa7-984a-4b49-a3b6-295e82dd705a`,
  historyServer: `${domain}:8102/api/v1`,
  chunkServer: `${domain}:8099/api/v1`,
  // authServer: 'http://10.183.158.11:9080/auth',
  // edgeServer: 'http://10.183.158.11:9070/ainow-user-acl-api',
  // kbFileServer: 'http://10.183.158.11:9070/lr-kb-service-api',
  // modelServer: 'http://10.176.238.83:8099/api/v1/flow/c7667aa7-984a-4b49-a3b6-295e82dd705a',
  // historyServer: 'http://10.176.238.83:8102/api/v1',
  // chunkServer: 'http://10.176.238.83:8099/api/v1',
  tokens: {
    access_token: '',
    refresh_token: '',
    lenovo_id: '',
    lenovo_username: ''
  },
  userInfo: {
    role: ''
  }
})
export class LocalStore {
  static getVal(key: string) {
    return localStorage.getItem(key)
  }
  static setVal(key: string, val: string) {
    return localStorage.setItem(key, val)
  }
}

// export class LocalStore {
//   constructor() {
//     this.store = new Store()
//   }
//   store: Store
//   getVal(key: string) {
//     return this.store.get(key)
//   }
//   setVal(key: string, val: string) {
//     return this.store.set(key, val)
//   }
// }
// export const store = new LocalStore()
