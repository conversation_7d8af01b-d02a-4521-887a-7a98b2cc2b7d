<template>
  <div class="ainow-layout_history-panel">
    <div v-if="route.name === 'chat'" class="history-panel-header">
      <div class="collapsed-icon-container">
        <SvgIcon
          name="historyCollapse"
          size="26"
          :class="{ 'collapse-icon-rotated': collapsed }"
          @click="emit('toggleHistory')"
        />
      </div>
      <AddBtn :collapsed="false" @click="handleNewChat"></AddBtn>
    </div>
    <span class="history-top-title">History</span>
    <div class="history-top-divider"></div>
    <div class="no-conversation" v-if="!isLoading && Object.keys(groupedHistory).length === 0">
      <SvgIcon name="history-empty" size="16" />
      <span class="content">No conversation yet.</span>
    </div>
    <div class="history-list">
      <div
        v-for="(group, groupName, index) in groupedHistory"
        :key="groupName"
        class="history-group"
      >
        <div class="group-title">{{ groupName }}</div>
        <ul>
          <li
            v-for="item in group"
            :key="item.id"
            class="history-item"
            :class="{ selected: item.id === props.selectedItemId }"
            @click="onItemClick(item.id)"
            @mouseenter="hoveredItemId = item.id"
            @mouseleave="hoveredItemId = null"
          >
            <ATooltip placement="top" :mouseEnterDelay="0.5" color="#525A69">
              <template #title v-if="isTruncated[item.id]">
                <div style="max-width: 400px; max-height: 400px; overflow: auto">
                  {{ item.title }}
                </div>
              </template>
              <span class="item-title" :ref="(el) => setItemRef(el, item.id)">{{
                item.title
              }}</span>
            </ATooltip>
            <a-popover
              v-model:open="popoverStates[item.id]"
              trigger="click"
              placement="bottomLeft"
              :arrow="false"
              overlayClassName="history-item-popover"
              :overlayStyle="{ marginTop: '5px' }"
            >
              <template #content>
                <div class="history-item-menu">
                  <div class="menu-item" @click.stop="renameItem(item)">Rename</div>
                  <div class="history-item-menu-divider"></div>
                  <div class="menu-item" @click.stop="deleteItem(item)">Delete</div>
                </div>
              </template>
              <div
                class="item-actions"
                :class="{ 'is-active': popoverStates[item.id] }"
                @click.stop
                v-show="hoveredItemId === item.id"
              >
                <MoreIcon />
              </div>
            </a-popover>
          </li>
        </ul>
        <div
          class="history-group-divider"
          v-if="index < Object.keys(groupedHistory).length - 1"
        ></div>
      </div>

      <AModal
        v-model:open="isRenameModalVisible"
        :title="null"
        :closable="false"
        centered
        class="rename-modal"
        :footer="null"
        @cancel="handleCancelRename"
      >
        <div class="rename-modal-title">
          <span>Rename Title</span>
        </div>
        <AInput v-model:value="renameInput" :maxlength="56" @keyup.enter="handleConfirmRename" />
        <div class="rename-modal-footer">
          <ABtn type="primary" @click="handleConfirmRename" :disabled="!renameInput.trim()"
            >Confirm</ABtn
          >
          <ABtn @click="handleCancelRename" :style="{ color: '#000000' }">Cancel</ABtn>
        </div>
      </AModal>
      <AModal
        v-model:open="isDeleteModalVisible"
        :title="null"
        centered
        :closable="false"
        :maskClosable="true"
        class="delete-modal"
        :footer="null"
        @cancel="handleCancelDelete"
      >
        <div class="delete-modal-title">
          <SvgIcon name="historyWarning" size="20" />
          <span>Delete?</span>
        </div>
        <p class="delete-modal-content">Are you sure you want to delete the conversation?</p>
        <div class="delete-modal-footer">
          <ABtn type="primary" danger @click="handleConfirmDelete">Delete</ABtn>
          <ABtn @click="handleCancelDelete" :style="{ color: '#000000' }">Cancel</ABtn>
        </div>
      </AModal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted, reactive } from 'vue'
import { historyBaseApi } from '@/renderer/src/api/historyBase'
import { APopover, ABtn, AModal, AInput, AMsg, ATooltip } from '@libs/a-comps'
import SvgIcon from '../../components/SvgIcon/SvgIcon.vue'
import MoreIcon from '../../components/MoreIcon/MoreIcon.vue'
import mySDK from '@renderer/views/Chat/sdkService'
import AddBtn from '@renderer/views/Main/comps/AddBtn.vue'
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import { emitter } from '../../../../utils/EventBus'
import { text } from 'stream/consumers'

interface HistoryItem {
  id: string
  title: string
  date: string
}

const props = defineProps<{
  collapsed: boolean
  selectedItemId: string | null
}>()

const emit = defineEmits(['newChat', 'toggleHistory', 'item-selected'])

const router = useRouter()
const route = useRoute()
let chatCompletedListenerId: string | undefined

const handleNewChat = () => {
  emit('newChat')
  emitter.emit('ref-click-2', false)
}

onMounted(() => {
  if (mySDK.userId) {
    fetchHistory()
  }
  window.addEventListener('userReady', fetchHistory)
  chatCompletedListenerId = emitter.on('chat-completed', handleChatCompleted)

  setupScrollDetection()
})

onUnmounted(() => {
  window.removeEventListener('userReady', fetchHistory)
  if (chatCompletedListenerId) {
    emitter.removeListen('chat-completed', chatCompletedListenerId)
  }

  const historyList = document.querySelector('.history-list') as HTMLElement
  if (historyList) {
    historyList.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})

const history = ref<HistoryItem[]>([])
const isLoading = ref(false)

const isRenameModalVisible = ref(false)
const editingItem = ref<HistoryItem | null>(null)
const renameInput = ref('')
const isDeleteModalVisible = ref(false)
const deletingItemId = ref<string | null>(null)
const popoverStates = ref<Record<string, boolean>>({})
const isTruncated = reactive<Record<string, boolean>>({})
const hoveredItemId = ref<string | null>(null)
const isScrolling = ref(false)
let scrollTimeout: ReturnType<typeof setTimeout> | null = null

const setupScrollDetection = () => {
  const historyList = document.querySelector('.history-list') as HTMLElement
  if (historyList) {
    historyList.addEventListener('scroll', handleScroll)
  }
}

const handleScroll = () => {
  isScrolling.value = true
  const historyList = document.querySelector('.history-list') as HTMLElement
  if (historyList) {
    historyList.classList.add('scrolling')
  }

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
    if (historyList) {
      historyList.classList.remove('scrolling')
    }
  }, 800)
}

const setItemRef = (el: any, id: string) => {
  if (el) {
    isTruncated[id] = el.scrollWidth > el.clientWidth
    emitter.emit('ref-click-2', false)
    emitter.emit('ref-click-expand', false)
  }
}

const groupedHistory = computed(() => {
  const groups = new Map<string, HistoryItem[]>()

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const sevenDaysAgo = new Date(today)
  sevenDaysAgo.setDate(today.getDate() - 7)

  const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)

  const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1)

  const oneYearAgo = new Date(today)
  oneYearAgo.setFullYear(today.getFullYear() - 1)

  const monthNames = [
    '一月',
    '二月',
    '三月',
    '四月',
    '五月',
    '六月',
    '七月',
    '八月',
    '九月',
    '十月',
    '十一月',
    '十二月'
  ]

  const sortedHistory = [...history.value].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  )

  sortedHistory.forEach((item) => {
    const itemDate = new Date(item.date)
    itemDate.setHours(0, 0, 0, 0)

    let groupName = ''

    if (itemDate.getTime() >= today.getTime()) {
      groupName = 'Today'
    } else if (itemDate >= sevenDaysAgo) {
      groupName = 'Last 7 days'
    } else if (itemDate >= thisMonthStart) {
      groupName = monthNames[itemDate.getMonth()]
    } else if (itemDate >= lastMonthStart) {
      groupName = 'Last month'
    } else if (itemDate >= oneYearAgo) {
      const yearString =
        itemDate.getFullYear() !== today.getFullYear() ? ` ${itemDate.getFullYear()}` : ''
      groupName = `${monthNames[itemDate.getMonth()]}${yearString}`
    } else {
      groupName = `${itemDate.getFullYear()} year`
    }

    if (!groups.has(groupName)) {
      groups.set(groupName, [])
    }
    groups.get(groupName)!.push(item)
  })

  return Object.fromEntries(groups.entries())
})

const onItemClick = async (itemId: string) => {
  emit('item-selected', itemId)
  try {
    const res = await historyBaseApi.getHistoryConversation(mySDK.userId, itemId)
    const historyData = res?.data?.data
    if (historyData) {
      emitter.emit('history-selected', { historyData, sessionId: itemId })
    }
  } catch (e) {}
}

const renameItem = (item: HistoryItem, event?: Event) => {
  if (event) {
    event.stopPropagation()
  }
  popoverStates.value[item.id] = false
  isRenameModalVisible.value = true
  editingItem.value = item
  renameInput.value = item.title
}

const handleConfirmRename = () => {
  if (renameInput.value.length > 50) {
    AMsg.error('标题长度不能超过50个字符')
    return
  }
  const trimmedInput = renameInput.value.trim()
  if (!trimmedInput) {
    return
  }

  const forbiddenChars = ['!', '！', '；', '{', ']']
  const usedForbiddenChars = forbiddenChars.filter((char) => trimmedInput.includes(char))

  if (usedForbiddenChars.length > 0) {
    AMsg.error(`标题不能包含以下字符：${usedForbiddenChars.join(' ')}`)
    return
  }

  if (editingItem.value) {
    const item = history.value.find((i) => i.id === editingItem.value!.id)
    if (item) {
      item.title = trimmedInput
      renameHistory(item.id, item.title)
    }
  }
  handleCancelRename()
}

const handleCancelRename = () => {
  isRenameModalVisible.value = false
  editingItem.value = null
  renameInput.value = ''
}

const deleteItem = (item: HistoryItem, event?: Event) => {
  if (event) {
    event.stopPropagation()
  }
  popoverStates.value[item.id] = false
  isDeleteModalVisible.value = true
  deletingItemId.value = item.id
}

const handleConfirmDelete = async () => {
  if (deletingItemId.value !== null) {
    await deleteHistory(deletingItemId.value)
    await fetchHistory()
    // 删除历史记录时，触发新建对话
    handleNewChat()
  }
  handleCancelDelete()
}

const handleCancelDelete = () => {
  isDeleteModalVisible.value = false
  deletingItemId.value = null
}

const handleChatCompleted = async (payload?: { sessionId?: string }) => {
  await fetchHistory()
  // 如果有sessionId，自动选中对应的记录
  if (payload?.sessionId) {
    emit('item-selected', payload.sessionId)
  }
}

// 获取历史记录列表
async function fetchHistory() {
  if (!mySDK.userId) return
  isLoading.value = true
  try {
    const res: any = await historyBaseApi.getHistoryList(mySDK.userId)
    const resData = res?.data
    if (resData?.code === 200 && Array.isArray(resData?.data)) {
      history.value = resData.data.map((item: any) => ({
        id: item.id,
        title: item.name || '',
        date: item.updatedAt
      }))
    } else {
      history.value = []
    }
  } catch (e) {
    history.value = []
  } finally {
    isLoading.value = false
  }
}

// 重命名历史记录
async function renameHistory(id: string, name: string) {
  try {
    const res = await historyBaseApi.renameCurHistory(mySDK.userId, id, name)
  } catch (e) {}
}

// 删除当前历史记录
async function deleteHistory(id: string) {
  if (!id) return
  try {
    await historyBaseApi.deleteCurHistory(mySDK.userId, id)
  } catch (e) {}
}
</script>

<style lang="less" scoped>
.ainow-layout_history-panel {
  width: 200px;
  height: 100vh;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  // flex-shrink: 0;
  transition: all 0.1s;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .history-panel-header {
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;

    :deep(.svg-icon) {
      cursor: pointer;
    }
    .collapsed-icon-container {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      cursor: pointer;
      :hover {
        background-color: white;
        border-radius: 3px;
      }
    }
  }

  :deep(.add-btn-container) {
    padding: 0 28px;
  }

  .history-top-title {
    font-size: 14px;
    margin-left: 12px;
    color: var(--text-color3);
  }

  .history-top-divider {
    height: 1px;
    background-color: rgba(0, 0, 0, 0.06);
    margin: 16px 8px 8px 8px;
  }
}
.history-list {
  padding: 8px;
  color: var(--text-color1);
  overflow-y: auto;
  flex-grow: 1;

  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }

  /* 滚动时显示滚动条 */
  &.scrolling::-webkit-scrollbar {
    width: 6px;
    background: transparent;
  }

  &.scrolling::-webkit-scrollbar-track {
    background: transparent;
  }

  &.scrolling::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    transition: background 0.1s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }
}

.history-group {
  margin-bottom: 16px;
}

.history-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.group-title {
  font-size: 12px;
  color: var(--text-color3);
  margin-bottom: 8px;
  padding: 0 8px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 8px;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background-color: var(--hover-color);
    color: #6441ab;
    font-weight: 500;
  }

  .item-actions-wrapper {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }

  .item-actions {
    flex-shrink: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    &.is-active {
      background-color: rgba(0, 0, 0, 0.15);
    }
  }
}

.item-title {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 4px;
  font-size: 14px;
}

.history-item-popover .history-item-menu {
  display: flex;
  flex-direction: column;
  min-width: 90px;
}

.history-item-popover .history-item-menu .menu-item:hover {
  background-color: #f0f0f0;
}

.history-item-menu {
  display: flex;
  flex-direction: column;
  padding: 0;

  .menu-item {
    width: 100%;
    text-align: left;
    padding-left: 2px;
    padding-top: 3px;
    padding-bottom: 3px;
    height: auto;
    line-height: 1.5;
    border-radius: 4px;
    cursor: pointer;
    color: #3b3b3b;
  }
}

.history-item-menu-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.06);
  margin: 4px 0;
}

:deep(.rename-modal .ant-input) {
  background-color: #f2f3f5;
  border-color: #f2f3f5;
  &:hover {
    background-color: #e8e9ec;
    border-color: #e8e9ec;
  }
  &:focus,
  &.ant-input-focused {
    background-color: #ffffff;
    border-color: #6441ab;
    box-shadow: none;
  }
}

.rename-modal-footer {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 24px;
}

:deep(.rename-modal .ant-modal-title) {
  text-align: center;
}

.rename-modal-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
}

.delete-modal-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.delete-modal-content {
  text-align: center;
  margin-top: 8px;
  color: var(--text-color2);
}

.delete-modal-footer {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 24px;
}

.history-group-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.06);
  margin-top: 16px;
}

.no-conversation {
  background-color: #ffffff80;
  height: 41px;
  display: flex;
  align-items: center;
  gap: 5px;
  text-align: center;
  color: var(--text-color3);
  font-size: 14px;
  margin: 0px 10px;
  padding-left: 10px;
  border-radius: 2px;
  .content {
    color: #696969;
    font-weight: 400;
  }
}

:deep(.ant-tooltip-inner) {
  background-color: #525a69;
}

:deep(.ant-tooltip-arrow::before) {
  clip-path: unset;
}

:deep(.ant-tooltip) {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
