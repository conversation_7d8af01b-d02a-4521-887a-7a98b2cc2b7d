<template>
  <img class="loading-img" :src="img" ref="loadImg" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import img from './loading.png'
import { onMounted } from 'vue'
</script>

<style lang="less" scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-img {
  animation: spin 2s linear infinite;
  /* 持续2秒，线性，无限循环 */
}
</style>
