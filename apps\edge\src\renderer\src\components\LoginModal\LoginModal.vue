<template>
  <div class="login-modal" v-if="open" ref="modal">
    <!-- <div class="login-modal_title global-title3">Lenovo AI Agent</div> -->
    <div class="login-modal_content">
      <div class="login-modal_content_center">
        <div v-if="initLoading" class="login-modal_content_center_loading">
          <Loading />
          <div class="global-title3">Launching resources...</div>
        </div>
        <LoginPanel
          v-else
          :status="curStatus"
          @on-login="handleLogin"
          @on-cancel="handleCancel"
        ></LoginPanel>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { ABtn } from '@libs/a-comps'
import { ref } from 'vue'
import { loginHooks } from '../../hooks/Auth'
import LoginPanel from './comps/LoginPanel.vue'
import { LOGIN_STATUS } from '@/types'
import { GlobalConfig } from '../../common'
import { electronHooks } from '../../electron'
import { AMsg, message } from '@libs/a-comps'
import { computed, watch } from 'vue'
import { onMounted } from 'vue'
import { dragHooks } from '../../hooks/Drag'
import Loading from '../Loading'
const modal = ref()
const initLoading = ref(true)
const emit = defineEmits(['initSuccess'])
const open = computed(() => {
  emit('initSuccess', curStatus.value == LOGIN_STATUS.LOGIN)
  return curStatus.value !== LOGIN_STATUS.LOGIN
})
const curStatus = ref(LOGIN_STATUS.NO_LOGIN)
const { setDrag } = dragHooks()
const { toLogin, init, loginWithCodeCallback } = loginHooks(curStatus, (res) => {
  curStatus.value = res
  message.destroy()
})
watch(
  () => GlobalConfig.tokens.access_token,
  (res) => {
    console.info('watch', res)
    if (!res) {
      curStatus.value = LOGIN_STATUS.NO_LOGIN
    }
  },
  {
    deep: true
  }
)
const electron = electronHooks()

const handleCancel = () => {
  curStatus.value = LOGIN_STATUS.NO_LOGIN
  message.destroy()
}

const handleLogin = () => {
  curStatus.value = LOGIN_STATUS.LOGIN_LOADING
  AMsg.destroy()
  setTimeout(
    () => {
      if (curStatus.value !== LOGIN_STATUS.LOGIN_LOADING) {
        return
      }

      curStatus.value = LOGIN_STATUS.NO_LOGIN
      AMsg.error({
        content: () => 'Sorry, time’s up! Please try again.',
        class: 'login-a-msg',
        duration: 0,
        style: {
          marginTop: '65vh'
        }
      })
    },
    3 * 60 * 1000
  )
  toLogin()
}

electron
  ?.getConfig()
  .then((res) => {
    if (res.edgeServer) {
      GlobalConfig.isEdge = true
      GlobalConfig.edgeServer = (res.edgeServer as string) || ''
      Object.assign(GlobalConfig, res)

      init()
        .then((res) => {
          // open.value = res !== LOGIN_STATUS.LOGIN
          curStatus.value = res
        })
        .finally(() => {
          initLoading.value = false
        })
    } else {
      initLoading.value = false
    }
  })
  .catch((err) => {
    console.error(err, '0000')
    initLoading.value = false
  })
if (!electron) {
  // web端
  init()
    .then((res) => {
      // open.value = res !== LOGIN_STATUS.LOGIN
      curStatus.value = res
    })
    .finally(() => {
      initLoading.value = false
    })
}
onMounted(() => {
  if (modal.value) {
    setDrag(modal.value)
  }
  if (!(window as any).electron) {
    // 如果是web端
    // 获取url上的登录code信息
    const url = new URL(window.location.href)
    const code = url.searchParams.get('code') as string
    loginWithCodeCallback(code)
  }
})
</script>

<style lang="less">
.login-modal {
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: absolute;
  top: 0;

  &_title {
    position: absolute;
    top: 6px;
    left: 16px;
  }

  &_content {
    display: flex;
    height: calc(100% - 32px);
    justify-content: center;
    align-items: center;

    &_center {
      text-align: center;

      &_center {
      }
    }
  }
}

.login-a-msg {
  .a-msg-notice {
    &-content {
      &:has(.a-msg-error) {
        background-color: var(--color-error-bg);
        color: var(--color-error);
        box-shadow: none;
      }
    }
  }

  // &-error{
  //     background-color: var(--color-error-bg);

  // }
}
</style>
