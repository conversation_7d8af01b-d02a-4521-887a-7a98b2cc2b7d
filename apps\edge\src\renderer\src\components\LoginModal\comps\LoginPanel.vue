<template>
  <div class="login-panels">
    <div class="login-panels_panel" v-if="status === LOGIN_STATUS.NO_LOGIN">
      <SvgIcon name="Out" size="31"></SvgIcon>
      <div class="login-panels_panel_title global-title3">Login with your Lenovo ID</div>
      <div class="login-panels_panel_content global-text2">
        Go to the website page to log in, then you can use Lenovo AI Agent !
      </div>

      <ABtn @click="handleLogin" type="primary">Log in</ABtn>
    </div>
    <div class="login-panels_panel" v-if="status === LOGIN_STATUS.LOGIN_LOADING">
      <Loading></Loading>
      <div class="login-panels_panel_title global-title3">Waiting to log in by browser...</div>
      <div class="login-panels_panel_content global-text2">
        Please switch to your browser and log in. Return here when you’re done.
      </div>

      <ABtn @click="handleCancel" type="primary">Cancel</ABtn>
    </div>
    <div class="login-panels_panel" v-if="status === LOGIN_STATUS.NO_AUTH">
      <SvgIcon name="Lock" size="31"></SvgIcon>

      <div class="login-panels_panel_title global-title3">No Authority</div>
      <div class="login-panels_panel_content global-text2">
        {{ GlobalConfig.tokens.lenovo_username || 'you' }} has no authority to Lenovo AI Agent.
        Please contact the administrator.
      </div>

      <ABtn @click="handleLogin" type="primary">Switch account</ABtn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LOGIN_STATUS } from '@/types'
import { ABtn } from '@libs/a-comps'
import SvgIcon from '../../SvgIcon'
import Loading from '../../Loading/Loading.vue'
import { GlobalConfig } from '@/renderer/src/common'
defineProps<{
  status: LOGIN_STATUS
}>()
const emit = defineEmits<{
  (e: 'onLogin'): void
  (e: 'onCancel'): void
}>()
const handleLogin = () => {
  emit('onLogin')
}
const handleCancel = () => {
  emit('onCancel')
}
</script>

<style lang="less" scoped>
.login-panels_panel {
  > *:first-child {
    margin-bottom: 38px;
  }

  &_title {
    margin-bottom: 8px;
  }

  &_content {
    margin-bottom: 28px;
  }
}
</style>
