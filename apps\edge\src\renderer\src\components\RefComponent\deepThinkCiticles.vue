<template>
  <div class="ref-panel">
    <div class="ref-panel_background">
      <div class="ref-panel_background-container">
        <div class="file-icon">
          <SvgIcon :name="getFileIcon(currentCiticle?.documentName)" />
          <div class="documentName">
            {{ currentCiticle?.documentName }}
          </div>
        </div>
        <div v-if="route.name === 'chat'" class="ref-panel-header" @click="refCollapse">
          <SvgIcon name="Close" size="10" />
        </div>
      </div>
      <div v-if="route.name === 'chat'" class="ref-list">
        <iframe :src="previewUrl" class="preview-iframe" frameborder="0"> </iframe>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted, reactive } from 'vue'
import SvgIcon from '../../components/SvgIcon/SvgIcon.vue'
import { useRoute } from 'vue-router'
import { emitter } from '../../../../utils/EventBus'
import { DocumentListType, ReferencesType } from '@libs/a-comps/ChatBaseComponent/types'
import { fileExtensionIconNameMap } from '@renderer/hooks/fileType'
import previewModle from '../../views/Chat/components/previewModle.vue'
import { GlobalConfig } from '@/renderer/src/common'

const previewUrl = ref('')
const showPreviewModal = ref(false)
const previewFile = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: '',
  knowledgeBaseId: ''
})

const props = defineProps<{
  collapsed: boolean
}>()

const handleFilePreview = (file: any) => {
  previewFile.value = {
    documentId: file.documentId || file.id,
    documentName: file.documentName,
    knowledgeId: file.knowledgeId || '',
    knowledgeBaseId: file.knowledgeBaseId || ''
  }
  showPreviewModal.value = true
}

const route = useRoute()
let currentCiticle = ref<ReferencesType | null>()
const refCollapse = () => {
  emitter.emit('ref-click-citicleClose', false)
}

const getFileIcon = (fileName: string) => {
  const extension = fileName?.split('.').pop()?.toLowerCase() || ''
  return fileExtensionIconNameMap[extension as keyof typeof fileExtensionIconNameMap] || 'general'
}

onMounted(() => {
  emitter.on('ref-click-citicles', async (data: ReferencesType) => {
    if (data) {
      currentCiticle.value = data
      console.log('data.documentId=', data.documentId)
      console.log('data.documentName=', data.documentName)
      console.log('data.siteName=', data.siteName)
      const fileUrl = `${GlobalConfig.kbFileServer}/api/v1/document/viewDocument?documentId=${data.documentId}`

      const config: { headers: Record<string, string> } = {
        headers: {
          token: GlobalConfig.tokens.access_token
        }
      }
      const response = await fetch(fileUrl, config)

      if (!response.ok) throw new Error('Failed to fetch file')

      const blob = await response.blob()
      // 释放之前的 URL
      if (previewUrl.value) {
        URL.revokeObjectURL(previewUrl.value)
        previewUrl.value = ''
      }
      previewUrl.value = `${URL.createObjectURL(blob)}#toolbar=0&navpanes=0&pagemode=none`
    }
  })
})

watch(
  () => currentCiticle.value,
  () => {
    currentCiticle.value
  },
  { deep: 1, immediate: true }
)

const currentDate = computed(() => {
  const currentDt = new Date()
  const year = currentDt.getFullYear()
  const month = String(currentDt.getMonth() + 1).padStart(2, '0')
  const day = String(currentDt.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
})
</script>

<style lang="less" scoped>
.ref-panel {
  width: 592px;
  height: 100vh;
  background: #f4f9ff;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .ref-panel_background {
    margin: 43px 0 0 0;
    background-color: white;
    padding: 10px 10px 0 10px;
    align-items: center;
    font-size: 14;
    font-weight: bold;
    .ref-panel_background-container {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e5e7ec;
      .document {
        margin-left: 0px;
        margin-top: 2px;
        display: flex; /* 启用flex布局 */
        font-size: 16px;
        display: inline;
      }
      .documentName {
        margin-left: 8px;
        font-size: 16px;
        display: inline;
      }
    }
  }
  .ref-panel-header {
    padding: 8px;
    cursor: pointer;
    :deep(.svg-icon) {
      margin-bottom: 8px;
      cursor: pointer;
    }
  }

  :deep(.add-btn-container) {
    padding-left: 8px;
    padding-right: 8px;
  }
}
.ref-list {
  width: 592px;
  height: auto;
  padding: 0px 10px 0 10px;
  background-color: white;
  color: var(--text-color1);
  overflow-y: auto;
  font-size: 12px;
  padding: 10px;
  flex-grow: 1;
  position: relative;
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;

  .preview-iframe {
    width: 592px;
    height: 70vh;
    border: none;
  }
}

.ref-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background-color: var(--hover-color);
    color: #6441ab;
    font-weight: 500;
  }
}
</style>
