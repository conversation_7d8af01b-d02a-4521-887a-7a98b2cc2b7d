<template>
  <div class="ref-panel">
    <div class="ref-panel_background">
      <div class="ref-panel_background-container">
        <div>References ({{ referenceListRef?.length }})</div>
        <div v-if="route.name === 'chat'" class="ref-panel-header" @click="refCollapse">
          <SvgIcon name="Close" size="10" />
        </div>
      </div>
    </div>
    <div class="ref-list">
      <div v-for="(item, url) in referenceListRef">
        <div class="refItem basic">
          <div class="ref-list-siteName">
            <div class="file-icon" v-if="item.documentSource !== 'network'">
              <SvgIcon :name="getFileIcon(item?.documentName)" />
            </div>
            <a
              :href="item?.url"
              class="web-refTitle"
              v-if="item.documentSource === 'network'"
              target="_blank"
              >{{ item.siteName }}</a
            >
            <div class="ref-pkbfileName" v-else @click="handleFilePreview(item)">
              {{ item?.documentName }}
            </div>
          </div>
          <div class="container" v-if="item.documentSource === 'network'">
            <div class="left-items">
              <SvgIcon name="weIcon" size="24" />
              <div class="ref-type">Web</div>
            </div>
            <div class="right-item">
              {{ currentDate }}
            </div>
          </div>
          <div class="container" v-else-if="item.documentSource === 'public'">
            <div class="left-items">
              <SvgIcon name="deepThinkRefPKB" size="24" />
              <div class="kbdiv" @click="handleFilePreview(item)">
                {{ keyValueMap[item.knowledgeId] }}
              </div>
            </div>
            <div class="right-item">
              {{ item.documentCreateTime }}
            </div>
          </div>
          <div class="container" v-else-if="item.documentSource === 'private'">
            <div class="left-items">
              <SvgIcon name="privatePKB" size="24" />
              <div>{{ item.documentName }}</div>
            </div>
            <div class="right-item">
              {{ currentDate }}
            </div>
          </div>
          <div v-else-if="item.documentSource === 'session'">
            <div class="left-items">
              <SvgIcon name="deepThinkRefSession" size="24" />
              <div>{{ item.documentName }}</div>
            </div>
            <div class="right-item">
              {{ currentDate }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <previewModle v-model:modelValue="showPreviewModal" :docObj="previewFile" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted, reactive } from 'vue'
import SvgIcon from '../../components/SvgIcon/SvgIcon.vue'
import { useRoute } from 'vue-router'
import { emitter } from '../../../../utils/EventBus'
import {
  DocumentListType,
  ReferencesType,
  RefKBNameType
} from '@libs/a-comps/ChatBaseComponent/types'
import { fileExtensionIconNameMap } from '@renderer/hooks/fileType'
import previewModle from '../../views/Chat/components/previewModle.vue'
import { getKBName } from '@/renderer/src/api/chatBase'

const showPreviewModal = ref(false)
const allRefList = ref([])
const previewFile = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: '',
  knowledgeBaseId: ''
})
const isScrolling = ref(false)
let scrollTimeout: ReturnType<typeof setTimeout> | null = null

const props = defineProps<{
  collapsed: boolean
}>()

const handleFilePreview = (file: any) => {
  previewFile.value = {
    documentId: file.documentId || file.id,
    documentName: file.documentName,
    knowledgeId: file.knowledgeId || '',
    knowledgeBaseId: file.knowledgeBaseId || ''
  }
  console.log('file=', file)
  showPreviewModal.value = true
}

const route = useRoute()
let referenceListRef = ref<ReferencesType[] | null>()
let refKBNameListRef = ref<RefKBNameType[]>([])
const refCollapse = () => {
  emitter.emit('ref-click-2', false)
}

const getFileIcon = (fileName: string) => {
  const extension = fileName?.split('.').pop()?.toLowerCase() || ''
  return fileExtensionIconNameMap[extension as keyof typeof fileExtensionIconNameMap] || 'general'
}

const keyValueMap = computed(() => {
  return refKBNameListRef.value.reduce((map, item) => {
    map[item.kbID] = item.KBName
    return map
  }, {})
})

onMounted(() => {
  emitter.on('ref-click-1', (data: ReferencesType[]) => {
    if (data) {
      referenceListRef.value = data
      refKBNameListRef.value.splice(0)
      // 为pkb 文件修改格式时间，去除时分秒，保持与web 一致。
      referenceListRef?.value.forEach((item) => {
        if (item.documentCreateTime) {
          const date = new Date(item.documentCreateTime)
          item.documentCreateTime =
            date.getFullYear() +
            '-' +
            String(date.getMonth() + 1).padStart(2, '0') +
            '-' +
            String(date.getDate()).padStart(2, '0')
        }
        if (item.documentSource === 'public' && item.knowledgeId) {
          const isDuplicate = refKBNameListRef.value.some(
            (item1) => item1.kbID === item.knowledgeId
          )
          if (!isDuplicate) {
            fetchKBName(item.knowledgeId)
          }
        }
      })
    }
  })

  
  setupScrollDetection()
})

const setupScrollDetection = () => {
  const refList = document.querySelector('.ref-list') as HTMLElement
  if (refList) {
    refList.addEventListener('scroll', handleScroll)
  }
}

const handleScroll = () => {
  isScrolling.value = true
  const refList = document.querySelector('.ref-list') as HTMLElement
  if (refList) {
    refList.classList.add('scrolling')
  }

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
    if (refList) {
      refList.classList.remove('scrolling')
    }
  }, 800)
}

onUnmounted(() => {
  const refList = document.querySelector('.ref-list') as HTMLElement
  if (refList) {
    refList.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})

const fetchKBName = async (id: string) => {
  const res = await getKBName({ knowledgeIds: id })
  if (res.data.data && res.data.data.length > 0) {
    const newFile: RefKBNameType = {
      kbID: id,
      KBName: res.data.data[0].knowledgeName
    }
    refKBNameListRef.value.push(newFile)
    /*     console.log('newFile.kbID =', newFile.kbID)
    console.log('newFile.KBName =', newFile.KBName) */
  }
}

watch(
  () => referenceListRef.value,
  () => {
    referenceListRef.value
  },
  { deep: 1, immediate: true }
)

watch(
  () => refKBNameListRef.value,
  () => {
    refKBNameListRef.value
  },
  { deep: 1, immediate: true }
)

const currentDate = computed(() => {
  const currentDt = new Date()
  const year = currentDt.getFullYear()
  const month = String(currentDt.getMonth() + 1).padStart(2, '0')
  const day = String(currentDt.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
})

computed(() => {
  const currentDt = new Date()
  const year = currentDt.getFullYear()
  const month = String(currentDt.getMonth() + 1).padStart(2, '0')
  const day = String(currentDt.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
})
</script>

<style lang="less" scoped>
.ref-panel {
  width: 376px;
  height: 100vh;
  background: #f4f9ff;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .ref-panel_background {
    margin: 43px 0 0 0;
    background-color: white;
    padding: 10px 10px 0 10px;
    align-items: center;
    font-size: 14;
    font-weight: bold;
    .ref-panel_background-container {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e5e7ec;
    }
  }
  .ref-panel-header {
    padding: 8px;
    display: inline;
    text-align: right;
    cursor: pointer;
    :deep(.svg-icon) {
      margin-bottom: 8px;
      cursor: pointer;
    }
  }

  :deep(.add-btn-container) {
    padding-left: 8px;
    padding-right: 8px;
  }
}
.ref-pkbfileName {
  width: 320px;
  color: black;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0px 5px 5px 5px;
}
.web-refTitle {
  width: 320px;
  color: black;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0px 5px 5px 5px;
}
.ref-list {
  padding: 0px 10px 0 10px;
  background-color: white;
  color: var(--text-color1);
  overflow-y: auto;
  font-size: 12px;
  padding: 10px;
  flex-grow: 1;

  &::-webkit-scrollbar {
    width: 6px;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  /* 滚动时显示滚动条 */
  &.scrolling::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }
  .refItem {
    margin: 0px 0px 6px 0px;
    border-radius: 4px;
    border: 1px solid #e5e7ec;
    .ref-list-siteName {
      font-size: 14px;
      color: black;
      margin: 10px;
      display: flex;
      text-align: left;
      align-items: flex-start;
    }
    /*     .ref-list-siteName:hover .file-icon {
      display: flex;
    } */
    .file-icon {
      margin-right: 10px;
      width: 16px;
      height: 16px;
      cursor: pointer;
      color: white;
    }
  }
  .basic:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  }
  .container {
    margin: 0px 10px 10px 10px;
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    justify-content: space-between;
  }
  .left-items {
    display: flex;
    gap: 10px; /* 左侧元素间距 */
  }
  .kbdiv {
    width: 230px;
    margin-top: 4px;
    font-size: 12px;
    color: #696969;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ref-list-siteName:hover .ref-pkbfileName {
    text-decoration: underline;
    text-decoration-color: #6441ab; /* 下划线颜色 */
    cursor: pointer;
  }
  .ref-type {
    font-size: 12px;
    display: flex;
    color: #696969;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
  }

  .right-item {
    font-size: 10px;
    color: #858585;
    margin-left: auto; /* 强制右对齐 */
  }
}

.ref-group {
  margin-bottom: 16px;
}

.ref-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.group-title {
  font-size: 12px;
  color: var(--text-color3);
  margin-bottom: 8px;
  padding: 0 8px;
}

.ref-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background-color: var(--hover-color);
    color: #6441ab;
    font-weight: 500;
  }

  .item-actions-wrapper {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }

  .item-actions {
    flex-shrink: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    &.selected {
      background-color: var(--hover-color);
    }
  }
}

.item-title {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

a {
  color: #696969;
  text-decoration: none; /* 去掉默认下划线 */
}
a:hover {
  color: #6441ab;
  text-decoration: underline;
}
</style>
