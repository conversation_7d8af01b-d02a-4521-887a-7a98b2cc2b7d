<template>
  <svg
    :class="{ svgClass, 'rotate-icon': props.rotate }"
    :style="{ width: size, height: size, color: color }"
    aria-hidden="true"
  >
    <use :xlink:href="`#icon-${name}`" />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string // svg 图标名称
  className?: string // 指定的类样式
  size?: string // 图标尺寸
  color?: string // 图标颜色
  rotate?: boolean // 是否图标旋转
}
const props = withDefaults(defineProps<Props>(), {
  className: '',
  size: '20px',
  color: ''
})
const svgClass = computed(() => {
  if (props.className) {
    return 'svg-icon ' + props.className
  }
  return 'svg-icon'
})
</script>

<style scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

@media print {
  .svg-icon {
    overflow: visible;
  }
}
.rotate-icon {
  transform-origin: center; /* 旋转中心为元素中心 */
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
