<template>
  <div class="win-bar-btns">
    <div class="win-bar-btns_tool" @click="handleWinSize(WinState.Min)">
      <SvgIcon name="Minimize" size="10"></SvgIcon>
    </div>
    <div class="win-bar-btns_tool" @click="setMaxOrNormalWin()">
      <SvgIcon :name="isMaximized ? 'Restore' : 'Maximize'" size="10"></SvgIcon>
    </div>
    <div class="win-bar-btns_tool" @click="handleWinClose">
      <SvgIcon name="Close" size="10"></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { WinState } from '@/types'
import { electronHooks } from '../../electron'
import SvgIcon from '../SvgIcon'
import { ref, onMounted } from 'vue'

const electronApi = electronHooks()
const isMaximized = ref(false)

// 检查窗口状态
const updateWindowState = async () => {
  if (electronApi) {
    const state = await electronApi.getWinState()
    isMaximized.value = state === WinState.Max
  }
}

const setMaxOrNormalWin = async () => {
  if (electronApi) {
    const state = await electronApi.getWinState()

    if (state === WinState.Max) {
      handleWinSize(WinState.Normal)
    } else {
      handleWinSize(WinState.Max)
    }

    // 更新图标状态
    await updateWindowState()
  }
}

const handleWinSize = (maxOrMin: WinState) => {
  if (electronApi) {
    switch (maxOrMin) {
      case WinState.Max:
        electronApi.winMax()
        break
      case WinState.Min:
        electronApi.winMin()
        break
      case WinState.Normal:
        electronApi.winRestore()
        break
      default:
        break
    }
  }
}

const handleWinClose = () => {
  if (electronApi) {
    electronApi.winClose()
  }
}

// 初始化和设置窗口状态监听
onMounted(async () => {
  await updateWindowState()

  // 添加窗口事件监听
  window.addEventListener('resize', updateWindowState)
})
</script>

<style lang="less" scoped>
.win-bar-btns {
  align-items: center;
  display: flex;
  // justify-content: space-between;

  > * {
    width: 46px;
    height: 46px;

    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  > *:hover {
    background-color: rgba(0, 0, 0, 0.1);
    // color: #fff;
  }

  > *:last-child:hover {
    background-color: red;
    color: #fff;
  }
}
</style>
