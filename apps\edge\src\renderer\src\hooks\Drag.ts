import { electronHooks } from '../electron'
export function dragHooks() {
  const electronApi = electronHooks()
  const setDrag = (el: HTMLElement) => {
    el.onmousedown = (e) => {
      e.preventDefault()
      e.stopPropagation()
      electronApi?.windowMove(true)
    }
    el.onmouseup = (e) => {
      e.preventDefault()
      e.stopPropagation()
      electronApi?.windowMove(false)
    }
    el.ondragend = (e) => {
      e.preventDefault()
      e.stopPropagation()
      electronApi?.windowMove(false)
    }
  }
  return {
    setDrag
  }
}
