export const mouseEnterTooltip = (el: any) => {
  const tooltip = el.querySelector('.ellipsis-cell__tooltip')
  const tdText = el.querySelector('.ellipsis-cell__text') as HTMLElement
  const tooltipSpan = el.querySelector('.ellipsis-cell__tooltip span')
  const tdTextSpan = el.querySelector('.ellipsis-cell__text span')

  let currentEle: any = tdTextSpan.scrollWidth ? tdTextSpan : tooltipSpan

  // console.log('el', tdText)
  if (currentEle.scrollWidth > currentEle.offsetWidth) {
    tdText?.classList.add('hide-tooltip')
    tooltip?.classList.remove('hide-tooltip')
  } else {
    tdText?.classList.remove('hide-tooltip')
    tooltip?.classList.add('hide-tooltip')
  }
}
