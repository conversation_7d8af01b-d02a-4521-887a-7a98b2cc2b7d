import doc from '@renderer/assets/icons/DOC.svg'
import ppt from '@renderer/assets/icons/PPT.svg'
import pdf from '@renderer/assets/icons/PDF.svg'
import txt from '@renderer/assets/icons/TXT.svg'
import excel from '@renderer/assets/icons/Excel.svg'
import jpeg from '@renderer/assets/icons/JPEG.svg'
import png from '@renderer/assets/icons/PNG.svg'
import bmp from '@renderer/assets/icons/Bump.svg'
import msg from '@renderer/assets/icons/MSG.svg'
import general from '@renderer/assets/icons/general.svg'
import folder from '@renderer/assets/icons/Folder.svg'

// 文件后缀图标名映射
export const fileExtensionIconNameMap = {
  doc: 'DOC',
  docx: 'DOC',
  xls: 'Excel',
  xlsx: 'Excel',
  csv: 'Excel',
  jpg: 'JPEG',
  jpeg: 'JPEG',
  msg: 'MSG',
  pdf: 'PDF',
  png: 'PNG',
  ppt: 'PPT',
  pptx: 'PPT',
  txt: 'TXT',
  bmp: 'Bump',
  folder: 'Folder'
}

// 文件后缀图标映射
export const FileIconMap: Record<string, string> = {
  doc,
  docx: doc,
  ppt,
  pptx: ppt,
  pdf,
  txt,
  xls: excel,
  xlsx: excel,
  csv: excel,
  jpg: jpeg,
  jpeg,
  png,
  bmp,
  msg,
  folder
}

/**
 * 提取本地文件后缀
 * @param filePath 文件路径
 * @returns 文件后缀
 */
export const getLocalFileExtension = (fileName: string) => {
  return fileName.split('.').pop()?.toLowerCase()
}

/**
 * 获取文件SVG名称
 * @param fileName 文件名
 * @returns 文件SVG名称
 */
export const getFileSvgNameSvgName = (fileName: string) => {
  const extension = getLocalFileExtension(fileName)
  return fileExtensionIconNameMap[extension as keyof typeof fileExtensionIconNameMap] || 'general'
}

/**
 * 获取文件SVG图标
 * @param fileName 文件名
 * @returns 文件SVG图标
 */
export const getFileSvgIcon = (fileName: string) => {
  const extension = getLocalFileExtension(fileName)
  return FileIconMap[extension as keyof typeof FileIconMap] || general
}
