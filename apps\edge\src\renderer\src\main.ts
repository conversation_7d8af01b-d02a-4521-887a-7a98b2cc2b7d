// import './assets/main.css'
import './style/index.less'
import { createApp } from 'vue'
import App from './App.vue'
import routers from '@renderer/routers'
import log from 'electron-log/renderer'
if (window.electron) {
  const { info, error, warn, silly, verbose, debug } = log.functions
  // console.log=logger.logger.log
  Object.assign(console, {
    info,
    error,
    warn,
    silly,
    verbose,
    debug
  })
}

console.log('render mode=>', import.meta.env.MODE)
console.log('env=>', import.meta.env.VITE_ENV)
createApp(App).use(routers).mount('#app')
