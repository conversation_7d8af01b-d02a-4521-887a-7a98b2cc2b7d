.global{
    &-title2{

        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        letter-spacing: 0%;
        
    }
    &-title3{
  
font-weight: 600;
font-size: 16px;
line-height: 24px;
letter-spacing: 0%;
    }
    &-text2{
      
font-weight: 400;
font-size: 14px;
line-height: 143%;
letter-spacing: 0.17px;


    }
}
.ellipsis {
    white-space: nowrap;         /* 强制文本在一行显示 */
    overflow: hidden;           /* 隐藏超出容器的文本 */
    text-overflow: ellipsis;    /* 使用省略号表示被剪切的文本 */
  }
  @media print {
    .ellipsis {
          overflow: visible;
      }
  }
  /* 全局滚动条样式 */
*::-webkit-scrollbar {
  width: 6px;
  
  }

  
  *::-webkit-scrollbar-thumb {
  background: #949494;
  border-radius: 3px;
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner{
  background-color: #6441AB;
  &::after {
      background-color: #fff;
      height: 2px;
  }
}