export function getCookie() {
  const cookieStr = document.cookie
  const cookieObj = {} as Record<string, string>
  const parts = cookieStr.split('; ')
  parts.forEach((part) => {
    const [key, value] = part.split('=')
    cookieObj[key] = value
  })
  return cookieObj
}
export function setCookie(name: string, value: string, domain = '', days?: number) {
  const cookieObj = getCookie()

  if (cookieObj[name]) {
    // 如果存在相同的key，删除原有的cookie
    const expires = new Date(0).toUTCString()
    console.log('domain', domain)

    document.cookie = name + '=' + cookieObj[name] + '; domain=' + domain + '; expires=' + expires
    console.log('cookie', document.cookie)
  }

  let expires = ''
  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
    expires = '; expires=' + date.toUTCString()
  }
  document.cookie = name + '=' + value + '; domain=' + domain + expires + '; path=/'
}
