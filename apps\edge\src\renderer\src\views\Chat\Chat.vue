<template>
  <div class="chat-wrap">
    <!-- 使用 v-if 来控制 ChatBase 的卸载和重新加载 -->
    <ChatBase v-if="isChatBaseVisible" :chat="chat">
      <!-- 接收问题数据 -->
      <template #questionSlot="{ questions }" v-if="!chat?.isWelcome">
        <div class="query-container">
          <QuestionPage :queryItem="questions" />
          <QueryOperation class="query-operation" :queryItem="questions" />
        </div>
      </template>

      <!-- 接收回答数据 -->
      <template #answerSlot="{ answers }" v-if="!chat?.isWelcome">
        <div class="answer-container">
          <AnswerPage
            :agentObj="agentObj"
            :answerItem="answers.answerData"
            :isDeepThink="chat?.isDeeoThinking"
          />
          <SourcePage
            v-if="
              answers.answerData?.references?.length > 0 &&
              answers.answerData.chatStatusAnswer.value === ChatStatus.DONE
            "
            :isDeepThink="chat?.isDeeoThinking"
            :answerItem="answers.answerData"
          />
          <!-- 答案生成过程中不显示工具条 加了v-show 勿删--->
          <MessageOperationToolbar
            v-show="answers.answerData.chatStatusAnswer.value === ChatStatus.DONE"
            class="answer-operation"
            :chat="chat"
            :answerItem="answers.answerData"
          />
        </div>
      </template>

      <template #welcomeSlot v-if="chat?.isWelcome">
        <div>
          <welcomePage class="chat-wrap_welcome" :welcomeConfig="chat?.welcomeConfig" />
        </div>
      </template>

      <template #inputSlot>
        <InputPage :chat="chat" :isComponentSmall="isComponentSmall" />
      </template>
    </ChatBase>
  </div>
</template>

<script setup lang="ts">
import { ChatBase, ChatController, ChatStatus, ChatComponentType, AnswerType } from '@libs/a-comps'
import { Question, Answer } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import mySDK from './sdkService'
import { onMounted, ref, watch, onUnmounted, inject } from 'vue'
import welcomePage from './components/welcome.vue'
import InputPage from './components/input.vue'
import QuestionPage from './components/question.vue'
import AnswerPage from './components/answer.vue'
import SourcePage from './components/sourcePage.vue'
import MessageOperationToolbar from './components/MessageOperationToolbar.vue'
import QueryOperation from './components/QueryOperation.vue'
import { getAgentList, postRegisterChat } from '@/services'
import { AgentVo } from '@/types'
import { electronHooks } from '@/renderer/src/electron'
import SvgIcon from '../../components/SvgIcon/SvgIcon.vue'
import { historyBaseApi } from '@/renderer/src/api/historyBase'
import { emitter } from '@/utils/EventBus'
import MarkdownIt from 'markdown-it'
import { stringType } from 'ant-design-vue/es/_util/type'

const chat = ref<ChatController>()
const isChatBaseVisible = ref(true)

const historyCollapsed = inject('historyCollapsed', ref(true))
const handleHistoryCollapse = inject('handleHistoryCollapse', () => {})

const props = defineProps({
  sessionNum: Number,
  isComponentSmall: Boolean
})

// 初始化聊天实例
const resetChatBase = async () => {
  isChatBaseVisible.value = false

  let sessionId: string | undefined

  await mySDK.init()
  window.dispatchEvent(new CustomEvent('userReady'))

  try {
    const res = await historyBaseApi.registHistory(mySDK.userId)
    const resData = (res as any)?.data
    if (resData?.code === 200 && resData?.data?.sessionId) {
      sessionId = resData.data.sessionId
    }
  } catch (error) {
    console.error('Failed to register history session:', error)
  }

  setTimeout(() => {
    chat.value = new ChatController(mySDK)
    if (sessionId) {
      chat.value.sessionId = sessionId
    }
    chat.value.welcomeConfig = {
      title: 'Hi there ! Ready to boost your productivity.',
      avatar: 'NewChat-icon'
    }
    chat.value.setWelcomeType(true)
    // 新会话重置重新生成标记
    chat.value.setRegenerate(false)
    isChatBaseVisible.value = true
    postRegisterChat(chat.value.sessionId)
  }, 0)
}

watch(
  () => props.sessionNum,
  () => {
    resetChatBase()
  },
  { immediate: true }
)

watch(
  () => chat.value?.chatStatus.value,
  (newStatus) => {
    if (
      [
        ChatStatus.SUCCESS,
        ChatStatus.DONE,
        ChatStatus.FAILURE,
        ChatStatus.FAIL,
        ChatStatus.STOP
      ].includes(newStatus as ChatStatus)
    ) {
      emitter.emit('chat-completed', { sessionId: chat.value?.sessionId })
    }
  }
)

const electronApi = electronHooks()
let historySelectedListenerId: string | undefined

onMounted(() => {
  getList()
  document.onclick = (event) => {
    let target = event.target as HTMLElement
    while (target && target.tagName !== 'A') {
      target = target.parentElement as HTMLElement
    }
    if (target && target.tagName === 'A' && target.hasAttribute('href')) {
      event.preventDefault()
      const href = target.getAttribute('href')
      console.log('链接地址:', href)
      electronApi?.openWebSite(href?.toString() || '')
    }
  }
  setupUploadAreaObserver()
  window.addEventListener('newChatCreated', resetChatBase)
  historySelectedListenerId = emitter.on(
    'history-selected',
    loadHistory as (payload: unknown) => void
  )
})

const setupUploadAreaObserver = () => {
  const updateLayoutForFileDisplay = () => {
    const fileDisplay = document.querySelector('.file-display') as HTMLElement
    const welcomeElement = document.querySelector('.chat-wrap_welcome') as HTMLElement
    const inputBox = document.querySelector('.input-box') as HTMLElement
    const chatWrap = document.querySelector('.chat-wrap') as HTMLElement

    if (!fileDisplay || !welcomeElement) return

    const computedStyle = window.getComputedStyle(fileDisplay)
    const isVisible = computedStyle.opacity !== '0' && computedStyle.display !== 'none'

    if (isVisible) {
      const filesContainer = fileDisplay.querySelector('.files-container') as HTMLElement
      if (filesContainer) {
        // 计算文件显示区域的实际行数
        const containerHeight = filesContainer.scrollHeight
        const lineHeight = 30 // 单行高度
        const rowCount = Math.ceil(containerHeight / lineHeight)

        if (rowCount > 1) {
          // 当文件超过一行时，需要调整布局以避免遮挡

          // 1. 调整欢迎内容的位置，向上移动
          const originalTop = 30
          // 每多一行，欢迎内容向上移动 3%
          const adjustedTop = Math.max(originalTop - (rowCount - 1) * 3, 10)
          welcomeElement.style.top = `${adjustedTop}%`
          welcomeElement.style.transition = 'top 0.3s ease'

          // 2. 调整输入框的整体位置，为文件区域预留空间
          if (inputBox) {
            // 根据文件行数调整输入框的 margin-top
            const additionalMargin = (rowCount - 1) * 15
            inputBox.style.marginTop = `${additionalMargin}px`
            inputBox.style.transition = 'margin-top 0.3s ease'
          }

          // 3. 确保聊天容器有足够的空间
          if (chatWrap) {
            chatWrap.style.paddingTop = '20px'
          }
        } else {
          // 恢复原始布局
          resetLayout()
        }
      }
    } else {
      // 文件区域不可见，恢复原始布局
      resetLayout()
    }
  }

  const resetLayout = () => {
    const welcomeElement = document.querySelector('.chat-wrap_welcome') as HTMLElement
    const inputBox = document.querySelector('.input-box') as HTMLElement
    const chatWrap = document.querySelector('.chat-wrap') as HTMLElement

    if (welcomeElement) {
      welcomeElement.style.top = '25%'
      welcomeElement.style.transition = 'top 0.3s ease'
    }

    if (inputBox) {
      inputBox.style.marginTop = '0'
      inputBox.style.transition = 'margin-top 0.3s ease'
    }

    if (chatWrap) {
      chatWrap.style.paddingTop = '16px'
    }
  }

  const updateAnswerContainerMargin = () => {
    const fileDisplay = document.querySelector('.file-display') as HTMLElement
    const answerContainers = document.querySelectorAll(
      '.answer-container'
    ) as NodeListOf<HTMLElement>
    const inputBoxContent = document.querySelector('.input-box_content') as HTMLElement

    if (!answerContainers.length) return

    const lastAnswerContainer = answerContainers[answerContainers.length - 1]

    if (fileDisplay) {
      const computedStyle = window.getComputedStyle(fileDisplay)
      const isVisible = computedStyle.opacity !== '0' && computedStyle.display !== 'none'

      // 动态设置 input-box_content 的 margin-top
      if (inputBoxContent) {
        inputBoxContent.style.marginTop = isVisible ? '80px' : '0'
      }

      if (isVisible) {
        // 增加基础间距，确保有足够空间
        const baseMargin = 32
        // 根据文件显示区域高度动态调整间距，增加额外的安全边距
        const marginBottom = Math.max(baseMargin, fileDisplay.offsetHeight + baseMargin + 16)
        lastAnswerContainer.style.marginBottom = `${marginBottom}px`
      } else {
        lastAnswerContainer.style.marginBottom = `${24}px`
      }
    } else {
      if (inputBoxContent) {
        inputBoxContent.style.marginTop = '0'
      }
      lastAnswerContainer.style.marginBottom = `${24}px`
    }
  }

  const observer = new MutationObserver((mutations) => {
    // 使用 requestAnimationFrame 来确保在下一帧更新，避免动画过程中的抖动
    requestAnimationFrame(() => {
      updateLayoutForFileDisplay()
      updateAnswerContainerMargin()
    })
  })

  // 观察整个聊天区域的变化
  const targetNode = document.querySelector('.chat-wrap') as HTMLElement
  if (targetNode) {
    observer.observe(targetNode, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class', 'opacity', 'transform']
    })
  }

  // 初始更新
  updateLayoutForFileDisplay()
  updateAnswerContainerMargin()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    requestAnimationFrame(() => {
      updateLayoutForFileDisplay()
      updateAnswerContainerMargin()
    })
  })

  // 组件卸载时清理
  onUnmounted(() => {
    // 断开观察器
    observer.disconnect()
    window.removeEventListener('resize', () => {
      requestAnimationFrame(() => {
        updateLayoutForFileDisplay()
        updateAnswerContainerMargin()
      })
    })
    window.removeEventListener('newChatCreated', resetChatBase)
    if (historySelectedListenerId) {
      emitter.removeListen('history-selected', historySelectedListenerId)
    }
  })
}

const agentObj = ref(
  {} as {
    video: AgentVo
    email: AgentVo
  }
)
const getList = () => {
  getAgentList()
    .then((res) => {
      if (res.data?.success) {
        const agents = res.data.data as unknown as AgentVo[]
        const videoAgent = agents.filter((agent) => agent.agentName === 'Video Agent')[0] || {}
        const emailAgent = agents.filter((agent) => agent.agentName === 'Email Agent')[0] || {}
        agentObj.value = {
          video: videoAgent,
          email: emailAgent
        }
      }
    })
    .catch((err) => {})
}

interface HistoryMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  createdAt: string
  references?: any[]
  documentList?: any[]
  docInfo?: any[]
  isDeepThink: boolean
  regen: boolean
  think: any[]
  citations?: any
  olds?: any[]
}

interface HistorySelectedPayload {
  historyData: HistoryMessage[]
  sessionId: string
}

const loadHistory = ({ historyData, sessionId }: HistorySelectedPayload) => {
  if (!chat.value) return

  chat.value.setWelcomeType(false)
  chat.value.chatAction.splice(0, chat.value.chatAction.length)
  chat.value.sessionId = sessionId

  const md = new MarkdownIt()

  historyData.forEach((item: HistoryMessage) => {
    if (item.role === 'user') {
      const question = new Question()

      const docInfo = (item as any).docInfo || []
      const files = docInfo.map((doc: any) => ({
        name: doc.documentName || doc.name || '',
        path: '',
        fileId: doc.knowledgeId || doc.fileId || '',
        documentId: doc.documentID || doc.documentId || ''
      }))

      question.setData({
        id: item.id,
        content: item.content,
        timestamp: new Date(item.createdAt).getTime(),
        type: ChatComponentType.QUERY,
        files: files
      })
      chat.value!.chatAction.push(question)
    } else if (item.role === 'assistant') {
      const lastQuestion = [...chat.value!.chatAction]
        .reverse()
        .find((m) => m instanceof Question) as Question | undefined
      const regex = new RegExp(`<div class="tool_selected">([\\s\\S]*?)</div>`, 'g')
      const thinkingContent = item.think
        .map((item) => item.response)
        .join('')
        .replace(regex, `<div class="tool_selected" style="display:none">$1</div>`)
      const thinkMarkdownContent = md
        .render(thinkingContent)
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
      const answer = new Answer()

      // 提取历史记录里的reference数据
      const entriesWithReferences = item.think.filter((item) => {
        // 确保 references 存在，且是数组，且长度 > 0
        return Array.isArray(item.references) && item.references.length > 0
      })
      if (entriesWithReferences) {
        entriesWithReferences.forEach((item) => {
          answer.toolType.push({
            toolIndex: item?.toolIndex,
            references: item?.references
          })
        })
      }

      // const isPart = localStorage.getItem(`history-${sessionId}`)
      const answerData: AnswerType = {
        id: item.id,
        questionId: lastQuestion ? lastQuestion.id : '',
        // content: isPart ? item.content : md.render(item.content),
        content: md.render(item.content),
        response: item.content,
        timestamp: new Date(item.createdAt).getTime(),
        type: ChatComponentType.ANSWER,
        queryData: lastQuestion ? lastQuestion.questionData : undefined
        // regen:item.regen
      }
      answer.thinkingContent = thinkMarkdownContent
      //emitter.emit('is-deepThinking', item.isDeepThink)
      answer.setData(answerData)
      answer.isDeepThink = item.isDeepThink
      answer.regen = item.regen
      answer.isFromHistory = true

      // 保存当前内容, 用于翻页恢复
      answer.originalContent = answer.answerData.content
      answer.originalResponse = answer.answerData.response
      // 保存当前references, 用于翻页恢复
      if (item.references) {
        answer.originalReferences = item.references
      }
      // 保存当前citations, 用于翻页恢复
      if (item.citations) {
        answer.originalCitations = item.citations
      }

      if (item.think) {
        answer.originalThink = item.think
      }

      // 保存当前thinkingContent, 用于翻页恢复
      answer.originalThinkingContent = thinkMarkdownContent

      if (item?.olds && item?.olds.length > 0) {
        item?.olds.forEach((oldItem: any) => {
          const oldAnswer = new Answer()
          const oldMd = new MarkdownIt()
          const oldAnswerData: AnswerType = {
            id: oldItem.id,
            questionId: lastQuestion ? lastQuestion.id : '',
            content: oldMd.render(oldItem.content),
            response: oldItem.content,
            timestamp: new Date(oldItem.createdAt).getTime(),
            type: ChatComponentType.ANSWER,
            queryData: lastQuestion ? lastQuestion.questionData : undefined
          }

          const thinkingContent = oldItem.think.map((item: any) => item.response).join('')

          const targetMarkdownContent = md
            .render(thinkingContent)
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')

          oldAnswer.setData(oldAnswerData)
          oldAnswer.isDeepThink = oldItem.isDeepThink

          oldAnswer.regen = oldItem.regen
          oldAnswer.isFromHistory = true
          oldAnswer.thinkingContent = targetMarkdownContent
          if (oldItem.references) {
            oldAnswer.references = oldItem.references
          }

          if (oldItem.citations) {
            oldAnswer.citations = oldItem.citations
          }

          if (oldItem.documentList) {
            oldAnswer.documentList = oldItem.documentList
          }

          oldAnswer.chatStatusAnswer.value = ChatStatus.DONE

          // 将olds添加到refreshArr
          answer.refreshArr.push(oldAnswer)
        })

        // 标记当前答案为重新生成的答案
        answer.regen = true
      }

      if (item.references) {
        answer.references = item.references
      }

      if (item.citations) {
        answer.citations = item.citations
      }
      if (item.documentList) {
        answer.documentList = item.documentList
      }

      answer.olds = item?.olds

      answer.chatStatusAnswer.value = ChatStatus.DONE
      chat.value!.chatAction.push(answer)
    }
  })

  chat.value.scrollToBottom()

  // 发送事件重置所有MessageOperationToolbar页码
  emitter.emit('reset-page-index')
}
</script>

<style lang="less" scoped>
.chat-wrap {
  padding: 16px;
  background: linear-gradient(
    121.75deg,
    rgba(241, 242, 255, 0.92) 0.51%,
    rgba(247, 249, 255, 0.92) 40.31%,
    rgba(234, 241, 255, 0.92) 100%
  );
  height: 100vh;
  position: relative;

  > * {
    // margin-bottom: 32px;

    // width: 80%;
  }

  &_welcome {
    position: absolute;
    top: 30%;
    width: 100%;
    text-align: center;
    // margin-bottom: 20px;
  }

  .font-segoe {
    font-family: Segoe UI;
  }

  .font-yh {
    font-family: Microsoft YaHei;
  }

  &_banner {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 50px;
  }
}

.answer-container {
  position: relative;

  &:hover .answer-operation {
    opacity: 1;
    visibility: visible;
  }
}

.answer-operation {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.query-container {
  position: relative;

  &:hover .query-operation {
    opacity: 1;
    visibility: visible;
  }
}

.query-operation {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease;
  z-index: 10;
  height: 24px;
}

:deep(.chat-box-flex) {
  display: flex;
  flex-direction: column;

  align-items: center;
}
</style>
