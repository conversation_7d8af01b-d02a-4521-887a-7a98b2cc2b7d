<template>
  <div class="kb-select-content kb-select-wrapper">
    <!-- 左侧知识库列表 -->
    <div class="kb-select-sider">
      <ul class="kb-select-sider-ul" v-for="item in kbMenus" :key="item.type">
        <li class="kb-select-sider-ul-title">{{ item.title + ' (' + item.data.length + ')' }}</li>
        <li
          v-for="menu in item.data"
          :key="menu.entityId"
          class="kb-select-sider-ul-item"
          :class="{ selected: menu.entityId == activeItem?.entityId }"
          @click="handleKBClick(menu)"
        >
          <section>
            <span class="kb-select-sider-itemname" :title="menu.knowledgeBaseName">
              {{ menu.knowledgeBaseName }}
            </span>
          </section>
          <div
            class="loading-icon"
            v-if="!!(knowledgeStore.knowledgeTaskMap[menu.entityId] && getTaskCount(menu.entityId))"
          >
            <SvgIcon name="progress-mini" :rotate="true" size="14" />
          </div>
        </li>
        <div
          class="kb-select-sider-ul-empty"
          v-if="item.type == KBType.TEAM && item.data.length == 0"
        >
          <label>
            <SvgIcon name="tip-icon" size="14" />
          </label>
          <label>No shared knowledge base available yet.</label>
        </div>
      </ul>
    </div>

    <!-- 右侧文件列表 -->
    <div class="kb-select-main">
      <!-- 显示加载中状态 -->
      <div v-if="initialLoading" class="kb-loading-state">
        <AResult title="Loading..." class="kb-loading-result">
          <template #icon>
            <SvgIcon name="progress-mini" :rotate="true" size="24" />
          </template>
        </AResult>
      </div>

      <template v-else>
        <header class="kb-select-main-header" v-if="dataSource.length > 0 || isLoading">
          <label class="title">{{
            activeItem?.knowledgeBaseName || activeItem?.knowledgeBaseId
          }}</label>
          <AInput
            class="kb-select-main-search-input"
            v-model:value="filter"
            placeholder="Search by name"
            :max-length="32"
            @press-enter="handleSearch"
          >
            <template #suffix>
              <ABtn type="text" class="kb-select-main-search-button" @click="handleSearch">
                <SvgIcon name="search-icon" size="11.9" @click="handleSearch" />
              </ABtn>
            </template>
          </AInput>
        </header>

        <!-- 数据列表 -->
        <template v-if="!(isFirstPageEmpty && !isLoading)">
          <div class="kb-select-main-table">
            <ATable
              ref="fileTableRef"
              :rowKey="getFileKey"
              :dataSource="dataSource"
              :columns="
                activeItem?.knowledgeBaseType === KBType.PERSON ? privateKBColumns : columns
              "
              :pagination="false"
              :row-selection="{
                selectedRowKeys: selectedIds,
                onChange: onSelectChange,
                hideSelectAll: false
              }"
              :row-class-name="() => 'kb-select-main-table-row'"
              :customRow="customRow"
              @change="tableChange"
              size="small"
              :scroll="{ y: 'calc(100% - 50px)', x: '100%' }"
              class="kb-file-table"
              :loading="isLoading"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'documentName'">
                  <span class="kb-select-main-file-name-wrapper">
                    <SvgIcon
                      class="kb-select-main-file-type-svg"
                      :name="
                        fileExtensionIconNameMap[
                          record.documentType as keyof typeof fileExtensionIconNameMap
                        ] || 'general'
                      "
                      size="24"
                    >
                    </SvgIcon>
                    <span :title="record.documentName">{{ record.documentName }}</span>
                  </span>
                </template>
                <template v-if="column.key === 'owner' || column.key === 'from'">
                  <span>{{
                    activeItem?.knowledgeBaseType === KBType.PERSON ? record.from : record.owner
                  }}</span>
                </template>
                <template v-else-if="column.key === 'updateTime'">
                  <div>{{ dayjs(record.updateTime).format('MMM DD, YYYY') }}</div>
                </template>
              </template>
            </ATable>

            <!-- 底部加载中提示，仅在加载更多时显示 -->
            <div v-if="isLoadingMore" class="kb-loading-more">
              <SvgIcon name="progress-mini" :rotate="true" size="14" />
              <span class="loading-text">Loading more...</span>
            </div>

            <!-- 底部到达结尾提示，仅在加载更多后没有更多数据时显示 -->
            <div v-if="reachedEnd" class="kb-no-more-data">
              <SvgIcon name="tip-icon" size="14" />
              <span class="no-more-text">No more data. Scroll up to view previous items.</span>
            </div>
          </div>
        </template>

        <!-- 空面板只在第一页没有数据时显示 -->
        <div v-if="isFirstPageEmpty && !isLoading" class="kb-empty-center-panel">
          <div class="kb-file-container__empty-icon"></div>
          <template v-if="isCanOperateDocument">
            <div class="kb-empty-desc">
              Add project files to turn knowledge into team productivity.
            </div>
            <div class="kb-file-container__empty-button-wrapper">
              <ABtn class="kb-file-container__import-button" type="primary" @click="handleUpload">
                <SvgIcon
                  class="kb-file-container__import-button-icon"
                  name="import-icon"
                  size="11"
                />Import
              </ABtn>
            </div>
          </template>
          <div v-else class="kb-empty-desc">The knowledge base is currently empty.</div>
        </div>
      </template>
    </div>
  </div>

  <!-- 底部按钮 -->
  <div class="kb-select-footer">
    <div class="footer-actions">
      <span class="file-selection-status">{{ fileSelectionStatusText }}</span>
      <ABtn @click="$emit('close')" class="footer-cancel-button">Cancel</ABtn>
      <ABtn
        type="primary"
        :disabled="selectedFiles.length === 0"
        @click="handleConfirm"
        class="footer-confirm-button"
      >
        Confirm
      </ABtn>
    </div>
  </div>

  <AModal
    class="file-limit-warning-modal"
    width="400px"
    centered
    v-model:open="showWarningModal"
    @ok="handleWarningModalOk"
    :z-index="9999"
  >
    <template #title>
      <div class="warning-modal__title">
        <SvgIcon class="warning-modal__title-icon" name="warn-icon" size="20" />
        <span class="warning-modal__title-text">Warning</span>
      </div>
    </template>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <ABtn type="primary" @click="handleWarningModalOk">OK</ABtn>
      </div>
    </template>
    <div class="warning-modal__body">You can add at most {{ remainingSelectionLimit }} files.</div>
  </AModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import { ATable, AInput, ABtn, Result as AResult, AModal } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import dayjs from 'dayjs'
import { fileExtensionIconNameMap } from '@renderer/hooks/fileType'
import { knowledgeBaseApi } from '@/renderer/src/api/knowledgeBase'
import { FileStatus } from '@/main/Service/fileSync/constants/types'
import { IKBItem, IKBFileItem, KBType, KBMenus } from '@/renderer/src/views/KnowledgeBase/type'
import { knowledgeStore } from '@/renderer/src/views/KnowledgeBase/store'

const emit = defineEmits(['close', 'confirm'])

const props = defineProps({
  alreadySelectedCount: {
    type: Number,
    default: 0
  }
})

interface ISelectedFile extends IKBFileItem {
  knowledgeId: string
}

// 知识库数据
const kbMenus = reactive<KBMenus[]>([])
const activeItem = ref<IKBItem | null>(null)
const selectedFiles = ref<ISelectedFile[]>([])
const selectedIds = computed(() => selectedFiles.value.map((f) => f.documentId))
const filter = ref('')
const dataSource = ref<IKBFileItem[]>([])
const showWarningModal = ref(false)

const MAX_SELECT_COUNT = 10
const remainingSelectionLimit = computed(() => {
  const limit = MAX_SELECT_COUNT - props.alreadySelectedCount
  return limit > 0 ? limit : 0
})

const fileSelectionStatusText = computed(() => {
  const selectedCount = selectedFiles.value.length
  const remainingCount = remainingSelectionLimit.value
  return `${selectedCount}/${remainingCount} files`
})

const isLoading = ref(false)
const initialLoading = ref(true)
const isLoadingMore = ref(false) // 加载更多状态
const reachedEnd = ref(false) // 是否已经到达列表底部
const isInitialLoad = ref(true) // 是否是初次加载
const isFirstPageEmpty = ref(false) // 第一页是否为空

const isCanOperateDocument = computed(() => {
  return activeItem.value ? [3, 4].includes(activeItem.value.permission) : false
})

// 查询参数
const defaultParams = {
  pageIndex: 1,
  pageSize: 20,
  keyword: '',
  sortBy: 'createTime', // 默认按创建时间排序
  sort: 'desc', // 默认降序排列
  getType: 1
}

// 设置知识库菜单数据
const setKBMenus = (data: IKBItem[]) => {
  const personDate = data.filter((item) => item.knowledgeBaseType === KBType.PERSON)
  const teamDate = data.filter((item) => item.knowledgeBaseType === KBType.TEAM)
  Object.assign(kbMenus, [
    { type: KBType.PERSON, title: 'Private', data: personDate },
    { type: KBType.TEAM, title: 'Shared', data: teamDate }
  ])

  // 设置活动项目并加载文件
  if (personDate.length > 0) {
    activeItem.value = personDate[0]
  } else if (teamDate.length > 0) {
    activeItem.value = teamDate[0]
  } else {
    activeItem.value = null
  }

  // 清除任何之前选择的文件
  selectedFiles.value = []

  // 清除文件列表
  dataSource.value = []

  // 如果存在活动项，则加载文件
  if (activeItem.value && activeItem.value.entityId) {
    getFileList(activeItem.value.entityId)
  }
}

// 获取知识库列表
const getKBList = async () => {
  try {
    initialLoading.value = true
    const res = await knowledgeBaseApi.getKBList()
    if (res.status == 200 && res.data) {
      if (typeof res.data === 'object' && 'success' in res.data && res.data.success) {
        const kbItems =
          'data' in res.data && Array.isArray(res.data.data) ? (res.data.data as IKBItem[]) : []
        setKBMenus(kbItems)
      } else {
        initialLoading.value = false
      }
    }
  } catch (error) {
    initialLoading.value = false
  }
}

// 获取文件列表
const getFileList = (knowledgeId?: string, isLoadMore = false, extraParams = {}) => {
  const entityId = knowledgeId || activeItem.value?.entityId

  if (!entityId) {
    initialLoading.value = false
    return
  }

  const messageParams = {
    ...defaultParams,
    knowledgeId: entityId,
    knowledgeBaseId: activeItem.value?.knowledgeBaseId,
    ...extraParams
  }

  // 设置加载状态
  if (isLoadMore) {
    isLoadingMore.value = true
  } else {
    isLoading.value = true
  }

  knowledgeBaseApi
    .getKBFileList(messageParams)
    .then((response) => response.data)
    .then((res: any) => {
      const { code, data } = res
      if (code === 200 && data) {
        const { documentList, total } = data

        // 如果是首页且没有数据，标记首页为空
        if (!isLoadMore && (!documentList || documentList.length === 0)) {
          isFirstPageEmpty.value = true
        } else if (!isLoadMore) {
          isFirstPageEmpty.value = false
        }

        if (isLoadMore && documentList && documentList.length > 0) {
          // 加载更多时，将新数据追加到现有数据
          dataSource.value = [...dataSource.value, ...documentList]
        } else if (!isLoadMore) {
          // 不是加载更多（是首次加载或重新加载），直接替换数据
          dataSource.value = documentList || []
          isInitialLoad.value = false
        } else if (isLoadMore && (!documentList || documentList.length === 0)) {
          // 加载更多但没有更多数据
          reachedEnd.value = true
        }

        // 判断是否还有更多数据可加载
        const hasMore = documentList && documentList.length === defaultParams.pageSize
        if (!hasMore) {
          // 没有更多数据了
          if (isLoadMore) {
            reachedEnd.value = true
          }
        } else {
          reachedEnd.value = false
        }
      } else {
        if (!isLoadMore) {
          dataSource.value = []
          isFirstPageEmpty.value = true
          isInitialLoad.value = false
        }
      }
    })
    .catch((error) => {
      if (!isLoadMore) {
        dataSource.value = []
        isFirstPageEmpty.value = true
        isInitialLoad.value = false
      }
    })
    .finally(() => {
      isLoading.value = false
      initialLoading.value = false
      isLoadingMore.value = false
    })
}

// 监听表格滚动，实现滚动到底部加载更多
const handleTableScroll = (e: Event) => {
  // 如果已经在加载，则不重复触发
  if (isLoading.value || isLoadingMore.value || reachedEnd.value) return

  const target = e.target as HTMLElement
  if (!target) return

  // 判断是否滚动到底部
  if (target.scrollHeight - target.scrollTop - target.clientHeight < 50) {
    defaultParams.pageIndex += 1 // 增加页码
    getFileList(activeItem.value?.entityId, true) // 加载更多数据并追加
  }
}

// 设置滚动监听
const setupScrollListener = () => {
  const tableBody = document.querySelector('.kb-file-table .ant-table-body')
  if (tableBody) {
    tableBody.addEventListener('scroll', handleTableScroll)
  }
}

// 移除滚动监听
const removeScrollListener = () => {
  const tableBody = document.querySelector('.kb-file-table .ant-table-body')
  if (tableBody) {
    tableBody.removeEventListener('scroll', handleTableScroll)
  }
}

const getTaskCount = (entityId: string) => {
  if (!knowledgeStore.knowledgeTaskMap[entityId]) return 0

  const { importingFileList = [], cutingTaskArr = [] } = knowledgeStore.knowledgeTaskMap[entityId]

  const importingTaskArr = importingFileList.filter((importingFileItem) => {
    return importingFileItem.status !== FileStatus.FAILED
  })

  return importingTaskArr.length + cutingTaskArr.length
}

// 点击知识库
const handleKBClick = (kb: IKBItem) => {
  if (kb.entityId === activeItem.value?.entityId) return

  const extraParams: { previousKnowledgeBaseId?: number } = {}
  if (activeItem.value) {
    extraParams.previousKnowledgeBaseId = activeItem.value.knowledgeBaseId
  }

  activeItem.value = kb

  // 设置加载状态，防止闪烁
  isLoading.value = true

  // 重置搜索和参数
  filter.value = ''
  resetDefaultParams()
  reachedEnd.value = false
  isInitialLoad.value = true
  isFirstPageEmpty.value = false

  // 获取选中的知识库文件
  if (kb && kb.entityId) {
    getFileList(kb.entityId, false, extraParams)
    // 重新设置滚动监听
    setTimeout(setupScrollListener, 500)
  }
}

// 点击搜索
const handleSearch = () => {
  resetDefaultParams()
  defaultParams.keyword = filter.value.trim()
  dataSource.value = []
  reachedEnd.value = false
  isInitialLoad.value = true
  isFirstPageEmpty.value = false

  if (activeItem.value && activeItem.value.entityId) {
    getFileList(activeItem.value.entityId)
    // 重新设置滚动监听
    setTimeout(setupScrollListener, 500)
  }
}

// 重置参数
const resetDefaultParams = () => {
  defaultParams.pageIndex = 1
  defaultParams.keyword = ''
}

const columns = [
  {
    title: 'Name',
    dataIndex: 'documentName',
    key: 'documentName',
    width: '71%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true,
    responsive: ['xs', 'sm', 'md', 'lg', 'xl']
  },
  {
    title: 'Modified',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: '29%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true,
    responsive: ['sm', 'md', 'lg', 'xl']
  }
]

const privateKBColumns = [
  {
    title: 'Name',
    dataIndex: 'documentName',
    key: 'documentName',
    width: '71%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true,
    responsive: ['xs', 'sm', 'md', 'lg', 'xl']
  },
  {
    title: 'Modified',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: '29%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true,
    responsive: ['sm', 'md', 'lg', 'xl']
  }
]

const getFileKey = (file: IKBFileItem) => {
  return file.documentId
}

const handleWarningModalOk = () => {
  showWarningModal.value = false
}

const onSelectChange = (selectedRowKeys: string[]) => {
  const limit = remainingSelectionLimit.value
  const currentSelectedIds = selectedFiles.value.map((file) => file.documentId)
  const newSelections = selectedRowKeys.filter((id) => !currentSelectedIds.includes(id))
  const deselections = currentSelectedIds.filter((id) => !selectedRowKeys.includes(id))
  const totalSelected = selectedFiles.value.length + newSelections.length - deselections.length

  // 检查是否反选
  const isDeselecting = deselections.length > 0 && newSelections.length === 0
  const isSelectingAll = selectedRowKeys.length === dataSource.value.length

  // 如果是反选操作不显示警告弹窗
  if (isDeselecting) {
    // 移除未选中的文件 - 只移除当前知识库的文件
    deselections.forEach((id) => {
      const index = selectedFiles.value.findIndex(
        (file) => file.documentId === id && file.knowledgeId === activeItem.value?.entityId
      )
      if (index !== -1) {
        selectedFiles.value.splice(index, 1)
      }
    })
    return
  }

  // 如果是全选操作且超过限制，显示警告弹窗
  if (isSelectingAll && totalSelected > limit) {
    const unselectedIds = dataSource.value
      .map((item) => item.documentId)
      .filter((id) => !selectedRowKeys.includes(id))

    // 移除未选中的文件 - 只移除当前知识库的文件
    unselectedIds.forEach((id) => {
      const index = selectedFiles.value.findIndex(
        (file) => file.documentId === id && file.knowledgeId === activeItem.value?.entityId
      )
      if (index !== -1) {
        selectedFiles.value.splice(index, 1)
      }
    })

    // 添加新选中的文件
    selectedRowKeys.forEach((id) => {
      const isSelected = selectedFiles.value.some((file) => file.documentId === id)
      if (!isSelected) {
        const fileToAdd = dataSource.value.find((item) => item.documentId === id)
        if (fileToAdd) {
          selectedFiles.value.push({
            ...fileToAdd,
            knowledgeId: activeItem.value!.entityId!
          })
        }
      }
    })

    // 显示警告弹窗
    // showWarningModal.value = true
    return
  }

  // 如果不是全选操作但超过限制，只选择允许的数量
  if (totalSelected > limit) {
    const canAddCount = limit - selectedFiles.value.length
    const allowedNewSelections = newSelections.slice(0, canAddCount)
    const finalSelections = [
      ...currentSelectedIds.filter((id) => !deselections.includes(id)),
      ...allowedNewSelections
    ]

    showWarningModal.value = true

    // 清空当前选择
    selectedFiles.value = []

    // 只添加允许的文件
    finalSelections.forEach((id) => {
      const fileToAdd = dataSource.value.find((item) => item.documentId === id)
      if (fileToAdd) {
        selectedFiles.value.push({
          ...fileToAdd,
          knowledgeId: activeItem.value!.entityId!
        })
      }
    })

    return
  }

  // 正常选择
  const unselectedIds = dataSource.value
    .map((item) => item.documentId)
    .filter((id) => !selectedRowKeys.includes(id))

  unselectedIds.forEach((id) => {
    const index = selectedFiles.value.findIndex(
      (file) => file.documentId === id && file.knowledgeId === activeItem.value?.entityId
    )
    if (index !== -1) {
      selectedFiles.value.splice(index, 1)
    }
  })

  // 添加新选中的文件
  selectedRowKeys.forEach((id) => {
    const isSelected = selectedFiles.value.some((file) => file.documentId === id)
    if (!isSelected) {
      const fileToAdd = dataSource.value.find((item) => item.documentId === id)
      if (fileToAdd) {
        selectedFiles.value.push({
          ...fileToAdd,
          knowledgeId: activeItem.value!.entityId!
        })
      }
    }
  })
}

const customRow = (record: IKBFileItem) => {
  return {
    onClick: (event: MouseEvent) => {
      const index = selectedFiles.value.findIndex((file) => file.documentId === record.documentId)
      if (index === -1) {
        // 如果未选中，检查是否超过限制
        const totalAfterAdding = props.alreadySelectedCount + selectedFiles.value.length + 1
        if (totalAfterAdding > MAX_SELECT_COUNT) {
          showWarningModal.value = true
          return
        }
        // 添加到选择中
        selectedFiles.value.push({ ...record, knowledgeId: activeItem.value!.entityId! })
      } else {
        // 如果已选中，从选择中删除
        selectedFiles.value.splice(index, 1)
      }
    }
  }
}

const tableChange = (
  pagination: any,
  filters: any,
  { field, order }: { field: string; order: string }
) => {
  if (order) {
    defaultParams.sortBy = field
    defaultParams.sort = order === 'ascend' ? 'asc' : 'desc'
  } else {
    defaultParams.sortBy = 'createTime' // 重置为默认排序字段
    defaultParams.sort = 'desc'
  }
  defaultParams.pageIndex = 1

  // 重置状态
  reachedEnd.value = false
  isFirstPageEmpty.value = false

  // 清除现有数据
  dataSource.value = []

  // 使用新排序参数重新加载数据
  if (activeItem.value && activeItem.value.entityId) {
    getFileList(activeItem.value.entityId)
    // 重新设置滚动监听
    setTimeout(setupScrollListener, 500)
  }
}

const handleConfirm = () => {
  if (selectedFiles.value.length > 0) {
    // 检查是否会超过总限制
    const totalAfterAdding = props.alreadySelectedCount + selectedFiles.value.length
    if (totalAfterAdding > MAX_SELECT_COUNT) {
      showWarningModal.value = true
      return
    }

    const fileInfoArray = selectedFiles.value.map((file) => ({
      documentId: file.documentId,
      documentName: file.documentName,
      knowledgeId: file.knowledgeId
    }))

    emit('confirm', fileInfoArray)
  }
}

const handleUpload = () => {
  // 使用可选链和类型断言来处理 TypeScript 错误
  ;(window.api as any)
    ?.selectFiles?.({
      source: activeItem.value?.entityId,
      isLimitFileCount: false
    })
    .then((res: any) => {})
}

onMounted(() => {
  getKBList()
  // 设置滚动监听
  setTimeout(setupScrollListener, 1000)
})

onBeforeUnmount(() => {
  removeScrollListener()
})
</script>

<style lang="less" scoped>
.kb-select-content {
  display: flex;
  height: 450px;
  width: 100%;
  max-width: 100%;
  min-width: 730px;
  overflow: hidden;
  border-top: 1px solid #e5e7ec;
}

/* 只针对当前组件的样式 */
.kb-select-wrapper {
  :global(.ant-modal-root .ant-modal-mask) {
    border-radius: 10px;
    max-width: 98%;
    max-height: 97%;
    overflow: hidden;
    margin: auto;
  }

  :global(.kb-select-modal .ant-modal-content) {
    padding: 0;
    border-radius: 10px;
    min-width: 750px;
  }

  :global(.kb-select-modal .ant-modal) {
    min-width: 750px;
  }

  :global(.kb-select-modal .ant-modal .ant-modal-close) {
    top: 8px;
    right: 10px;
  }

  :global(
    .kb-select-modal
      .ant-modal-confirm
      .ant-modal-confirm-body
      .ant-modal-confirm-title
      + .ant-modal-confirm-content
  ) {
    max-width: 100%;
    margin-top: 0px;
    margin-bottom: 0px;
  }

  :global(.kb-select-modal .ant-modal-confirm .ant-modal-confirm-body .ant-modal-confirm-title) {
    color: #696969;
    background: #f8f9fb;
    font-weight: 600;
    font-size: 14px;
    padding: 5px 5px 5px 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    display: flex;
    align-items: center;
  }
}

// 左侧样式
.kb-select-sider {
  width: 210px;
  min-width: 180px;
  flex-shrink: 0;
  border-right: 1px solid #e5e7ec;
  overflow-y: auto;
  background: #f8f9fb;

  &-title {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    display: table-cell;
    vertical-align: middle;
    padding: 0 16px;
    height: 40px;
  }

  &-ul {
    color: #000000;
    padding: 0;
    margin: 0;

    &-item {
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
      padding: 9px 16px;
      margin: 4px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &:hover {
        background: #f5f2fe;
        border-radius: 2px;
      }

      &.selected {
        background: #f5f2fe;
        border-radius: 2px;
        color: #6441ab;
        font-weight: 600;
      }

      .loading-icon {
        color: #6441ab;
      }

      section {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    &-title {
      font-size: 14px;
      line-height: 22px;
      color: var(--text-color4);
      height: 40px;
      padding: 0 16px;
      margin: 4px 0;
      display: flex;
      align-items: center;
    }

    &-empty {
      display: flex;
      border: 1px solid #e5e7ec;
      padding: 9px 15px;
      border-radius: 2px;

      label:first-child {
        margin-right: 9px;
      }

      label:last-child {
        line-height: 20px;
      }
    }
  }

  &-itemname {
    display: inline-block;
    width: 90%;
    color: #000000;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 右侧样式
.kb-select-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 0 0 0;
  overflow: hidden;
  box-sizing: border-box;
  height: 100%;

  .kb-empty-center-panel {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 350px;
    text-align: center;
    width: 100%;

    .kb-file-container__empty-icon {
      width: 72px;
      height: 72px;
      margin-bottom: 12px;
      background: url('@renderer/assets/images/<EMAIL>') no-repeat;
      background-size: 100%;
    }

    .kb-empty-icon-bg {
      margin-bottom: 32px;
    }

    .kb-empty-desc {
      min-width: 800px;
      font-weight: 400;
      margin-bottom: 24px;
      color: #3f3f46;
    }

    .kb-file-container__empty-button-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 8px;
    }

    .kb-file-container__empty-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: calc(100vh - 100px);
    }

    .kb-file-container__import-button {
      height: initial;
      margin-right: 12px;
      padding: 4px 15px;
      vertical-align: middle;
    }

    .kb-file-container__import-button-icon {
      margin-right: 5.5px;
      vertical-align: initial;
      color: #fff;
    }

    .kb-empty-import-btn {
      background: #7c4dcc;
      color: #fff;
      border: none;
      border-radius: 4px;
      padding: 10px 32px;
      font-size: 18px;
      font-weight: 400;
      cursor: pointer;
      outline: none;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.2s;
    }

    .kb-empty-import-btn:hover {
      background: #6a3bb3;
    }

    .kb-empty-import-btn .plus {
      font-size: 22px;
      font-weight: 500;
      margin-right: 8px;
      line-height: 1;
    }
  }
  &-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 10px 10px 10px;

    .title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #000000;
      max-width: 60%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &-tool {
    margin: 0;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  &-search-input {
    width: 168px;
    height: 32px;
    border-color: #f2f3f5;
    background: #f2f3f5;
    vertical-align: middle;

    :deep(.ant-input) {
      background-color: #f2f3f5;

      &::placeholder {
        color: var(--text-color4);
      }
    }
  }

  &-search-button {
    padding: 0px;
    width: 14px;
    height: 14px;
    line-height: 14px;
    border: none;
  }

  &-refresh-button {
    height: initial;
    padding: 4px 10.46px;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  &-refresh-button-icon {
    vertical-align: middle;
    color: #3b3b3b;
  }

  &-table {
    flex: 1;
    overflow: hidden;
    width: 100%;
    position: relative;
    height: calc(100% - 100px);
    display: flex;
    flex-direction: column;

    :deep(.ant-table) {
      height: 100%;
      overflow: hidden;
      width: 100%;

      .ant-table-container {
        border-radius: 0;
        width: 100%;
        height: 100%;
      }

      .ant-table-body {
        height: 100%;
        max-height: none !important;
        overflow-y: auto !important;
      }

      .ant-table-content {
        width: 100%;
        height: 100%;
      }
    }

    // 添加底部加载更多的样式
    .kb-loading-more {
      text-align: center;
      padding: 10px 0;
      background-color: rgba(245, 242, 254, 0.5);
      color: #6441ab;
      font-size: 12px;

      .loading-text {
        margin-left: 8px;
        display: inline-block;
      }
    }

    // 添加没有更多数据的样式
    .kb-no-more-data {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0;
      background-color: #f8f9fb;
      color: #6441ab;
      font-size: 13px;
      font-weight: 500;
      border-top: 1px solid #e5e7ec;
      position: relative;
      margin-top: 5px;
      z-index: 10;
      box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);

      .no-more-text {
        margin-left: 8px;
      }
    }
  }

  :deep(.kb-select-main-table-row) {
    .ant-checkbox-wrapper {
      display: none;
    }

    .ant-checkbox-wrapper-checked {
      display: inline-flex;
    }

    &:hover {
      .ant-checkbox-wrapper {
        display: inline-flex;
      }
    }
  }

  &-file-name-wrapper {
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &-file-type-svg {
    flex-shrink: 0;
    margin-right: 8px;
    vertical-align: bottom;
    color: transparent;
  }
}

.kb-select-footer {
  text-align: center;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 16px;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top: 1px solid #e5e7ec;

  .footer-actions {
    display: flex;
    align-items: center;
  }

  .file-selection-status {
    margin-right: 16px;
  }

  .footer-cancel-button {
    margin-left: 8px;
    color: black;
    background: #f2f3f5;
    border: none;
    width: 120px;
    border-radius: 3px;

    &:hover {
      background: #f5f2fe;
    }
  }

  .footer-confirm-button {
    margin-left: 8px;
    width: 120px;
    border-radius: 3px;
  }
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #6441ab;
  border-color: #6441ab;
}

:deep(.ant-checkbox-inner::after) {
  border-color: #fff;
}

:deep(.kb-file-table) {
  height: 100%;

  .ant-spin-nested-loading,
  .ant-spin-container,
  .ant-table,
  .ant-table-container {
    height: 100%;
  }

  .ant-table-body {
    height: calc(100% - 35px) !important;
    max-height: none !important;
    overflow-y: auto !important;
  }
}

// 响应式调整表格列
:deep(.ant-table-thead > tr > th) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.ant-table-tbody > tr > td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.kb-select-content {
  min-width: 320px;
}

.kb-select-main-header {
  display: flex;
  justify-content: space-between;
  height: auto !important;
}

:deep(.kb-select-sider-ul-item.selected .kb-select-sider-itemname) {
  color: #6441ab;
  font-weight: 600;
}

.kb-loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.kb-loading-result {
  :deep(.ant-result-icon) {
    margin-bottom: 16px;
    color: #6441ab;
  }

  :deep(.ant-result-title) {
    font-size: 16px;
    color: #3f3f46;
  }

  width: 730px;
  margin: 0 auto;
}

.file-limit-warning-modal {
  :deep(.ant-modal-wrap) {
    z-index: 9999 !important;
  }

  :deep(.ant-modal-mask) {
    z-index: 9998 !important;
    background-color: rgba(0, 0, 0, 0.65) !important;
  }

  :deep(.ant-modal) {
    z-index: 9999 !important;
  }

  :deep(.ant-modal-header) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    background-color: #fff;
  }

  :deep(.ant-modal-body) {
    padding: 24px;
    text-align: center;
    background-color: #fff;
  }

  :deep(.ant-modal-footer) {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    background-color: #fff;
  }

  .warning-modal__title {
    display: flex;
    align-items: center;
    gap: 8px;

    &-icon {
      color: #faad14;
      flex-shrink: 0;
    }

    &-text {
      font-size: 16px;
      font-weight: 600;
      color: #000000;
    }
  }

  .warning-modal__body {
    font-size: 14px;
    color: #3f3f46;
    line-height: 1.5;
    margin: 8px 0;
  }

  :deep(.ant-btn-primary) {
    background-color: #6441ab;
    border-color: #6441ab;

    &:hover {
      background-color: #7c4dcc;
      border-color: #7c4dcc;
    }
  }
}
</style>
