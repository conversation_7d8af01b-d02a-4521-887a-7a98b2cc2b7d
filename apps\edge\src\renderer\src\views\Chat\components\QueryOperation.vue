<template>
  <div class="query-page-box">
    <SvgIcon
      class="copysvg"
      name="copy"
      :size="'20'"
      v-if="!isCopyDone"
      color="#52525B"
      @click="clickCopy"
    />
    <SvgIcon class="copyDonesvg" name="CopyDone" :size="'20'" v-else color="#6441AB" />
  </div>
</template>
<script setup lang="ts">
import { Question } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { ref } from 'vue'
const props = defineProps<{
  queryItem: Question | undefined
}>()
const isCopyDone = ref(false)
const clickCopy = () => {
  navigator.clipboard
    .writeText(props.queryItem?.questionData.content || 'copy failed')
    .then(() => {
      isCopyDone.value = true
      setTimeout(() => {
        isCopyDone.value = false
      }, 2000)
    })
    .catch((err) => {
      console.error('Failed to copy text: ', err)
    })
}
</script>
<style scoped lang="less">
.query-page-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  :deep(.svgClass) {
    cursor: pointer;
    path {
      fill: currentColor !important;
    }
  }
}
</style>
