<template>
  <div class="answer-content_DeepThinking" v-if="answerItem?.isDeepThink">
    <div
      class="answer-loading_thinkingTitle"
      @click="clickExpanded"
      :class="{
        thinkcompleted:
          ChatStatus.SUCCESS === answerItem?.chatStatusAnswer.value ||
          ChatStatus.DONE === answerItem?.chatStatusAnswer.value
      }"
    >
      {{ thinkingTitle }}
      <SvgIcon class="svgMargin" name="SourceTop" size="16" v-if="isExpanded" />
      <SvgIcon
        class="svgMargin"
        name="WebSearchDownward"
        color="rgba(82, 82, 91, 1)"
        size="16"
        v-else
      />
    </div>
    <Skeleton
      active
      class="skeleton-bar"
      style="width: 500px"
      v-if="answerItem?.chatStatusAnswer.value == ChatStatus.START"
    ></Skeleton>
  </div>

  <div class="answer-loading" @click="undeepThinkingReference" v-if="!answerItem?.isDeepThink">
    <!-- <Vue3Lottie :animationData="AstronautJSON" :height="23" :width="23" /> -->
    <div class="answer-loading_title">
      {{ unDeepThinkTitle }}
    </div>
    <button
      class="unthinkingTitle-Icon"
      v-if="answerItem?.chatStatusAnswer.value == ChatStatus.DONE && unDeepThinkTitle.length"
    ></button>
    <Skeleton
      active
      class="skeleton-bar"
      v-if="answerItem?.chatStatusAnswer.value == ChatStatus.ONGOING"
      style="width: 500px"
    ></Skeleton>
  </div>
  <img
    class="answer-img"
    v-if="
      answerItem?.chatStatusAnswer.value === ChatStatus.ONGOING && !answerItem?.answerData.content
    "
    src="../../../assets/answerPng.png"
  />
  <div class="answer-content" @click="handleContentClick">
    <div class="thinkingLine">
      <div v-html="tinkingPart" v-if="isExpanded"></div>
    </div>
    <div v-html="refAnswer" id="answerid"></div>
  </div>
  <DocumentTable
    :answerItem="answerItem"
    v-if="answerItem?.documentList && answerItem?.documentList.length > 0"
  />
  <div class="answer-agent" v-if="answerItem?.isAgent" @click="skipAgent">
    <SvgIcon name="AgentVideo" :size="'18'" />
    Open {{ answerItem?.isAgent }} Creation Assistant
  </div>
  <previewModle v-model:modelValue="showPreviewModal" :docObj="previewFile" />
</template>

<script setup lang="ts">
import { ChatStatus } from '@libs/a-comps'
import { Answer } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import DocumentTable from './documentTable.vue'
import { AgentVo } from '@/types'
import router from '@/renderer/src/routers'
import { Skeleton } from 'ant-design-vue'
import { Vue3Lottie } from 'vue3-lottie'
import AstronautJSON from '../../../assets/Astronaut.json'
import { emitter } from '@/utils/EventBus'
import { onMounted, onUnmounted, watch, nextTick, reactive } from 'vue'
import { ref, computed } from 'vue'
import { historyBaseApi } from '@/renderer/src/api/historyBase'
import mySDK from '@renderer/views/Chat/sdkService'
import { DocumentSource, refciticlesNum } from '@libs/a-comps/ChatBaseComponent/types'
import {
  DocumentListType,
  ReferencesType,
  RefKBNameType
} from '@libs/a-comps/ChatBaseComponent/types'
import { getKBName } from '@/renderer/src/api/chatBase'
import { Console } from 'console'

let thinkingTitle = ref('Thinking...')
let refAnswer = ref('')
let tinkingPart = ref('')
let unDeepThinkTitle = ref('Analyzing your request...')
const isExpanded = ref(true)
let currentisExpanded = ref(false)
let iscurrentDeepThink = ref(false)
let isShowHistoryPanel = ref(false)
const showPreviewModal = ref(false)
const previewFile = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: '',
  knowledgeBaseId: ''
})
let currentpageindex = ref<number>(0)

const regex = /<corner>([\s\S]*?)<\/corner>/g
const regexSec = /<corner>(\d+)<\/corner>/g
const regexThinkTitle = /<p>([\s\S]*?)<\/p>/g
const regexThinkCompleteLine = /<p>([\s\S]*?)<br><br><\/p>/g
const regexTable = /<table>/g
const regexTh = /<th>/g
const regexTd = /<td>/g
const regexThinkingResult = /<span>([\s\S]*?)<\/span>/g
const regexthinkSearching = /<div class="tool_selected">([\\s\\S]*?)<\/div>/g
const windowWidth = ref(0)
const windowHeight = ref(0)

emitter.on('clear-answer-content', (answerId: string) => {
  if (props.answerItem && props.answerItem.id === answerId) {
    refAnswer.value = ''
    tinkingPart.value = ''
    unDeepThinkTitle.value = 'Analyzing your request...'
    if (props.answerItem.references) {
      props.answerItem.references = []
    }
    if (props.answerItem.citations) {
      props.answerItem.citations = {}
    }
  }
})

const clickExpanded = () => {
  isExpanded.value = !isExpanded.value
}


const undeepThinkingReference = () => {
  emitter.emit('ref-click-2', true)
  
  // 根据当前页面索引获取对应的references
  const references = currentpageindex.value === 1 
    ? props.answerItem?.references
    : props.answerItem?.olds?.[currentpageindex.value - 2]?.references
  
  if (references) {
    emitter.emit('ref-click-1', references)
  }
}

interface ExtendedWindow extends Window {
  userInitiatedScroll?: boolean
  originalScrollTo?: Function
  scrollTimer?: number
  handleCiticleContainerClick?: (url: string) => void
  handleCiticlefileClick?: (documentName: string) => void
}

enum ThinkingStatus {
  ThinkingONGOING = 'Thinking...', // 用户请求成功，当前请求结束
  ThinkingComplete = 'Thinking completed'
}

const props = defineProps<{
  isDeepThink: boolean | undefined
  answerItem: Answer | undefined
  agentObj: {
    video: AgentVo
    email: AgentVo
  }
}>()

onMounted(() => {
  initScroll()
  // 初始化时立即重置滚动标记
  resetScrollFlag()

  // 监听New Chat事件
  window.addEventListener('newChatCreated', handleNewChat)
  // 监听内容重生成事件
  window.addEventListener('contentRegenerated', handleContentRegenerated)
  currentisExpanded.value = props.isDeepThink
  ;(window as ExtendedWindow).handleCiticleContainerClick = handleCiticleContainerClick
  ;(window as ExtendedWindow).handleCiticlefileClick = handleCiticlefileClick
})
onMounted(() => {
  emitter.on('is-deepThinking', (isDeepThink: boolean) => {
    currentisExpanded.value = isDeepThink
  })
  emitter.on('history-page-index', (index: number) => {
    currentpageindex.value = index
  })
})

onMounted(() => {
  emitter.on('getcurrent-width', (width: number, historyCollapsed: boolean) => {
    isShowHistoryPanel.value = !historyCollapsed
  })
})

// 处理窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  handleResize() // 初始化
  window.addEventListener('resize', handleResize)
})

onMounted(() => {
  window.addEventListener('mousemove', (e) => {
    nextTick(() => {
      const dynWidth = isShowHistoryPanel.value ? 200 : 0 // 200是历史记录宽度
      // web 角标
      const citicleWebArray = document.getElementsByClassName('tooltip-content')
      if (citicleWebArray) {
        const hoverArea = document.getElementById('answerid')
        if (hoverArea) {
          const tw = 420
          const th = 150
          let left = 0
          let top = 0
          // console.log(`e.clientX = ${e.clientX}, e.clientY = ${e.clientY}`)
          // 右侧空间不足,不显示历史记录时
          if (!isShowHistoryPanel.value) {
            if (e.clientX + tw > windowWidth.value - 50) {
              left = -(e.clientX + tw - windowWidth.value + 200)
            }
            // 下侧空间不足，向上靠
            if (e.clientY + th > windowHeight.value - 170) {
              top = top - th - 15
            }
          }
          // 右侧空间不足,显示历史记录时
          else {
            if (e.clientX + tw > windowWidth.value - 50) {
              // console.log('e.clientX + tw - windowWidth.value + 50 = ',e.clientX + tw - windowWidth.value + 50)
              left = -(e.clientX + tw - windowWidth.value + 300)
              if (e.clientX + tw - windowWidth.value + 50 > 200) {
                left = -(e.clientX + tw - windowWidth.value + 200)
              } else if (e.clientX + tw - windowWidth.value + 50 > 100)
                left = -(e.clientX + tw - windowWidth.value + 100)
              else if (e.clientX + tw - windowWidth.value + 50 > 50) {
                left = -(e.clientX + tw - windowWidth.value + 80)
              } else if (e.clientX + tw - windowWidth.value + 50 < 50) {
                left = -(e.clientX + tw - windowWidth.value + 70)
              }
            }
            // 下侧空间不足，向上靠
            if (e.clientY + th > windowHeight.value - 170) {
              top = top - th - 15
            }
          }

          for (let i = 0; i < citicleWebArray.length; i++) {
            const currentTooltip = citicleWebArray[i]
            currentTooltip.style.left = `${left}px`
            currentTooltip.style.top = `${top + 18}px`
            if (e.clientY + th > windowHeight.value - 170 && currentTooltip.clientHeight < 140) {
              currentTooltip.style.top = `${top + 35}px`
            }
          }
        }
      }

      //文档角标
      const citicleDocArray = document.getElementsByClassName('tooltip-filecontent')
      if (citicleDocArray) {
        const hoverArea = document.getElementById('answerid')
        if (hoverArea) {
          const rect = hoverArea.getBoundingClientRect()
          const tw = 281
          const th = 60
          let left = 0
          let top = 0

          // 右侧空间不足，向左靠
          if (e.clientX + tw > rect.right) {
            if (rect.right - e.clientX > tw / 2) {
              left = left - tw / 2 + dynWidth
            } else left = left - tw + dynWidth
          }
          // 下侧空间不足，向上靠
          if (e.clientY + th > windowHeight.value - 170) {
            top = top - th - 40
          }
          for (let i = 0; i < citicleDocArray.length; i++) {
            const currentTooltip = citicleDocArray[i]
            currentTooltip.style.left = `${left + 15}px`
            currentTooltip.style.top = `${top + 15}px`
          }
        }
      }
    })
  })
})

onUnmounted(() => {
  restoreScroll()
})

// 重置滚动标记，允许自动滚动
const resetScrollFlag = () => {
  ;(window as ExtendedWindow).userInitiatedScroll = false
}

const handleContentClick = (event: any) => {
  const action = event.target.dataset.action
  if (action.includes('index-')) {
    const parts = action.split('-')
    const reference = Array.from(
      // 使用 Map 存储唯一的 toolIndex，保留第一个出现的对象
      new Map(props.answerItem?.toolType.map((item) => [item.toolIndex, item])).values()
    )
    for (let i = 0; i < parts.length; i++) {
      if (parts[i] === 'index') {
        const number = parts[i + 1]
        // console.log('分步查找的reference', reference)
        emitter.emit('ref-click-2', true)
        emitter.emit('ref-click-1', reference?.[number - 1]?.references)
        return
      }
    }
  }
  switch (action) {
    case 'citicle':
      excuteCiticle(event)
      break
    case 'totalRef':
      emitter.emit('ref-click-2', true)
      // if(props?.answerItem)
      emitter.emit('ref-click-1', props.answerItem?.references)
      break
    default:
  }
}

const excuteCiticle = (event: any) => {
  if (event.target.dataset.action === 'citicle-disabled') {
    return
  }

  const citationIndex = Number(event.target.textContent)
  const citations = props.answerItem?.citations

  if (citations && citations[citationIndex]) {
    const citicle = citations[citationIndex]
    const { documentSource, siteName, url, documentName } = citicle
    if (documentSource === 'network') window.open(url, '_blank')
    else if (documentSource === 'public') {
      // 通知layout展开
      emitter.emit('ref-click-expand', true)
      // 传递citicle
      emitter.emit('ref-click-citicles', citicle)
      console.log('当前citile = ', citicle)
    }
  }
}

const handleCiticleContainerClick = (url: string) => {
  window.open(url, '_blank')
}
const handleCiticlefileClick = (citicle: string) => {
  const citations = props?.answerItem?.citations

  if (citations && Object.keys(citations).length > 0) {
    for (const key in citations) {
      if (citations.hasOwnProperty(key)) {
        const item = citations[key]
        // 检查当前项的documentName是否与目标匹配
        if (item && item.documentName === citicle) {
          // 通知layout展开
          emitter.emit('ref-click-expand', true)
          // 传递citicle
          emitter.emit('ref-click-citicles', item)
          break
        }
      }
    }
  }
}

const initScroll = () => {
  const chatBoxRegion = document.querySelector('.chat-box_region')
  if (chatBoxRegion && !(window as ExtendedWindow).originalScrollTo) {
    // 重写scrollTo方法以支持用户滚动
    const originalScrollTo = chatBoxRegion.scrollTo
    ;(window as ExtendedWindow).originalScrollTo = originalScrollTo
    // 重写scrollTo方法
    chatBoxRegion.scrollTo = function (...args: any) {
      // 如果用户手动滚动，不要自动滚动
      if ((window as ExtendedWindow).userInitiatedScroll) {
        //console.log('Prevented auto-scroll because user is manually scrolling')

        return
      }
      // 否则使用原始的scrollTo
      return originalScrollTo.apply(this, args)
    }

    // 在初始化后立即滚动到底部
    setTimeout(() => {
      chatBoxRegion.scrollTo({
        top: chatBoxRegion.scrollHeight,
        behavior: 'smooth'
      })
    }, 100)
  }

  // 添加滚动事件监听器以检测手动滚动
  chatBoxRegion?.addEventListener(
    'scroll',
    (e) => {
      // 设置标志，当用户手动滚动时
      if (e.isTrusted) {
        ;(window as ExtendedWindow).userInitiatedScroll = true
      }
    },
    { passive: true }
  )
}

// 恢复原始的scrollTo方法
const restoreScroll = () => {
  const chatBoxRegion = document.querySelector('.chat-box_region')
  if (chatBoxRegion && (window as ExtendedWindow).originalScrollTo) {
    chatBoxRegion.scrollTo = (window as ExtendedWindow).originalScrollTo as any
    ;(window as ExtendedWindow).originalScrollTo = undefined
  }

  // 移除事件监听器
  chatBoxRegion?.removeEventListener('scroll', () => {})

  // 清除定时器
  if ((window as ExtendedWindow).scrollTimer) {
    clearTimeout((window as ExtendedWindow).scrollTimer)
  }
}

// 滚动到最新内容的方法
const scrollToLatest = () => {
  const chatBoxRegion = document.querySelector('.chat-box_region')
  if (chatBoxRegion) {
    chatBoxRegion.scrollTo({
      top: chatBoxRegion.scrollHeight,
      behavior: 'smooth'
    })
  }
}

// 监听回答状态变化，只有在生成完后才重置滚动标志
watch(
  () => props.answerItem?.chatStatusAnswer.value,
  (newStatus, oldStatus) => {
    // 当状态从START变为其他状态时，滚动到最新内容
    if (oldStatus === ChatStatus.START && newStatus !== ChatStatus.START) {
      // 滚动到最新内容
      setTimeout(() => {
        scrollToLatest()
      }, 200)
    }
    // 当状态从生成中变为完成时，可以重置滚动标志
    if (newStatus === ChatStatus.SUCCESS) {
      thinkingTitle.value = ThinkingStatus.ThinkingComplete
      // 滚动到最新内容
      setTimeout(() => {
        scrollToLatest()
        // 给用户一些时间阅读，然后再允许自动滚动
        setTimeout(() => {
          ;(window as ExtendedWindow).userInitiatedScroll = false
        }, 3000) // 3秒后恢复自动滚动
      }, 300)
    } else if (newStatus === ChatStatus.DONE) {
      thinkingTitle.value = ThinkingStatus.ThinkingComplete
    } else {
      thinkingTitle.value = ThinkingStatus.ThinkingONGOING
    }
  }
)

const generateThinkingSpan = (content: string, index: number) => {
  const actionParams = `index-${index}`
  // 构建包含动态参数的HTML模板
  return `<div style="
    font-weight:500;
    text-align: center;
    font-size: 14px;
    display: inline-block;
    color:#3B3B3B;"  
    margin: 0;         
    padding: 0;  
    onmouseover="this.style.backgroundColor='#F5F2FE';this.style.color='#6441AB';this.style.cursor='pointer';"
    onmouseout="this.style.color='#3B3B3B';" 
    data-action="${actionParams}">${content}</div>`
}

const webCiticle = (
  citicleindex: number,
  p1: string,
  url: string,
  siteName: string,
  content: string
) => {
  return `<div class="btn-class" data-action="citicle">${p1}
        <div class="tooltip-content" onclick="handleCiticleContainerClick('${url}')">
          <div class="citicle-container">
              <button class="citicles-Icon"></button>
              <div class="tooltip-title" >${siteName}</div>
          </div>    
          <div class="citicle-content">
              <span class="citicleContent leftfix">${content}</span>
              <button class="citicles-goto rightfix"></button>
          </div>
        </div>
    </div>`
}

const documentCiticle = (
  citicleindex: number,
  p1: string,
  documentName: string,
  documentCreateTimeconvert: string
) => {
  return `<div class="btn-class" data-action="citicle">${p1}
        <div class="tooltip-filecontent" onclick="handleCiticlefileClick('${documentName}')">
          <div class="citicle-container clearfix">
              <button class="citiclespdf-Icon leftfix"></button>
              <div class="tooltip-title leftfix" >${documentName}</div>
          </div>
          <div class="citiclefile-content clearfix">
              <button class="citiclespkb-Icon leftfix"></button>
              <div class="kbdiv leftfix"></div>
              <div class="right-item rightfix">${documentCreateTimeconvert}</div>
          </div>
        </div>
    </div>`
}

const fetchKBName = async (id: string) => {
  const res = await getKBName({ knowledgeIds: id })
  if (res.data.data && res.data.data.length > 0) {
    const newFile: RefKBNameType = {
      kbID: id,
      KBName: res.data.data[0].knowledgeName
    }
    //console.log('newFile.kvName = ', newFile.KBName)
    refAnswer.value = refAnswer.value.replace(
      `<div class="kbdiv leftfix"></div>`,
      `<div class="kbdiv leftfix">'${newFile.KBName}'</div>`
    )
  }
}

const GetpkbFileNameForCiticle = () => {
  for (const key in props.answerItem?.citations) {
    // 确保只遍历对象自身的属性
    if (props.answerItem?.citations.hasOwnProperty(key)) {
      if (
        props.answerItem?.citations[key].documentSource === 'public' &&
        props.answerItem?.citations[key].knowledgeId
      ) {
        fetchKBName(props.answerItem?.citations[key].knowledgeId)
      }
    }
  }
}

watch(
  () => [
    props.answerItem?.answerData.content,
    props.answerItem?.citations,
    props.answerItem?.thinkingContent
  ],
  ([newContent, newCitations, newThinkingContent]) => {
    if (newContent) {
      const unescaped =
        newContent
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"') || 'default value'
      // 2.查找匹配项
      const matches = unescaped.match(regex) || 'default value'
      console.log('matches', matches)
      // 3.动态替换<corner>1</corner>角标，并增加hover style
      let stepthinkingindex = 0
      let citicleindex = 0
      refAnswer.value = unescaped
        .replace(regexSec, (match: string, p1: string) => {
          const index = parseInt(p1) - 1
          let obj = newCitations || props?.answerItem?.citations
          if (obj && obj[p1] && Object.keys(obj).length > 0) {
            const {
              documentID,
              documentName,
              documentSource,
              knowleageId,
              documentCreateTime,
              content,
              siteName,
              url
            } = obj[p1]
            const date = new Date(documentCreateTime)
            // 调整时间格式
            const documentCreateTimeconvert =
              date.getFullYear() +
              '-' +
              String(date.getMonth() + 1).padStart(2, '0') +
              '-' +
              String(date.getDate()).padStart(2, '0')
            // 文档类的citicle
            if (documentName) {
              if (documentSource === 'public') citicleindex++
              //citicle pkb pdf type
              return documentCiticle(citicleindex, p1, documentName, documentCreateTimeconvert)
            } else {
              // web 类型角标
              return webCiticle(citicleindex, p1, url, siteName, content)
            }
          } else {
            // 当没有引用数据时，返回一个不可点击的占位符，保持样式一致
            return `<div class="btn-class no-citation" style="cursor: default;" data-action="citicle-disabled">${p1}</div>`
          }
        })
        .replace(regexThinkingResult, (matches: string, content: string) => {
          stepthinkingindex++
          return generateThinkingSpan(content, stepthinkingindex)
        })

      // 4.为table 动态添加边框，背景颜色等style
      refAnswer.value =
        refAnswer.value
          .replace(
            regexTable,
            `<table style="border: 1px solid #ddd;border-collapse: collapse;width: 100%;">`
          )
          .replace(
            regexTh,
            `<th style="border: 1px solid #ddd;padding: 8px;text-align: left; background-color: #f2f2f2;">`
          )
          .replace(regexTd, `<td style="border: 1px solid #ddd;">`) || 'default value'
      // thinkingPart expand
      if (refAnswer.value.includes('<br><br></p>')) {
        const tempRefAnswer = refAnswer.value.replace(
          regexThinkCompleteLine,
          `<p style="
          font-size: 14px;
          color: #6E6E78"
          >$1<br><br></p>`
        )
        const parts = tempRefAnswer.split('<br><br></p>')
        if (parts[0]) {
          tinkingPart.value = parts[0]
            .replace(regexThinkTitle, `<p style="font-size: 14px;color: #6E6E78;margin:0">$1</p>`)
            .replace(/<p>([\s\S]*?)/g, `<p style= "font-size: 14px; color: #6E6E78">$1`)
            .replace(
              regexthinkSearching,
              `<div class="tool_selected" style="display:none">$1</div>`
            )
        }
        if (parts[1]) {
          if (parts[1].includes('<p>')) {
            refAnswer.value = parts[1].replace(
              regexThinkTitle,
              `<p style= "font-size: 16px;">$1</p>`
            )
          } else refAnswer.value = parts[1]
          emitter.emit('answer-interrupt-save', [tinkingPart.value, refAnswer.value])
        }
      } else if (props.answerItem?.chatStatusAnswer.value === ChatStatus.ONGOING) {
        const { references, citations, documentList, stepMode } = props.answerItem
        if (props.answerItem?.stepMode === 'analyze_request') {
          refAnswer.value = refAnswer.value.replace(
            /<p><div class="analyze_request">([\s\S]*?)<\/p>/g,
            `<p><div class="analyze_request" style=" font-size: 14px;color: #6E6E78"></div></p>`
          )
          console.log('analyze_request=', refAnswer.value)
        } else if (props.answerItem?.stepMode === 'searching') {
          refAnswer.value = refAnswer.value.replace(
            /<p><div class="analyze_request">([\s\S]*?)<\/p>/g,
            `<p><div class="analyze_request" style=" font-size: 14px;color: #6E6E78"></div></p>`
          )
          refAnswer.value = refAnswer.value.replace(
            /<p><div class="searching">([\s\S]*?)<\/p>/g,
            `<p><div class="searching" style=" font-size: 14px;display:none"></div></p>`
          )
          unDeepThinkTitle.value = 'Searching…'
          console.log('searching=', refAnswer.value)
        } else if (props.answerItem?.stepMode === 'search_result') {
          console.log('search_result= ', unDeepThinkTitle.value)
          refAnswer.value = refAnswer.value
            .replace(
              /<p><div class="analyze_request">([\s\S]*?)<\/p>/g,
              `<p><div class="analyze_request" style=" font-size: 14px;display:none"></div></p>`
            )
            .replace(
              /<p><div class="searching">([\s\S]*?)<\/p>/g,
              `<p><div class="searching" style=" font-size: 14px;display:none"></div></p>`
            )
            .replace(/<div class="search_result">([\s\S]*?)<\/div>/g, (match, group1) => {
              console.log('#@#################3@#@#@#@#@#')
              unDeepThinkTitle.value = group1 // 保存提取到的内容
              return `<div class="search_result" style=" font-size: 14px;display:none">$1</div>`
            })
        } else if (props.answerItem?.stepMode === 'tool_selected') {
          //
        } else {
          // 替换掉/p中的内容
          refAnswer.value = refAnswer.value
            .replace(
              /<p><div class="analyze_request">([\s\S]*?)<\/p>/g,
              `<p><div class="analyze_request" style=" font-size: 14px;display:none"></div></p>`
            )
            .replace(
              /<p><div class="searching">([\s\S]*?)<\/p>/g,
              `<p><div class="searching" style=" font-size: 14px;display:none"></div></p>`
            )
        }
        console.log('props.answerItem?.stepMode =', props.answerItem?.stepMode)
        refAnswer.value = refAnswer.value.replace(
          regexThinkTitle,
          `<p style=" font-size: 14px;color: #6E6E78">$1</p>`
        )
        // 中断回答时，保存思考过程和回答。
        emitter.emit('answer-interrupt-save', [refAnswer.value])
      }
      // 不走props.answerItem?.answerData.content watch，在这里添加thinking 状态限制。
      else if (props.answerItem?.chatStatusAnswer.value === ChatStatus.SUCCESS) {
        let isSimpleAnswer = true
        if (props.answerItem?.isDeepThink) {
          thinkingTitle.value = ThinkingStatus.ThinkingComplete
        } else {
          refAnswer.value = refAnswer.value
            .replace(
              /<div class="analyze_request">([\s\S]*?)<\/div>/g,
              `<div class="analyze_request" style=" font-size: 14px;display:none"></div>`
            )
            .replace(
              /<div class="searching">([\s\S]*?)<\/div>/g,
              `<div class="searching" style=" font-size: 14px;display:none"></div>`
            )
            .replace(/<div class="search_result">([\s\S]*?)<\/div>/g, (match, group1) => {
              isSimpleAnswer = false
              unDeepThinkTitle.value = group1 // 保存提取到的内容
              return `<div class="search_result" style=" font-size: 14px;display:none;">$1</div>`
            })
          if (isSimpleAnswer) {
            unDeepThinkTitle.value = ''
          }
        }

        // 如果有文档类的citicle，去查找
        if (props?.answerItem?.citations) {
          GetpkbFileNameForCiticle()
        }
      }
      // 去除analyze_request，searching 的显示
      else if (props.answerItem?.chatStatusAnswer.value === ChatStatus.DONE) {
        thinkingTitle.value = ThinkingStatus.ThinkingComplete
        if (props.answerItem.regen && !props.answerItem?.isDeepThink) {
          unDeepThinkTitle.value = ''
        }
        refAnswer.value = refAnswer.value
          .replace(
            /<div class="analyze_request">([\s\S]*?)<\/div>/g,
            `<div class="analyze_request" style=" font-size: 14px;display:none"></div>`
          )
          .replace(
            /<div class="searching">([\s\S]*?)<\/div>/g,
            `<div class="searching" style=" font-size: 14px;display:none"></div>`
          )
          .replace(/<div class="search_result">([\s\S]*?)<\/div>/g, (match, group1) => {
            unDeepThinkTitle.value = group1 // 保存提取到的内容
            return `<div class="search_result" style=" font-size: 14px;display:none;">$1</div>`
          })
      }
      // 统一处理thinkingContent的逻辑（包括初始加载和分页切换）
      const currentThinkingContent = props.answerItem?.thinkingContent
      if (currentThinkingContent) {
        let isSimpleAnswer = true
        console.log('props.answerItem?.thinkingContent', currentThinkingContent)
        stepthinkingindex = 0
        tinkingPart.value = currentThinkingContent
          .replace(/&quot;/g, '"')
          .replace(
            /<div class="analyze_request">([\s\S]*?)<\/div>/g,
            `<div class="searching" style=" font-size: 14px;display:none">$1</div>`
          )
          .replace(
            /<div class="searching">([\s\S]*?)<\/div>/g,
            `<div class="searching" style=" font-size: 14px;display:none">$1</div>`
          )
          .replace(/<p><div class="search_result">([\s\S]*?)<\/div><\/p>/g, (match, group1) => {
            isSimpleAnswer = false
            console.log('group1 ###', group1)
            unDeepThinkTitle.value = group1 // 保存提取到的内容
            return `<p><div class="search_result" style=" font-size: 14px;display:none"></div></p>`
          })
          .replace(regexThinkTitle, `<p style=" font-size: 14px;color: #6E6E78">$1</p>`)
          .replace(regexThinkingResult, (matches: string, content: string) => {
            stepthinkingindex++
            return generateThinkingSpan(content, stepthinkingindex)
          })
          .replace(regexthinkSearching, `<div class="tool_selected" style="display:none">$1</div>`)
        if (isSimpleAnswer) {
          unDeepThinkTitle.value = ''
        }
      }

      // 保存当前的ChatStatus
      emitter.emit('answer-current-status', props.answerItem?.chatStatusAnswer.value)
    }
  },
  { immediate: true, deep: true }
)

watch(
  () => refAnswer.value,
  () => {
    refAnswer.value
  },
  { deep: 1, immediate: true }
)
watch(
  () => refAnswer.value,
  () => {
    refAnswer.value
  },
  { deep: 1, immediate: true }
)
watch(
  () => currentisExpanded.value,
  () => {
    currentisExpanded.value
  },
  { deep: 1, immediate: true }
)
watch(
  () => iscurrentDeepThink.value,
  () => {
    iscurrentDeepThink.value
  },
  { deep: 1, immediate: true }
)
watch(
  () => isShowHistoryPanel.value,
  () => {
    isShowHistoryPanel.value
  },
  { deep: 1, immediate: true }
)

// 监听answerItem变化，重新初始化滚动行为
watch(
  () => props.answerItem,
  (newItem) => {
    // 重置滚动状态
    restoreScroll()
    // 重新初始化滚动行为
    initScroll()
    // 重置用户滚动标志
    resetScrollFlag()
    // 如果是新的answerItem，滚动到底部
    if (newItem) {
      setTimeout(() => {
        scrollToLatest()
      }, 200)
      if (props.answerItem?.chatStatusAnswer.value === ChatStatus.DONE)
        thinkingTitle.value = ThinkingStatus.ThinkingComplete
    }
  }
)

const skipAgent = () => {
  if (!props.answerItem?.isAgent) return

  const agentType = props.answerItem.isAgent as keyof typeof props.agentObj
  let agent = props.agentObj[agentType] as AgentVo

  router.push({
    name: 'agent',
    query: {
      origin: agent.origin,
      id: agent.entityId,
      name: agent.agentName
    }
  })
}

// 处理新建聊天的情况
const handleNewChat = () => {
  registSession()
  // 先恢复原始滚动
  restoreScroll()
  // 短暂延迟后重新初始化和重置
  nextTick(() => {
    setTimeout(() => {
      initScroll()
      resetScrollFlag()
      scrollToLatest()
    }, 300)
  })
}

async function registSession() {
  try {
    await historyBaseApi.registHistory(mySDK.userId)
  } catch (e) {}
}

// 处理内容重生成的情况
const handleContentRegenerated = () => {
  //console.log('Content regenerated, adjusting scroll')
  // 重置滚动标志并滚动到最新内容
  resetScrollFlag()
  // 稍微延迟以确保内容已经渲染
  setTimeout(() => {
    scrollToLatest()
  }, 300)
}
</script>

<style scoped lang="less">
@delay: 2.5;
@delay2: 1.5;
@width: 30%;
.bordered-table {
  border-collapse: collapse;
  width: 100%;
}

.bordered-table,
.bordered-table th,
.bordered-table td {
  border: 1px solid #ddd;
}

.bordered-table th,
.bordered-table td {
  padding: 8px;
  text-align: left;
}

.bordered-table th {
  background-color: #f2f2f2;
}

.thinkingLine {
  //border-left: 2px solid red;
}

.answer-content {
  /*   :deep(p) {
    margin: 0;
    padding: 0;
  }
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  min-height: 20px;
  will-change: contents;
  transition: height 0.1s ease-in-out; */
  font-size: 14px;
  .highlight {
    width: 16px;
    height: 16px;
    font-size: 12px;
    text-align: center;
    border: 0;
    padding: 0;
    vertical-align: middle;
    color: #6441ab;
    background-color: #ebe9f8;
    border-radius: 20px;
    cursor: pointer;
  }
  .highlight:hover {
    background-color: #6441ab;
    color: #ffffff;
    cursor: pointer;
  }
}

.citicle-btn {
  width: 16px;
  height: 16px;
  font-size: 12px;
  display: inline-block;
  text-align: center;
  border: 0;
  padding: 0;
  vertical-align: middle;
  color: #6441ab;
  background-color: #ebe9f8;
  border-radius: 20px;
}

.skeleton-bar {
  ::after {
    background: linear-gradient(
      90deg,
      rgba(141, 100, 221, 0.1) 25%,
      rgba(141, 100, 221, 0.4) 37%,
      rgba(141, 100, 221, 0.2) 63%
    ) !important;
  }

  :deep(.ant-skeleton-title) {
    display: none;
  }
  :deep(.ant-skeleton-paragraph) {
    margin-top: 12px;
    margin-bottom: 0;
    li {
      opacity: 0;
      animation: fadeIn 1s ease-in-out forwards;
      &::after {
        animation-duration: 3s;
      }
      &:nth-of-type(1) {
        animation-delay: ~'@{delay}s';
      }
      &:nth-of-type(2) {
        &::after {
          animation-delay: 1s;
        }

        animation-delay: @delay + 1s;
      }
      &:nth-of-type(3) {
        &::after {
          animation-delay: 2s;
        }

        animation-delay: @delay + 2s;
      }
    }
    > li + li {
      margin-block-start: 5px;
    }
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.answer-loading {
  height: 22px;
  // display: flex;
  align-items: center;
  .lottie-animation-container {
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    margin-top: 2px;
  }
  svg {
    vertical-align: middle;
  }
  &_title {
    display: inline-block;
    vertical-align: middle;
    margin-left: 0px;
    background: linear-gradient(90deg, #8d64dd 25.5%, #858585 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
    font-size: 14;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    > div {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      background: rgba(247, 249, 255, 1);
      animation: typing ~'@{delay2}s' ease-in-out forwards;
    }
    &::after {
      position: absolute;
      top: 0;
      bottom: 20%;
      left: 0%;
      box-shadow: 0px 0px 30px 0px rgba(247, 249, 255, 1);
      width: @width;
      inset-inline-start: -@width;
      // background: linear-gradient(
      //   90deg,
      //   rgba(247, 249, 255, 0.2) 5px,
      //   rgba(255, 255, 255, 0.5) 50%,
      //   rgba(255, 255, 255, 0)
      // );
      background: radial-gradient(rgba(247, 249, 255, 0.6), rgba(255, 255, 255, 0));
      border-radius: 100%;
      animation-name: text-bling;
      animation-duration: 1.4s;
      animation-timing-function: ease;
      animation-iteration-count: infinite;
      animation-delay: ~'@{delay2}s';
      content: '';
    }
  }
  &_thinkingTitle {
    display: inline-block;
    vertical-align: middle;
    margin-left: 0px;
    background: linear-gradient(90deg, #8d64dd 25.5%, #858585 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
    height: 22px;
    font-size: 14;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: flex-end; /* 底部对齐 */
    > div {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      background: rgba(247, 249, 255, 1);
      animation: typing ~'@{delay2}s' ease-in-out forwards;
    }

    &::after {
      position: absolute;
      top: 4;
      bottom: 20%;
      left: 0%;
      box-shadow: 0px 0px 30px 0px rgba(247, 249, 255, 1);
      width: @width;
      inset-inline-start: -@width;

      background: radial-gradient(rgba(247, 249, 255, 0.6), rgba(255, 255, 255, 0));
      border-radius: 100%;
      animation-delay: ~'@{delay2}s';
      content: '';
    }
  }

  @keyframes typing {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  @keyframes text-bling {
    0% {
      transform: translateX(-@width);
    }
    100% {
      transform: translateX(13 * @width);
    }
  }
}
.show-left-border {
  border-left: 4px solid #ff5722; /* 橙色边线 */
  padding-left: 12px;
}
.show-left-border1 {
  border-left: 4px solid yellow; /* 橙色边线 */
  padding-left: 12px;
}

.thinkcompleted {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0px;
  background: linear-gradient(90deg, #696969 25.5%, #858585 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  height: 22px;
  font-size: 14;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-end; /* 底部对齐 */
}

.answer-img {
  width: 100%;
  height: 52px;
  margin-top: 12px;
}
.answer-agent {
  width: 247px;
  height: 28px;
  background: rgba(100, 65, 171, 1);
  color: rgba(255, 255, 255, 1);
  display: flex;
  margin-top: 12px;
  border-radius: 2px;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  line-height: 28px;
  cursor: pointer;

  :deep(.svgClass) {
    margin: 0 8px 0 16px;
  }
}
.svgMargin {
  margin-bottom: 1px;
}

/*  tooltip 容器 - 用于定位 */
.tooltip-wrapper {
  position: relative;
  cursor: pointer;
  color: #2563eb;
  text-decoration: underline dotted;
  padding: 0 4px;
}

/* 自定义 tooltip 基础样式 - 默认隐藏 */
::v-deep .tooltip-content {
  display: none;
  position: absolute;
  z-index: 100;
  min-width: 100px;
  width: 448px;
  height: auto;
  background: white;
  border-radius: 4px;
  left: 50%;
  transform: translateX(-1%);
  top: calc(100% + 2px);
  padding: 14px;
  cursor: pointer;
  box-shadow: 0 16px 36px 0 rgba(0, 0, 0, 0.16);
}

::v-deep .tooltip-filecontent {
  display: none;
  position: absolute;
  z-index: 100;
  min-width: 100px;
  width: 281px;
  height: 60px;
  background: white;
  border-radius: 4px;
  left: 50%;
  transform: translateX(-1%);
  top: calc(100% + 2px);
  padding: 14px;
  cursor: pointer;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.16);
}

::v-deep .citicles-goto {
  width: 16px;
  height: 16px;
  background-image: url('../../../assets/images/arrow-right.png');
  border: 0px;
  background-color: white;
  text-align: right;
  margin-top: 50px;
}

/* 悬停时显示 tooltip */
::v-deep .tooltip-wrapper:hover .tooltip-content {
  display: block;
}

::v-deep .tooltip-wrapper:hover .tooltip-filecontent {
  display: block;
}

::v-deep .btn-class:hover {
  background-color: #6441ab;
  color: white;
}

::v-deep.citicles-container {
}

::v-deep .citicles-Icon {
  width: 20px;
  height: 20px;
  background-image: url('../../../assets/images/webicon.png');
  border-radius: 100px;
  background-size: auto auto;
  display: inline-block;
  vertical-align: top;
  border: 0px;
  margin-left: 0px;
  margin-top: 0px;
  cursor: pointer;
}
::v-deep .citicles-Icon:hover .citicles-goto:hover {
  cursor: pointer;
}
::v-deep .citicles-goto:hover {
  cursor: pointer;
}

::v-deep .kbdiv {
  width: 100px;
  height: 24px;
  text-align: left;
  padding-top: 4px;
  color: #696969;
}

::v-deep .citiclespdf-Icon {
  width: 24px;
  height: 24px;
  background-image: url('../../../assets/images/PDFType.png');
  border-radius: 100px;
  background-size: auto auto;
  display: inline-block;
  vertical-align: top;
  border: 0px;
  cursor: pointer;
}

::v-deep .unthinkingTitle-Icon {
  position: absolute;
  margin-top: 8px;
  margin-left: 6px;
  width: 16px;
  height: 16px;
  background-image: url('../../../assets/images/arrow-greater.png');
  display: inline-block;
  border: 0px;
  cursor: pointer;
  background-color: transparent;
}

::v-deep .citiclespkb-Icon {
  width: 24px;
  height: 24px;
  background-image: url('../../../assets/images/pkbicon.png');
  border-radius: 100px;
  background-size: auto auto;
  display: inline-block;
  vertical-align: top;
  border: 0px;
  cursor: pointer;
}

::v-deep .citicle-content {
  width: 100%;
  height: 70px;
  font-size: 12px;
  line-height: 1.8;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-top: 12px;
  text-align: left;
}

::v-deep .citiclefile-content {
  width: 100%;
  height: auto;
  font-size: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-top: 10px;
}

::v-deep .btn-class {
  text-align: center;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  display: inline-block;
  line-height: 16px;
  border: 0;
  padding: 1px;
  color: #6441ab;
  background-color: #ebe9f8;
  vertical-align: top;
  position: relative;
  margin-left: 3px;
  margin-top: 3px;
}

::v-deep .btn-class:hover .tooltip-content {
  display: block;
  color: black;
}

::v-deep .btn-class:hover .tooltip-filecontent {
  display: block;
  color: black;
}

::v-deep .citicle-container {
  width: 100%;
  display: flex;
  align-items: flex-start;
  line-height: 1.3;
  margin-top: 0;
  vertical-align: top;
}

::v-deep .tooltip-title {
  font-weight: 400;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制显示的行数 */
  -webkit-box-orient: vertical;
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  margin-left: 8px;
  margin-top: 0px;
  text-align: left;
}

::v-deep .clearfix::after {
  content: '';
  display: block;
  clear: both;
}
::v-deep .leftfix {
  float: left;
  color: #696969;
}

::v-deep .rightfix {
  float: right;
}
::v-deep .right-item {
  width: 100px;
  height: 24px;
  text-align: center;
  color: #858585;
  padding-top: 4px;
}
::v-deep ul {
  padding-left: 15px;
  /* 让列表符号显示在内容外侧（浏览器默认是缩进后显示符号，此属性改变显示位置） */
  list-style-position: outside;
}
/* 若想让 li 内容和其他元素对齐更灵活，可额外调整 li 的 margin，比如让 li 左侧有间距时 */
::v-deep li {
  margin-left: 0;
}
::v-deep p {
  margin-top: 5px;
  margin-bottom: 3px;
  padding: 0;
}
/* 当citations无内容时，保持hover样式但不显示tooltip */
::v-deep
  .btn-class[data-action='citicle']:not(:has(.tooltip-content)):not(
    :has(.tooltip-filecontent)
  ):hover {
  background-color: #ebe9f8;
  color: #6441ab;
}

/* 禁用状态的引用按钮 */
::v-deep .btn-class.no-citation {
  cursor: default !important;
  background-color: #ebe9f8;
  color: #6441ab;
}

::v-deep .btn-class.no-citation:hover {
  background-color: #ebe9f8;
  color: #6441ab;
  transform: none;
}
</style>
