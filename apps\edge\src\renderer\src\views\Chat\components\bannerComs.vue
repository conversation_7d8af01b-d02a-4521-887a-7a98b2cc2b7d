<template>
  <div class="banner-box">
    <div class="banner-box_item" v-for="items in data">
      <SvgIcon :name="items.avatar" size="18" :color="items.color" />
      <div>{{ items.title }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'

const data = ref([
  {
    avatar: 'BannerKnowledge',
    title: 'Knowledge Q&A',
    color: '#992E8A'
  },
  {
    avatar: 'BannerVideo',
    title: 'Video Creation',
    color: '#6441AB'
  },
  {
    avatar: 'BannerDocument',
    title: 'Document Summary',
    color: '#0E4BCE'
  },
  {
    avatar: 'BannerAll',
    title: '',
    color: '#0176A9'
  }
])
</script>

<style scoped lang="less">
.banner-box {
  display: flex;

  &_item {
    display: flex;
    padding: 5px 16px;
    justify-content: center;
    align-items: center;
    background: #fff;
    border-radius: 28px;
    cursor: pointer;
    margin-right: 16px;

    div {
      margin-left: 4px;
    }
  }

  &_item:last-child {
    margin: 0;
  }
}
</style>
