<template>
  <div class="native-table-container" v-if="answerItem?.documentList">
    <table class="data-table">
      <thead>
        <tr class="header-row">
          <th class="col-name">Name</th>
          <th class="col-from">From</th>
          <!-- <th class="col-owner">Owner</th> -->
          <th class="col-action">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in answerItem.documentList" :key="index" class="data-row">
          <td class="col-name" :title="item.documentName" @click="getDocId(item)">
            <div class="iconDiv">
              <SvgIcon :name="getFileSvgNameSvgName(item.documentName)" :size="'24'" />
            </div>
            <span class="text-ellipsis">{{ item.documentName }}</span>
          </td>
          <td class="col-from" :title="item.knowledgeName">
            <span class="text-ellipsis">{{ item.knowledgeName }}</span>
          </td>
          <!-- <td class="col-owner">--</td> -->
          <td class="col-action">
            <div class="edit-btn" @click="addFileSearch(item)">
              <SvgIcon name="AskAINowSvg" :size="'10'" />
              AI Ask
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <previewModle v-model:modelValue="showModal" :docObj="docItem" />
</template>
<script setup lang="ts">
import { getFileSvgNameSvgName } from '@/renderer/src/hooks/fileType'
import { DocumentListType } from '@libs/a-comps/ChatBaseComponent/types'
import { Answer } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import previewModle from './previewModle.vue'
import { ref } from 'vue'

const props = defineProps<{
  answerItem: Answer | undefined
}>()

const showModal = ref(false)
const docItem = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: ''
})

const getDocId = async (item: DocumentListType) => {
  // const res = await getFileView(item.documentId)
  try {
    docItem.value = item
    showModal.value = !showModal.value
  } catch (error) {
    console.error('open model error:', error)
  }
}

const addFileSearch = (item: DocumentListType) => {
  if (!props.answerItem) return

  if (props.answerItem.getData().questionId === props.answerItem.getData().questionId) {
    props.answerItem.setSelectFileSearch(item)
  }
}
</script>
<style scoped lang="less">
.native-table-container {
  max-height: 354px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
  margin-top: 12px;

  .data-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;

    tr {
      display: flex !important;
    }

    thead {
      display: table;
      width: calc(100%);

      .header-row {
        background: #f5f7fa;

        th {
          height: 24px;
          padding: 2px 12px;
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
          color: #000;
          text-align: left;
          border-bottom: 1px solid #ebeef5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    tbody {
      display: block;
      max-height: 320px;
      overflow-y: auto;

      .data-row {
        display: table;
        width: 100%;

        td {
          padding: 6px 12px;
          font-size: 14px;
          line-height: 22px;
          border-bottom: 1px solid #ebeef5;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          align-items: center;

          .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
          }
        }

        tr {
          display: inline-block;
          vertical-align: middle;
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:hover {
          background: rgba(242, 243, 245, 1);

          .col-name {
            color: rgba(100, 65, 171, 1);
          }
        }
      }
    }

    .col-name {
      width: 35%;
      cursor: pointer;
    }
    .iconDiv {
      color: white;
      display: inline-block;
    }

    .col-from {
      width: 35%;
    }

    .col-owner {
      width: 10%;
    }

    .col-action {
      width: 20%;
    }

    .edit-btn {
      color: rgba(100, 65, 171, 1);
      cursor: pointer;
      font-size: 12px;
    }
  }
}
</style>
