<template>
  <div :class="'input-box ' + (chat?.isWelcome ? '' : 'input-box-bottom')">
    <!-- <div class="input-box_banner-top" v-if="!chat?.isWelcome">
      <BannerPage />
    </div> -->
    <div class="input-box_content" @dragover="handleDragover" @drop="handleDrop">
      <!-- 上传的文件显示区域 -->
      <div class="file-display" v-if="uploadFiles.length > 0">
        <div class="files-container">
          <div
            class="file-info"
            v-for="(file, idx) in uploadFiles"
            :key="file.name"
            @click="handleFilePreview(file)"
          >
            <span v-if="file.status === 'parsing'" class="loading-circle"></span>
            <span v-else-if="file.status === 'documentCutError'" class="error-icon"></span>
            <SvgIcon v-else :name="getFileIcon(file.name)" size="16" class="upload-svg-icon" />
            <span class="file-name">{{ file.name }}</span>
            <div class="file-delete" @click.stop="removeSingleFile(idx)">
              <SvgIcon name="Close" size="9" />
            </div>
          </div>
        </div>
        <div class="file-clear-btn" @click="batchDelete">
          <SvgIcon name="trash" size="14" />
        </div>
      </div>
      <div class="input-box_content_inp">
        <!-- :autoSize="{ minRows: 2, maxRows: 8 }" -->

        <ATextarea
          v-model:value="inputValue"
          :bordered="false"
          resize="none"
          @pressEnter="pressEnterSend"
          class="nput-box_content_inp_autotextarea"
          placeholder="Ask me a question"
        >
        </ATextarea>
      </div>
      <div class="input-box_content_ability">
        <div class="input-box_content_ability_left">
          <ATooltip v-if="isComponentSmall">
            <template #title>Deep Thinking</template>
            <div
              :class="
                'input-box_content_ability_left_thinking ' +
                (selectThinking ? 'input-box_content_ability_left_select-all' : ' ')
              "
              @click="clickThinking"
            >
              <SvgIcon name="DeepThinking" size="18.5" />
              <div class="input-box__deep-thinking-button-text">Deep Thinking</div>
            </div>
          </ATooltip>
          <template v-else>
            <div
              :class="
                'input-box_content_ability_left_thinking ' +
                (selectThinking ? 'input-box_content_ability_left_select-all' : ' ')
              "
              @click="clickThinking"
            >
              <SvgIcon name="DeepThinking" size="18.5" />
              <div>Deep Thinking</div>
            </div>
          </template>
          <div
            :class="[
              'input-box_content_ability_left_websearch',
              { 'input-box_content_ability_left_select-all': showWebSearchDropdown },
              { 'both-active': webSearchEnabled && hasAnyKBSelected }
            ]"
            @click="toggleWebSearchDropdown"
          >
            <div class="websearch-active-icons">
              <SvgIcon
                name="WebSearch"
                size="16"
                :class="{ 'inactive-icon': !webSearchEnabled, 'active-icon': webSearchEnabled }"
              />
              <SvgIcon
                name="KB"
                size="14"
                :class="{
                  'inactive-icon': !hasAnyKBSelected,
                  'active-icon': hasAnyKBSelected
                }"
              />
              <SvgIcon name="arrowDown" size="14" :class="{ 'inactive-icon': !hasAnyKBSelected }" />
            </div>
            <!-- Web Search Dropdown -->
            <div
              :class="['websearch-dropdown', { 'websearch-dropdown-top': !chat?.isWelcome }]"
              v-if="showWebSearchDropdown"
              @click.stop
            >
              <div class="dropdown-item">
                <div class="dropdown-item-label">
                  <SvgIcon name="WebSearch" size="16" />
                  <span>Web Search</span>
                </div>
                <a-checkbox v-model:checked="webSearchEnabled" />
              </div>
              <div class="dropdown-item kb-item">
                <div class="dropdown-item-label">
                  <SvgIcon name="KB" size="14" />
                  <span>Knowledge Base</span>
                </div>
                <a-checkbox
                  :checked="isAllKBSelected"
                  :indeterminate="isKBIndeterminate"
                  @change="handleKBChecked"
                />
              </div>
              <!-- Knowledge Base List -->
              <div class="kb-list" v-if="knowledgeBases.length > 0">
                <div
                  class="kb-list-item"
                  v-for="kb in knowledgeBases"
                  :key="kb.id"
                  @click="kbChecked(kb.id)"
                >
                  <ATooltip placement="top" v-if="needsTooltip(kb.id)" color="#525A69">
                    <template #title>{{ kb.name }}</template>
                    <span class="kb-name" :ref="(el) => (kbNameRefs[kb.id] = el)">{{
                      kb.name
                    }}</span>
                  </ATooltip>
                  <span v-else class="kb-name" :ref="(el) => (kbNameRefs[kb.id] = el)">{{
                    kb.name
                  }}</span>
                  <a-checkbox :checked="kb.selected"></a-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="input-box_content_ability_right">
          <div
            class="input-box_content_ability_right_upload-wrapper"
            @mouseleave="hideUpboxWithDelay"
          >
            <ATooltip :title="isUploadDisabled ? 'You can add at most 10 files' : ''">
              <SvgIcon
                class="input-box_content_ability_right_upload-wrapper_updata"
                :class="{ 'upload-disabled': isUploadDisabled }"
                name="FileUp"
                @mouseenter="isUploadDisabled ? undefined : (showUpbox = true)"
                @click="isUploadDisabled ? undefined : (showUpbox = !showUpbox)"
                size="28"
              />
            </ATooltip>
            <div
              :class="
                'input-box_content_ability_right_upload-wrapper_upbox ' +
                (chat?.isWelcome ? '' : 'input-box_content_ability_right_upload-wrapper_upbox-top')
              "
              v-show="showUpbox"
              @mouseenter="cancelHideUpbox"
            >
              <div
                class="input-box_content_ability_right_upload-wrapper_upbox_local"
                @click.stop="handleFileSelect"
                :style="{ cursor: 'pointer' }"
              >
                From the Local PC
              </div>
              <div
                class="input-box_content_ability_right_upload-wrapper_upbox_knowledge"
                @click.stop="openKBModal"
              >
                From knowledge base
              </div>
            </div>
          </div>
          <SvgIcon
            class="input-box_content_ability_right_sendbut"
            :name="
              chat?.getChatStatus() === ChatStatus.DONE
                ? isSendButtonEnabled
                  ? 'SendBut'
                  : 'SendButDisabled'
                : 'StopSend'
            "
            size="38"
            @click="handleSendClick"
            :style="{
              cursor: isSendButtonEnabled ? 'pointer' : 'not-allowed',
              opacity: isSendButtonEnabled ? 1 : 0.4
            }"
          />
        </div>
      </div>
    </div>
  </div>
  <div v-if="!chat?.isWelcome" class="input-text">AI can make mistakes.</div>
  <previewModle v-model:modelValue="showPreviewModal" :docObj="previewFile" />

  <!-- <div class="mobile-scroll-indicator" @click="scrollToLatestContent" v-show="showScrollIndicator">
    <SvgIcon name="answerDown" size="20" />
  </div> -->
</template>

<script setup lang="ts">
import { ChatController, ATextarea, ChatStatus, ATooltip } from '@libs/a-comps'
import { Checkbox as ACheckbox, message } from 'ant-design-vue'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
// import BannerPage from './bannerComs.vue'
import { ref, reactive, onMounted, onUnmounted, computed, watch, createVNode, nextTick } from 'vue'
import { deleteKBFile, deleteKBFiles, getFileStatus } from '@/renderer/src/api/chatBase'
import { knowledgeBaseApi } from '@/renderer/src/api/knowledgeBase'
import { Answer } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import mySDK from '@renderer/views/Chat/sdkService'
import type { DocumentListType } from '@libs/a-comps/ChatBaseComponent/types'
import { Modal } from 'ant-design-vue'
import KnowledgeBaseSelectContent from './KnowledgeBaseSelectContent.vue'
import { fileExtensionIconNameMap } from '@renderer/hooks/fileType'
import previewModle from './previewModle.vue'
import { httpFileUpLoad } from '@/services/aixos'
import { addHistoryConversation, sendAnswerChunk } from '@/renderer/src/api/historyBase'
import { emitter } from '@/utils/EventBus'

let resourceId: string
let resourceType: string | undefined
emitter.on('agent-changed', (id: string, type: string) => {
  resourceId = id
  resourceType = type
})

interface ExtendedWindow extends Window {
  userInitiatedScroll?: boolean
}

const handleDragover = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
}

const handleDrop = async (e: DragEvent) => {
  e.preventDefault()
  if (!e.dataTransfer) return
  const files = e.dataTransfer.files
  if (files.length === 0) return

  // 检查文件数量限制
  const MAX_FILES = 10
  if (uploadFiles.length + files.length > MAX_FILES) {
    Modal.warning({
      title: '拖拽失败',
      content: `You can add at most ${MAX_FILES} files.`,
      okText: 'OK',
      centered: true,
      class: 'file-limit-warning-modal',
      width: 400,
      closable: true
    })
    return
  }

  Array.from(files).forEach(async (file) => {
    const id = props?.chat?.sessionId
    if (id) {
      const typedFile = file as File
      const res = await httpFileUpLoad(typedFile, id)
      if (res.data.code === 200) {
        handleUploadFile(res.data.data.documentId, typedFile.name, res.data.data.documentId, '')
      } else {
        Modal.error({
          title: '文件上传失败',
          content: res.data.msg || '上传文件时发生错误',
          okText: '确定'
        })
      }
    }
  })
}

interface UploadedFile {
  name: string
  path: string
  fileId: string
  documentId: string
  status?: 'parsing' | 'done' | 'documentCutError'
  isLocal?: boolean
}

const props = defineProps<{
  chat: ChatController | undefined
  isComponentSmall: boolean
}>()
const showUpbox = ref(false)
const inputValue = ref('')
const isDocumentParsing = ref(false)
let hideUpboxTimer: NodeJS.Timeout | null = null
const isFileSelecting = ref(false)
let answer_interrupt: any[] = []
let currentChatStatus = ref(ChatStatus.ONGOING)

const fileSearchList = computed(() => {
  const currentAnswer = props.chat?.chatAction.find(
    (item) => item instanceof Answer && item.getData().questionId === props.chat?.currentQueryId
  ) as Answer | undefined
  return currentAnswer?.fileSearchList || []
})

onMounted(() => {
  emitter.on('answer-interrupt-save', (arr: any) => {
    answer_interrupt = arr
  })
  emitter.on('answer-current-status', (status: ChatStatus) => {
    currentChatStatus.value = status
  })
})

// 添加监视器以处理文件搜索列表更改
watch(
  fileSearchList,
  (newFiles: DocumentListType[]) => {
    if (newFiles && newFiles.length > 0) {
      const filesToAdd = newFiles.filter(
        (file) => !uploadFiles.some((f) => f.documentId === file.documentId)
      )

      filesToAdd.forEach((file) => {
        const newFile: UploadedFile = {
          name: file.documentName,
          fileId: file.knowledgeId,
          documentId: file.documentId,
          path: '',
          status: 'done',
          isLocal: false
        }
        uploadFiles.push(newFile)
        props.chat?.uploadedFiles.push({
          name: file.documentName,
          fileId: file.knowledgeId,
          documentId: file.documentId,
          path: ''
        })
      })

      const documentIds = newFiles.map((file) => file.documentId)
      props.chat?.setDocumentIds(documentIds)
    }
  },
  { deep: true }
)

// 文件数据
const uploadFiles = reactive<UploadedFile[]>([])

watch(
  uploadFiles,
  () => {
    if (isFileSelecting.value && uploadFiles.length > 0) {
      isFileSelecting.value = false
    }
  },
  { deep: true }
)

const isSendButtonEnabled = computed(() => {
  if (props.chat?.getChatStatus() !== ChatStatus.DONE) {
    return true
  }

  if (isDocumentParsing.value) {
    return false
  }

  if (uploadFiles.some((file) => file.status !== 'done')) {
    return false
  }

  if (!inputValue.value.trim()) {
    return false
  }

  return true
})

const isUploadDisabled = computed(() => {
  return uploadFiles.length >= 10
})

const getFileStatusInfo = async (documentId: string[]) => {
  const res = await getFileStatus({ documentIds: documentId, knowledgeBaseId: resourceId })
  const data = res?.data
  if (data?.code === 200) {
    if (Array.isArray(data?.data) && data.data.length > 0) {
      // 检查所有文件是否已完成
      const allCompleted = data.data.every(
        (file) => file?.status === 'documentCutCompleted' || file?.status === 'documentCutError'
      )

      // 更新文件的状态
      data.data.forEach((file) => {
        if (file?.documentId) {
          const fileIndex = uploadFiles.findIndex((f) => f.documentId === file.documentId)
          if (fileIndex !== -1) {
            if (file?.status === 'documentCutCompleted') {
              uploadFiles[fileIndex].status = 'done'
            } else if (file?.status === 'documentCutError') {
              uploadFiles[fileIndex].status = 'documentCutError'
            }
          }
        }
      })

      if (allCompleted) {
        clearTimer()
        isDocumentParsing.value = false
      } else {
        // 检查当前所有文件是否都已完成
        const currentAllCompleted = uploadFiles.every(
          (file) => file.status === 'done' || file.status === 'documentCutError'
        )
        if (currentAllCompleted) {
          clearTimer()
          isDocumentParsing.value = false
        } else {
          isDocumentParsing.value = true
        }
      }
    } else {
      // 如果没有返回文件数据，检查当前所有文件是否都已完成
      const allFilesCompleted = uploadFiles.every(
        (file) => file.status === 'done' || file.status === 'documentCutError'
      )
      if (allFilesCompleted) {
        clearTimer()
        isDocumentParsing.value = false
      }
    }
  }
}

/**
 * 当按下回车键时发送输入的内容
 *
 * @param e 事件对象，包含按下的键和阻止默认行为的方法
 */
const pressEnterSend = (e?: { key: string; preventDefault: () => void }) => {
  if (e?.key === 'Enter') {
    e.preventDefault()
  }
  if (props.chat?.getChatStatus() !== ChatStatus.DONE || !isSendButtonEnabled.value) return

  props.chat?.setWelcomeType(false)

  // 设置查询值为输入文本（如果没有则为空字符串）
  props.chat?.setQueryValue(inputValue.value.trim())

  // 清空chunk数据
  props.chat?.clearRawChunkData()

  // 发送后重置输入值
  inputValue.value = ''

  // 从UI中清除上传的文件
  uploadFiles.splice(0, uploadFiles.length)

  // 清空chat中的文件相关状态
  if (props.chat) {
    props.chat.uploadedFiles = []
    props.chat.documentIds = []
  }

  // 如果需要，清除定时器
  clearTimer()

  // 确保发送后滚动到最新内容
  setTimeout(() => {
    scrollToLatestContent()
    // 重置用户滚动标志以确保自动滚动生效
    ;(window as ExtendedWindow).userInitiatedScroll = false
  }, 100)
}

const selectThinking = ref<boolean>(false)
// const selectWebSearch = ref<boolean>(true)

const clickThinking = () => {
  selectThinking.value = !selectThinking.value
  console.log('clickThinking = ', selectThinking.value)
  props.chat?.setDeepThinking(selectThinking.value)
  emitter.emit('is-deepThinking', selectThinking.value)
}

const stopFun = () => {
  try {
    // ToDO : 获取当前会话是否完整的状态

    let isPart
    if (currentChatStatus.value === ChatStatus.ONGOING) {
      isPart = true
    } else if (!isPart) {
      isPart = false
    }
    // console.log('点击停止时，当前session为 props?.chat?.sessionId = ', props?.chat?.sessionId)
    localStorage.setItem(`history-${props?.chat?.sessionId}` || 'default value', isPart.toString())
    // 手动调用接口添加未完成的对话。
    // addHistoryConversation(mySDK.userId, props?.chat?.sessionId, {
    //   role: 'assistant',
    //   content: answer_interrupt[1],
    //   think: answer_interrupt[0],
    //   isDeepThink: deepThinkingEnabled.value
    // })

    sendAnswerChunk({
      sessionId: props?.chat?.sessionId,
      content: props.chat?.getRawChunkData() || [],
      regen: props.chat?.isReGenerate || false,
      isDeepThink: props.chat?.isDeeoThinking
    })
  } catch (error) {
    // 异常处理逻辑
    console.error('停止生成失败...', error)
  }

  // TODO 发送刷新历史列表的通知

  props.chat?.stopRegenerate()

  // 确保只有点击刷新按钮操作链中regen为true, 其他所有场景(新会话、新轮次问答)都为false
  setTimeout(() => {
    props.chat?.setRegenerate(false)
  }, 100)

  const documentIds = uploadFiles
    .map((file) => file?.documentId)
    .filter((id): id is string => id !== undefined)

  // 清空输入框
  //   inputValue.value = ''

  // 清空文件列表
  uploadFiles.splice(0, uploadFiles.length)

  // 保持文档ID在对话中
  props.chat?.setDocumentIds(documentIds)

  // 如果需要，清除定时器
  clearTimer()

  // 停止重生成后，滚动到最新内容
  setTimeout(() => {
    scrollToLatestContent()
    // 重置用户滚动标志以确保自动滚动生效
    ;(window as ExtendedWindow).userInitiatedScroll = false
    // 触发内容重生成事件
    window.dispatchEvent(new CustomEvent('contentRegenerated'))
  }, 100)
}

const handleSendClick = () => {
  if (!isSendButtonEnabled.value) return
  if (props.chat?.getChatStatus() === ChatStatus.DONE) {
    pressEnterSend()
  } else {
    stopFun()
  }
}

const handleFileSelect = async () => {
  if (isFileSelecting.value) return

  // 检查文件数量限制
  const MAX_FILES = 10
  if (uploadFiles.length >= MAX_FILES) {
    Modal.error({
      title: '文件选择失败',
      content: 'You can add at most 10 files',
      okText: '确定'
    })
    return
  }

  try {
    isFileSelecting.value = true
    isDocumentParsing.value = true

    // @ts-ignore
    const result = await window.api.chooseFiles({
      source: props?.chat?.sessionId,
      userIdForUpload: mySDK.userId,
      resourceId,
      resourceType,
      remainingSlots: MAX_FILES - uploadFiles.length
    })

    // 检查主进程返回的结果
    if (result && result.error === 'FILE_LIMIT_EXCEEDED') {
      return
    }
    if (result && result.error === 'FILE_SIZE_EXCEEDED') {
      return
    }
  } catch (error) {
    if (uploadFiles.every((file) => file.status === 'done')) {
      isDocumentParsing.value = false
    }

    // 显示错误提示
    if (error instanceof Error) {
      Modal.error({
        title: '文件选择错误',
        content: error.message || '选择文件时发生错误',
        okText: '确定'
      })
    }
  } finally {
    if (!uploadFiles.some((file) => file.status === 'parsing')) {
      isFileSelecting.value = false
    } else {
      setTimeout(() => {
        isFileSelecting.value = false
      }, 500)
    }
  }
}

const openKBModal = () => {
  showUpbox.value = false
  Modal.confirm({
    title: 'Knowledge Base',
    icon: null,
    content: createVNode(KnowledgeBaseSelectContent, {
      alreadySelectedCount: uploadFiles.length,
      onClose: () => {
        Modal.destroyAll()
      },
      onConfirm: (
        fileInfos:
          | { documentId: string; documentName: string; knowledgeId: string }[]
          | { documentId: string; documentName: string; knowledgeId: string }
      ) => {
        handleKBDocSelect(fileInfos)
        Modal.destroyAll()
      }
    }),
    width: 'auto',
    style: { width: 'auto', maxWidth: '80vw' },
    bodyStyle: { width: 'auto', padding: 0 },
    footer: null,
    closable: true,
    closeIcon: createVNode(SvgIcon, { name: 'Close', size: '12' }),
    wrapClassName: 'kb-select-modal',
    centered: true
  })
}

// 从知识库中选择文档
const handleKBDocSelect = (
  fileInfos:
    | { documentId: string; documentName: string; knowledgeId: string }[]
    | { documentId: string; documentName: string; knowledgeId: string }
) => {
  const filesArray = Array.isArray(fileInfos) ? fileInfos : [fileInfos]

  if (filesArray.length === 0) {
    return
  }

  filesArray.forEach((fileInfo) => {
    if (fileInfo && fileInfo.documentId) {
      // 检查文件是否已经在列表中
      if (uploadFiles.some((file) => file.documentId === fileInfo.documentId)) {
        return
      }

      // 创建新的文件对象，状态为 'done'
      const newFile: UploadedFile = {
        name: fileInfo.documentName,
        fileId: fileInfo.knowledgeId,
        documentId: fileInfo.documentId,
        path: '',
        status: 'done',
        isLocal: false
      }

      // 添加到 uploadFiles
      uploadFiles.push(newFile)

      // 添加到 chat uploaded files
      props.chat?.uploadedFiles.push({
        name: fileInfo.documentName,
        fileId: fileInfo.knowledgeId,
        documentId: fileInfo.documentId,
        path: ''
      })
    }
  })

  // 更新 documentIds
  const documentIds = uploadFiles
    .map((file) => file?.documentId)
    .filter((id): id is string => id !== undefined)
  props.chat?.setDocumentIds(documentIds)

  // 更新当前答案的 fileSearchList
  const currentAnswer = props.chat?.chatAction.find(
    (item) => item instanceof Answer && item.getData().questionId === props.chat?.currentQueryId
  ) as Answer | undefined

  if (currentAnswer) {
    if (!currentAnswer.fileSearchList) {
      currentAnswer.fileSearchList = []
    }

    filesArray.forEach((fileInfo) => {
      if (!currentAnswer.fileSearchList.some((file) => file.documentId === fileInfo.documentId)) {
        currentAnswer.fileSearchList.push({
          documentId: fileInfo.documentId,
          documentName: fileInfo.documentName,
          knowledgeId: fileInfo.knowledgeId,
          knowledgeBaseId: resourceId
        })
      }
    })
  }
}

// 清空定时器
const clearTimer = () => {
  if (statusTimer) {
    clearInterval(statusTimer as any)
    statusTimer = null
  }
}

let statusTimer: NodeJS.Timer | null = null

const showScrollIndicator = ref(false)
let scrollHandler: (() => void) | null = null

onMounted(() => {
  // @ts-ignore: Property type not defined
  window.api.onChatUploadCompleted((file) => {
    const {
      fileId,
      data: { documentId },
      filename,
      path
    } = file

    handleUploadFile(documentId, filename, fileId, path)
  })

  // 添加滚动监听器以检测滚动位置
  const container = document.querySelector('.chat-box_region') as HTMLElement
  if (container) {
    scrollHandler = () => {
      // 如果滚动位置距离底部超过100px，显示滚动指示器
      const scrollDiff = container.scrollHeight - container.scrollTop - container.clientHeight
      if (scrollDiff > 100) {
        showScrollIndicator.value = true
      } else {
        showScrollIndicator.value = false
      }
    }

    container.addEventListener('scroll', scrollHandler, { passive: true })

    // 初始检查滚动位置
    scrollHandler()

    // 确保在元素大小变化时也检查滚动位置
    const resizeObserver = new ResizeObserver(() => {
      if (scrollHandler) scrollHandler()
    })
    resizeObserver.observe(container)
  }

  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    const dropdown = document.querySelector('.websearch-dropdown')
    const trigger = document.querySelector('.input-box_content_ability_left_websearch')

    if (dropdown && trigger && !dropdown.contains(target) && !trigger.contains(target)) {
      showWebSearchDropdown.value = false
    }
  })

  watch(showWebSearchDropdown, (newValue) => {
    if (newValue) {
      // 当下拉菜单显示时，重新测量KB名称宽度
      nextTick(() => {
        setTimeout(() => {
          measureKBNameWidths()
        }, 50)
      })
    }
  })
})

onUnmounted(() => {
  clearTimer()
  if (hideUpboxTimer) {
    clearTimeout(hideUpboxTimer)
  }
  // @ts-ignore: Property 'removeAllListeners' does not exist on type
  window.api.removeAllListeners()

  // 移除滚动监听器
  const container = document.querySelector('.chat-box_region') as HTMLElement
  if (container && scrollHandler) {
    container.removeEventListener('scroll', scrollHandler)
  }
})

function handleUploadFile(documentId: string, filename: string, fileId: string, path: string) {
  isDocumentParsing.value = true

  // 检查是否已有相同documentId的文件并且已完成
  const existingFile = uploadFiles.find((f) => f.documentId === documentId && f.status === 'done')

  // 初始 status，如果有相同ID的文件已完成，则直接标记为完成
  const uploadedTargetFile: UploadedFile = {
    name: filename,
    fileId,
    documentId: documentId as string,
    path,
    status: existingFile ? 'done' : 'parsing',
    isLocal: true
  }

  uploadFiles.splice(uploadFiles.length, 0, uploadedTargetFile)

  // 保存到 chat.uploadedFiles
  if (typeof documentId === 'string') {
    const fileForChat = {
      name: filename,
      fileId,
      documentId,
      path,
      status: existingFile ? 'done' : 'parsing'
    }
    props.chat?.uploadedFiles.push(fileForChat)
  }

  const documentIds = uploadFiles
    .map((file) => file?.documentId)
    .filter((id): id is string => id !== undefined)
  props.chat?.setDocumentIds(documentIds)

  // 检查是否所有文件都已完成
  const allFilesCompleted = uploadFiles.every((file) => file.status === 'done')
  if (allFilesCompleted) {
    clearTimer()
    isDocumentParsing.value = false
  } else if (documentIds.length > 0) {
    // 只有在有未完成的文件时才启动定时器
    clearTimer()
    statusTimer = setInterval(() => {
      getFileStatusInfo(documentIds)
    }, 3000)
  }
}

async function removeSingleFile(idx: number) {
  const file = uploadFiles[idx]
  const documentId = file?.documentId

  uploadFiles.splice(idx, 1)

  const documentIds = uploadFiles
    .map((file) => file?.documentId)
    .filter((id): id is string => id !== undefined)
  props.chat?.setDocumentIds(documentIds)

  const currentAnswer = props.chat?.chatAction.find(
    (item) => item instanceof Answer && item.getData().questionId === props.chat?.currentQueryId
  ) as Answer | undefined

  if (currentAnswer && documentId) {
    currentAnswer.fileSearchList = currentAnswer.fileSearchList.filter(
      (item) => item.documentId !== documentId
    )
  }

  clearTimer()

  // 如果所有剩余文件都已完成，则重置isDocumentParsing
  const allFilesCompleted = uploadFiles.every(
    (file) => file.status === 'done' || file.status === 'documentCutError'
  )
  if (allFilesCompleted || uploadFiles.length === 0) {
    isDocumentParsing.value = false
  }

  if (documentId) {
    // 如果是本地上传的文件，需要调用删除接口
    if (file.isLocal) {
      try {
        await deleteKBFile({ documentId, knowledgeBaseId: resourceId })
      } catch (error) {
        console.error('Error deleting file:', error)
      }
    }
  }

  // 如果删除的是最后一个文件，重置布局
  if (uploadFiles.length === 0) {
    resetLayout()
  }
}

async function batchDelete() {
  // 获取所有本地上传的文件的 documentId
  const localDocumentIds = uploadFiles
    .filter((file) => file.isLocal)
    .map((file) => file.documentId)
    .filter((id): id is string => id !== undefined)

  // const allDocumentIds = uploadFiles
  //   .map((file) => file.documentId)
  //   .filter((id): id is string => id !== undefined)

  uploadFiles.splice(0, uploadFiles.length)

  props.chat?.setDocumentIds([])

  const currentAnswer = props.chat?.chatAction.find(
    (item) => item instanceof Answer && item.getData().questionId === props.chat?.currentQueryId
  ) as Answer | undefined

  if (currentAnswer) {
    currentAnswer.fileSearchList = []
  }

  clearTimer()

  // 重置文档解析，因为所有文件都被删除
  isDocumentParsing.value = false

  // 如果有本地上传的文件，需要批量删除
  if (localDocumentIds.length > 0) {
    try {
      await deleteKBFiles({ docIds: localDocumentIds, knowledgeBaseId: resourceId })
    } catch (error) {}
  }

  // 重置布局，恢复欢迎内容和输入框的原始位置
  resetLayout()
}

// 重置布局，恢复欢迎内容和输入框的原始位置
const resetLayout = () => {
  setTimeout(() => {
    const welcomeElement = document.querySelector('.chat-wrap_welcome') as HTMLElement
    const inputBox = document.querySelector('.input-box') as HTMLElement
    const chatWrap = document.querySelector('.chat-wrap') as HTMLElement

    if (welcomeElement) {
      welcomeElement.style.top = '30%'
      welcomeElement.style.transition = 'top 0.3s ease'
    }

    if (inputBox) {
      inputBox.style.marginTop = '0'
      inputBox.style.transition = 'margin-top 0.3s ease'
    }

    if (chatWrap) {
      chatWrap.style.paddingTop = '16px'
    }
  }, 100)
}

// 延迟隐藏上传选项菜单
const hideUpboxWithDelay = () => {
  hideUpboxTimer = setTimeout(() => {
    showUpbox.value = false
  }, 300)
}

// 显示上传限制警告
const showUploadLimitWarning = () => {
  message.warning('You can add at most 10 files.')
}

// 取消隐藏上传选项菜单
const cancelHideUpbox = () => {
  if (hideUpboxTimer) {
    clearTimeout(hideUpboxTimer)
    hideUpboxTimer = null
  }
}

const getFileIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  return fileExtensionIconNameMap[extension as keyof typeof fileExtensionIconNameMap] || 'general'
}

const showPreviewModal = ref(false)
const previewFile = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: '',
  knowledgeBaseId: ''
})

const scrollToLatestContent = () => {
  const container = document.querySelector('.chat-box_region') as HTMLElement
  if (container) {
    // 设置一个标志，表示用户发起的滚动
    ;(window as ExtendedWindow).userInitiatedScroll = true

    // 执行滚动
    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth'
    })

    // 隐藏滚动指示器
    showScrollIndicator.value = false

    // 在滚动动画完成后重置标志
    setTimeout(() => {
      ;(window as ExtendedWindow).userInitiatedScroll = false
    }, 1000)
  }
}

const handleFilePreview = (file: UploadedFile) => {
  previewFile.value = {
    documentId: file.documentId,
    documentName: file.name,
    knowledgeId: file.fileId,
    knowledgeBaseId: resourceId
  }
  showPreviewModal.value = true
}

const showWebSearchDropdown = ref(false)
const webSearchEnabled = ref(localStorage.getItem('webSearchChecked') === 'true')
const deepThinkingEnabled = ref(true)
const isKnowledgeBasesLoading = ref(false)

const toggleWebSearchDropdown = async () => {
  if (showWebSearchDropdown.value) {
    showWebSearchDropdown.value = false
  } else {
    // 下拉框隐藏，加载数据
    if (!isKnowledgeBasesLoading.value) {
      isKnowledgeBasesLoading.value = true
      try {
        const success = await initKnowledgeBases()
        if (success) {
          showWebSearchDropdown.value = true
        }
      } catch (error) {
      } finally {
        isKnowledgeBasesLoading.value = false
      }
    }
  }
}

watch(webSearchEnabled, (newValue) => {
  props.chat?.setWebSearch(newValue)
  localStorage.setItem('webSearchChecked', newValue.toString())
})

watch(deepThinkingEnabled, (newValue) => {
  props.chat?.setDeepThinking(newValue)
})

watch(
  () => props.chat,
  (newChat) => {
    if (newChat) {
      // 初始化时同步状态
      newChat.setWebSearch(webSearchEnabled.value)
      newChat.setDeepThinking(deepThinkingEnabled.value)

      // 初始化勾选知识库列表
      const kbIds = knowledgeBases.value.filter((kb) => kb.selected).map((kb) => kb.id)
      newChat.setKnowledgeIds(kbIds)
    }
  }
)

interface KnowledgeBase {
  id: string
  name: string
  selected: boolean
  knowledgeBaseId: string
}

const knowledgeBases = ref<KnowledgeBase[]>([])
const kbNameRefs = ref<any>({})
const kbNameWidths = ref<Record<string, number>>({})
const maxKBNameWidth = 120

// 从 localStorage 读取知识库选中状态
const getStoredKBSelections = () => {
  const saved = localStorage.getItem('kBSelections')
  if (saved) {
    try {
      const parsed = JSON.parse(saved)
      // 如果解析出的对象不为空，则直接使用
      if (Object.keys(parsed).length > 0) {
        return parsed
      }
    } catch (error) {}
  }
  // 默认情况下知识库选中
  return { _default: true }
}

// 缓存的知识库选中状态
const cachedKBSelections = ref<Record<string, boolean>>(getStoredKBSelections())

const hasAnyKBSelected = computed(() => {
  // 如果有缓存的状态，使用缓存
  if (Object.keys(cachedKBSelections.value).length > 0) {
    // 如果缓存中标记为 all: true，则认为有选中的
    if (cachedKBSelections.value.all) {
      return true
    }
    // 否则检查是否有任何一个为 true
    return Object.values(cachedKBSelections.value).some((selected) => selected)
  }
  // 如果没有缓存，但有知识库数据，则使用实际数据
  return knowledgeBases.value.some((kb) => kb.selected)
})

const isAllKBSelected = computed(() => {
  return knowledgeBases.value.length > 0 && knowledgeBases.value.every((kb) => kb.selected)
})

// 部分选中
const isKBIndeterminate = computed(() => {
  const selectedCount = knowledgeBases.value.filter((kb) => kb.selected).length
  return selectedCount > 0 && selectedCount < knowledgeBases.value.length
})

const initKnowledgeBases = async () => {
  try {
    const response = await knowledgeBaseApi.getKBList()
    if (response?.status === 200) {
      const data = response.data as {
        data: { entityId: string; knowledgeBaseName: string; knowledgeBaseId: string }[]
      }

      if (!data.data || !Array.isArray(data.data)) {
        return false
      }

      // 从localStorage读取上次的kb选择状态
      const savedSelections = localStorage.getItem('kBSelections')
      let savedSelectionsMap: Record<string, boolean> = {}

      if (savedSelections) {
        try {
          savedSelectionsMap = JSON.parse(savedSelections)
        } catch (error) {
          console.warn('Failed to parse saved knowledge base selections:', error)
        }
      }

      knowledgeBases.value = data.data.map((kb) => ({
        id: kb.entityId,
        name: kb.knowledgeBaseName,
        // 如果有保存的状态就使用保存的，否则默认选中
        selected: savedSelectionsMap.hasOwnProperty(kb.entityId)
          ? savedSelectionsMap[kb.entityId]
          : true,
        knowledgeBaseId: kb.knowledgeBaseId
      }))

      // 更新缓存状态
      const newSelectionsMap: Record<string, boolean> = {}
      knowledgeBases.value.forEach((kb) => {
        newSelectionsMap[kb.id] = kb.selected
      })
      cachedKBSelections.value = newSelectionsMap

      return true
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}

const handleKBChecked = () => {
  const shouldSelectAll = !isAllKBSelected.value
  knowledgeBases.value.forEach((kb) => {
    kb.selected = shouldSelectAll
  })
}

const kbChecked = (id: string) => {
  const kb = knowledgeBases.value.find((kb) => kb.id === id)
  if (kb) {
    kb.selected = !kb.selected
  }
}

// 保存知识库选择状态到localStorage
const savekBSelections = (kbs: KnowledgeBase[]) => {
  const selectionsMap: Record<string, boolean> = {}
  kbs.forEach((kb) => {
    selectionsMap[kb.id] = kb.selected
  })
  localStorage.setItem('kBSelections', JSON.stringify(selectionsMap))
  // 更新缓存状态
  cachedKBSelections.value = selectionsMap
}

// 监听知识库列表的变化，同步到chat实例并保存状态
watch(
  knowledgeBases,
  (kbs) => {
    const selectedKbs = kbs.filter((kb) => kb.selected)
    const kbIds = selectedKbs.map((kb) => kb.id)

    props.chat?.setKnowledgeIds(kbIds)
    savekBSelections(kbs)
  },
  { deep: true }
)

// 测量KB名称的实际宽度
const measureKBNameWidths = () => {
  nextTick(() => {
    knowledgeBases.value.forEach((kb) => {
      const element = kbNameRefs.value[kb.id]
      if (element) {
        const width = element.scrollWidth
        kbNameWidths.value[kb.id] = width
      }
    })
  })
}

// 检查KB名称是否需要tooltip
const needsTooltip = computed(() => {
  return (kbId: string) => {
    const actualWidth = kbNameWidths.value[kbId] || 0
    const needsTooltip = actualWidth > maxKBNameWidth
    return needsTooltip
  }
})
</script>

<style scoped lang="less">
.input-box {
  width: 100%;
  height: 108px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  position: relative;

  .upload-disabled {
    opacity: 0.5;
    cursor: not-allowed !important;
  }

  &_banner-top {
    position: relative;
    display: flex;
    width: 100%;
    margin-bottom: 8px;
  }

  &_content {
    width: 100%;
    max-width: 940px;
    // min-width: 378px;
    min-height: 108px;
    position: relative;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.04);
    background: #fff;
    border: 1px solid;
    border-image-source: linear-gradient(
      180deg,
      rgba(198, 212, 238, 0.31) 0%,
      rgba(165, 177, 200, 0.24) 100%
    );
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 0;
    // margin-top: 60px;
    // margin-bottom: 38px;

    &_inp {
      width: 97%;
      height: 48px;
      margin: 8px 16px 0 16px;
      display: flex;
      flex-direction: column;

      :deep(textarea) {
        caret-color: var(--Semantic-Primary-normal, rgba(100, 65, 171, 1)) !important;
        caret-width: 2px;
        height: 100%;
      }
    }

    &_ability {
      width: 100%;
      height: 44.44%;
      display: flex;
      justify-content: space-between;
      margin: 0;
      background: #fff;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;

      &_left {
        height: 100%;
        display: flex;
        margin-left: 5px;
        align-items: center;
        gap: 5px;

        &_thinking {
          height: 26px;
          display: flex;
          padding: 2px 10.8px;
          align-items: center;
          user-select: none;
          cursor: pointer;
          border: 0px solid rgba(0, 0, 0, 0.11);
          background: rgba(247, 248, 250, 1);
          border-radius: 4px;

          &:hover {
            //border-color: rgba(100, 65, 171, 0.5);
            background: rgba(229, 231, 236, 1);
            //color: rgba(100, 65, 171, 1) !important;

            :deep(.svg-icon) {
              color: inherit !important;

              path {
                fill: currentColor !important;
              }
            }
          }
        }

        &_websearch {
          height: 26px;
          display: flex;
          padding: 2px 10.8px;
          align-items: center;
          user-select: none;
          cursor: pointer;
          background: #f7f8fa;
          // border: 1px solid rgba(0, 0, 0, 0.11);
          border-radius: 4px;
          position: relative;

          .arrow-icon {
            margin-left: 4px;
            color: rgba(82, 82, 91, 1);
          }

          .websearch-active-icons {
            display: flex;
            align-items: center;
            gap: 8px;

            .inactive-icon {
              opacity: 0.3;
            }

            .active-icon {
              color: rgba(100, 65, 171, 1);
            }
          }

          &.both-active {
            background: rgba(245, 242, 254, 1);
            color: rgba(100, 65, 171, 1) !important;

            :deep(.svg-icon) {
              color: inherit !important;

              path {
                fill: currentColor !important;
              }
            }

            div {
              color: rgba(100, 65, 171, 1);
            }
          }

          &:hover {
            // border-color: rgba(100, 65, 171, 0.5);
            background: rgba(229, 231, 236, 1);
            color: rgba(100, 65, 171, 1) !important;

            :deep(.svg-icon) {
              color: inherit !important;

              path {
                fill: currentColor !important;
              }
            }

            div {
              color: rgba(100, 65, 171, 1);
            }
          }

          &_ken {
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        div {
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          letter-spacing: 0%;
          vertical-align: middle;
          color: rgba(82, 82, 91, 1);
          margin-left: 4px;
        }

        &_select-all {
          border-color: rgba(100, 65, 171, 0.5);
          background: rgba(245, 242, 254, 1);
          color: rgba(100, 65, 171, 1) !important;

          &:hover {
            background: rgba(229, 219, 252, 1);

            :deep(.svg-icon) {
              color: inherit !important;

              path {
                fill: currentColor !important;
              }
            }
          }
          :deep(.svg-icon) {
            color: inherit !important;

            path {
              fill: currentColor !important;
            }
          }

          div {
            color: rgba(100, 65, 171, 1);
          }
        }
      }

      &_right {
        display: flex;
        align-items: center;
        margin-right: 8px;
        position: relative;

        :deep(&_sendbut) {
          color: #fff;
          cursor: pointer;
        }

        .SendBut {
          color: #fff;
          cursor: pointer;
        }

        &_upload-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
          z-index: 300;

          &_upbox {
            width: 160px;
            height: 80px;
            background: #fff;
            position: absolute;
            top: calc(100% + 8px);
            right: -8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            opacity: 1;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            z-index: 301;
            border-radius: 8px;

            &_local {
              width: calc(100% - 12px);
              height: 36px;
              font-weight: 400;
              font-size: 14px;
              line-height: 36px;
              color: rgba(0, 0, 0, 1);
              display: flex;
              align-items: center;
              padding-left: 12px;
              cursor: pointer;
              position: relative;
              z-index: 501;

              &:hover {
                background: rgba(242, 243, 245, 1);
                color: rgba(100, 65, 171, 1) !important;
              }
            }

            &_knowledge {
              width: calc(100% - 12px);
              height: 36px;
              font-weight: 400;
              font-size: 14px;
              line-height: 36px;
              color: rgba(0, 0, 0, 1);
              display: flex;
              align-items: center;
              padding-left: 12px;
              cursor: pointer;
              position: relative;
              z-index: 501;

              &:hover {
                background: rgba(242, 243, 245, 1);
                color: rgba(100, 65, 171, 1) !important;
              }
            }
          }

          &_upbox-top {
            top: auto;
            bottom: calc(100%);
          }

          :deep(&_updata) {
            color: rgba(110, 110, 120, 1);
            background: #fff;
            margin-right: 8px;
            outline: none;

            &:hover {
              background: rgba(245, 242, 254, 1);
              color: rgba(100, 65, 171, 1) !important;

              :deep(.svg-icon) {
                color: inherit !important;

                path {
                  fill: currentColor !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 欢迎模式下输入框的特殊样式
:deep(.chat-box-flex) .input-box {
  width: 100%;
  max-width: 940px;
  margin-top: -20px; /* 微调位置，向上移动一点 */
}

.input-box-bottom {
  //   bottom: 38px;
}

.input-text {
  width: 100%;
  height: 38px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  color: rgba(115, 115, 115, 1);
  position: relative;
}

:deep(textarea) {
  resize: none;
  padding: 0;
}

.file-display {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 6px;
  background: transparent;
  border: none;
  min-width: 40px;
  border-bottom: 1px solid #f7f9ff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  transition: all 0.3s ease;
  transform-origin: bottom;
  transform: scaleY(0);
  opacity: 0;
  pointer-events: none;
  max-height: 95px;
  z-index: 10;
  background-color: #f7f8fa;
  box-shadow: 0px -4px 6px 0px rgba(0, 0, 0, 0.04);

  &:not(:empty) {
    transform: scaleY(1);
    opacity: 1;
    pointer-events: auto;
  }

  .files-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: calc(100% - 32px);
    overflow-y: auto;
    max-height: 70px;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-right: 2px;
    scroll-snap-type: y mandatory;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .file-info {
    display: flex;
    align-items: center;
    background: #ffffff;
    border-radius: 24px;
    padding: 0 12px 3px 12px;
    border: 1px solid #e5e5e5;
    position: relative;
    width: calc(25% - 10px);
    min-width: 100px;
    max-width: calc(25% - 10px);
    margin-right: 2px;
    box-sizing: border-box;
    height: 30px;
    scroll-snap-align: start;
    flex-shrink: 0;

    &:hover {
      background: #f7f8fa;
    }

    &:active {
      background: #f2f3f5;
    }

    .upload-svg-icon {
      cursor: pointer;
      color: white;
    }

    .file-name {
      font-size: 14px;
      color: #333;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 4px;
      margin-right: 4px;
      flex: 1;
      cursor: pointer;
    }

    .file-delete {
      cursor: pointer;
      margin-left: auto;
      flex-shrink: 0;

      :deep(.svg-icon) {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
}

.file-clear-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
  flex-shrink: 0;
  z-index: 11; /* 确保在滚动条上方 */

  &:hover {
    cursor: pointer;
    background: rgba(0, 0, 0, 0.05);
  }
}

.input-box_content_ability_right_sendbut {
  cursor: pointer;
}

.loading-circle {
  display: inline-block;
  width: 15px;
  height: 15px;
  margin: 3px 10px 0 3px;
  border: 4px solid #e5dff7;
  border-top: 4px solid #6c47c6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  vertical-align: middle;
}

.error-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 15px;
  border-radius: 50%;
  position: relative;
  vertical-align: middle;
  margin: 0 4px 0 1px;
}

.error-icon::after {
  content: '!';
  color: #dc2626;
  font-weight: bold;
  font-size: 16px;
  line-height: 1;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.mobile-scroll-indicator {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 230px;
  background-color: #7b5bc5;
  color: white;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: bounce 2s infinite;

  span {
    font-size: 10px;
  }

  &:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateX(-50%);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-2px);
  }
}

.websearch-dropdown {
  position: absolute;
  top: calc(100% + 16px);
  left: -5.8px;
  width: 220px;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0;

  &.websearch-dropdown-top {
    top: auto;
    bottom: calc(100% + 8px);
  }

  .dropdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px 12px 10px;
    cursor: default;
    position: relative;

    &:first-child {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: -3.8px;
        right: 0;
        height: 1px;
        background-color: rgba(0, 0, 0, 0.06);
        z-index: 1;
      }

      &:hover::before {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }
    }

    .dropdown-item-label {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    span {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }

    :deep(.ant-checkbox) {
      margin-right: 0;

      .ant-checkbox-inner {
        border-color: #d9d9d9;
        border-radius: 2px;
      }

      &.ant-checkbox-checked .ant-checkbox-inner {
        background-color: #6441ab;
        border-color: #6441ab;
      }

      &:hover .ant-checkbox-inner {
        border-color: #6441ab;
      }
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -5px;
      right: 0;
      bottom: 0;
      background-color: transparent;
      transition: background-color 0.2s ease;
      z-index: -1;
    }

    &:hover::before {
      background-color: rgba(245, 242, 254, 0.5);
    }
  }

  .kb-item {
    :deep(.ant-checkbox) {
      margin-right: 0;
    }
  }

  .kb-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 4px 0;
    background: rgba(250, 250, 250, 0.8);

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    .kb-list-item {
      padding: 12px 16px 12px 8px;
      cursor: pointer;
      transition: background-color 0.2s;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      // 确保勾选框对齐
      :deep(.ant-checkbox) {
        margin-right: 0;
      }

      .kb-name {
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        padding-left: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        vertical-align: middle;
        display: inline-block;
        max-width: 220px;
      }

      :deep(.ant-checkbox-wrapper) {
        margin-left: 20px;

        .ant-checkbox {
          top: 0;

          .ant-checkbox-inner {
            border-color: #6441ab;
          }
        }

        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #6441ab;
          border-color: #6441ab;
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -8px;
        right: 0;
        bottom: 0;
        background-color: transparent;
        transition: background-color 0.2s ease;
        z-index: -1;
      }

      &:hover::before {
        // 下边这个颜色再加深点
        background-color: rgb(219, 221, 226);
        // background-color: rgba(229, 231, 236, 1);
      }
    }
  }
}
</style>
<style lang="less">
@media screen and (max-width: 1200px) {
  .input-box__deep-thinking-button-text {
    display: none;
  }
}
</style>
