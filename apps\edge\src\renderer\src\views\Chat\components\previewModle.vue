<template>
  <div class="preview-model-box" v-if="modelValue">
    <AModal
      v-model:open="open"
      :footer="null"
      title="file preview"
      width="80%"
      @cancel="handleCancel"
    >
      <div class="preview-content">
        <!-- 图片预览 -->
        <img v-if="isImage" :src="previewUrl" class="preview-image" />

        <!-- PDF预览 -->
        <iframe v-else-if="isPDF" :src="previewUrl" class="preview-iframe" frameborder="0"></iframe>

        <!-- 通用文件处理 -->
        <div v-else class="default-preview">
          <p>Online preview is not supported. Please download and check</p>
          <a class="download-link" @click="downloadFile(docObj)">
            Download file {{ docObj.documentName || 'file' }}
          </a>
        </div>

        <!-- 错误提示 -->
        <a-alert
          v-if="previewError"
          type="error"
          message="Preview loading failed"
          :description="errorMessage"
          closable
          class="error-alert"
        />
      </div>
    </AModal>
  </div>
</template>

<script setup lang="ts">
import { AModal, Alert as AAlert } from '@libs/a-comps'
import { ref, computed, watch, onBeforeUnmount } from 'vue'
import { DocumentListType } from '@libs/a-comps/ChatBaseComponent/types'
import { getFileSvgNameSvgName } from '@/renderer/src/hooks/fileType'
import { GlobalConfig } from '@/renderer/src/common'

const props = defineProps<{
  modelValue: boolean
  docObj: DocumentListType
}>()

const emit = defineEmits(['update:modelValue'])

const open = ref(false)
const previewError = ref(false)
const errorMessage = ref('')
const previewUrl = ref('')

watch(
  () => props.modelValue,
  (newVal) => {
    open.value = newVal
  }
)

watch(open, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    previewError.value = false
  }

  if (newVal && (isImage.value || isPDF.value)) {
    fetchPreviewUrl()
  }
})
// 文件类型判断
const fileExtension = computed(() => {
  return getFileSvgNameSvgName(props.docObj.documentName)
})

const isImage = computed(() => ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP'].includes(fileExtension.value))
const isPDF = computed(() => fileExtension.value === 'PDF')
const fileUrl = computed(() => {
  return `${GlobalConfig.kbFileServer}/api/v1/document/viewDocument?documentId=${props.docObj.documentId}`
})
// 获取预览URL
const fetchPreviewUrl = async () => {
  try {
    const config: { headers: Record<string, string> } = {
      headers: {
        token: GlobalConfig.tokens.access_token
      }
    }
    if (props.docObj.knowledgeBaseId) {
      config.headers['resource-Id'] = props.docObj.knowledgeBaseId
      config.headers['resource-Type'] = '2'
    }

    const response = await fetch(fileUrl.value, config)

    if (!response.ok) throw new Error('Failed to fetch file')

    const blob = await response.blob()

    // 释放之前的 URL
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value)
      previewUrl.value = ''
    }
    previewUrl.value = URL.createObjectURL(blob)
  } catch (error) {
    console.error('Preview error:', error)
    handleMediaError()
  }
}
// 处理媒体加载错误
const handleMediaError = () => {
  previewError.value = true
  errorMessage.value = `not load ${props.docObj.documentName || 'file'}`
  console.log('handleMediaError', props.docObj)
}

watch(
  () => props.docObj.documentId,
  () => {
    previewError.value = false
  }
)

onBeforeUnmount(() => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
})

const handleCancel = () => {
  emit('update:modelValue', false)

  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
}

const downloadFile = async (docObjItem: DocumentListType) => {
  try {
    const response = await fetch(fileUrl.value, {
      headers: {
        token: GlobalConfig.tokens.access_token
      }
    })
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = docObjItem.documentName || 'file'
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Download error:', error)
  }
}
</script>

<style scoped lang="less">
.preview-content {
  position: relative;
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }

  .preview-iframe {
    width: 100%;
    height: 70vh;
    border: none;
  }

  .preview-video {
    max-width: 100%;
    max-height: 70vh;
  }

  .default-preview {
    text-align: center;

    .download-link {
      margin-top: 16px;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-alert {
    margin-top: 16px;
  }
}
</style>
