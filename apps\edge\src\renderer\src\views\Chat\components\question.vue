<template>
  <div class="query-box">
    <div v-if="displayFiles.length" class="file-display">
      <div class="files-container">
        <div
          class="file-info"
          v-for="file in displayFiles"
          :key="file.name"
          @click="handleFilePreview(file)"
        >
          <div class="file-icon">
            <SvgIcon class="question-svg-icon" :name="getFileIcon(file.name)" />
          </div>
          <span class="file-name">{{ file.name }}</span>
        </div>
      </div>
      <div v-if="showToggleArrow" class="toggle-arrow" @click="toggleExpand">
        <svg
          :style="{ transform: expanded ? 'rotate(0deg)' : 'rotate(180deg)' }"
          width="14"
          height="14"
          viewBox="0 0 14 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3.5 8.5L7 5L10.5 8.5"
            stroke="#55575C"
            stroke-width="1.1"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div class="query-box_text">{{ queryItem?.questionData.content }}</div>
  </div>
  <previewModle v-model:modelValue="showPreviewModal" :docObj="previewFile" />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Question } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { fileExtensionIconNameMap } from '@renderer/hooks/fileType'
import previewModle from './previewModle.vue'
import { DocumentListType } from '@libs/a-comps/ChatBaseComponent/types'

const props = defineProps<{
  queryItem: Question | undefined
}>()

const expanded = ref(false)
const maxShow = 4
const showPreviewModal = ref(false)
const previewFile = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: '',
  knowledgeBaseId: ''
})

const files = computed(() => props.queryItem?.questionData?.files || [])
const showToggleArrow = computed(() => files.value.length > maxShow)
const displayFiles = computed(() => {
  if (!showToggleArrow.value) return files.value
  return expanded.value ? files.value : files.value.slice(0, maxShow)
})

const toggleExpand = () => {
  expanded.value = !expanded.value
}

const handleFilePreview = (file: any) => {
  previewFile.value = {
    documentId: file.documentId || file.id,
    documentName: file.name,
    knowledgeId: file.knowledgeId || '',
    knowledgeBaseId: file.knowledgeBaseId || ''
  }
  showPreviewModal.value = true
}

const getFileIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  return fileExtensionIconNameMap[extension as keyof typeof fileExtensionIconNameMap] || 'general'
}
</script>

<style scoped lang="less">
.query-box {
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .file-display {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 6px 0 6px 6px;
    background: transparent;
    border: none;
    min-width: 120px;
    border-bottom: 1px solid #f7f9ff;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-bottom: 4px;
    position: relative;
    width: 100%;

    .files-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      width: 100%;
      padding: 0;
      box-sizing: border-box;
      margin-right: v-bind('showToggleArrow ? "32px" : "0px"');
      justify-content: flex-end;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #fff;
      border-radius: 24px;
      padding: 6px 12px 6px 12px;
      border: 1px solid #e5e5e5;
      position: relative;
      min-width: 0;
      box-sizing: border-box;
      width: v-bind(
        'showToggleArrow ? "calc((100% - 30px) / 4)" : "calc((100% - 30px) / " + Math.min(4, displayFiles.length) + ")"'
      );

      .file-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .question-svg-icon {
          cursor: pointer;
          color: white;
        }
      }

      .file-name {
        font-size: 15px;
        color: #333;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1;
        flex: 1;
        min-width: 0;
        cursor: pointer;
      }
    }

    .toggle-arrow {
      position: absolute;
      top: 6px;
      right: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 18px;
      user-select: none;
      z-index: 1;
    }
  }

  &_text {
    display: inline-block;
    padding: 8px 24px;
    border-top-left-radius: 25px;
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 25px;
    background: rgba(235, 233, 248, 1);
    font-weight: 400;
    font-size: 14px;
    vertical-align: middle;
    max-width: 60vw;
    min-width: 32px;
    white-space: pre-line;
  }
}
</style>
