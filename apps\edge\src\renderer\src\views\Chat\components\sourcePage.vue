<template>
  <div class="answer-source" v-if="answerItem?.isExpanded">
    <div class="answer-source_content" v-if="isExpanded">
      <div
        class="answer-source_content_top"
        v-for="(item, index) in answerItem?.references"
        :key="index"
        @click="openData(item)"
      >
        <div class="answer-source_content_top_num">{{ index + 1 }}</div>
        <div class="answer-source_content_top_content">
          <div class="answer-source_content_top_content_title">
            {{ item.siteName || item.documentName }}
            <span class="answer-source_content_top_content_title_source"
              >[{{ getSource(item.documentSource) }}]</span
            >
          </div>
          <!-- <div class="answer-source_content_top_content_bottom">2025-08-08 | Author's name</div> -->
        </div>
      </div>
    </div>
    <div
      :class="{ 'answer-source_deepthink': true, 'answer-source_top-se': isExpanded }"
      @click="deepthinkClickExpanded"
    >
      {{ answerItem?.references?.length }} References
      <SvgIcon name="DeepthinkingReference" size="16" />
    </div>
    <!--     <div
      :class="{ 'answer-source_top': true, 'answer-source_top-se': isExpanded }"
      @click="clickExpanded"
      v-else
    >
      {{ answerItem?.references?.length }} References
      <SvgIcon name="SourceTop" size="16" v-if="isExpanded" />
      <SvgIcon name="WebSearchDownward" color="rgba(82, 82, 91, 1)" size="16" v-else />
    </div> -->
    <previewModle v-model:modelValue="showModal" :docObj="docItem" />
  </div>
</template>

<script setup lang="ts">
import { electronHooks } from '@/renderer/src/electron'
import {
  DocumentListType,
  DocumentSource,
  ReferencesType
} from '@libs/a-comps/ChatBaseComponent/types'
import { Answer } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import previewModle from './previewModle.vue'
import { ref, onMounted } from 'vue'
import { emitter } from '../../../../../utils/EventBus'

const isExpanded = ref(false)
let referenceExpanded = ref(false)
const props = defineProps<{
  answerItem: Answer | undefined
  isDeepThink: boolean | undefined
}>()
const showModal = ref(false)
const docItem = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: ''
})

const clickExpanded = () => {
  // isExpanded.value = !isExpanded.value
  emitter.emit('ref-click-2', true)
  emitter.emit('ref-click-1', props.answerItem?.references)
}
const deepthinkClickExpanded = () => {
  emitter.emit('ref-click-2', true)
  emitter.emit('ref-click-1', props.answerItem?.references)
}

onMounted(() => {
  referenceExpanded.value = props.isDeepThink
})
onMounted(() => {
  emitter.on('is-deepThinking', (isDeepThink: boolean) => {
    referenceExpanded.value = isDeepThink
  })
})

const electronApi = electronHooks()

const getSource = (documentSource: DocumentSource) => {
  switch (documentSource) {
    case 'public':
    case 'private':
      // 合并处理公开和私有知识库
      return 'Edge'
    case 'network':
      // 处理网络搜索结果
      return 'Web'
    case 'session':
      // 处理会话文档
      return 'Doc'
  }
}

const openData = (item: ReferencesType) => {
  const typeData = getSource(item.documentSource)
  if (typeData !== 'Web') {
    docItem.value = {
      documentId: item.documentId!,
      documentName: item.documentName || '',
      knowledgeId: item.knowledgeId!
    }
    console.log(item, typeData, docItem.value)
    showModal.value = !showModal.value
  } else {
    electronApi?.openWebSite(item.url!)
  }
}
</script>

<style scoped lang="less">
.answer-source {
  display: flex;
  flex-direction: column;
  width: 100%;

  &_top {
    border-radius: 16px;
    padding: 4px 16px;
    // background: rgba(242, 243, 245, 1);
    border: 1px solid rgba(229, 231, 236, 1);
    color: #000;
    display: flex;
    flex-grow: 0;
    align-self: flex-start;
    align-items: center;
    cursor: pointer;
    margin: 8px 0;
    :deep(.svgClass) {
      margin-left: 4px;
    }

    &:hover {
      background: rgba(232, 230, 246, 1);
      color: rgba(100, 65, 171, 1);
      :deep(.svgClass) {
        color: inherit !important;
        path {
          fill: currentColor !important;
        }
      }
    }
  }

  &_deepthink {
    border-radius: 16px;
    padding: 4px 16px;
    border: 1px solid rgba(229, 231, 236, 1);
    color: #000;
    display: flex;
    flex-grow: 0;
    align-self: flex-start;
    align-items: center;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 1);
    margin: 8px 0;
    :deep(.svgClass) {
      margin-left: 4px;
    }

    &:hover {
      background: rgba(229, 231, 236, 1);
      :deep(.svgClass) {
        color: inherit !important;
        path {
          fill: currentColor !important;
        }
      }
    }
    &:active {
      background: rgba(245, 242, 254, 1);
      color: rgba(100, 65, 171, 1);
    }
  }

  &_top-se {
    background: rgba(242, 243, 245, 1);
    border: 1px solid rgba(242, 243, 245, 1);
  }
  &_content {
    display: flex;
    flex-direction: column;
    padding-left: 16px;

    &_top {
      display: flex;
      margin-top: 8px;
      cursor: pointer;
      color: rgba(59, 59, 59, 1);
      &:hover {
        color: rgba(100, 65, 171, 1) !important;
      }

      &_num {
        font-weight: 600;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0%;
        margin-right: 8px;
      }

      &_content {
        &_title {
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;

          &_source {
            color: rgba(100, 65, 171, 1);
          }
        }

        &_bottom {
          display: flex;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          color: rgba(105, 105, 105, 1);
        }
      }
    }
  }
}
</style>
