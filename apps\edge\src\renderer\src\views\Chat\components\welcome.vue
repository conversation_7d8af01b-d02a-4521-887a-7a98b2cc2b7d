<template>
  <div class="welcome-box" :style="{ marginLeft: 0 }">
    <div class="welcome-box_icon">
      <SvgIcon :name="welcomeConfig?.avatar || 'NewChat-icon'" :size="'50'" />
    </div>
    <div class="welcome-box_title">{{ welcomeConfig?.title }}</div>
  </div>
</template>

<script setup lang="ts">
import { WelcomeType } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { emitter } from '@/utils/EventBus'
import { ref } from 'vue'
const welcomeWidth = ref()
defineProps<{
  welcomeConfig: WelcomeType | undefined
}>()

// emitter.on('getcurrent-width', (width: number) => {
//   if (width < 900 && width > 800) {
//     welcomeWidth.value = '630px'
//   } else {
//     welcomeWidth.value = '800px'
//   }
// })
</script>

<style scoped lang="less">
.welcome-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 16px;
  &_title {
    font-size: 32px;
    font-weight: 500;
    background: linear-gradient(90deg, #595959 -28.36%, #6428c8 72.66%, #3134cc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /** 添加不允许复制 */
    user-select: none;
    -webkit-user-select: none;
    /* Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE 10+ */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 30px;
  }

  &_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    border-radius: 120px;
    // box-shadow: -3px -4px 14px 0px rgba(97, 0, 255, 0.09) inset;
    // box-shadow: -3px -3px 2px 0px rgba(255, 255, 255, 0.3) inset;
    // box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.09);
    margin-right: 10px;
  }
}
</style>
