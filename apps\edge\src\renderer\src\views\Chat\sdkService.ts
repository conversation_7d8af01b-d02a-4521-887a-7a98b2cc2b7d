import { chatInterface, getUserId } from '@/services'
import { ChatSDK, ChatStatus, SDKResponse, StreamAnswerType } from '@libs/a-comps'
import { historyItemType } from '@libs/a-comps/ChatBaseComponent/types'
import MarkdownIt from 'markdown-it'
import { emitter } from '@/utils/EventBus'
import { historyBaseApi } from '../../api/historyBase'

let resourceId: string
let resourceType: string | undefined
emitter.on('agent-changed', (id: string, type: string) => {
  resourceId = id
  resourceType = type
})

interface MySDKImplType extends ChatSDK {
  streamController: AbortController | null
  dataCallback: ((data: StreamAnswerType) => void) | null
  markdownParser: MarkdownIt
  userId: string
}

class MySDKImpl implements MySDKImplType {
  streamController: AbortController | null = null
  dataCallback: ((data: StreamAnswerType) => void) | null = null
  markdownParser = new MarkdownIt()
  userId: string = ''
  sessionId: string = ''

  constructor() {}

  async init() {
    try {
      const res = await getUserId()
      this.userId = (res as any)?.data?.data || ''
    } catch (error) {
      console.log('getUserId error', error)
      this.userId = ''
    }
  }

  async sendMessage(
    text: string,
    sessionId: string,
    isDeepThink: boolean,
    isWebSearch: boolean,
    regen: boolean,
    history: historyItemType[],
    documentIds: Array<string>,
    knowledgeIds: Array<string>
  ): Promise<SDKResponse> {
    this.streamController = new AbortController()
    let reader: ReadableStreamDefaultReader<Uint8Array> | null = null

    console.log('regen=', regen)
    try {
      const response = await chatInterface({
        sessionId,
        agentId: 'LR',
        query: text,
        isStream: true,
        isWebSearch,
        regen,
        isDeepThink,
        history,
        documentIds,
        knowledgeIds,
        userId: this.userId,
        signal: this.streamController.signal,
        resourceId,
        resourceType
      })

      reader = response.data.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        try {
          const { done, value } = await reader.read()
          buffer += decoder.decode(value, { stream: true })
          const events = buffer.split('\n\n')
          buffer = events.pop() || '' // 保留未完成部分

          events.forEach((event) => {
            if (event.trim().startsWith('data: ')) {
              try {
                const data = JSON.parse(event.slice(6)) as StreamAnswerType
                handleFail(data)
                this.dataCallback?.(data)
              } catch (err) {}
            }
          })

          // 流结束时，buffer里如果还有内容且以 data: 开头，也要解析
          if (done) {
            if (buffer.trim().startsWith('data: ')) {
              try {
                const data = JSON.parse(buffer.trim().slice(6)) as StreamAnswerType
                handleFail(data)
                this.dataCallback?.(data)
              } catch (err) {}
            }
            break
          }
        } catch (error) {
          break
        }
      }

      return { status: ChatStatus.FAILURE, response: '' }
    } catch (error) {
      throw error
    } finally {
      reader?.releaseLock()
      this.streamController = null
    }

    // 将 "fail" 状态映射为有效的状态
    function handleFail(data: StreamAnswerType) {
      const statusStr = String(data.status) // 先转为字符串
      if (statusStr === 'fail') {
        data.status = ChatStatus.ONGOING
      }
    }
  }

  stopMessage(): void {
    // 中止流式请求
    if (this.streamController) {
      this.streamController.abort()
      this.streamController = null
    }
  }

  setDataCallback(callback: (data: StreamAnswerType) => void): void {
    this.dataCallback = callback
  }
}

export const mySDK: MySDKImplType = new MySDKImpl()
export default mySDK
