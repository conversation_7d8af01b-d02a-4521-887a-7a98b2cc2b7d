<template>
  <div class="explore-view">
    <div class="explore-view_banner">
      <Banner></Banner>
    </div>
    <div class="explore-view_agents">
      <div class="explore-view_agents_title global-title2">
        My Agents
        <span class="explore-view_agents_title-count global-text2">{{ agentList.length }}</span>
      </div>
      <div class="explore-view_agents_cards">
        <AgentCard
          v-for="item in agentList"
          :key="item.id"
          :agent="item"
          @click="handleAgent(item)"
        ></AgentCard>
      </div>
    </div>
    <div class="explore-view_footer global-text2">
      We are continuously expanding the AI Agent ecosystem. Stay tuned for more powerful features!
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Banner from './comps/Banner.vue'
import { getAgentList } from '@/services'
import { onMounted } from 'vue'
import AgentCard from './comps/AgentCard.vue'
import { AgentVo } from '@/types'
import { routeLocationKey, useRouter } from 'vue-router'
// import { info } from 'console';
const agentList = ref([] as Array<AgentVo>)
const router = useRouter()
const getList = () => {
  getAgentList()
    .then((res) => {
      console.log(res)
      if (res.data?.success) {
        agentList.value = res.data.data as AgentVo[]
      }
    })
    .catch((err) => {})
}
const handleAgent = (agent: AgentVo) => {
  console.log(agent, 'agent----')
  router.push({
    name: 'agent',
    query: {
      origin: agent.origin,
      id: agent.entityId,
      name: agent.agentName
    }
  })
}
onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.explore-view {
  padding: 8px 40px;
  padding-bottom: 48px;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;

  > * {
    margin: 0 auto;
    max-width: 940px;
  }

  &_banner {
    margin-bottom: 20px;
  }

  &_agents {
    &_title {
      margin-bottom: 25px;

      &-count {
        display: inline-block;
        border-radius: 10px;
        min-width: 20px;
        box-sizing: border-box;
        padding: 0 5px;
        text-align: center;
        line-height: 20px;
        background: var(--primary-color);
        color: #fff;
      }
    }

    &_cards {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      // grid-column-end: 0;
      // justify-content: space-between;

      > * {
        // flex: 1;
        // flex-shrink: 1;
        // flex-grow: 1;
      }
    }
  }

  &_footer {
    position: absolute;
    bottom: 0px;
    right: 0;
    left: 0;
    padding-bottom: 16px;
    width: 100%;
    text-align: center;
    background: linear-gradient(to top, #ffffff, #ffffff, transparent);
  }
}
</style>
