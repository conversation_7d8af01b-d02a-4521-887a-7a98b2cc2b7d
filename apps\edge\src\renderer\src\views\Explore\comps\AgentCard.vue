<template>
  <div class="agent-card">
    <div class="agent-card_header">
      <div class="agent-card_header_title global-title3">
        {{ agent.agentName }}
      </div>
      <div class="agent-card_header_owner">by {{ agent.origin }}</div>
    </div>
    <div class="agent-card_content">
      <div class="agent-card_content_icon">
        <img :src="agent.agentIcon" v-show="agent.agentIcon" alt="" />
      </div>
      <div class="agent-card_content_detail">{{ agent.agentDesc }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AgentVo } from '@/types'

defineProps<{
  agent: AgentVo
}>()
</script>

<style lang="less" scoped>
.agent-card {
  width: calc(50% - 8px);
  height: 140px;
  background: #fff;
  box-shadow: 0px 4px 6px 0px #0000000a;
  box-sizing: border-box;
  border-radius: 8px;
  padding: 13px 24px;
  cursor: pointer;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0px 8px 12px 0px rgba(0, 0, 0, 0.15);
  }

  &_header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;

    &_owner {
      color: var(--text-color3);
    }
  }

  &_content {
    display: flex;
    color: var(--text-color3);

    &_detail {
      display: -webkit-box;
      /* 兼容旧版WebKit */
      -webkit-line-clamp: 3;
      /* 限制文本为3行 */
      -webkit-box-orient: vertical;
      /* 垂直排列子元素 */
      overflow: hidden;
      /* 隐藏溢出的内容 */
    }

    &_icon {
      // border-radius: 50%;
      // overflow: hidden;
      margin-right: 22px;

      width: 64px;
      height: 64px;
      border-radius: 50%;
      background-color: var(--primary-color-bg);

      img {
        border-radius: 50%;

        width: 64px;
        height: 64px;
      }
    }
  }
}
</style>
