<template>
  <!-- <Row class="kb-container">
    <Col class="kb-list-c">
      <KBList
        @select-change="handleKBChange"
        @handleUpdateKnowledgeList="handleUpdateKnowledgeList"
      ></KBList>
    </Col>
    <Col class="kb-file-list">
      <KBFileList ref="kbFileListRef" :kbinfo="slectedKB"></KBFileList>
    </Col>
  </Row> -->
  <div class="kb-container">
    <div class="kb-file-list">
      <KBFileList ref="kbFileListRef" :kbinfo="slectedKB"></KBFileList>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Row, Col } from '@libs/a-comps'
import { IFileInfo } from '@/main/Service/fileSync/constants/types'
import { CutingTask } from '@/types'
import KBList from './components/KBList.vue'
import KBFileList from './components/KBFileList.vue'
// import { getCutTaskListData } from '@/services'
import { getCutTaskListData, getCutErrorListData } from '@/renderer/src/api/knowledgeBase'
import mySDK from '@renderer/views/Chat/sdkService'
import { GlobalConfig } from '@/renderer/src/common'
import { knowledgeStore } from './store'
import { IKBItem, KBType, IKBFileItem, CutErrorData, cutStatusMap, CutStatusEnum } from './type'
import { emitter } from '@/utils/EventBus'

const route = useRoute()

watch(
  () => route.query,
  (query) => {
    const { entityId, knowledgeBaseId, knowledgeBaseName, knowledgeBaseType, permission } = query
    if (route.name === 'knowledgeBase' && entityId) {
      handleKBChange({
        entityId,
        knowledgeBaseId,
        knowledgeBaseName,
        knowledgeBaseType,
        permission: Number(permission)
      })

      getCutTaskList(entityId as string)
    }
  }
)

const slectedKB = ref<IKBItem>({
  entityId: '',
  knowledgeBaseId: '',
  knowledgeBaseName: '',
  knowledgeBaseType: KBType.PERSON,
  createUserId: 0,
  createUserName: '',
  createTime: '',
  documentCount: 0,
  origin: '',
  loading: null,
  id: '',
  updateTime: '',
  permission: 0
})

// 切片任务数组
// const cutingTaskArr = ref<CutingTask[]>([])

const kbFileListRef = ref<InstanceType<typeof KBFileList> | null>(null)

// 切片错误数据
const cutErrorData = ref<CutErrorData>({
  totalCount: 0,
  pageCount: 0,
  knowledgeId: '',
  knowledgeName: '',
  storeGraph: false,
  documentList: []
})

const handleKBChange = (item: any) => {
  slectedKB.value = item

  cutErrorData.value = {
    totalCount: 0,
    pageCount: 0,
    knowledgeId: '',
    knowledgeName: '',
    storeGraph: false,
    documentList: []
  }

  // 更新切片错误文件列表
  updateCutErrorList(item.entityId)
}

/**
 * 更新切片错误文件列表
 * @param knowledgeId 知识库ID
 * @returns void
 */
const updateCutErrorList = (knowledgeId: string) => {
  if (kbFileListRef.value && slectedKB.value) {
    getCutErrorListData({
      knowledgeId,
      knowledgeBaseId: String(slectedKB.value.knowledgeBaseId)
    }).then((res) => {
      const { code, data } = res.data
      if (code === 200 && data) {
        cutErrorData.value = data
      }
    })
  }
}

/**
 * 获取切片任务列表
 * @param knowledgeId
 * @param callback
 * @returns
 */
const getCutTaskList = async (knowledgeId: string, callback?: () => void) => {
  try {
    if (!slectedKB.value) return
    const res = await getCutTaskListData({
      knowledgeId: knowledgeId,
      userId: mySDK.userId,
      knowledgeBaseId: String(slectedKB.value.knowledgeBaseId)
    })
    const { code, data } = res.data

    if (code === 200 && data) {
      let cutingTaskArr: CutingTask[] = []
      cutingTaskArr = data.map((item: CutingTask) => {
        return {
          ...item,
          name: item.documentName,
          taskType: 'cut',
          cutStatus: cutStatusMap[item.status] || item.status,
          fileId: item.documentId
        }
      })

      if (callback) {
        callback()
      } else {
        // 检查哪些任务已经完成（从列表中消失了）
        const oldCutingTaskArr = knowledgeStore.knowledgeTaskMap[knowledgeId]?.cutingTaskArr || []
        const newDocumentIds = new Set(cutingTaskArr.map((task) => task.documentId))

        // 找出已完成的任务（在旧列表中但不在新列表中的）
        const completedTasks = oldCutingTaskArr.filter(
          (task) =>
            !newDocumentIds.has(task.documentId) && task.cutStatus === CutStatusEnum.PROCESSING
        )

        // 为每个完成的任务触发单文件完成事件
        completedTasks.forEach((completedTask) => {
          emitter.emit('single-file-completed', {
            documentId: completedTask.documentId,
            knowledgeId: knowledgeId
          })
        })

        const { cutingTaskTimer } = knowledgeStore.knowledgeTaskMap[knowledgeId] || {}
        if (cutingTaskTimer) {
          clearTimeout(cutingTaskTimer)
          knowledgeStore.updateKnowledgeCutingTaskTimer({
            knowledgeId,
            cutingTaskTimer: null
          })
        }

        // 过滤PROCESSING的切片任务
        const processingCutingTaskArr = cutingTaskArr.filter((cutingTaskItem) => {
          return cutingTaskItem.cutStatus === CutStatusEnum.PROCESSING
        })

        if (processingCutingTaskArr.length > 0) {
          const newCutingTaskTimer = setTimeout(() => {
            getCutTaskList(knowledgeId)
          }, 1000)

          knowledgeStore.updateKnowledgeCutingTaskTimer({
            knowledgeId,
            cutingTaskTimer: newCutingTaskTimer
          })
        } else {
          if (kbFileListRef.value && slectedKB.value.entityId === knowledgeId) {
            // 无正在处理的切片任务，说明所有任务都已完成或失败,触发刷新列表事件让已完成文件显示在列表中
            emitter.emit('refresh-knowledge-list')

            // 刷新选中知识库文件表格
            kbFileListRef.value.refreshFileList()

            // 更新切片错误文件列表
            updateCutErrorList(knowledgeId)
          }
        }
      }

      // 更新知识库任务映射
      knowledgeStore.updateKnowledgeCutingTaskArr({
        knowledgeId,
        cutingTaskArr
      })
    }
  } catch (error) {
    console.error('root: Get cut task list failed:', error)

    // 出错后的重试逻辑 延长重试间隔
    const newCutingTaskTimer = setTimeout(() => {
      getCutTaskList(knowledgeId)
    }, 3000)

    knowledgeStore.updateKnowledgeCutingTaskTimer({
      knowledgeId,
      cutingTaskTimer: newCutingTaskTimer
    })
  }
}

provide('cutErrorData', {
  cutErrorData,
  updateCutErrorList,
  getCutTaskList
})

/**
 * 处理更新知识库列表
 * @param knowledgeArr 知识库列表
 */
const handleUpdateKnowledgeList = (knowledgeArr: IKBItem[]) => {
  // 创建知识库任务映射
  knowledgeStore.createKnowledgeTaskMapByKnowledgeArr(knowledgeArr)

  knowledgeArr.forEach((knowledgeItem: IKBItem) => {
    const { entityId } = knowledgeItem

    // 获取切片任务列表
    getCutTaskList(entityId)
  })
}

onMounted(async () => {
  // activeItem.value = menusPrivate[0];

  const { entityId, knowledgeBaseId, knowledgeBaseName, knowledgeBaseType, permission } =
    route.query
  if (entityId) {
    handleKBChange({
      entityId,
      knowledgeBaseId,
      knowledgeBaseName,
      knowledgeBaseType,
      permission: Number(permission)
    })

    getCutTaskList(entityId as string)
  }

  // 获取待处理文件
  window.api.getPendingFiles().then((pendingFiles: IFileInfo[]) => {
    // 创建知识库任务映射
    knowledgeStore.createKnowledgeTaskMap(pendingFiles)

    knowledgeStore.updateKnowledgeImportingFileListByPendingFiles({
      pendingFiles
    })
  })

  // 取消监听文件上传相关事件
  window.api.removeAllListeners()

  // 渲染进程响应心跳
  window.api.onReceiveHeartbeat((data: { timestamp: string }) => {
    console.log('Renderer received heartbeat at:', data.timestamp)
  })

  // 通知主进程渲染进程已就绪
  try {
    await window.api.notifyRendererReady()
  } catch (error) {
    console.error('通知主进程就绪失败:', error)
  }

  // 监听选择文件后的上传列表变化
  window.api.onFileListUpdated(
    (currentKBFilesInfo: { source: string; files: IFileInfo[]; pendingFiles: IFileInfo[] }) => {
      const { source, files, pendingFiles } = currentKBFilesInfo

      try {
        // 创建知识库任务映射
        knowledgeStore.createKnowledgeTaskMap(pendingFiles)

        // 更新知识库导入文件列表
        knowledgeStore.updateKnowledgeImportingFileList({
          knowledgeId: source,
          importingFileList: files
        })
      } catch (error) {
        console.error('Failed to handle file list update:', error)
      }
    }
  )

  //UPLOAD success
  window.api.onOneUploadCompleted(
    (uploadSuccessInfo: {
      source: string
      sourceFiles: IFileInfo[]
      pendingFiles: IFileInfo[]
    }) => {
      const { source, sourceFiles, pendingFiles } = uploadSuccessInfo

      try {
        knowledgeStore.updateKnowledgeImportingFileList({
          knowledgeId: source,
          importingFileList: sourceFiles
        })

        // 获取切片任务列表
        getCutTaskList(source, () => {
          // 如果已经没有定时任务则开始
          if (!knowledgeStore.knowledgeTaskMap[source].cutingTaskTimer) {
            getCutTaskList(source)
          }
        })
      } catch (error) {
        console.error('Failed to handle OneUploadCompleted:', error)
      }
    }
  )

  // 监听worker开始上传
  window.api.onUploadToWorkerStart((messageData: any) => {
    if (messageData?.isReady) {
      console.log('onUploadToWorkerStart:', messageData)
      kbFileListRef?.value?.openDrawer()
    }
  })
})

onUnmounted(() => {
  // 取消监听文件上传相关事件
  window.api.removeAllListeners()

  // 清除定时器
  for (const knowledgeId in knowledgeStore.knowledgeTaskMap) {
    const { cutingTaskTimer } = knowledgeStore.knowledgeTaskMap[knowledgeId]
    if (cutingTaskTimer) {
      clearTimeout(cutingTaskTimer)
      knowledgeStore.updateKnowledgeCutingTaskTimer({
        knowledgeId,
        cutingTaskTimer: null
      })
    }
  }
})
</script>
<style lang="less" scoped>
.kb-container {
  background-color: #ffffff;
  height: calc(100vh - 40px);
  // width: 100%;
  padding: 0 18px 0 25px;
  font-family: Segoe UI Variable Static Text;
  .kb-list-c {
    width: 200px;
    height: 100%;
    border-right: 1px solid #e5e7ec;
    padding: 0 4px;
  }
  .kb-file-list {
    // width: calc(100% - 212px);
    // margin-left: 12px;
  }
}
</style>
<style lang="less">
.ant-tooltip .ant-tooltip-arrow::before {
  clip-path: path(
    'M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z'
  );
}
</style>
