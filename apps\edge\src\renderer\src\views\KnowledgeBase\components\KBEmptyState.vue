<template>
  <Transition
    name="kb-empty-state-fade"
    enter-active-class="transition-opacity duration-300 ease-in-out"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition-opacity duration-300 ease-in-out"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="shouldShowEmpty"
      class="kb-file-container__empty-wrapper"
      :class="{ 'kb-file-container__empty-wrapper--hidden': !isFullyVisible }"
    >
      <div class="kb-file-container__empty-icon"></div>
      <template v-if="canOperateDocument">
        <div class="kb-file-container__empty-text">
          Add project files to turn knowledge into team productivity.
        </div>
        <div class="kb-file-container__empty-button-wrapper">
          <ABtn class="kb-file-container__import-button" type="primary" @click="handleUpload">
            <SvgIcon
              class="kb-file-container__import-button-icon"
              name="import-icon"
              size="11"
            />Import
          </ABtn>
        </div>
      </template>
      <div v-else class="kb-file-container__empty-text">The knowledge base is currently empty.</div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ABtn } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'

interface Props {
  isEmpty: boolean
  isInitialized: boolean
  hasSearchKeyword: boolean
  canOperateDocument: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  upload: []
}>()

// 内部状态管理
const isVisible = ref(false)
const isFullyVisible = ref(false)
let showTimer: NodeJS.Timeout | null = null
let hideTimer: NodeJS.Timeout | null = null

// 防抖延迟，避免快速闪烁
const DEBOUNCE_DELAY = 150
// 最小显示时间，确保完全显示
const MIN_DISPLAY_TIME = 200

// 计算是否应该显示空状态
const shouldShowEmpty = computed(() => {
  return props.isEmpty && props.isInitialized && !props.hasSearchKeyword
})

// 监听显示状态变化
watch(
  shouldShowEmpty,
  async (newVal, oldVal) => {
    // 清理之前的定时器
    if (showTimer) {
      clearTimeout(showTimer)
      showTimer = null
    }
    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }

    if (newVal) {
      // 从隐藏到显示：添加防抖延迟
      showTimer = setTimeout(() => {
        isVisible.value = true
        nextTick(() => {
          // 确保DOM更新后再设置为完全可见
          setTimeout(() => {
            isFullyVisible.value = true
          }, 50)
        })
      }, DEBOUNCE_DELAY)
    } else {
      // 从显示到隐藏：立即开始隐藏动画
      isFullyVisible.value = false
      hideTimer = setTimeout(() => {
        isVisible.value = false
      }, MIN_DISPLAY_TIME)
    }
  },
  { immediate: true }
)

// 处理上传按钮点击
const handleUpload = () => {
  emit('upload')
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (showTimer) {
    clearTimeout(showTimer)
    showTimer = null
  }
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
})
</script>

<style lang="less" scoped>
.kb-file-container__empty-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: calc(100vh - 100px);
  transition: opacity 0.3s ease-in-out;

  &.kb-file-container__empty-wrapper--hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.kb-file-container__empty-icon {
  width: 72px;
  height: 72px;
  margin-bottom: 12px;
  background: url('@renderer/assets/images/<EMAIL>') no-repeat;
  background-size: 100%;
}

.kb-file-container__empty-text {
  margin-bottom: 24px;
  color: #3f3f46;
}

.kb-file-container__import-button {
  height: initial;
  margin-right: 12px;
  padding: 4px 15px;
  vertical-align: middle;
}

.kb-file-container__import-button-icon {
  margin-right: 5.5px;
  vertical-align: initial;
  color: #fff;
}

// 淡入淡出动画
.kb-empty-state-fade-enter-active,
.kb-empty-state-fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.kb-empty-state-fade-enter-from,
.kb-empty-state-fade-leave-to {
  opacity: 0;
}
</style>
