<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\KnowledgeBase\components\KBFileDrawer.vue
-->
<template>
  <Drawer
    v-model:open="isOpenDrawer"
    class="kb-file-drawer"
    root-class-name="kb-file-drawer-root"
    get-container="#app"
    :closable="false"
    :bodyStyle="{ padding: '6px 16px' }"
  >
    <a-tabs v-model:activeKey="activeTabKey">
      <a-tab-pane key="1" tab="Importing">
        <div class="kb-file-drawer__empty-wrapper" v-if="importingTableDataSource.length === 0">
          <div class="kb-file-drawer__empty-icon"></div>
          <div class="kb-file-drawer__empty-text">No Files</div>
        </div>
        <template v-else>
          <div class="kb-file-drawer__importing-header">
            <span class="kb-file-drawer__importing-buttons-wrapper">
              <!-- <ABtn
                class="kb-file-drawer__importing-button"
                @click="handleClickStartAllButton"
                :disabled="!hasSelectedImportingFiles"
              >
                <SvgIcon
                  name="start-all-icon"
                  class="kb-file-drawer__importing-button-icon"
                  size="14"
                />
                <span class="kb-file-drawer__importing-button-text">Start</span>
              </ABtn>
              <ABtn
                class="kb-file-drawer__importing-button"
                @click="handleClickPauseAllButton"
                :disabled="!hasSelectedImportingFiles"
              >
                <SvgIcon
                  name="pause-icon"
                  class="kb-file-drawer__importing-button-icon"
                  size="14"
                />
                <span class="kb-file-drawer__importing-button-text">Pause</span>
              </ABtn> -->
              <ABtn
                class="kb-file-drawer__importing-button"
                @click="handleClickDeleteAllButton"
                :disabled="!hasSelectedImportingFiles"
              >
                <SvgIcon
                  name="delete-icon"
                  class="kb-file-drawer__importing-button-icon"
                  size="14"
                />
                <span class="kb-file-drawer__importing-button-text">Delete</span>
              </ABtn>
            </span>
            <div class="kb-file-drawer__importing-status-wrapper">
              <div class="kb-file-drawer__importing-status">
                total {{ importingTableDataSource.length }} files
              </div>
              <!-- <div class="kb-file-drawer__importing-status">
                Paused {{ importingTablePausedArr.length }}
              </div> -->
            </div>
          </div>
          <div class="kb-file-drawer__importing-body">
            <ATable
              :columns="importingTableColumns"
              :dataSource="importingTableDataSource"
              row-key="fileId"
              :row-selection="{
                selectedRowKeys: selectedImportingRowKeyArr,
                onChange: handleChangSelectedImportingRowKeys
              }"
              :row-class-name="() => 'kb-file-drawer__importing-table-row'"
              :customRow="importingTableCustomRow"
              :pagination="false"
              size="small"
              :scroll="{ y: 'calc(100vh - 186px)' }"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'name'">
                  <SvgIcon
                    class="kb-file-drawer__file-type-svg"
                    :name="getFileSvgNameSvgName(record.name)"
                    size="24"
                  >
                  </SvgIcon>
                  <ATooltip
                    placement="topLeft"
                    color="#525A69"
                    :overlayStyle="{ maxWidth: '260px' }"
                  >
                    <template #title>{{ record.name }}</template>
                    <span>{{ record.name }}</span>
                  </ATooltip>
                </template>
                <template v-if="column.key === 'status'">
                  <span v-show="currentImportTableRowIndex !== index">
                    <span
                      v-if="record.taskType === 'cut'"
                      class="kb-file-drawer__importing-table-status cut"
                      :class="`kb-file-drawer__importing-table-status_${record.cutStatus.split(' ').join('-')}`"
                      >{{ record.cutStatus }}</span
                    >
                    <span
                      v-else
                      class="kb-file-drawer__importing-table-status upload"
                      :class="`kb-file-drawer__importing-table-status_${record.status.split(' ').join('-')}`"
                    >
                      {{ record.status }}
                    </span>
                    <!-- <span
                      v-else
                      class="kb-file-drawer__importing-table-status upload"
                      :class="`kb-file-drawer__importing-table-status_${record.status.split(' ').join('-')}`"
                    >
                      {{ record.status }}
                    </span> -->
                  </span>
                  <span
                    class="kb-file-drawer__importing-table-row-action-wrapper"
                    v-show="currentImportTableRowIndex === index"
                  >
                    <!-- <ABtn
                      v-if="
                        record.status === FileStatus.PAUSED ||
                        record.cutStatus === CutStatusEnum.PAUSED
                      "
                      type="link"
                      class="kb-file-drawer__failed-table-button"
                      @click="handleClickStartButton(record)"
                      >Start</ABtn
                    >
                    <ABtn
                      v-else-if="
                        record.status === FileStatus.PENDING ||
                        record.status === FileStatus.UPLOADING ||
                        record.cutStatus === CutStatusEnum.PROCESSING
                      "
                      type="link"
                      class="kb-file-drawer__failed-table-button"
                      @click="handleClickPauseButton(record)"
                      >Pause</ABtn
                    > -->
                    <ABtn
                      type="link"
                      class="kb-file-drawer__failed-table-button"
                      @click="handleClickDeleteButton(record)"
                      >Delete</ABtn
                    >
                  </span>
                </template>
              </template>
            </ATable>
          </div>
        </template>
      </a-tab-pane>
      <a-tab-pane key="2" tab="Failed">
        <div class="kb-file-drawer__empty-wrapper" v-if="failedTableDataSource.length === 0">
          <div class="kb-file-drawer__empty-icon"></div>
          <div class="kb-file-drawer__empty-text">No Files</div>
        </div>
        <template v-else>
          <div class="kb-file-drawer__importing-header">
            <span class="kb-file-drawer__importing-buttons-wrapper">
              <ABtn
                class="kb-file-drawer__importing-button"
                @click="handleClickRetryAllButton"
                :disabled="!hasSelectedFailedFiles"
              >
                <SvgIcon
                  name="retry-icon"
                  class="kb-file-drawer__importing-button-icon"
                  size="14"
                />
                <span class="kb-file-drawer__importing-button-text">Retry</span>
              </ABtn>
              <ABtn
                class="kb-file-drawer__importing-button"
                @click="handleClickClearAllButton"
                :disabled="!hasSelectedFailedFiles"
              >
                <SvgIcon
                  name="delete-icon"
                  class="kb-file-drawer__importing-button-icon"
                  size="14"
                />
                <span class="kb-file-drawer__importing-button-text">Delete</span>
              </ABtn>
            </span>
            <div class="kb-file-drawer__failed-status-wrapper">
              <div class="kb-file-drawer__failed-status">
                total {{ failedTableDataSource.length }} files
              </div>
            </div>
          </div>
          <div class="kb-file-drawer__failed-body">
            <ATable
              :columns="failedTableColumns"
              :dataSource="failedTableDataSource"
              row-key="fileId"
              :row-selection="{
                selectedRowKeys: selectedFailedRowKeyArr,
                onChange: handleChangSelectedFailedRowKeys
              }"
              :row-class-name="() => 'kb-file-drawer__failed-table-row'"
              :customRow="failedTableCustomRow"
              :pagination="false"
              size="small"
              :scroll="{ y: 'calc(100vh - 186px)' }"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'name'">
                  <SvgIcon
                    class="kb-file-drawer__file-type-svg"
                    :name="getFileSvgNameSvgName(record.name)"
                    size="24"
                  >
                  </SvgIcon>
                  <ATooltip
                    placement="topLeft"
                    color="#525A69"
                    :overlayStyle="{ maxWidth: '260px' }"
                  >
                    <template #title>{{ record.name }}</template>
                    <span
                      :class="{
                        'kb-file-drawer__failed-name-truncated':
                          currentFailedTableRowIndex === index
                      }"
                      >{{ record.name }}</span
                    >
                  </ATooltip>
                  <span
                    class="kb-file-drawer__failed-table-row-action-wrapper"
                    v-if="currentFailedTableRowIndex === index"
                  >
                    <ABtn
                      type="link"
                      class="kb-file-drawer__failed-table-button"
                      @click="handleClickRetryButton(record)"
                      >Retry</ABtn
                    >
                    <ABtn
                      type="link"
                      class="kb-file-drawer__failed-table-button"
                      @click="handleClickClearButton(record)"
                      >Delete</ABtn
                    >
                  </span>
                </template>
                <template v-if="column.key === 'status'">
                  <ATooltip
                    v-if="record.taskType === 'cut'"
                    placement="top"
                    color="#525A69"
                    :overlayStyle="{
                      paddingRight: '18px',
                      minWidth: '172px',
                      maxWidth: '300px',
                      wordWrap: 'break-word'
                    }"
                  >
                    <template #title>{{ record.errorInfo || record.cutStatus }}</template>

                    <span
                      class="kb-file-drawer__importing-table-status cut kb-file-drawer__importing-table-status_Failed"
                      >{{ record.cutStatus }}</span
                    >
                  </ATooltip>
                  <ATooltip
                    v-else
                    placement="top"
                    color="#525A69"
                    :overlayStyle="{
                      paddingRight: '18px',
                      minWidth: '135px',
                      maxWidth: '300px',
                      wordWrap: 'break-word'
                    }"
                  >
                    <template #title>{{ record.errorInfo || record.status }}</template>

                    <span
                      class="kb-file-drawer__importing-table-status upload"
                      :class="`kb-file-drawer__importing-table-status_${record.status.split(' ').join('-')}`"
                    >
                      {{ record.status }}
                    </span>
                  </ATooltip>
                </template>
              </template>
            </ATable>
          </div>
        </template>
      </a-tab-pane>
    </a-tabs>
    <div class="kb-file-drawer__button-wrapper">
      <ABtn class="kb-file-drawer__import-button" type="primary" @click="handleClickImportButton">
        <SvgIcon class="kb-file-drawer__import-button-icon" name="import-icon" size="11" />
      </ABtn>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
import { ref, computed, inject, Ref, watch, createVNode } from 'vue'
import { Drawer, ATabs, ATabPane, ABtn, ATable, AModal, ATooltip } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { getFileSvgNameSvgName } from '@renderer/hooks/fileType'
import { truncateText } from '@/utils'
import {
  pauseCutTask,
  startCutTask,
  deleteCutTask,
  knowledgeBaseApi,
  retryCutTask
} from '@/renderer/src/api/knowledgeBase'
import { knowledgeStore } from '../store'
import { FileStatus, IFileInfo } from '@/main/Service/fileSync/constants/types'
import { CutingTask } from '@/types'
import mySDK from '@renderer/views/Chat/sdkService'
import { ImportingFileItem, CutErrorData, cutStatusMap, IKBFileItem, CutStatusEnum } from '../type'
import { emitter } from '@/utils/EventBus'

const props = defineProps<{
  knowledgeId: string
  knowledgeBaseId: string
}>()

const { cutErrorData, updateCutErrorList, getCutTaskList } = inject('cutErrorData') as {
  cutErrorData: Ref
  updateCutErrorList: (knowledgeId: string) => void
  getCutTaskList: (knowledgeId: string) => void
}

const isOpenDrawer = defineModel()

const activeTabKey = ref('1')

// 选中Importing行相关
const selectedImportingRowKeyArr = ref<string[]>([])
const selectedImportingRowArr = ref<ImportingFileItem[]>([])

// 当前ImportingTable行索引
const currentImportTableRowIndex = ref<number | null>(null)

const selectedUploadingTaskIdArr = computed(() => {
  return selectedImportingRowArr.value
    .filter((item) => item.taskType !== 'cut' && item.status === FileStatus.UPLOADING)
    .map((item) => item.fileId)
})

const selectedWaitingTaskIdArr = computed(() => {
  return selectedImportingRowArr.value
    .filter((item) => item.taskType !== 'cut' && item.status === FileStatus.PENDING)
    .map((item) => item.fileId)
})

const selectedUploadPausedTaskIdArr = computed(() => {
  return selectedImportingRowArr.value
    .filter((item) => item.taskType !== 'cut' && item.status === FileStatus.PAUSED)
    .map((item) => item.fileId)
})

const selectedNoCutNoUploadingTaskIdArr = computed(() => {
  return selectedImportingRowArr.value
    .filter((item) => item.taskType !== 'cut' && item.status !== FileStatus.UPLOADING)
    .map((item) => item.fileId)
})

// cut相关
// 选中cut任务数组
const selectedCutingTaskArr = computed(() => {
  return selectedImportingRowArr.value.filter((item) => item.taskType === 'cut')
})

const selectedCutingTaskIdArr = computed(() => {
  return selectedCutingTaskArr.value.map((item) => item.documentId)
})

const selectedProcessingTaskIdArr = computed(() => {
  return selectedImportingRowArr.value
    .filter((item) => item.taskType === 'cut' && item.cutStatus === CutStatusEnum.PROCESSING)
    .map((item) => item.documentId)
})

const selectedCutPausedTaskIdArr = computed(() => {
  return selectedImportingRowArr.value
    .filter((item) => item.taskType === 'cut' && item.cutStatus === CutStatusEnum.PAUSED)
    .map((item) => item.documentId)
})

// Failed相关
// 选中Failed行key数组
const selectedFailedRowKeyArr = ref<string[]>([])
const selectedFailedRowArr = ref<ImportingFileItem[]>([])

const hasSelectedImportingFiles = computed(() => {
  return selectedImportingRowKeyArr.value.length > 0
})

const hasSelectedFailedFiles = computed(() => {
  return selectedFailedRowKeyArr.value.length > 0
})

const selectedCutErrorTaskIdArr = computed(() => {
  return selectedFailedRowArr.value
    .filter((item) => item.taskType === 'cut' && item.cutStatus === CutStatusEnum.FAILED)
    .map((item) => item.documentId)
})

const selectedUploadFailedTaskIdArr = computed(() => {
  return selectedFailedRowArr.value
    .filter((item) => item.taskType !== 'cut' && item.status === FileStatus.FAILED)
    .map((item) => item.fileId)
})

const importingTableColumns = [
  {
    key: 'name',
    title: 'Name',
    dataIndex: 'name',
    width: '59%',
    ellipsis: true
  },
  {
    key: 'status',
    title: 'Action',
    dataIndex: 'status',
    width: '41%'
  }
]

const failedTableColumns = [
  {
    key: 'name',
    title: 'Name',
    dataIndex: 'name',
    width: '74%',
    ellipsis: true
  },
  {
    key: 'status',
    title: 'State',
    dataIndex: 'status',
    width: '26%'
  }
]

// 导入表格数据源
const importingFileArr = computed(() => {
  return knowledgeStore.knowledgeTaskMap[props.knowledgeId]?.importingFileList || []
})

// 正在导入任务数组
const importingTaskArr = computed(() => {
  return importingFileArr.value.filter((importingFileItem) => {
    return importingFileItem.status !== FileStatus.FAILED
  })
})

// 切片任务数组
const cutingTaskArr = computed(() => {
  return knowledgeStore.knowledgeTaskMap[props.knowledgeId]?.cutingTaskArr || []
})

const importingTableDataSource = computed(() => {
  return [...cutingTaskArr.value, ...importingTaskArr.value]
})

// 导入中表格暂停任务数组
// const importingTablePausedArr = computed(() => {
//   // 切块暂停任务数组
//   const cutingPausedTaskArr = cutingTaskArr.value.filter((cutingTaskItem) => {
//     return cutingTaskItem.cutStatus === CutStatusEnum.PAUSED
//   })

//   // 上传暂停任务数组
//   const importingPausedTaskArr = importingFileArr.value.filter((importingFileItem) => {
//     return importingFileItem.status === FileStatus.PAUSED
//   })

//   return [...cutingPausedTaskArr, ...importingPausedTaskArr]
// })

// 当前FailedTable行索引
const currentFailedTableRowIndex = ref<number | null>(null)

// 切片错误文件表格数据源
const cutErrorTableDataSource = computed(() => {
  return cutErrorData.value.documentList.map((cutErrorItem: IKBFileItem) => {
    const { documentName, status, documentId } = cutErrorItem

    let errorInfo = ''
    if (status === 'documentCutError') {
      errorInfo = 'Failed to parse the file.'
      // errorInfo = 'File parsing timed out.'
    } else if (status === 'documentCutTimeOut') {
      errorInfo = 'File parsing timed out.'
    }

    return {
      ...cutErrorItem,
      cutStatus: cutStatusMap[status] || status,
      name: documentName,
      taskType: 'cut',
      fileId: documentId,
      errorInfo
    }
  })
})

// 失败文件表格数据源
const importingFailedFileArr = computed(() => {
  const failedFiles = importingFileArr.value.filter((importingFileItem) => {
    return importingFileItem.status === FileStatus.FAILED
  })

  return failedFiles.map((file) => {
    if (file.size === 0) {
      return {
        ...file,
        errorInfo: 'The file is empty.'
      }
    }
    return file
  })
})

const failedTableDataSource = computed(() => {
  return [...cutErrorTableDataSource.value, ...importingFailedFileArr.value]
})

// 监听importingTableDataSource变化，过滤selectedImportingRowKeyArr元素和selectedImportingRowArr元素
watch(importingTableDataSource, (newDataSource: ImportingFileItem[]) => {
  selectedImportingRowKeyArr.value = selectedImportingRowKeyArr.value.filter(
    (selectedImportingRowKeyItem) => {
      return newDataSource.some((newDataSourceItem) => {
        const { documentId, fileId, taskType } = newDataSourceItem
        if (taskType === 'cut') {
          return documentId === selectedImportingRowKeyItem
        } else {
          return fileId === selectedImportingRowKeyItem
        }
      })
    }
  )

  selectedImportingRowArr.value = selectedImportingRowArr.value.filter(
    (selectedImportingRowItem) => {
      return newDataSource.some((newDataSourceItem) => {
        const { documentId, fileId, taskType } = newDataSourceItem
        if (taskType === 'cut') {
          return documentId === selectedImportingRowItem.documentId
        } else {
          return fileId === selectedImportingRowItem.fileId
        }
      })
    }
  )
})

// 监听cutErrorTableDataSource变化，过滤selectedCutErrorTaskIdArr元素和selectedCutErrorTaskArr元素
watch(cutErrorTableDataSource, (newDataSource: ImportingFileItem[]) => {
  selectedFailedRowKeyArr.value = selectedFailedRowKeyArr.value.filter(
    (selectedFailedRowKeyItem) => {
      return newDataSource.some((newDataSourceItem) => {
        const { documentId, fileId, taskType } = newDataSourceItem
        if (taskType === 'cut') {
          return documentId === selectedFailedRowKeyItem
        } else {
          return fileId === selectedFailedRowKeyItem
        }
      })
    }
  )

  selectedFailedRowArr.value = selectedFailedRowArr.value.filter((selectedFailedRowItem) => {
    return newDataSource.some((newDataSourceItem) => {
      const { documentId, fileId, taskType } = newDataSourceItem
      if (taskType === 'cut') {
        return documentId === selectedFailedRowItem.documentId
      } else {
        return fileId === selectedFailedRowItem.fileId
      }
    })
  })
})

// importingTableCustomRow
const importingTableCustomRow = (record: ImportingFileItem, index: number) => {
  return {
    onclick: () => {
      if (!selectedImportingRowKeyArr.value.includes(record.fileId)) {
        selectedImportingRowKeyArr.value.push(record.fileId)
        selectedImportingRowArr.value.push(record)
      }
    },
    onMouseenter: (event: MouseEvent) => {
      currentImportTableRowIndex.value = index
    },
    onMouseleave: () => {
      currentImportTableRowIndex.value = null
    }
  }
}

const failedTableCustomRow = (record: ImportingFileItem, index: number) => {
  return {
    onclick: () => {
      if (!selectedFailedRowKeyArr.value.includes(record.fileId)) {
        selectedFailedRowKeyArr.value.push(record.fileId)
      }
    },
    onMouseenter: (event: MouseEvent) => {
      currentFailedTableRowIndex.value = index
    },
    onMouseleave: () => {
      currentFailedTableRowIndex.value = null
    }
  }
}

/**
 * 处理改变选中行
 * @param selectedRowKeys 选中行的key数组
 * @returns void
 */
const handleChangSelectedImportingRowKeys = (
  selectedRowKeys: string[],
  selectedRows: ImportingFileItem[]
) => {
  selectedImportingRowKeyArr.value = selectedRowKeys

  selectedImportingRowArr.value = selectedRows
}

/**
 * 处理改变选中失败行
 * @param selectedRowKeys 选中行的key数组
 * @returns void
 */
const handleChangSelectedFailedRowKeys = (
  selectedRowKeys: string[],
  selectedRows: ImportingFileItem[]
) => {
  selectedFailedRowKeyArr.value = selectedRowKeys
  selectedFailedRowArr.value = selectedRows
}

/**
 * 处理点击导入按钮
 * @returns void
 */
const handleClickImportButton = () => {
  window.api.selectFiles({
    source: props.knowledgeId,
    isLimitFileCount: false,
    userIdForUpload: mySDK.userId,
    resourceId: props.knowledgeBaseId,
    resourceType: '2' // 2表示知识库
  })
}

/**
 * 暂停切片任务
 * @param documentIds 文档ID数组
 * @returns void
 */
const pauseCutingTask = (documentIds: string[]) => {
  pauseCutTask(documentIds).then((res) => {
    const { code } = res.data
    if (code === 200) {
    }
  })
}

/**
 * 开始切片任务
 * @param documentIds
 * @returns void
 */
const startCutingTask = (documentIds: string[]) => {
  startCutTask(documentIds).then((res) => {
    const { code } = res.data
    if (code === 200) {
      // 获取切片任务列表
      getCutTaskList(props.knowledgeId)

      // 统一触发刷新事件，避免多次刷新
      setTimeout(() => {
        emitter.emit('refresh-knowledge-list')
      }, 300)
    }
  })
}

/**
 * 删除切片任务
 * @param documentIds 文档ID数组
 */
const deleteCutingTask = (documentIds: string[]) => {
  deleteCutTask(documentIds).then((res) => {
    const { code } = res.data
    if (code === 200) {
      // 删除对应文件
      knowledgeBaseApi
        .deleteKBFiles({
          fileIds: documentIds,
          knowledgeBaseId: props.knowledgeBaseId
        })
        .then((deleteRes) => {
          const { code: deleteCode } = deleteRes.data
        })
        .catch((error) => {
          console.error('delete error:', error)
        })
    }
  })
}

/**
 * 清空选择Importing行
 * @param
 * @returns void
 */
const clearSelectedImportingTableRow = () => {
  selectedImportingRowKeyArr.value = []
  selectedImportingRowArr.value = []
}

/**
 * 清空选择Failed行
 * @param
 * @returns void
 */
const clearSelectedFailedTableRow = () => {
  selectedFailedRowKeyArr.value = []
  selectedFailedRowArr.value = []
}

/**
 * 处理点击暂停按钮
 * @returns void
 */
const handleClickPauseAllButton = () => {
  if (selectedProcessingTaskIdArr.value.length) {
    // 暂停切片任务
    pauseCutingTask(selectedProcessingTaskIdArr.value)
  }

  if (selectedUploadingTaskIdArr.value.length) {
    // 暂停上传中任务
    window.api.pauseUploadingTask(selectedUploadingTaskIdArr.value)
  }

  if (selectedWaitingTaskIdArr.value.length) {
    // 暂停等待上传任务
    window.api.cancelUpload(selectedWaitingTaskIdArr.value)
  }

  clearSelectedImportingTableRow()
}

/**
 * 处理点击行暂停按钮
 * @param record 行数据
 * @returns void
 */
const handleClickPauseButton = (record: ImportingFileItem) => {
  const { documentId, fileId, taskType, status, cutStatus } = record

  if (taskType === 'cut' && cutStatus === CutStatusEnum.PROCESSING) {
    // 如果任务类型是切片任务，则暂停切片任务
    pauseCutingTask([documentId])
  }

  if (taskType !== 'cut' && status === 'Uploading') {
    window.api.pauseUploadingTask([fileId])
  }

  if (taskType !== 'cut' && status !== 'Uploading') {
    window.api.cancelUpload([fileId])
  }
}

/**
 * 处理点击开始按钮
 * @returns void
 */
const handleClickStartAllButton = () => {
  if (selectedCutPausedTaskIdArr.value.length) {
    // 开始切片任务
    startCutingTask(selectedCutPausedTaskIdArr.value)
  }

  if (selectedUploadPausedTaskIdArr.value.length) {
    // 开始上传任务
    window.api.startUpload(selectedUploadPausedTaskIdArr.value)
  }

  clearSelectedImportingTableRow()
}

/**
 * 处理点击行开始按钮
 * @param record 行数据
 * @returns void
 */
const handleClickStartButton = (record: ImportingFileItem) => {
  const { documentId, fileId, taskType, status, cutStatus } = record

  if (taskType === 'cut' && cutStatus === CutStatusEnum.PAUSED) {
    // 如果任务类型是切片任务，则开始切片任务
    startCutingTask([documentId])
  }

  if (taskType !== 'cut' && status === FileStatus.PAUSED) {
    window.api.startUpload([fileId])
  }
}

/**
 * 处理点击删除按钮
 * @returns void
 */
const handleClickDeleteAllButton = () => {
  if (
    selectedCutingTaskIdArr.value.length ||
    selectedUploadingTaskIdArr.value.length ||
    selectedNoCutNoUploadingTaskIdArr.value.length
  ) {
    showConfirm().then((res) => {
      if (res) {
        if (selectedCutingTaskIdArr.value.length) {
          // 删除切片任务
          deleteCutingTask(selectedCutingTaskIdArr.value)
        }

        if (selectedUploadingTaskIdArr.value.length) {
          // 删除上传中任务
          window.api.deleteUploadingTask(selectedUploadingTaskIdArr.value)
        }

        if (selectedNoCutNoUploadingTaskIdArr.value.length) {
          // 删除任务
          window.api.deleteFiles(selectedNoCutNoUploadingTaskIdArr.value)
        }

        clearSelectedImportingTableRow()
      }
    })
  }
}

/**
 * 处理点击行删除按钮
 * @param fileId 文件ID
 * @returns void
 */
const handleClickDeleteButton = (record: ImportingFileItem) => {
  showConfirm().then((res) => {
    if (res) {
      const { documentId, fileId, taskType, status } = record

      if (taskType === 'cut') {
        // 如果任务类型是切片任务，则删除切片任务
        deleteCutingTask([documentId])
      }

      if (taskType !== 'cut' && status === 'Uploading') {
        window.api.deleteUploadingTask([fileId])
      }

      // 如果任务类型不是切片任务，并且状态不是上传中，则删除任务
      if (taskType !== 'cut' && status !== 'Uploading') {
        // 删除任务
        window.api.deleteFiles([fileId])
      }
    }
  })
}

/**
 * 删除切片错误文档
 * @param documentIds 文档ID数组
 * @returns void
 */
const deleteCutErrorDocument = (documentIds: string[]) => {
  knowledgeBaseApi
    .deleteKBFiles({
      fileIds: documentIds,
      knowledgeBaseId: props.knowledgeBaseId
    })
    .then((res) => {
      const { code } = res.data
      if (code === 200) {
        // 更新切片错误文档列表
        updateCutErrorList(props.knowledgeId)

        // 统一触发刷新事件，避免多次刷新
        setTimeout(() => {
          emitter.emit('refresh-knowledge-list')
        }, 300)
      }
    })
}

/**
 * 重试失败任务
 * @param fileIds 文件ID数组
 * @returns void
 */
const retryCutErrorTask = (documentIds: string[]) => {
  retryCutTask(documentIds).then((res) => {
    const { code } = res.data
    if (code === 200) {
      // 获取切片任务列表
      getCutTaskList(props.knowledgeId)

      // 更新切片错误文档列表
      updateCutErrorList(props.knowledgeId)

      // 统一触发刷新事件，避免多次刷新
      setTimeout(() => {
        emitter.emit('refresh-knowledge-list')
      }, 300)
    }
  })
}

/**
 * 处理点击清除所有按钮
 * @returns void
 */
const handleClickClearAllButton = () => {
  if (selectedCutErrorTaskIdArr.value.length || selectedUploadFailedTaskIdArr.value.length) {
    showConfirm().then((res) => {
      if (res) {
        if (selectedCutErrorTaskIdArr.value.length) {
          // 删除切片错误任务
          deleteCutErrorDocument(selectedCutErrorTaskIdArr.value)
        }

        if (selectedUploadFailedTaskIdArr.value.length) {
          // 删除失败任务
          window.api.deleteFiles(selectedUploadFailedTaskIdArr.value)
        }

        clearSelectedFailedTableRow()
      }
    })
  }
}

/**
 * 处理点击行清除按钮
 * @param record 行数据
 * @returns void
 */
const handleClickClearButton = (record: ImportingFileItem) => {
  showConfirm().then((res) => {
    if (res) {
      const { documentId, fileId, taskType, status, cutStatus } = record

      if (taskType === 'cut' && cutStatus === CutStatusEnum.FAILED) {
        // 如果任务类型是切片任务，则删除切片任务
        deleteCutErrorDocument([documentId])
      }

      if (taskType !== 'cut' && status === FileStatus.FAILED) {
        // 如果任务类型不是切片任务，并且状态是失败，则删除失败任务
        window.api.deleteFiles([fileId])
      }
    }
  })
}

/**
 * 处理点击重试所有按钮
 * @returns void
 */
const handleClickRetryAllButton = () => {
  if (selectedCutErrorTaskIdArr.value.length) {
    // 重试切片错误任务
    retryCutErrorTask(selectedCutErrorTaskIdArr.value)
  }

  if (selectedUploadFailedTaskIdArr.value.length) {
    // 重试失败任务
    window.api.startUpload(selectedUploadFailedTaskIdArr.value)
  }

  clearSelectedFailedTableRow()
}

/**
 * 处理点击重试按钮
 * @param record 行数据
 * @returns void
 */
const handleClickRetryButton = (record: ImportingFileItem) => {
  const { documentId, fileId, taskType, status, cutStatus } = record

  if (taskType === 'cut' && cutStatus === CutStatusEnum.FAILED) {
    // 如果任务类型是切片任务，则重试切片任务
    retryCutErrorTask([documentId])
  }

  if (taskType !== 'cut' && status === FileStatus.FAILED) {
    // 如果任务类型不是切片任务，并且状态是失败，则重试失败任务
    window.api.startUpload([fileId])
  }
}

// 显示删除确认弹窗
const showConfirm = () => {
  return new Promise((resolve) => {
    AModal.confirm({
      title: createVNode(
        'div',
        {
          style: {
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
            display: 'flex',
            height: '20px'
          }
        },
        [
          createVNode(
            'span',
            {
              style: {
                width: '53px',
                height: '20px',
                fontSize: '16px',
                lineHeight: '20px',
                display: 'inline-flex',
                alignItems: 'center',
                flexShrink: 0
              }
            },
            'Delete?'
          ),
          createVNode(SvgIcon, {
            name: 'warn-icon',
            size: '20',
            style: { width: '20px', height: '20px', flexShrink: 0 }
          })
        ]
      ),
      icon: null,
      content: createVNode(
        'div',
        {
          style: {
            textAlign: 'center',
            color: '#000',
            fontSize: '14px',
            width: '400px',
            minHeight: '44px',
            lineHeight: '22px',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            paddingTop: '0'
          }
        },
        'Are you sure to delete the files from the knowledge base?'
      ),
      okText: 'Delete',
      onOk() {
        resolve(true)
      },
      onCancel() {
        resolve(false)
      },
      wrapClassName: 'kb-modal-confirm',
      class: `kb-modal-confirm-top`
    })
  })
}
</script>

<style lang="less" scoped>
.kb-file-drawer-root {
  position: absolute;

  .kb-file-drawer__empty-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: calc(100vh - 100px);
  }

  .kb-file-drawer__empty-icon {
    width: 72px;
    height: 72px;
    margin-bottom: 16px;
    background: url('@renderer/assets/images/<EMAIL>') no-repeat;
    background-size: 100%;
  }

  .kb-file-drawer__empty-text {
    color: #3f3f46;
  }

  .kb-file-drawer__importing-header {
    height: 35.5px;
    margin-bottom: 16px;
  }

  .kb-file-drawer__importing-buttons-wrapper {
    display: inline-block;
  }

  .kb-file-drawer__importing-button {
    margin-right: 8px;
    padding: 5px 10.5px;
  }

  .kb-file-drawer__importing-button-icon {
    margin-right: 8px;
    vertical-align: middle;
    color: #3b3b3b;
  }

  .kb-file-drawer__importing-button-text {
    vertical-align: middle;
    color: #000;
  }

  .kb-file-drawer__importing-status-wrapper {
    float: right;
    margin-top: 7px;
    text-align: right;
  }

  .kb-file-drawer__file-type-svg {
    margin-right: 8px;
    vertical-align: bottom;
    color: transparent;
  }

  .kb-file-drawer__importing-status {
    font-size: 14px;
    line-height: 18px;
    color: #3b3b3b;
  }

  .kb-file-drawer__importing-table-status {
    display: inline-block;
    height: 22px;
    line-height: 22px;
    padding: 0 8px;
    border-radius: 2px;
  }

  .kb-file-drawer__importing-table-status_Processing,
  .kb-file-drawer__importing-table-status_Uploading {
    background-color: #f5f2fe;
    color: #6441ab;
  }

  .kb-file-drawer__importing-table-status_Waiting {
    background-color: #eff5fe;
    color: #0e4bce;
  }

  .kb-file-drawer__importing-table-status_Cut-Paused,
  .kb-file-drawer__importing-table-status_Upload-Paused,
  .kb-file-drawer__importing-table-status_Paused {
    // background-color: #fff2d9;
    // color: #735823;
  }

  .kb-file-drawer__importing-table-status_Cut-Failed,
  .kb-file-drawer__importing-table-status_Upload-Failed,
  .kb-file-drawer__importing-table-status_Failed {
    display: inline-block;
    max-width: 100.5px;
    vertical-align: middle;
    background-color: #ffeeed;
    color: #bf1f17;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .kb-file-drawer__failed-status-wrapper {
    float: right;
    margin-top: 7px;
  }

  .kb-file-drawer__failed-table-row-action-wrapper {
    display: inline-flex;
    align-items: center;
  }

  .kb-file-drawer__failed-table-button {
    margin-right: 8px;
    padding: 0;
    color: #6441ab;

    &:last-child {
      margin-right: 0;
    }

    &.ant-btn:hover {
      background-color: transparent;
      color: #6441ab;
    }
  }

  .kb-file-drawer__failed-name-truncated {
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    align-items: center;
    vertical-align: bottom;
    flex-shrink: 0;
    padding-right: 2px;
  }

  .kb-file-drawer__button-wrapper {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  .kb-file-drawer__import-button {
    padding: 4px 10px;
  }
}
</style>
