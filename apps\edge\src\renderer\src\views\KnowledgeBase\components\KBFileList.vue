<template>
  <!-- 右侧知识库中的文件列表 -->
  <div class="kb-file-container">
    <div class="kb-file-container__content">
      <header>
        <label class="title">
          <span class="title__prefix" @click="handleClickTitlePrefix">My Knowledge /</span>
          {{ kbinfo?.knowledgeBaseName || kbinfo?.knowledgeBaseId }}
        </label>
        <div class="file-count" v-if="isCanOperateDocument">
          <ABtn type="text" class="import-status-btn" @click="showImportStatus">
            <span class="import-status-button__text">{{ importingCutingTaskCount }} files</span>
            <SvgIcon name="import-status-icon" size="11" />
          </ABtn>
        </div>
      </header>
      <!-- 加载状态 -->
      <div
        class="kb-file-container__loading-wrapper"
        v-if="!state.isInitialized || state.showLoadingState"
      >
        <div class="kb-file-container__loading-spinner"></div>
        <!-- <div class="kb-file-container__loading-text">Loading files...</div> -->
      </div>

      <KBEmptyState
        v-else
        :isEmpty="dataSource.length === 0"
        :isInitialized="state.isInitialized"
        :hasSearchKeyword="!!searchInputValue.value"
        :canOperateDocument="isCanOperateDocument"
        @upload="handleUpload"
      />

      <template v-else>
        <div class="kb-file-container_tool">
          <ABtn
            class="kb-file-container__import-button"
            v-if="isCanOperateDocument"
            type="primary"
            @click="handleUpload"
          >
            <SvgIcon class="kb-file-container__import-button-icon" name="import-icon" size="11" />
            <span style="margin-left: 5px">Import</span>
          </ABtn>
          <!-- <ABtn class="kb-file-container__import-button" type="primary" @click="handleClickAutoUpload">
          <SvgIcon class="kb-file-container__import-button-icon"
            name="import-icon"
            size="11"
          /> Auto Upload
        </ABtn> -->
          <AInput
            class="kb-file-container__search-input"
            v-model:value="searchInputValue"
            placeholder="Search by name"
            style="width: 168px; margin-left: 12px"
            :max-length="32"
            :allow-clear="true"
            @change="handleChangeSearchInput"
            @press-enter="handleSearch"
          >
            <template #suffix>
              <ABtn type="text" class="kb-file-container__search-button" @click="handleSearch">
                <SvgIcon name="search-icon" size="11.9" />
              </ABtn>
            </template>
          </AInput>
          <ATooltip title="Sync files">
            <ABtn class="kb-file-container__synchronize-button" @click="handleRefresh">
              <SvgIcon
                class="kb-file-container__synchronize-button-icon"
                name="synchronize-icon"
                size="11"
              />
            </ABtn>
          </ATooltip>
          <ABtn class="kb-file-container__delete-button" @click="handleDeletes" v-if="hasSelected">
            <SvgIcon class="kb-file-container__delete-button-icon" name="delete-icon" size="14" />
          </ABtn>
        </div>
        <div class="kb-file-container_table">
          <div class="kb-file-table_header-wrapper">
            <ATable
              class="kb-file-table_header"
              size="small"
              :rowKey="getKye"
              :dataSource="dataSource"
              :columns="kbinfo?.knowledgeBaseType === KBType.PERSON ? privateKBColumns : columns"
              :pagination="false"
              :row-selection="rowSelection"
              :row-class-name="() => 'kb-file-container__table-row'"
              :customRow="customRow"
              @change="tableChange"
            ></ATable>
          </div>
          <ATable
            :show-header="false"
            ref="fileTableRef"
            :rowKey="getKye"
            :dataSource="dataSource"
            :columns="kbinfo?.knowledgeBaseType === KBType.PERSON ? privateKBColumns : columns"
            :pagination="false"
            :row-selection="rowSelection"
            :row-class-name="() => 'kb-file-container__table-row'"
            :customRow="customRow"
            @change="tableChange"
            :sticky="true"
            size="small"
            class="kb-file-table"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'tags'">
                <div class="tags-container">
                  <span v-for="tag in record.tags?.slice(0, 3)" :key="tag" class="tag-item">{{
                    tag
                  }}</span>
                  <span v-if="record.tags?.length > 3" class="tag-item-ellipsis">...</span>
                </div>
              </template>
              <template v-if="column.key === 'documentName'">
                <!-- <span
                class="kb-file-container__file-type-icon"
                :style="{
                  backgroundImage: `url(${FileIconMap[record.documentType]})`
                }"
              ></span> -->
                <span
                  class="kb-file-container__file-name-wrapper"
                  @click="handleClickFileName(record)"
                >
                  <SvgIcon
                    class="kb-file-container__file-type-svg"
                    :name="
                      fileExtensionIconNameMap[
                        record.documentType as keyof typeof fileExtensionIconNameMap
                      ] || 'general'
                    "
                    size="24"
                  >
                  </SvgIcon>
                  <ATooltip
                    placement="topLeft"
                    color="#525A69"
                    overlay-class-name="kb-file-container__file-name-tooltip"
                  >
                    <template #title>{{ record.documentName }}</template>
                    <span>{{ record.documentName }}</span>
                  </ATooltip>
                </span>
              </template>
              <template v-if="column.key === 'owner' || column.key === 'from'">
                <span>
                  {{ kbinfo?.knowledgeBaseType === KBType.PERSON ? record.from : record.owner }}
                </span>
                <!-- <span
                class="kb-file-container__table-row-action-wrapper"
                v-show="currentRowIndex === index"
              >
                <ABtn
                  class="kb-file-container__table-row-action-button"
                  type="link"
                  @click="handleDelete(record.documentId)"
                  >Delete</ABtn
                >
              </span> -->
              </template>
              <template v-else-if="column.key === 'updateTime'">
                <!-- <span
                class="kb-file-container__table-row-action-wrapper"
                v-show="currentRowIndex === index"
              >
                <ABtn
                  class="kb-file-container__table-row-action-button"
                  type="link"
                  @click="handleDelete(record.documentId)"
                  >Delete</ABtn
                >
              </span> -->
                <div>
                  {{ dayjs(record.updateTime).format('MMM DD, YYYY') }}
                </div>
              </template>
              <template v-else-if="column.key === 'action'">
                <span
                  v-if="isCanOperateDocument"
                  v-show="currentRowIndex === index"
                  style="vertical-align: sub"
                >
                  <SvgIcon
                    class="kb-file-table__row-button-icon"
                    name="file-download-icon"
                    size="16"
                    @click.stop="handleClickDownloadIcon(record)"
                  />
                  <SvgIcon
                    class="kb-file-table__row-button-icon"
                    name="delete-icon"
                    size="16"
                    @click="handleClickDeleteIcon(record.documentId)"
                  />
                </span>
                <span
                  v-if="isCanOperateDocument"
                  v-show="currentRowIndex !== index"
                  style="vertical-align: sub"
                >
                  <SvgIcon name="more-icon" size="16" />
                </span>
              </template>
            </template>
          </ATable>
        </div>
      </template>
    </div>

    <!-- 分页器 -->
    <div class="kb-file-container__pagination-wrapper">
      <APagination
        class="kb-file-container__pagination"
        v-model:current="currentPage"
        :pageSize="defaultParams.pageSize"
        :total="totalCount"
        show-quick-jumper
        show-size-changer
        show-less-items
        :pageSizeOptions="['20', '50', '100']"
        :show-total="(total: number) => `Total ${total} items  ${currentPage}/${pageCount}`"
        @change="handleChangePageNumber"
        @showSizeChange="handleShowSizeChange"
      ></APagination>
    </div>
  </div>
  <KBFileDrawer
    v-model="isOpenDrawer"
    :knowledgeId="kbinfo.entityId"
    :knowledgeBaseId="kbinfo.knowledgeBaseId"
  />
  <previewModle v-model:modelValue="isShowPreviewModal" :docObj="previewFileObj" />
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  reactive,
  computed,
  watch,
  createVNode,
  onUnmounted,
  useTemplateRef,
  inject,
  Ref
} from 'vue'
import { useRouter } from 'vue-router'
import { AInput, ATable, ABtn, AModal, ATooltip, message, APagination } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import KBFileDrawer from './KBFileDrawer.vue'
import KBEmptyState from './KBEmptyState.vue'
import dayjs from 'dayjs'
import { fileExtensionIconNameMap } from '@renderer/hooks/fileType'
import { knowledgeBaseApi } from '@/renderer/src/api/knowledgeBase'
import { Modal } from 'ant-design-vue'
import previewModle from '@/renderer/src/views/Chat/components/previewModle.vue'
import { DocumentListType } from '@libs/a-comps/ChatBaseComponent/types'
import { FileStatus } from '@/main/Service/fileSync/constants/types'
import { IKBItem, IKBFileItem, KBType, CutErrorData } from '../type'
import { knowledgeStore } from '../store'
import mySDK from '@renderer/views/Chat/sdkService'
import { GlobalConfig } from '@/renderer/src/common'
import { emitter } from '@/utils/EventBus'

const router = useRouter()

const props = defineProps<{
  kbinfo: IKBItem
}>()

const { cutErrorData } = inject('cutErrorData') as {
  cutErrorData: Ref
}

const defaultParams = {
  pageIndex: 1,
  pageSize: 20,
  sortBy: 'createTime', //排序字段 支持：documentName、updateTime、owner
  sort: 'desc' // 排序字段  asc：升序  desc：降序
}

const searchInputValue = ref('')

const isShowPreviewModal = ref(false)
const previewFileObj = ref<DocumentListType>({
  documentId: '',
  documentName: '',
  knowledgeId: '',
  knowledgeBaseId: ''
})

const fileTableRef = useTemplateRef('fileTableRef')
const dataSource = ref<IKBFileItem[]>([])

const currentPage = ref(1)
const totalCount = ref(0)
const pageCount = ref(0)

const isCanOperateDocument = computed(() => {
  return [2, 3, 4].includes(props.kbinfo.permission)
})

// 是否有更多数据
const isHasMoreDataSource = ref<boolean>(false)
const isScrolling = ref(false)
let scrollTimeout: ReturnType<typeof setTimeout> | null = null

const importingFileArr = computed(() => {
  return knowledgeStore.knowledgeTaskMap[props.kbinfo.entityId]?.importingFileList || []
})

// 切片任务数组
const cutingTaskArr = computed(() => {
  return knowledgeStore.knowledgeTaskMap[props.kbinfo.entityId]?.cutingTaskArr || []
})

// 正在导入任务数组
// const importingTaskArr = computed(() => {
//   return importingFileArr.value.filter((importingFileItem) => {
//     return importingFileItem.status !== FileStatus.FAILED
//   })
// })

// 处理中任务数量
const importingCutingTaskCount = computed(() => {
  return (
    importingFileArr.value.length +
    cutingTaskArr.value.length +
    cutErrorData.value.documentList.length
  )
})

const columns = [
  {
    title: 'Name',
    dataIndex: 'documentName',
    key: 'documentName',
    width: '35%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true
  },
  {
    title: 'Uploader',
    dataIndex: 'owner',
    key: 'owner',
    // width: '20%',
    sorter: true,
    showSorterTooltip: false
    // customCell: (record: IKBFileItem, index: number) => ({
    //   colSpan: currentRowIndex.value === index ? 2 : 1
    // })
  },
  // {
  //   title: 'Tag',
  //   dataIndex: 'tags',
  //   // width: '30%',
  //   key: 'tags'
  // },
  {
    title: 'Modified',
    dataIndex: 'updateTime',
    key: 'updateTime',
    // width: '20%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true
    // customCell: (record: IKBFileItem, index: number) => ({
    //   colSpan: currentRowIndex.value === index ? 0 : 1
    // })
  },
  {
    title: 'Action',
    key: 'action',
    // width: '10%',
    align: 'center'
  }
]
const privateKBColumns = [
  {
    title: 'Name',
    dataIndex: 'documentName',
    key: 'documentName',
    // width: '70%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true
  },
  // {
  //   title: 'From',
  //   dataIndex: 'from',
  //   key: 'from',
  //   width: '19%',
  //   customCell: (record: IKBFileItem, index: number) => ({
  //     colSpan: currentRowIndex.value === index ? 2 : 1
  //   })
  // },
  {
    title: 'Modified',
    dataIndex: 'updateTime',
    key: 'updateTime',
    // width: '30%',
    sorter: true,
    showSorterTooltip: false,
    ellipsis: true
    // customCell: (record: IKBFileItem, index: number) => ({
    //   colSpan: currentRowIndex.value === index ? 1 : 1
    // })
  },
  {
    title: 'Action',
    key: 'action',
    // width: '10%',
    align: 'center'
  }
]

// 当前行索引
const currentRowIndex = ref<number | null>(null)

const getKye = (file: IKBFileItem) => {
  return file.documentId
}

const state = reactive<{
  selectedRowKeys: string[]
  loading: boolean
  isFirstLoad: boolean
  isInitialized: boolean
  showLoadingState: boolean
}>({
  selectedRowKeys: [], // Check here to configure the default column
  loading: false,
  isFirstLoad: true,
  isInitialized: false,
  showLoadingState: false
})

// 延迟显示加载状态的定时器
let loadingTimer: NodeJS.Timeout | null = null
let loadingStartTime: number = 0

// 刷新锁机制 - 防止短时间内多次刷新
let refreshLock = false
let lastRefreshTime = 0
const MIN_REFRESH_INTERVAL = 1000 // 最小刷新间隔1秒

// const isCancelButtonDisabled = ref(false);

// 是否打开抽屉
const isOpenDrawer = ref(false)

const hasSelected = computed(() => state.selectedRowKeys.length > 0)
const onSelectChange = (selectedRowKeys: string[]) => {
  state.selectedRowKeys = selectedRowKeys
}

const rowSelection = computed(() => {
  return isCanOperateDocument.value
    ? {
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange
      }
    : null
})

const customRow = (record: IKBFileItem, index: number) => {
  return isCanOperateDocument.value
    ? {
        onclick: () => {
          if (!state.selectedRowKeys.includes(record.documentId)) {
            state.selectedRowKeys.push(record.documentId)
          }
        },
        onMouseenter: (event: MouseEvent) => {
          currentRowIndex.value = index
        },
        onMouseleave: () => {
          currentRowIndex.value = null
        }
      }
    : null
}

/**获取文件列表 */
const getFileList = (isScrollFileDate: boolean = false, isDeleteOperation: boolean = false) => {
  if (!props.kbinfo?.entityId) {
    return
  }

  // 检查刷新锁 - 防止短时间内多次刷新
  const now = Date.now()
  if (refreshLock && now - lastRefreshTime < MIN_REFRESH_INTERVAL) {
    // 刷新已锁定，跳过此次刷新
    return
  }

  // 清除之前的定时器
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }

  state.loading = true
  refreshLock = true
  lastRefreshTime = now

  // 延迟显示加载状态，避免快速闪烁
  loadingStartTime = Date.now()
  loadingTimer = setTimeout(() => {
    state.showLoadingState = true
  }, 200) // 200ms延迟，如果加载很快则不显示加载状态

  // 非首次加载且非删除操作时，根据是否滚动加载来决定是否清空数据
  if (!state.isFirstLoad && !isDeleteOperation) {
    dataSource.value = isScrollFileDate ? dataSource.value : []
  }
  // 删除操作时保持当前数据，避免闪烁

  knowledgeBaseApi
    .getKBFileList({
      ...defaultParams,
      keyword: searchInputValue.value.trim(),
      knowledgeId: props.kbinfo?.entityId,
      knowledgeBaseId: props.kbinfo?.knowledgeBaseId
    })
    .then((response) => response.data)
    .then((res: any) => {
      // 清除定时器
      if (loadingTimer) {
        clearTimeout(loadingTimer)
        loadingTimer = null
      }

      // 确保加载状态至少显示300ms，避免闪烁
      const loadingDuration = Date.now() - loadingStartTime
      const minDisplayTime = 300 // 最小显示时间

      if (loadingDuration < minDisplayTime) {
        setTimeout(() => {
          state.loading = false
          state.showLoadingState = false
          state.isFirstLoad = false
          state.isInitialized = true
          // 释放刷新锁
          refreshLock = false
        }, minDisplayTime - loadingDuration)
      } else {
        state.loading = false
        state.showLoadingState = false
        state.isFirstLoad = false
        state.isInitialized = true
        // 释放刷新锁
        refreshLock = false
      }

      const { code, data } = res
      if (code === 200 && data) {
        const { documentList, pageCount: totalPages, totalCount: total } = data

        const newData = isScrollFileDate ? [...dataSource.value, ...documentList] : documentList
        safeUpdateDataSource(newData, isDeleteOperation)

        // 更新分页信息
        totalCount.value = total || 0
        pageCount.value = totalPages || 0
        currentPage.value = defaultParams.pageIndex

        if (total > 20 && totalPages >= defaultParams.pageIndex) {
          // 有多页 并且当前页的数据满足20条（最后一页时 没有更多数据了）
          isHasMoreDataSource.value = true
        } else {
          isHasMoreDataSource.value = false
        }

        // 数据加载完成后重新设置滚动检测
        setTimeout(() => {
          setupScrollDetection()
        }, 100)
      } else {
        // 请求失败时，确保清空数据源
        dataSource.value = []
      }
    })
}

/**
 * 对外暴露方法
 */
defineExpose({
  refreshFileList: getFileList,
  openDrawer: () => (isOpenDrawer.value = true)
})

/**上传 */
const handleUpload = () => {
  window.api
    .selectFiles({
      source: props.kbinfo?.entityId,
      isLimitFileCount: false,
      userIdForUpload: mySDK.userId,
      resourceId: props.kbinfo?.knowledgeBaseId,
      resourceType: '2' // 2表示知识库
    })
    .then((res: any) => {})
}

// const handleClickAutoUpload = () => {
//   window.api.selectCustomPathFilesUpload('d:/ProjectSpace/smb/ai now/edge')
// };

/**刷新 */
const handleRefresh = () => {
  defaultParams.pageIndex = 1
  getFileList()
}

/**
 * 处理改变搜索输入框
 * @param event
 */
const handleChangeSearchInput = (event: Event) => {
  const inputValue = (event.target as HTMLInputElement).value
  if (!inputValue) {
    defaultParams.pageIndex = 1
    getFileList()
  }
}

/**搜索 */
const handleSearch = () => {
  defaultParams.pageIndex = 1
  getFileList()
}

// 批量删除
const handleDeletes = () => {
  showConfirm((resolve, reject) => {
    // isCancelButtonDisabled.value = true

    knowledgeBaseApi
      .deleteKBFiles({
        fileIds: state.selectedRowKeys,
        knowledgeBaseId: String(props.kbinfo?.knowledgeBaseId)
      })
      .then((response) => response.data)
      .then((res: any) => {
        if (res.code === 200) {
          resolve()

          // 直接从页面数据源中移除删除的文件，避免重新请求导致的闪烁
          const deletedFileIds = state.selectedRowKeys
          dataSource.value = dataSource.value.filter(
            (file) => !deletedFileIds.includes(file.documentId)
          )

          state.selectedRowKeys = []
          defaultParams.pageIndex = 1
        } else {
          // 提示错误信息
          message.error(res.msg || 'delete file failed')
        }
      })
      .catch((error) => {
        reject(error)
      })
    // .finally(() => {
    //   isCancelButtonDisabled.value = false
    // })
  })
}

// 单个删除
const handleClickDeleteIcon = (fileId: string) => {
  showConfirm((resolve, reject) => {
    // isCancelButtonDisabled.value = true

    knowledgeBaseApi
      .deleteKBFile({
        fileId,
        knowledgeBaseId: String(props.kbinfo?.knowledgeBaseId)
      })
      .then((response) => response.data)
      .then((res: any) => {
        if (res.code === 200) {
          resolve()

          // 直接从页面数据源中移除删除的文件，避免重新请求导致的闪烁
          dataSource.value = dataSource.value.filter((file) => file.documentId !== fileId)

          // 从选中状态中移除
          const i = state.selectedRowKeys.indexOf(fileId)
          if (i > -1) {
            state.selectedRowKeys.splice(i, 1)
          }
          defaultParams.pageIndex = 1
        } else {
          // 提示错误信息
          message.error(res.msg || 'delete files failed')
        }
      })
      .catch((error) => {
        reject(error)
      })
    // .finally(() => {
    //   isCancelButtonDisabled.value = false
    // })
  })
}

// 显示删除确认弹窗
const showConfirm = (
  requestCallback: (resolve: () => void, reject: (error: Error) => void) => void
) => {
  Modal.confirm({
    title: createVNode(
      'div',
      {
        style: {
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          display: 'flex',
          height: '20px'
        }
      },
      [
        createVNode(SvgIcon, {
          name: 'warn-icon',
          size: '20',
          style: { width: '20px', height: '20px', flexShrink: 0 }
        }),
        createVNode(
          'span',
          {
            style: {
              width: '53px',
              height: '20px',
              fontSize: '16px',
              lineHeight: '20px',
              display: 'inline-flex',
              alignItems: 'center',
              flexShrink: 0
            }
          },
          'Delete?'
        )
      ]
    ),
    icon: null,
    content: createVNode(
      'div',
      {
        style: {
          textAlign: 'center',
          color: '#000',
          fontSize: '14px',
          width: '400px',
          minHeight: '44px',
          lineHeight: '22px',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          paddingTop: '0'
        }
      },
      'Are you sure to delete the files from the knowledge base?'
    ),
    okText: 'Delete',
    // cancelButtonProps: {
    //   disabled: true// isCancelButtonDisabled.value
    // },
    onOk() {
      return new Promise((resolve, reject) => {
        requestCallback && requestCallback(resolve, reject)
      })
    },
    onCancel() {
      // resolve(false)
    },
    wrapClassName: 'kb-modal-confirm',
    class: `kb-modal-confirm-top`
  })
}

const handleClickDownloadIcon = async (record: IKBFileItem) => {
  const { documentId, documentName } = record

  try {
    message.loading('正在下载...', 0)

    const response = await fetch(
      `${GlobalConfig.kbFileServer}/api/v1/document/viewDocument?documentId=${documentId}`,
      {
        headers: {
          'resource-Id': props.kbinfo.knowledgeBaseId,
          'resource-Type': '2',
          token: GlobalConfig.tokens.access_token
        }
      }
    )

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status}`)
    }

    const blob = await response.blob()
    const savedPath = await window.api.saveFile(documentName || 'file', blob)

    message.destroy()
    message.success(`下载成功, 已下载至 ${savedPath}`, 5)
  } catch (error) {
    message.destroy()
    const errorMessage = error instanceof Error ? error.message : '网络错误，请重试'
    message.error(`下载失败: ${errorMessage}`, 4)
  }
}

/** 显示导入状态抽屉 */
const showImportStatus = () => {
  isOpenDrawer.value = true
}

/** 排序触发事件 */
const tableChange = (
  pagination: any,
  filters: any,
  { field, order }: { field: string; order: string }
) => {
  if (order) {
    defaultParams.sortBy = field
    defaultParams.sort = order === 'ascend' ? 'asc' : 'desc'
  } else {
    defaultParams.sortBy = 'createTime'
    defaultParams.sort = 'desc'
  }
  defaultParams.pageIndex = 1
  // 清空复选框
  state.selectedRowKeys = []
  getFileList()
}

// 重置参数
const resetDefaultParams = () => {
  searchInputValue.value = ''
  defaultParams.pageIndex = 1
  defaultParams.sortBy = 'createTime'
  defaultParams.sort = 'desc'
  state.selectedRowKeys = []
}

// 监听props.kbinfo变化
const handleKBChange = async (kbinfo: Object) => {
  // 清空filter，重置参数
  resetDefaultParams()

  // 重置首次加载状态
  state.isFirstLoad = true
  state.isInitialized = false

  getFileList()
}

watch(() => props.kbinfo, handleKBChange, { immediate: true, deep: true })
const handleChangePageNumber = (page: number, pageSize: number) => {
  defaultParams.pageIndex = page
  defaultParams.pageSize = pageSize
  getFileList()
}

const handleShowSizeChange = (current: number, size: number) => {
  defaultParams.pageIndex = 1 // 重置到第一页
  defaultParams.pageSize = size
  getFileList()
}
/**
 * 处理滚动表格
 * @param event
 */
const handleScrollTable = (event: Event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target as HTMLDivElement

  const isScrollToBottom = scrollTop + clientHeight >= scrollHeight - 10
  if (isScrollToBottom && !state.loading && isHasMoreDataSource.value) {
    // 加载更多数据
    defaultParams.pageIndex += 1
    getFileList(true)
  }
}

/**
 * 处理点击文件名
 * @param record
 */
const handleClickFileName = (record: IKBFileItem) => {
  const { documentId, documentName } = record
  isShowPreviewModal.value = true
  previewFileObj.value = {
    documentId,
    documentName,
    knowledgeId: props.kbinfo.entityId,
    knowledgeBaseId: String(props.kbinfo.knowledgeBaseId)
  }
}

/**
 * @description 处理点击标题前缀
 */
const handleClickTitlePrefix = () => {
  // 团队知识库
  router.push({
    name: 'myKnowledge'
  })
}

// 防抖刷新函数
const debouncedRefresh = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null
  return () => {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      defaultParams.pageIndex = 1
      getFileList()
    }, 300) // 300ms 防抖延迟
  }
})()

// 刷新 - 带时间戳检查
const optimizedRefresh = (() => {
  let lastRefreshTime = 0
  const REFRESH_THROTTLE = 500 // 500ms节流间隔

  return () => {
    const now = Date.now()
    if (now - lastRefreshTime < REFRESH_THROTTLE) {
      return
    }
    lastRefreshTime = now
    defaultParams.pageIndex = 1
    getFileList()
  }
})()

// 安全的数据更新 - 确保不会出现空状态闪烁
const safeUpdateDataSource = (newData: IKBFileItem[], isDeleteOperation = false) => {
  if (isDeleteOperation) {
    // 删除操作时，如果新数据为空，延迟更新以避免闪烁
    if (newData.length === 0 && dataSource.value.length > 0) {
      setTimeout(() => {
        dataSource.value = newData
      }, 150) // 增加延迟时间，确保空状态组件有足够时间处理状态变化
    } else {
      dataSource.value = newData
    }
  } else {
    dataSource.value = newData
  }
}

const setupScrollDetection = () => {
  // 延迟检测，确保表格已经渲染
  setTimeout(() => {
    const kbFileTableBody = document.querySelector('.kb-file-table .ant-table-body') as HTMLElement
    if (kbFileTableBody) {
      // 先移除已存在的事件监听器，避免重复
      kbFileTableBody.removeEventListener('scroll', handleScroll)
      kbFileTableBody.addEventListener('scroll', handleScroll)
    } else {
      // 如果还没有渲染，再次尝试
      setTimeout(() => {
        const kbFileTableBodyRetry = document.querySelector(
          '.kb-file-table .ant-table-body'
        ) as HTMLElement
        if (kbFileTableBodyRetry) {
          kbFileTableBodyRetry.removeEventListener('scroll', handleScroll)
          kbFileTableBodyRetry.addEventListener('scroll', handleScroll)
        }
      }, 500)
    }
  }, 100)
}

const handleScroll = () => {
  isScrolling.value = true
  const kbFileTableBody = document.querySelector('.kb-file-table .ant-table-body') as HTMLElement
  if (kbFileTableBody) {
    kbFileTableBody.classList.add('scrolling')
  }

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
    if (kbFileTableBody) {
      kbFileTableBody.classList.remove('scrolling')
    }
  }, 300)
}

// 单文件完成处理
const handleSingleFileComplete = (data: { documentId: string; knowledgeId: string }) => {
  if (data.knowledgeId === props.kbinfo.entityId) {
    // 使用优化的刷新方式，避免重复刷新
    optimizedRefresh()
  }
}

onMounted(() => {
  // 设置滚动检测
  setupScrollDetection()

  // 监听知识库文件解析完成事件
  emitter.on('refresh-knowledge-list', optimizedRefresh)

  // 监听单文件完成事件
  emitter.on('single-file-completed', handleSingleFileComplete)
})

onUnmounted(() => {
  // 移除滚动事件监听
  const kbFileTableBody = document.querySelector('.kb-file-table .ant-table-body') as HTMLElement
  if (kbFileTableBody) {
    kbFileTableBody.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  // 移除全部知识库文件解析完成事件监听
  emitter.removeEvent('refresh-knowledge-list')

  // 移除单文件完成事件监听
  emitter.removeEvent('single-file-completed')

  // 清理防抖定时器
  const debouncedRefreshTimer = (debouncedRefresh as any).timer
  if (debouncedRefreshTimer) {
    clearTimeout(debouncedRefreshTimer)
  }

  // 清理优化刷新定时器
  const optimizedRefreshTimer = (optimizedRefresh as any).timer
  if (optimizedRefreshTimer) {
    clearTimeout(optimizedRefreshTimer)
  }

  // 清理加载状态定时器
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }

  // 释放刷新锁
  refreshLock = false
})
</script>
<style lang="less" scoped>
.kb-file-container {
  // padding-right: 12px;
  display: flex;
  flex-direction: column;
  height: 100%;

  .kb-file-container__content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .tags-container {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .tag-item {
    background-color: #f0f0f0;
    padding: 2px 8px;
    border-radius: 2px;
    color: #6441ab;
    font-size: 12px;
  }

  .tag-item-ellipsis {
    color: #6441ab;
  }

  header {
    height: 38px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #000000;
      max-width: calc(100% - 100px);
      overflow: hidden;
      text-overflow: ellipsis;
    }
    @media print {
      .title {
        overflow: visible;
      }
    }

    .title__prefix {
      color: #3b3b3b;
      cursor: pointer;
      font-weight: 400;
      &:hover {
        color: #6441ab;
      }
    }

    // .file-count {
    //   font-size: 14px;
    //   line-height: 22px;
    //   color: #6441ab;
    // }

    .import-status-btn {
      // margin-left: 16px;
      margin-right: 2px;
      padding: 5px 12px;
    }

    .import-status-button__text {
      margin-right: 9px;
    }
  }

  .kb-file-container__loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: calc(100vh - 60px);
  }

  .kb-file-container__loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #6441ab;
    border-radius: 50%;
    animation: kb-loading-spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .kb-file-container__loading-text {
    color: #3f3f46;
    font-size: 14px;
  }

  @keyframes kb-loading-spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  &_tool {
    margin-top: 8px;
    margin-bottom: 10px;
    height: 32px;

    .icon-btn {
      width: 32px;
      height: 32px;
      padding: 0;
    }
  }

  .kb-file-container__search-input {
    width: 168px;
    height: 32px;
    margin-right: 12px;
    border-color: #f2f3f5;
    background: #f2f3f5;
    vertical-align: middle;

    ::v-deep(.ant-input) {
      background-color: #f2f3f5;

      &::placeholder {
        color: var(--text-color4);
      }
    }
  }
  .kb-file-container__search-button {
    padding: 0px;
    width: 14px;
    height: 14px;
    line-height: 14px;
    border: none;
  }

  .kb-file-container__synchronize-button {
    height: initial;
    margin-right: 12px;
    padding: 4px 10.46px;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  .kb-file-container__synchronize-button-icon {
    vertical-align: baseline;
    color: #3b3b3b;
  }

  .kb-file-container__delete-button {
    // height: initial;
    // padding: 9px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    padding: 0;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  .kb-file-container__delete-button-icon {
    vertical-align: baseline;
    color: #3b3b3b;
  }

  &_table {
    height: calc(100vh - 225px);
    overflow: hidden;

    .file-icon {
      width: 24px;
      height: 24px;
    }
  }

  .kb-file-table_header-wrapper {
    padding-right: 6px;
  }

  .kb-file-table_header {
    ::v-deep(.ant-table-tbody) {
      display: none;
    }
  }

  .kb-file-table {
    ::v-deep(.ant-table-body) {
      overflow-y: auto;
      max-height: calc(100vh - 265px);

      &::-webkit-scrollbar {
        width: 6px;
        background: transparent;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 3px;
        transition: background 0.2s ease;
      }

      /* 滚动时显示滚动条 */
      &.scrolling::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
      }

      &.scrolling::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }

  ::v-deep(.ant-table-footer) {
    background: none;
  }
  ::v-deep(.kb-file-container__table-row) {
    .ant-btn-link {
      padding: 0;
    }

    .ant-checkbox-wrapper {
      display: none;
    }

    .ant-checkbox-wrapper-checked {
      display: inline-flex;
    }

    &:hover {
      .ant-checkbox-wrapper {
        display: inline-flex;
      }
    }
  }

  .kb-file-container__file-name-wrapper {
    cursor: pointer;
  }

  // .kb-file-container__file-type-icon {
  //   display: inline-block;
  //   width: 24px;
  //   height: 24px;
  //   margin-right: 8px;
  //   background-repeat: no-repeat;
  //   background-size: initial;
  //   background-position: center center;
  //   vertical-align: bottom;
  // }

  .kb-file-container__file-type-svg {
    margin-right: 8px;
    vertical-align: bottom;
    color: transparent;
  }

  .kb-file-container__table-row-action-button {
    &.ant-btn:hover {
      background-color: transparent;
    }
  }

  .kb-file-table__row-button-icon {
    color: #3b3b3b;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;

    &:not(:last-child) {
      margin-right: 14px;
    }

    &:hover {
      background-color: #e5e7ec;
    }

    &:active {
      background-color: #c9cdd4;
    }
  }

  .kb-modal-confirm {
    // min-width: 464px;
    // max-width: 500px;
    min-width: 464px;
  }

  :global(.kb-modal-confirm .kb-modal-confirm-top) {
    top: calc(50vh - 111px);
  }

  :global(.kb-modal-confirm .ant-modal) {
    display: flex;
    justify-content: center;
    text-align: center;
  }

  :global(.kb-modal-confirm .ant-modal .ant-modal-content) {
    padding: 24px 32px 32px;
    width: 464px;
  }

  :global(.ant-modal-confirm .ant-modal-confirm-body) {
    display: block;
  }

  :global(
    .ant-modal-confirm .ant-modal-confirm-body .ant-modal-confirm-title + .ant-modal-confirm-content
  ) {
    margin-top: 24px;
    margin-bottom: 24px;
  }

  :global(.ant-modal-confirm .ant-modal-confirm-body .ant-modal-confirm-title) {
    // flex: none !important;
    display: inline;
    // overflow: hidden;
    color: #000000;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }

  :global(.ant-modal-confirm .ant-modal-confirm-btns) {
    text-align: center;
  }

  .kb-file-container__pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 25px;
  }
}
</style>
<style lang="less">
.kb-file-container__file-name-tooltip {
  max-width: 520px;
}

.kb-file-drawer-root {
  position: absolute;
}
</style>
