<template>
  <!-- 左侧知识库列表 -->
  <div class="kb-list">
    <header class="kb-list_title">Knowledge Base</header>
    <ul class="kb-list_ul" v-for="item in menus" :key="item.type">
      <li class="kb-list_ul_title">
        {{ `${item.title} ${item.type == KBType.TEAM ? `(${item.data.length})` : ''}` }}
      </li>
      <div
        :class="{
          'kb-list__team-knowledge-wrapper': item.type == KBType.TEAM
        }"
      >
        <li
          v-for="menu in item.data"
          :key="menu.entityId"
          class="kb-list_ul_item"
          :class="{ selected: menu.entityId == activeItem?.entityId }"
          @click="handleKBClick(menu)"
        >
          <section>
            <span class="kb-list__item-name" :title="menu.knowledgeBaseName">
              {{ menu.knowledgeBaseName }}
            </span>
            <Popover
              trigger="click"
              placement="rightTop"
              overlayClassName="kb-detail"
              class="kb-list__knowledge-popover"
              :arrow="false"
            >
              <template #title>
                <div class="kb-list__popover-title">
                  {{ detailCard?.knowledgeBaseName }}
                </div>
              </template>
              <template #content>
                <table class="kb-popover-table">
                  <tr>
                    <td class="col1">Type</td>
                    <td class="col2">
                      {{ detailCard?.knowledgeBaseType == KBType.PERSON ? 'Private' : 'Shared' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="col1">Description</td>
                    <td class="col2">{{ detailCard?.knowledgeBaseDesc }}</td>
                  </tr>
                  <tr>
                    <td class="col1">Owner</td>
                    <td class="col2">{{ detailCard?.createUserName || 'null' }}</td>
                  </tr>
                  <tr>
                    <td class="col1">File</td>
                    <td class="col2">{{ detailCard?.documentCount || 0 }} files</td>
                  </tr>
                </table>
              </template>
              <Button
                type="link"
                class="kb-list__knowledge-more-button"
                @click.stop="handleMoreClick(menu)"
              >
                <SvgIcon name="more-icon" size="14" />
              </Button>
            </Popover>
          </section>
          <ATooltip
            placement="top"
            class="loading-icon"
            v-if="!!(knowledgeStore.knowledgeTaskMap[menu.entityId] && getTaskCount(menu.entityId))"
          >
            <template #title>
              <span> Importing {{ getTaskCount(menu.entityId) }} files </span>
            </template>
            <Button class="icon-btn" type="text">
              <SvgIcon name="progress-mini" :rotate="true" size="14" />
            </Button>
          </ATooltip>
        </li>
      </div>
      <div class="kb-list_ul_empty" v-if="item.type == KBType.TEAM && item.data.length == 0">
        <label><SvgIcon name="tip-icon" size="14" /></label>
        <label>No shared knowledge base available yet.</label>
      </div>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { ATooltip, Popover, Button, message } from '@libs/a-comps'
import { knowledgeBaseApi } from '@/renderer/src/api/knowledgeBase'
import { FileStatus } from '@/main/Service/fileSync/constants/types'
// import { emitAgentChanged } from '@/utils'
import { IKBItem, KBMenus, KBType } from '../type'
import { knowledgeStore } from '../store'

// const emit = defineEmits(['selectChange', 'handleUpdateKnowledgeList'])
const emit = defineEmits<{
  (e: 'selectChange', value: IKBItem): void
  (e: 'handleUpdateKnowledgeList', value: IKBItem[]): void
}>()

const timer = ref(0)
const activeItem = ref<IKBItem | null>(null)
const menus = reactive<KBMenus[]>([])

const detailCard = ref<IKBItem>()

// watch(
//   () => knowledgeStore.knowledgeTaskMap,
//   (newVal) => {
//     // console.log('knowledgeStore.knowledgeTaskMap', newVal)
//   },
//   { deep: 1, immediate: true }
// )

// 在导入任务数
const getTaskCount = (entityId: string) => {
  const { importingFileList, cutingTaskArr } = knowledgeStore.knowledgeTaskMap[entityId]

  const importingTaskArr = importingFileList.filter((importingFileItem) => {
    return importingFileItem.status !== FileStatus.FAILED
  })

  return importingTaskArr.length + cutingTaskArr.length
}

const handleKBClick = (e: IKBItem) => {
  // emitAgentChanged(e.knowledgeBaseId, '2')
  activeItem.value = e
  emit('selectChange', e)
  // 右侧列表滚动条重置为顶部
  document.querySelector('.kb-file-container_table .ant-table')?.scrollTo({
    top: 0
    //behavior: 'smooth'
  })
}
const handleMoreClick = (e: IKBItem) => {
  detailCard.value = { ...e }
  const messageParams = {
    pageIndex: 1,
    pageSize: 20,
    keyword: '',
    sortBy: '',
    sort: '',
    knowledgeId: e.entityId
  }
  knowledgeBaseApi
    .getKBFileList(messageParams)
    .then((response) => response.data)
    .then((res: any) => {
      if (res.code == 200 && res.data) {
        // detailCard.value?.documentCount = res.data.totalCount
        detailCard.value = {
          ...e,
          documentCount: res.data.totalCount
        }
      }
    })
}
const setKBMenus = (data: IKBItem[]) => {
  const personDate = data.filter((item) => item.knowledgeBaseType === KBType.PERSON)
  const teamDate = data.filter((item) => item.knowledgeBaseType === KBType.TEAM)
  Object.assign(menus, [
    { type: KBType.PERSON, title: 'Private', data: personDate },
    { type: KBType.TEAM, title: 'Shared', data: teamDate }
  ])

  activeItem.value =
    personDate.length > 0 ? personDate[0] : teamDate.length > 0 ? teamDate[0] : null
  emit('selectChange', activeItem.value)
}

const getKBList = async () => {
  const res = await knowledgeBaseApi.getKBList()
  // console.log('getKBList--', res)
  if (res.status == 200) {
    if (res.data?.success) {
      setKBMenus(res.data?.data ?? [])

      emit('handleUpdateKnowledgeList', res.data?.data || [])
    } else {
      message.error(res.data?.msg ?? 'get knowledgebase list error!')
    }
  }
}

onMounted(() => {
  getKBList()
})
onBeforeUnmount(() => {
  // stopFreshKBList()
})
</script>

<style lang="less" scoped>
.table-title {
  width: 346px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0%;
  line-height: 24px;
  vertical-align: middle;
}

.kb-list__popover-title {
  padding: 6px 12px 0;
}

.kb-popover-table {
  border-collapse: collapse;
  margin-left: 12px;
  margin-right: 12px;
}

.kb-popover-table td {
  border-bottom: 1px solid #e5e7ec;
  // height: 22px;
  letter-spacing: 0;
  padding: 7px 0;
}

.kb-popover-table .col1 {
  font-size: 14px;
  // line-height: 22px;
  width: 87px;
}

.kb-popover-table .col2 {
  width: 275px;
}

.kb-popover-table tr:last-child td {
  border-bottom: none;
}

.ant-popover-inner .ant-popover-title p {
  margin-left: 12px;
}

.kb-list {
  height: 100%;

  &_title {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    display: table-cell;
    vertical-align: middle;
    padding: 0 16px;
    height: 40px;
  }

  &_ul {
    color: #000000;
    padding: 0;
    margin: 0;

    .kb-list__team-knowledge-wrapper {
      height: calc(100vh - 238px);
      overflow: auto;
    }

    &_item {
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
      // height: 40px;
      padding: 9px 16px;
      margin-bottom: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &:hover {
        background: #f5f2fe;
        border-radius: 2px;
      }

      &.selected {
        background: #f5f2fe;
        border-radius: 2px;
        color: #6441ab;
        font-weight: 600;
      }

      .loading-icon {
        position: absolute;
        right: 16px;
        background: transparent;
        opacity: 1;
        height: 32px;
        padding: 0;
        color: #6441ab;
      }

      section {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .kb-list__knowledge-more-button {
          display: none;
          position: absolute;
          right: 16px;
          padding-left: 0;
          padding-right: 0;
          line-height: 14px;

          &:focus {
            display: inline-block;
          }
        }

        &:hover {
          .kb-list__knowledge-more-button {
            display: inline-block;
          }
        }
      }

      section:hover + .loading-icon {
        display: none;
      }

      .kb-list__item-name {
        display: inline-block;
        width: 90%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      @media print {
        .kb-list__item-name {
          overflow: visible;
        }
      }
    }

    &_title {
      font-size: 12px;
      line-height: 22px;
      color: var(--text-color4);
      height: 40px;
      padding: 0 16px;
      margin: 4px 0;
      border-top: 1px solid #e5e7ec;
      display: flex;
      align-items: center;
    }
    &_empty {
      display: flex;
      border: 1px solid #e5e7ec;
      padding: 9px 15px;
      border-radius: 2px;
      label:first-child {
        margin-right: 9px;
        svg {
          margin-top: 3px;
        }
      }
      label:last-child {
        line-height: 20px;
      }
    }
  }
}

.kb-detail {
  padding: 12px 24px;
  width: 410px;
  margin-right: 12px;

  :deep(.ant-popover-content) {
    padding: 12px 24px;
    width: 410px;
  }
}
</style>
