<template>
  <div class="load-more">
    <div ref="loadMoreButton">
      <ABtn @click="loadMore" type="text" :disabled="isLoading" class="load-more-button">
        {{ isLoading ? 'loading' : 'loading' }}
      </ABtn>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps } from 'vue'
import { ABtn } from '@libs/a-comps'

const props = defineProps<{
  loadMoreFunc: () => void
}>()

const isLoading = ref(false)
const loadMoreButton = ref<HTMLElement | null>(null)
onMounted(() => {
  init()
})
const init = () => {
  const io = new IntersectionObserver(
    (entries) => {
      if (entries[0].intersectionRatio > 0.3) {
        console.log('entries----', entries)
        // 触发加载更多数据的逻辑
        loadMore()
      }
    },
    {
      rootMargin: '0px 0px 50px 0px', // 设置根元素的边距
      root: null, //document.querySelector('.ant-table-small'), // 观察的根元素
      threshold: 1 // 阀值设为1，当只有比例达到1时才触发回调函数
    }
  )
  io.observe(loadMoreButton.value!)
}
const loadMore = async () => {
  if (isLoading.value) return
  isLoading.value = true

  try {
    // 模拟加载更多数据的异步操作
    //await new Promise((resolve) => setTimeout(resolve, 500))
    // 在这里处理加载更多的逻辑
    props.loadMoreFunc()
    console.log('加载更多数据完成')
  } catch (error) {
    console.error('加载更多数据失败', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.load-more {
  text-align: center;

  .load-more-button {
    color: #666;
    &:hover {
      background-color: #0056b3;
    }
  }
}
</style>
