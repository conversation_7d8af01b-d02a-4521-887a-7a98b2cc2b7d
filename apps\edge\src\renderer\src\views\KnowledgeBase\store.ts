/*
 * @Description:
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\KnowledgeBase\store.ts
 */
import { reactive } from 'vue'
import { IFileInfo } from '@/main/Service/fileSync/constants/types'
import { CutingTask } from '@/types'
import { IKBItem } from './type'
import { KnowledgeRecord } from '@/types/Service'

interface knowledgeTaskMap {
  [knowledgeId: string]: {
    cutingTaskTimer: NodeJS.Timeout | null
    importingFileList: IFileInfo[]
    cutingTaskArr: CutingTask[]
  }
}

export const knowledgeStore = reactive<{
  knowledgeTaskMap: knowledgeTaskMap
  createKnowledgeTaskMap: (pendingFiles: IFileInfo[]) => void
  createKnowledgeTaskMapByKnowledgeArr: (knowledgeArr: IKBItem[]) => void
  createKnowledgeTaskMapByGroupKnowledgeArr: (groupKnowledgeArr: KnowledgeRecord[]) => void
  updateKnowledgeCutingTaskArr: (param: {
    knowledgeId: string
    cutingTaskArr: CutingTask[]
  }) => void
  updateKnowledgeCutingTaskTimer: (param: {
    knowledgeId: string
    cutingTaskTimer: NodeJS.Timeout | null
  }) => void
  updateKnowledgeImportingFileList: (param: {
    knowledgeId: string
    importingFileList: IFileInfo[]
  }) => void
  updateKnowledgeImportingFileListByPendingFiles: (param: {
    knowledgeId?: string
    pendingFiles: IFileInfo[]
  }) => void
}>({
  knowledgeTaskMap: {},
  // 创建知识库任务映射
  createKnowledgeTaskMap(pendingFiles: IFileInfo[]) {
    pendingFiles.forEach((pendingFileItem) => {
      // 如果知识库任务映射中不存在该知识库，则创建一个空对象
      if (!this.knowledgeTaskMap[pendingFileItem.source]) {
        this.knowledgeTaskMap[pendingFileItem.source] = {
          cutingTaskTimer: null,
          importingFileList: [],
          cutingTaskArr: []
        }
      }
    })
  },
  // 创建知识库任务映射通过知识库数组
  createKnowledgeTaskMapByKnowledgeArr(knowledgeArr: IKBItem[]) {
    knowledgeArr.forEach((knowledgeItem) => {
      if (!this.knowledgeTaskMap[knowledgeItem.entityId]) {
        this.knowledgeTaskMap[knowledgeItem.entityId] = {
          cutingTaskTimer: null,
          importingFileList: [],
          cutingTaskArr: []
        }
      }
    })
  },
  createKnowledgeTaskMapByGroupKnowledgeArr(groupKnowledgeArr: KnowledgeRecord[]) {
    groupKnowledgeArr.forEach((groupKnowledgeItem) => {
      const { displayType, kbList, entityId } = groupKnowledgeItem

      if (displayType === 'KB') {
        this.knowledgeTaskMap[entityId] = {
          cutingTaskTimer: null,
          importingFileList: [],
          cutingTaskArr: []
        }
      } else if (displayType === 'GROUP') {
        if (kbList) {
          kbList.forEach((kbItem) => {
            if (!this.knowledgeTaskMap[kbItem.entityId]) {
              this.knowledgeTaskMap[kbItem.entityId] = {
                cutingTaskTimer: null,
                importingFileList: [],
                cutingTaskArr: []
              }
            }
          })
        }
      }
    })
  },
  // 更新知识库切块任务数组
  updateKnowledgeCutingTaskArr(param: { knowledgeId: string; cutingTaskArr: CutingTask[] }) {
    const { knowledgeId, cutingTaskArr } = param
    this.knowledgeTaskMap[knowledgeId].cutingTaskArr = cutingTaskArr
  },
  // 更新知识库切块任务定时器
  updateKnowledgeCutingTaskTimer(param: {
    knowledgeId: string
    cutingTaskTimer: NodeJS.Timeout | null
  }) {
    const { knowledgeId, cutingTaskTimer } = param
    this.knowledgeTaskMap[knowledgeId].cutingTaskTimer = cutingTaskTimer
  },
  // 更新知识库上传文件列表
  updateKnowledgeImportingFileList(param: { knowledgeId: string; importingFileList: IFileInfo[] }) {
    const { knowledgeId, importingFileList } = param
    this.knowledgeTaskMap[knowledgeId].importingFileList = importingFileList
  },
  // 更新知识库上传文件列表通过待上传文件数组
  updateKnowledgeImportingFileListByPendingFiles(param: {
    knowledgeId?: string
    pendingFiles: IFileInfo[]
  }) {
    const { knowledgeId, pendingFiles } = param

    pendingFiles.forEach((pendingFileItem) => {
      if (knowledgeId && pendingFileItem.source === knowledgeId) {
        this.knowledgeTaskMap[knowledgeId].importingFileList.push(pendingFileItem)
      } else {
        this.knowledgeTaskMap[pendingFileItem.source].importingFileList.push(pendingFileItem)
      }
    })
  }
})
