export enum KBType {
  PERSON = 'PERSON',
  TEAM = 'TEAM'
}
export type IKBItem = {
  knowledgeBaseId: string
  knowledgeBaseName: string
  knowledgeBaseIcon?: string
  knowledgeBaseDesc?: string
  knowledgeBaseType: KBType
  createUserId: number | string
  createUserName: string
  documentCount: number
  origin: string
  entityId: string
  loading?: null | ISyncProgress
  id?: string
  createTime?: string
  updateTime?: string
  permission: number
}
export type KBMenus = {
  type: KBType
  title: string
  data: IKBItem[]
}
// {
//     "knowledgeBaseId":123,                           // 知识库ID
//     "knowledgeBaseName":"aaa",                       // 名称
//     "knowledgeBaseIcon":"http://xxx/image.jpg",      // 图标
//     "knowledgeBaseDesc":"xxx",                       // 描述
//     "knowledgeBaseType":"TEAM",                      //枚举：PERSON, TEAM
//    "createUserId": 455,
//     "createUserName": "13641086448",                 // 创建人owner
//     "documentCount": "10",                           // 文件数
//    "origin": "LR",
//     "entityId": "1001"                               // LR侧的知识库ID,调用LR接口时需要传的ID
// }

export interface ISyncProgress {
  total: number
  processed: number
  deleted: number
  conflicts: number
}
export type IKBFileItem = {
  documentId: string
  documentName: string
  documentSize: number
  chunkCount: number
  graphChunkCount: number
  graphChunkCompleteCount: number
  taskPosition: number
  textLength: number
  documentType: string
  enable: boolean
  tags: string[]
  status: string
  errorInfo: string
  createTime: string
  updateTime: string
  owner?: string
}

export const fileExtensionIconMap = {
  doc: 'DOC',
  docx: 'DOC',
  xls: 'EXCEL',
  xlsx: 'Excel',
  csv: 'Excel',
  jpg: 'JPG',
  jpeg: 'JPEG',
  msg: 'MSG',
  pdf: 'PDF',
  png: 'PNG',
  ppt: 'PPT',
  pptx: 'PPT',
  // md: 'MD',
  // txt: 'TEXT',
  // html: 'HTML',
  // zip: 'ZIP',
  // book: 'BOOK',
  txt: 'TXT'
}

/**
 * 同步状态枚举
 */
export enum CutStatusEnum {
  PROCESSING = 'Processing',
  PAUSED = 'Paused',
  FAILED = 'Failed'
}

// 切片任务状态映射
export const cutStatusMap: Record<string, string> = {
  documentCutPending: CutStatusEnum.PROCESSING,
  documentCutProcessing: CutStatusEnum.PROCESSING,
  documentCutPaused: CutStatusEnum.PAUSED,
  documentCutError: CutStatusEnum.FAILED,
  documentCutTimeOut: CutStatusEnum.FAILED
}

export type ImportingFileItem = {
  name: string
  fileId: string
  taskType?: string
  status: string
  documentId: string
  cutStatus?: string
}

export type CutErrorData = {
  totalCount: number
  pageCount: number
  knowledgeId: string
  knowledgeName: string
  storeGraph: boolean
  documentList: IKBFileItem[]
}
