<template>
  <Layout
    v-if="GlobalConfig.tokens.access_token"
    :menu-items="menu"
    :should-hide-my-knowledge="shouldHideMyKnowledge"
  >
    <!-- <template #top-slot="{ collapsed }">
      <AddBtn :collapsed="collapsed" @click="handleNewChat"></AddBtn>
    </template> -->
    <template #default="{ sessionNum }">
      <RouterView :sessionNum="sessionNum"></RouterView>
    </template>
  </Layout>
</template>

<script setup lang="ts">
import { h, ref, onMounted } from 'vue'
import { GlobalConfig } from '@/renderer/src/common'
// import AddBtn from './comps/AddBtn.vue'
import Layout from '../../components/Layout'
// import SvgIcon from '../../components/SvgIcon'
import { useRouter } from 'vue-router'
import { getAgentList } from '@/services'
import { AgentVo } from '@/types'
const router = useRouter()
const menu = ref<any[]>([])
const shouldHideMyKnowledge = ref(false)

onMounted(() => {
  getAgentList().then((res) => {
    if (res?.data?.success) {
      if (Array.isArray(res?.data?.data)) {
        // 是否需要隐藏 My Knowledge 菜单
        shouldHideMyKnowledge.value =
          res?.data?.data?.length === 1 && res?.data?.data?.[0]?.origin === 'xinliu'

        const agentMenus = res.data.data.map((agent: AgentVo) => {
          const isKnowledgeAgent = agent.agentName === 'Knowledge Agent'
          return {
            name: agent.agentName,
            to: {
              name: isKnowledgeAgent ? 'chat' : 'agent',
              query: {
                origin: agent.origin,
                agentUrl: agent.agentUrl,
                id: agent.entityId,
                name: agent.agentName,
                agentId: agent.agentId
              }
            },
            resourceType: isKnowledgeAgent && '1',
            icon: h('img', {
              src: agent.agentIcon,
              style: {
                width: '38px',
                height: '38px'
              }
            })
          }
        })
        menu.value = agentMenus
        const knowledgeAgent = res.data.data.find(
          (agent: AgentVo) => agent.agentName === 'Knowledge Agent'
        )
        const videoAgent = res.data.data.find((agent: AgentVo) => agent.origin === 'xinliu')
        const agents = res?.data?.data
        const hasOnlyVideoAgent = agents?.length === 1 && agents?.[0]?.origin === 'xinliu'
        if (hasOnlyVideoAgent) {
          const { entityId, origin, agentName, agentId, agentUrl } = videoAgent!
          router.replace({
            name: 'agent',
            query: {
              id: entityId,
              origin,
              name: agentName,
              agentId,
              agentUrl
            }
          })
        } else if (knowledgeAgent) {
          // 有多个菜单或者只有Knowledge Agent，显示Knowledge Agent页面
          const { entityId, origin, agentName, agentId } = knowledgeAgent
          router.replace({
            name: 'chat',
            query: {
              id: entityId,
              origin,
              name: agentName,
              agentId
            }
          })
        } else if (videoAgent) {
          // 没有Knowledge Agent但有Video Agent(多个菜单中有Video Agent)
          const { entityId, origin, agentName, agentId, agentUrl } = videoAgent
          router.replace({
            name: 'agent',
            query: {
              id: entityId,
              origin,
              name: agentName,
              agentId,
              agentUrl
            }
          })
        }
      }
    }
  })
})
// const handleNewChat = () => {
//   router.push({ name: 'chat' })
//   // 分发新聊天创建事件，供answer.vue监听
//   window.dispatchEvent(new CustomEvent('newChatCreated'))
// }
</script>

<style scoped></style>
