<template>
  <div class="add-btn-container">
    <ABtn
      shape="round"
      size="large"
      :class="{ 'add-btn': true, 'add-btn-collapsed': collapsed }"
      block
      type="primary"
    >
      <SvgIcon name="Add" size="18" class="add-btn_icon"></SvgIcon>
      {{ collapsed ? '' : 'New Chat' }}
    </ABtn>
  </div>
</template>

<script setup lang="ts">
import { ABtn } from '@libs/a-comps'
import SvgIcon from '../../../components/SvgIcon'
defineProps<{
  collapsed: boolean
}>()
</script>

<style lang="less" scoped>
.add-btn-container {
  width: 100%;
  box-sizing: border-box;
}

.add-btn.ant-btn-primary.add-btn-collapsed {
  width: 40px;
  height: 40px;
  padding: 10px;
  margin-left: 4px;

  :deep(.add-btn_icon) {
    margin-right: 0;
  }
}

.add-btn.ant-btn-primary {
  padding: 5px 12px 5px 10px;
  line-height: 20px;
  text-align: left;
  font-weight: 600;
  background-color: var(--bg-color1);
  color: var(--primary-color);
  box-shadow: 0px 2px 2px 0px #00000012;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: none;
  margin: 0;

  &:hover {
    background-color: var(--hover-color);
  }
}

:deep(.add-btn_icon) {
  margin-right: 9px;
  display: flex;
  align-items: center;
}
</style>
