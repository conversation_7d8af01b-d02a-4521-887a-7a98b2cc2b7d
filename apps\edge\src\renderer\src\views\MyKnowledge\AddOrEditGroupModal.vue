<template>
  <AModal
    class="my-knowledge__create-group-modal"
    width="464px"
    centered
    v-model:open="isModalOpen"
    @after-close="handleAfterClose"
  >
    <template #title>
      <div class="create-group-modal__title">
        <span class="create-group-modal__title-text">{{ title }}</span>
      </div>
    </template>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <ABtn type="primary" @click="handleConfirm">Confirm</ABtn>
        <ABtn @click="handleCancel">Cancel</ABtn>
      </div>
    </template>
    <div class="create-group-modal__body">
      <span class="group-name-label">Group name</span>
      <AInput
        class="create__group-input"
        v-model:value="createGroupInputValue"
        placeholder="Please enter"
        :status="isGroupNameInvalid || isShowGroupNameEmptyError ? 'error' : ''"
        @change="handleChangeInput"
      >
      </AInput>
      <div v-if="isGroupNameInvalid" class="group-name-error-message">
        *The name of the group cannot exceed 20 characters.
      </div>
      <div v-else-if="isShowGroupNameEmptyError" class="group-name-error-message">
        *The name of the group cannot be empty.
      </div>
    </div>
  </AModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ABtn, AModal, AInput } from '@libs/a-comps'

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  initialValue: {
    type: String,
    default: ''
  }
})

const title = computed(() => (props.isEdit ? 'Edit Group' : 'Add new group'))

const isModalOpen = defineModel({
  type: Boolean,
  default: false
})

const emit = defineEmits<{
  (e: 'confirm', groupName: string): void
  (e: 'afterClose'): void
}>()

const createGroupInputValue = ref('')
const isShowGroupNameEmptyError = ref(false)
const isGroupNameInvalid = computed(() => createGroupInputValue.value.length > 20)

watch(isModalOpen, (isOpen) => {
  if (isOpen) {
    isShowGroupNameEmptyError.value = false
    createGroupInputValue.value = props.initialValue
  }
})

/**
 * @description 处理改变输入框
 * @param {InputEvent} event 输入框事件
 * @returns {void}
 */
const handleChangeInput = (event: InputEvent) => {
  const target = event.target as HTMLInputElement
  isShowGroupNameEmptyError.value = !target.value
}

const handleConfirm = () => {
  if (!createGroupInputValue.value) {
    isShowGroupNameEmptyError.value = true
  }

  if (!createGroupInputValue.value || isGroupNameInvalid.value) {
    return
  }
  emit('confirm', createGroupInputValue.value)
  isModalOpen.value = false
}

const handleCancel = () => {
  isModalOpen.value = false
}

const handleAfterClose = () => {
  createGroupInputValue.value = ''
  if (props.isEdit) {
    emit('afterClose')
  }
}
</script>

<style lang="less" scoped>
.my-knowledge__create-group-modal {
  .create-group-modal__title {
    padding: 4px 0;
    text-align: center;
  }

  .create-group-modal__title-text {
    vertical-align: middle;
  }

  .create-group-modal__body {
    padding: 16px 24px 16px;
    text-align: left;
    color: #000;

    .create__group-input {
      width: 100%;
      height: 32px;
      margin-right: 12px;
      // border-color: #f2f3f5;
      background: #f2f3f5;
      vertical-align: middle;

      ::v-deep(.ant-input) {
        background-color: #f2f3f5;

        &::placeholder {
          color: var(--text-color4);
        }
      }
    }

    .group-name-label {
      display: block;
      margin-bottom: 8px;
    }

    .group-name-label::after {
      content: '*';
      color: #f2463d;
      margin-left: 2px;
    }

    .group-name-error-message {
      margin-top: 8px;
      color: #f2463d;
    }
  }
}

:deep(.a-btn.ant-btn.ant-btn-default) {
  color: #000000;
}
</style>
