<template>
  <Drawer
    class="add-knowledge-base-drawer"
    width="384"
    :title="title"
    :open="isOpenAddKnowledgeBaseDrawer"
    :closable="false"
    :header-style="{ padding: '16px' }"
    :body-style="{ padding: '16px' }"
    :mask-style="{ backgroundColor: 'rgba(15, 17, 20, 0.12)' }"
    root-class-name="create-kb-drawer-root"
    get-container="#app"
    @close="handleClose"
    @after-open-change="handleAfterOpenChange"
    @after-visible-change="handleAfterVisibleChange"
  >
    <div class="add-knowledge-base-drawer__body">
      <Form
        ref="formReference"
        class="create-kb__form"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :model="formState"
        :rules="rules"
        :colon="false"
      >
        <AFormItem label="KB Name" name="kbName">
          <AInput
            class="kb-name-input"
            v-model:value="formState.kbName"
            placeholder="Please enter"
            :disabled="!!initialData"
          />
        </AFormItem>
        <AFormItem label="Group" name="groupId">
          <ASelect
            v-model:value="formState.groupId"
            placeholder="Please select"
            :options="groupOptions"
            :field-names="{ label: 'groupName', value: 'id' }"
          />
        </AFormItem>
        <AFormItem
          label="Description"
          name="description"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
        >
          <ATextarea
            v-model:value="formState.description"
            placeholder="This is a description of the design team Agent."
            :rows="6"
            :disabled="!!initialData"
          />
        </AFormItem>
      </Form>
    </div>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; gap: 12px">
        <ABtn @click="handleClose">Cancel</ABtn>
        <ABtn type="primary" @click="handleConfirm" :loading="isConfirmLoading">Confirm</ABtn>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { reactive, ref, useTemplateRef, watch } from 'vue'
import { Drawer, Form, AFormItem, AInput, ASelect, ATextarea, ABtn } from '@libs/a-comps'
import { GroupItem, KnowledgeRecord } from '@/types'

const props = defineProps({
  title: {
    type: String,
    default: 'Add Knowledge Base'
  },
  groupOptions: {
    type: Array as () => GroupItem[],
    default: () => []
  },
  initialData: {
    type: Object as () => KnowledgeRecord
  },
  isConfirmLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['handleConfirmAddKnowledgeBase', 'handleCloseAddKnowledgeBase'])

const isOpenAddKnowledgeBaseDrawer = defineModel({
  default: false
})

const formReference = useTemplateRef<typeof Form>('formReference')

watch(isOpenAddKnowledgeBaseDrawer, (newVal) => {
  if (newVal && formReference.value) {
    formReference.value.resetFields()
  }
})

interface FormState {
  kbName: string
  groupId: string | undefined
  description: string
}

const formState = reactive<FormState>({
  kbName: '',
  groupId: undefined,
  description: ''
})

const rules = reactive({
  kbName: [{ required: true, message: '*The KB name cannot be empty.' }]
})

const handleClose = () => {
  isOpenAddKnowledgeBaseDrawer.value = false
}

const handleAfterVisibleChange = (visible: boolean) => {
  if (!visible) {
    emit('handleCloseAddKnowledgeBase')
  }
}

const handleConfirm = () => {
  if (formReference.value) {
    formReference.value
      .validate()
      .then(() => {
        emit('handleConfirmAddKnowledgeBase', formState)
      })
      .catch((error: Error) => {
        console.error('error', error)
      })
  }
}

const handleAfterOpenChange = (open: boolean) => {
  if (open) {
    if (props.initialData) {
      formState.kbName = props.initialData.knowledgeBaseName
      formState.groupId = props.initialData.groupId === '0' ? undefined : props.initialData.groupId
      formState.description = props.initialData.knowledgeBaseDesc
    } else {
      formState.kbName = ''
      formState.groupId = undefined
      formState.description = ''
    }
  }
}
</script>

<style lang="less" scoped>
.add-knowledge-base-drawer {
  .add-knowledge-base-drawer__body {
    .kb-name-input {
      height: 32px;
    }
  }
}
:deep(.ant-drawer-header) {
  border-bottom: none;
  padding-top: 0px;
}
:deep(.ant-drawer-title) {
  font-weight: 600;
  font-size: 16px;
}
:deep(.ant-form-item-label label) {
  color: #3b3b3b;
  font-weight: 400;
}
:deep(.ant-input),
:deep(.ant-select-selector) {
  background-color: #f7f8f8 !important;
}
:deep(.ant-input::placeholder),
:deep(.ant-select-selection-placeholder) {
  color: #696969;
}
:deep(.ant-drawer-footer) {
  border-top: none;
}
:deep(.ant-form-item-label) {
  text-align: left;
}
:deep(.a-btn.ant-btn) {
  min-width: 102px;
}
:deep(.a-btn.ant-btn.ant-btn-default) {
  color: #000000;
}
:deep(
    .ant-form-item
      .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)
  )::before {
  display: none;
}
:deep(
    .ant-form-item
      .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)
  )::after {
  display: inline-block;
  margin-inline-end: 4px;
  color: #e1251b;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
:deep(.ant-form-item-explain-error) {
  background: transparent;
  box-shadow: none;
  padding-left: 0px;
  padding-top: 0px;
}
:deep(.a-form-item .ant-form-item-explain-error::before) {
  display: none;
}
</style>

<style lang="less">
.create-kb-drawer-root {
  position: absolute;
}
</style>
