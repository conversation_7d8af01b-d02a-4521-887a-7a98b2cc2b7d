<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\MyKnowledge\DeleteModal.vue
-->
<template>
  <AModal
    class="my-knowledge__delete-modal"
    width="464px"
    centered
    v-model:open="isOpenDeleteModal"
    @ok="handleOkDeleteModal"
    @cancel="handleCancelDeleteModal"
  >
    <template #title>
      <div class="delete-modal__title">
        <SvgIcon class="delete-modal__title-icon" name="warn-icon" size="20" />
        <span class="delete-modal__title-text">Delete?</span>
      </div>
    </template>
    <template #footer>
      <div style="display: flex; justify-content: center">
        <ABtn type="primary" :loading="isDeleteButtonLoading" @click="handleOkDeleteModal"
          >Delete</ABtn
        >
        <ABtn @click="handleCancelDeleteModal" class="delete-cancel-text"> Cancel </ABtn>
      </div>
    </template>
    <div class="delete-modal__body">
      <slot></slot>
    </div>
  </AModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ABtn, AModal } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'

const props = defineProps<{
  isDeleteButtonLoading: boolean
}>()

const isOpenDeleteModal = defineModel({
  default: false
})

// 定义事件
const emit = defineEmits<{
  (e: 'handleConfirmDelete'): void
  (e: 'handleCancelDelete'): void
}>()

/**
 * @description 确认删除弹窗
 */
const handleOkDeleteModal = () => {
  emit('handleConfirmDelete')
}

/**
 * @description 取消删除弹窗
 */
const handleCancelDeleteModal = () => {
  isOpenDeleteModal.value = false
  emit('handleCancelDelete')
}
</script>

<style lang="less" scoped>
.my-knowledge__delete-modal {
  .delete-modal__title {
    padding: 4px 0;
    text-align: center;
  }

  .delete-modal__title-icon {
    margin-right: 8px;
    vertical-align: middle;
  }

  .delete-modal__title-text {
    vertical-align: middle;
  }

  .delete-modal__body {
    padding: 16px 0 24px;
    text-align: center;
    color: #000;
  }
  .delete-cancel-text {
    color: #000000;
  }
}
</style>
