<template>
  <Drawer
    class="kb-auth-drawer"
    width="384px"
    :title="title"
    :open="isOpenAddKnowledgeBaseDrawer"
    :closable="false"
    :header-style="{ padding: '16px' }"
    :body-style="{ padding: '16px' }"
    :mask-style="{ backgroundColor: 'rgba(15, 17, 20, 0.12)' }"
    root-class-name="kb-auth-drawer-root"
    get-container="#app"
    @close="handleClose"
    @after-open-change="handleAfterOpenChange"
    @after-visible-change="handleAfterVisibleChange"
  >
    <div class="kb-auth-drawer__body">
      <div class="controls-wrapper">
        <div class="search-add-wrapper">
          <AAutoComplete
            allowClear
            placeholder="Search by name"
            v-model:value="searchInputValue"
            class="search-input"
            :options="searchedUserOptions"
            popup-class-name="user-search-dropdown"
            :get-popup-container="(triggerNode) => triggerNode.parentNode"
            @search="handleSearchUser"
            @select="handleSelectUser"
          >
            <template #option="{ userName, nickName }">
              <div class="user-search-option-username">{{ userName }}</div>
              <div class="user-search-option-nickname">{{ nickName }}</div>
            </template>
          </AAutoComplete>
          <ASelect
            v-model:value="newUserPermission"
            :options="roleOptions"
            class="top-role-select"
            allow-clear
            placeholder="Select a role"
          />
        </div>
        <ABtn class="add-btn" type="bordered" @click="handleAdd">
          <SvgIcon class="kb-auth__add-group-button-icon" name="import-icon" size="11" />
          Add
        </ABtn>
      </div>
      <div v-if="showUserExistsError" class="exist-tip">User already in the list and authed</div>
      <div class="access-title">Who have access</div>
      <div class="auth-list">
        <div v-for="user in allUsers" :key="user.id" class="auth-item">
          <div class="user-info">
            <div class="name">
              <ATooltip
                v-if="user.nickName ? user.nickName.length > 20 : user.userName.length > 20"
              >
                <template #title>
                  {{ user.nickName ? user.nickName : user.userName }}
                </template>
                {{
                  user.nickName
                    ? user.nickName.slice(0, 20) + '...'
                    : user.userName.slice(0, 20) + '...'
                }}
              </ATooltip>
              <template v-else>
                {{ user.nickName ? user.nickName : user.userName }}
              </template>
            </div>
            <div v-if="Number(user?.permission || 0) !== 4" class="delete-container">
              <SvgIcon
                class="delete-icon"
                name="delete-icon"
                size="16"
                @click="handleDelete(user)"
              />
            </div>
          </div>
          <div v-if="Number(user?.permission || 0) === 4" class="auth-tag">Owner</div>
          <div v-else class="auth-actions">
            <ASelect
              v-model:value="user.permission"
              :options="roleOptions"
              class="role-select"
              @change="handleRoleChange(user, $event)"
            />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <ABtn @click="handleClose">Cancel</ABtn>
        <ABtn type="primary" @click="handleConfirm" :loading="isConfirmLoading">Confirm</ABtn>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import {
  Drawer,
  ASelect,
  ABtn,
  AInput,
  message,
  AutoComplete as AAutoComplete,
  ATooltip
} from '@libs/a-comps'
import { KnowledgeRecord } from '@/types'
import SvgIcon from '@renderer/components/SvgIcon'
import { fetchKBUserList, updateKBAuth } from '@/renderer/src/api/myKnowledge'
import { getAccountList } from '@/renderer/src/api/userManagement'

const props = defineProps({
  title: {
    type: String,
    default: 'Knowledge Base authorization'
  },
  groupOptions: {
    type: Array as () => unknown[],
    default: () => []
  },
  initialData: {
    type: Object as () => KnowledgeRecord | null,
    default: null
  }
})

const emit = defineEmits(['handleConfirmAddKnowledgeBase', 'handleCloseKBAuth'])

const isOpenAddKnowledgeBaseDrawer = defineModel({
  default: false
})

const searchInputValue = ref('')
const searchedUserOptions = ref<any[]>([])
const isSearchingUser = ref(false)
const selectedUserToAdd = ref<any>(null)
const newUserPermission = ref<number>()

const allUsers = ref<any[]>([])
const isConfirmLoading = ref<boolean>(false)
const showUserExistsError = ref(false)

const fetchAllUsersForAuth = async () => {
  try {
    const result = await fetchKBUserList({
      knowledgeBaseId: props?.initialData?.knowledgeBaseId
    })
    if (result.data.success && result.data.data) {
      // @ts-ignore
      const data = result.data.data
      allUsers.value = data
    } else {
      allUsers.value = []
    }
  } catch (error) {
    allUsers.value = []
  }
}

onMounted(() => {
  initData(props.initialData)
})

watch(
  () => props.initialData,
  (newData) => {
    if (newData) {
      initData(newData)
    }
  },
  { immediate: true }
)

const initData = (obj: any) => {
  if (obj && obj?.knowledgeBaseId) {
    showUserExistsError.value = false
    fetchAllUsersForAuth()
  } else {
    allUsers.value = []
  }
}

const roleOptions = ref([
  { value: 3, label: 'Can Manage' },
  { value: 2, label: 'Can Edit' },
  { value: 1, label: 'Can View' }
])

const handleSearchUser = async (searchText: string) => {
  showUserExistsError.value = false
  if (!searchText) {
    searchedUserOptions.value = []
    return
  }
  isSearchingUser.value = true
  try {
    const result = await getAccountList({
      userName: searchText,
      role: '',
      current: 1,
      size: 999
    })
    if (result.data.success && result.data.data) {
      const userList = result.data.data.records || []
      searchedUserOptions.value = userList.map((user: any) => ({
        value: user.userName,
        userId: user.userId,
        userName: user.userName,
        nickName: user.nickName
      }))
    } else {
      searchedUserOptions.value = []
    }
  } catch (error) {
    searchedUserOptions.value = []
  } finally {
    isSearchingUser.value = false
  }
}

const handleSelectUser = (value: any, option: any) => {
  showUserExistsError.value = false
  selectedUserToAdd.value = option
}

const handleAdd = () => {
  if (!selectedUserToAdd.value) {
    message.warning('Please search and select a user to add.')
    return
  }

  if (newUserPermission.value === undefined) {
    message.warning('Please select a role.')
    return
  }

  const userExists = allUsers.value.some((user) => user.userId === selectedUserToAdd.value.userId)
  if (userExists) {
    showUserExistsError.value = true
    return
  }

  showUserExistsError.value = false

  const userToAdd = selectedUserToAdd.value
  allUsers.value.push({
    ...userToAdd,
    permission: newUserPermission.value
  })

  // 重置数据以便下次添加
  searchInputValue.value = ''
  selectedUserToAdd.value = null
  searchedUserOptions.value = []
  newUserPermission.value = undefined
}

const handleDelete = (userToDelete: any) => {
  allUsers.value = allUsers.value.filter((user) => user.userId !== userToDelete.userId)
}

const handleRoleChange = (userToUpdate: any, permission: number) => {
  const user = allUsers.value.find((u) => u.userId === userToUpdate.userId)
  if (user) {
    user.permission = permission
  }
}

const handleClose = () => {
  isOpenAddKnowledgeBaseDrawer.value = false
}

const handleConfirm = () => {
  if (!props.initialData?.knowledgeBaseId) {
    return
  }
  if (allUsers.value.length === 0) {
    message.warning('Please add at least one user.')
    return
  }

  isConfirmLoading.value = true

  const authedUserList = allUsers.value.map((user) => {
    return {
      userId: user.userId,
      permission: user.permission
    }
  })

  updateKBAuth({
    knowledgeBaseId: props.initialData.knowledgeBaseId,
    authedUserList
  })
    .then((result) => {
      if (result.data.success) {
        emit('handleConfirmAddKnowledgeBase')
        handleClose()
      } else {
        message.error('Auth failed')
      }
    })
    .catch(() => {
      message.error('Auth failed')
    })
    .finally(() => {
      isConfirmLoading.value = false
    })
}

const handleAfterOpenChange = (open: boolean) => {
  if (open) {
    searchInputValue.value = ''
    searchedUserOptions.value = []
    if (props.initialData) {
    }
  }
}

const handleAfterVisibleChange = (visible: boolean) => {
  if (!visible) {
    emit('handleCloseKBAuth')
  }
}
</script>

<style lang="less" scoped>
.kb-auth-drawer {
  .kb-auth-drawer__body {
    height: calc(100vh - 120px); // 减去header和footer的高度，确保内容不会被遮挡
    max-height: calc(100vh - 120px); // 设置最大高度
    display: flex;
    flex-direction: column;
    .controls-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 6px;
      margin-bottom: 16px;

      .search-add-wrapper {
        display: flex;
        flex-grow: 1;
        align-items: center;
        background-color: #f7f8f8;
        padding-left: 2px;
        // background-color: rgba(0, 0, 0, 0.1);
      }

      .search-input {
        flex-grow: 1;

        // AutoComplete的选择器样式
        :deep(.ant-select-selector) {
          width: 100%;
          height: 35px;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
        }

        // AutoComplete的输入框样式
        :deep(.ant-input) {
          background-color: #f2f3f5 !important;
          border-radius: 4px 0 0 4px;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;

          &:focus {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }

          &:hover {
            border: none !important;
            box-shadow: none !important;
          }
        }

        // AutoComplete焦点状态
        &.ant-select-focused {
          :deep(.ant-select-selector) {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }

          :deep(.ant-input) {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        // AutoComplete打开状态
        &.ant-select-open {
          :deep(.ant-select-selector) {
            border: none !important;
            box-shadow: none !important;
          }
        }

        :deep(.ant-input:focus::placeholder) {
          color: #696969;
        }

        :deep(.ant-input-clear-icon) {
          display: none;
        }

        // 移除所有可能的边框和焦点效果
        :deep(*) {
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
        }
      }

      .top-role-select {
        min-width: 120px;
        margin-right: 2px;
        margin-top: 2px;
        margin-bottom: 2px;

        // 移除所有边框和焦点效果
        :deep(.ant-select-selector) {
          width: 100%;
          height: 35px;
          border: none !important;
          background-color: #ffffff !important;
          box-shadow: none !important;
          outline: none !important;
          display: flex !important;
          align-items: center !important;

          // 文本垂直居中调整
          .ant-select-selection-search,
          .ant-select-selection-placeholder,
          .ant-select-selection-item {
            line-height: 31px !important; // 调整行高适应新的padding
            margin-top: 0 !important;
          }

          &:hover {
            background-color: #f7f8fa !important;
            border: none !important;
            box-shadow: none !important;
          }

          &:active,
          &:focus {
            background-color: #e5e7ec !important;
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        // 针对焦点状态的特殊处理
        &.ant-select-focused {
          :deep(.ant-select-selector) {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        // 针对打开状态的处理
        &.ant-select-open {
          :deep(.ant-select-selector) {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        // 全局覆盖，确保没有边框
        :deep(.ant-select-selector::before),
        :deep(.ant-select-selector::after) {
          display: none !important;
        }
      }

      .role-select {
        min-width: 120px;
        margin-right: 2px;

        :deep(.ant-select-selector) {
          width: 100%;
          height: 35px;
          border: none;

          &:hover {
            background-color: #e5e7ec !important;
          }
        }
      }

      .add-btn {
        margin-left: 10px;
        height: 36px;
        padding: 4px 15px;
        vertical-align: middle;
        border-color: #6441ab;
        &:hover {
          border-color: #4f3387;
        }
        &:active {
          border-color: #3c2766;
        }

        .kb-auth__add-group-button-icon {
          margin-right: 5.5px;
          vertical-align: initial;
        }
      }
    }

    .exist-tip {
      font-weight: 500;
      font-size: 14px;
      color: #e1251b;
      font-family: Segoe UI Variable Static Text;
    }

    .access-title {
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
      margin-top: 30px;
      margin-bottom: 16px;
      font-weight: 500;
      font-size: 14px;
      color: #3b3b3b;
      font-family: Segoe UI Variable Static Text;
    }

    .auth-list {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .auth-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 2px 6px 2px;
        border-radius: 4px;

        :deep(.ant-select-selector) {
          width: 100%;
          height: 35px;
          border: none;
          display: flex;
          align-items: center;

          &:hover {
            background-color: #e5e7ec !important;
          }
        }

        &:hover {
          background-color: #f7f8f8;
        }

        &:has(.auth-tag) {
          &:hover {
            background-color: transparent;
          }
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .name {
            font-weight: 400;
            color: #000000;
          }

          .delete-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.1);
            }

            &:active {
              background-color: rgba(0, 0, 0, 0.2);
            }
          }
        }

        .auth-tag {
          padding: 4px 6px;
          text-align: center;
          background-color: #f5f2fe;
          border-radius: 2px;
          color: #6441ab;
          font-size: 14px;

          &:hover {
            background-color: #f5f2fe;
          }
        }

        .auth-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .delete-icon {
          display: none;
          cursor: pointer;
          color: #696969;
        }

        &:hover {
          .delete-icon {
            display: block;
          }
        }

        .role-select {
          min-width: 120px;
        }
      }
    }
  }

  // 确保在小屏幕下也能正确显示
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 0;
    background-color: transparent;
  }
}

:deep(.ant-drawer-header) {
  border-bottom: none;
  padding-top: 0px;
}

:deep(.ant-drawer-title) {
  font-weight: 600;
  font-size: 16px;
}

:deep(.ant-form-item-label label) {
  color: #3b3b3b;
  font-weight: 400;
}

:deep(.ant-input),
:deep(.ant-select-selector) {
  background-color: #f7f8f8 !important;
}

:deep(.ant-input::placeholder),
:deep(.ant-select-selection-placeholder) {
  color: #696969;
}

:deep(.ant-drawer-footer) {
  border-top: none;
}

:deep(.ant-form-item-label) {
  text-align: left;
}

:deep(.a-btn.ant-btn) {
  min-width: 80px;
}

:deep(.a-btn.ant-btn.ant-btn-default) {
  color: #000000;
}

:deep(
    .ant-form-item
      .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)
  )::before {
  display: none;
}

:deep(
    .ant-form-item
      .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)
  )::after {
  display: inline-block;
  margin-inline-end: 4px;
  color: #e1251b;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

:deep(.ant-form-item-explain-error) {
  background: transparent;
  box-shadow: none;
  padding-left: 0px;
  padding-top: 0px;
}

:deep(.a-form-item .ant-form-item-explain-error::before) {
  display: none;
}

:deep(.user-search-dropdown) {
  min-width: 260px !important;
  padding-left: 0px;
  padding-right: 0px;
  // box-shadow: 0 0 10.67px rgba(0, 0, 0, 0.1474), 0 0 32px rgba(197, 79, 79, 0.188) !important;

  .ant-select-item {
    margin-bottom: 8px;
    height: 50px;

    &:hover {
      background-color: #f5f2fe;
    }
  }

  .ant-select-item-option-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: normal;
  }

  .user-search-option-username {
    color: #000000;
    font-size: 14px;
    line-height: 22px;
  }

  .user-search-option-nickname {
    color: #858585;
    font-size: 14px;
    line-height: 18px;
  }
}
</style>

<style lang="less">
.kb-auth-drawer-root {
  position: absolute;

  // 确保drawer在小屏幕下的兼容性
  .ant-drawer-content-wrapper {
    max-height: 100vh;
  }

  .ant-drawer-content {
    display: flex;
    flex-direction: column;
    max-height: 100vh;
  }

  .ant-drawer-body {
    flex: 1;
    overflow: hidden;
    padding: 16px;
  }

  .ant-drawer-footer {
    flex-shrink: 0;
    padding: 16px;
  }

  // 小屏幕适配
  @media (max-height: 600px) {
    .ant-drawer-body {
      padding: 12px 16px;
    }

    .ant-drawer-footer {
      padding: 12px 16px;
    }
  }
}
</style>
