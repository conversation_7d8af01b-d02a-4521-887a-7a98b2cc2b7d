<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\MyKnowledge\index.vue
-->
<template>
  <div class="my-knowledge">
    <div class="my-knowledge__header">My Knowledge</div>
    <div class="my-knowledge__body">
      <div class="my-knowledge__buttons-wrapper">
        <ABtn class="my-knowledge__add-knowledge-button" type="primary" @click="handleAddKBClick">
          <SvgIcon class="my-knowledge__add-knowledge-button-icon" name="import-icon" size="11" />
          Add Knowledge Base
        </ABtn>
        <ABtn class="my-knowledge__add-group-button" type="bordered" @click="createNewGroup">
          <SvgIcon class="my-knowledge__add-group-button-icon" name="import-icon" size="11" />
          Add New Group
        </ABtn>
        <AInput
          class="my-knowledge__search-input"
          v-model:value="searchInputValue"
          placeholder="Search by name"
          @pressEnter="handleClickSearchButton"
        >
          <template #suffix>
            <SvgIcon
              class="my-knowledge__search-input-icon"
              name="search-icon"
              size="11"
              @click="handleClickSearchButton"
            />
          </template>
        </AInput>
        <ASelect
          class="my-knowledge__groups-select"
          placeholder="All Groups"
          :options="groupsOptions"
          :allow-clear="true"
          v-model:value="groupsSelectValue"
          :field-names="{ label: 'groupName', value: 'id' }"
          @change="handleChangeGroupsSelect"
        ></ASelect>
        <ABtn class="my-knowledge__synchronize-button" @click="handleClickSynchronizeButton">
          <SvgIcon
            class="my-knowledge__synchronize-button-icon"
            name="synchronize-icon"
            size="11"
          />
        </ABtn>
        <!-- <ABtn
          class="my-knowledge__delete-button"
          @click="handleClickBatchDeleteButton"
          v-if="selectedRowKeyArr.length"
        >
          <SvgIcon class="my-knowledge__delete-button-icon" name="delete-icon" size="14" />
        </ABtn> -->
      </div>
      <div class="my-knowledge__knowledge-table-wrapper">
        <div class="knowledge-table__header-wrapper">
          <ATable
            class="knowledge-table_header"
            size="small"
            :columns="columns"
            :pagination="false"
            @change="handleChangeKnowledgeTable"
          ></ATable>
        </div>
        <ATable
          class="knowledge-table"
          :show-header="false"
          size="small"
          :columns="columns"
          :data-source="dataSource"
          :pagination="false"
          :scroll="{ y: 'calc(100vh - 193px)' }"
          :row-class-name="
            (record: any, index: number) =>
              `knowledge-table__row ${selectedRowIndex === index ? 'selected-row' : ''}`
          "
          :default-expand-all-rows="true"
          v-model:expanded-row-keys="expandedRowKeys"
          :indent-size="0"
          row-key="displayId"
          children-column-name="kbList"
          :custom-row="customRow"
        >
          <template #expandIcon="{ expanded, onExpand, record }">
            <template v-if="record.displayType === 'GROUP'">
              <template v-if="expanded">
                <SvgIcon
                  class="knowledge-table__expand-icon"
                  name="arrow-down-icon"
                  size="14"
                  @click="
                    (e: MouseEvent) => {
                      onExpand(record, e)
                    }
                  "
                />
              </template>
              <template v-else>
                <SvgIcon
                  class="knowledge-table__expand-icon knowledge-table__expand-icon_collapse"
                  name="arrow-down-icon"
                  size="14"
                  @click="
                    (e: MouseEvent) => {
                      onExpand(record, e)
                    }
                  "
                />
              </template>
            </template>
            <template
              v-else-if="record.displayType === 'KB' && record.knowledgeBaseType === 'PERSON'"
            >
              <SvgIcon
                class="knowledge-table__expand-icon knowledge-table__expand-icon_private"
                name="PersonKnow"
                size="16"
              />
            </template>
            <template v-else>
              <SvgIcon style="margin-right: 11px" name="" size="14" />
            </template>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'displayName'">
              <template v-if="record.displayType === 'GROUP'">
                <span class="knowledge-table__group-name">
                  {{ record.displayName }}
                </span>
                <span class="knowledge-table__group-children-count">
                  {{ record.kbList ? record.kbList.length : 0 }}
                </span>
              </template>
              <template v-if="record.displayType === 'KB' && record.knowledgeBaseType === 'TEAM'">
                <span
                  class="knowledge-table__knowledge-name-wrapper"
                  @click="handleClickKnowledgeName(record)"
                >
                  <!-- <SvgIcon
                    class="knowledge-table__knowledge-icon"
                    name="knowledge-default-icon"
                    size="16"
                  /> -->
                  {{ record.displayName }}
                </span>
                <SvgIcon
                  class="knowledge-table__knowledge-storage-icon"
                  v-if="
                    !!(
                      knowledgeStore.knowledgeTaskMap[record.entityId] &&
                      getTaskCount(record.entityId)
                    )
                  "
                  name="knowledge-storage-icon"
                  :rotate="true"
                  size="16"
                />
              </template>
              <template v-if="record.displayType === 'KB' && record.knowledgeBaseType === 'PERSON'">
                <span
                  class="knowledge-table__knowledge-name-wrapper knowledge-table__knowledge-name-wrapper_private"
                  @click="handleClickKnowledgeName(record)"
                >
                  {{ record.displayName }}
                </span>
                <SvgIcon
                  class="knowledge-table__knowledge-storage-icon"
                  v-if="
                    !!(
                      knowledgeStore.knowledgeTaskMap[record.entityId] &&
                      getTaskCount(record.entityId)
                    )
                  "
                  name="knowledge-storage-icon"
                  :rotate="true"
                  size="16"
                />
              </template>
            </template>
            <!-- <template v-else-if="column.key === 'knowledgeBaseDesc'">
              <span class="knowledge-table__description" :title="record.knowledgeBaseDesc">
                {{ record.knowledgeBaseDesc }}
              </span>
            </template> -->
            <template v-else-if="column.key === 'knowledgeBaseDesc'">
              <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                <div class="ellipsis-cell__tooltip hide-tooltip">
                  <ATooltip color="#525A69">
                    <template #title>{{ record.knowledgeBaseDesc }}</template>
                    <span style="color: #000">{{ record.knowledgeBaseDesc }}</span>
                  </ATooltip>
                </div>
                <div class="ellipsis-cell__text">
                  <span style="color: #000">{{ record.knowledgeBaseDesc }}</span>
                </div>
              </div>
            </template>
            <!-- <template v-else-if="column.key === 'createUserName'">
              <span class="knowledge-table__create-user" :title="record.createUser?.userName">
                {{ record.createUser?.userName }}
              </span>
            </template> -->
            <template v-else-if="column.key === 'createUserName'">
              <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                <div class="ellipsis-cell__tooltip hide-tooltip">
                  <ATooltip color="#525A69">
                    <template #title>{{
                      record.createUser?.nickName || record.createUser?.userName
                    }}</template>
                    <span style="color: #000">{{
                      record.createUser?.nickName || record.createUser?.userName
                    }}</span>
                  </ATooltip>
                </div>
                <div class="ellipsis-cell__text">
                  <span style="color: #000">{{
                    record.createUser?.nickName || record.createUser?.userName
                  }}</span>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'documentCount'">
              <span class="knowledge-table__document-ccount">
                {{ record.documentCount }}
              </span>
            </template>
            <template v-if="column.key === 'relatedAgentListLength'">
              <ATooltip color="#525A69" placement="bottom">
                <template #title>{{ record.relatedAgentList }}</template>
                <span class="knowledge-table_related-agent-tag">{{
                  record.relatedAgentList?.length
                }}</span>
              </ATooltip>
            </template>
            <template v-else-if="column.key === 'updateTime'">
              <span v-if="record.updateTime" class="knowledge-table__update-time">
                {{ dayjs(record.updateTime).format('MMM DD, YYYY HH:mm:ss') }}
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <template v-if="record.displayType === 'KB' && record.knowledgeBaseType === 'TEAM'">
                <span
                  v-if="record.permission === 3 || record.permission === 4"
                  v-show="currentRowIndex === index"
                  style="vertical-align: sub"
                >
                  <SvgIcon
                    class="knowledge-table__row-button-icon knowledge-table__row-button-icon_authorisation"
                    name="authorisation-icon"
                    size="16"
                    @click="handleKBAuthClick(record)"
                  />
                  <SvgIcon
                    class="knowledge-table__row-button-icon knowledge-table__row-button-icon_delete"
                    name="delete-icon"
                    size="16"
                    @click="handleClickDeleteButton(record)"
                  />
                  <SvgIcon
                    class="knowledge-table__row-button-icon"
                    name="edit-icon"
                    size="16"
                    @click="handleKBClick(record)"
                  />
                </span>
                <span
                  v-if="record.permission === 3 || record.permission === 4"
                  v-show="currentRowIndex !== index"
                  style="vertical-align: sub"
                >
                  <SvgIcon class="knowledge-table__row-button-icon" name="more-icon" size="16" />
                </span>
              </template>
              <template
                v-if="record.displayType === 'GROUP' && record.groupId && record.groupId !== '0'"
              >
                <span v-show="currentRowIndex === index" style="vertical-align: sub">
                  <SvgIcon
                    class="knowledge-table__row-button-icon knowledge-table__row-button-icon_delete"
                    name="delete-icon"
                    size="16"
                    @click="handleClickDeleteGroupButton(record)"
                  />
                  <SvgIcon
                    class="knowledge-table__row-button-icon"
                    name="edit-icon"
                    size="16"
                    @click="handleClickEditGroupButton(record)"
                  />
                </span>
                <span v-show="currentRowIndex !== index" style="vertical-align: sub">
                  <SvgIcon class="knowledge-table__row-button-icon" name="more-icon" size="16" />
                </span>
              </template>
            </template>
          </template>
        </ATable>
      </div>
    </div>
    <DeleteModal
      v-model="isOpenDeleteKnowledgeModal"
      :isDeleteButtonLoading="isKnowledgeDeleteButtonLoading"
      @handleConfirmDelete="handleConfirmDeleteKnowledge"
      @handleCancelDelete="handleCancelDeleteKnowledge"
    >
      <div>
        Are you sure to delete {{ currentDataSourceItem?.knowledgeBaseName }} ?
        <br />
        You will delete all the files in the knowledge base.
      </div>
    </DeleteModal>
    <DeleteModal
      v-model="isOpenBatchDeleteModal"
      :isDeleteButtonLoading="isKnowledgeDeleteButtonLoading"
      @handleConfirmDelete="handleConfirmBatchDelete"
      @handleCancelDelete="handleCancelDelete"
    >
      <div>
        Are you sure to delete {{ selectedRowArr.length }} Knowledge Base from the list?
        <br />
        You will delete all the files in those knowledge bases.
      </div>
    </DeleteModal>
    <DeleteModal
      v-model="isOpenDeleteGroupModal"
      :isDeleteButtonLoading="isKnowledgeDeleteButtonLoading"
      @handleConfirmDelete="handleConfirmDeleteGroup"
      @handleCancelDelete="handleCancelDelete"
    >
      <div>
        Are you sure to delete Group {{ currentDataSourceItem?.groupName }}?
        <br />
        After deletion, all the Knowledge Bases under this group will be moved to the "Unorganized"
        category.
      </div>
    </DeleteModal>
    <KBAuthDrawer
      :key="drawerKey"
      v-model="isOpenKBAuthDrawer"
      :group-options="groupsOptions"
      :initial-data="currentDataSourceItem"
      title="Knowledge Base authorization"
      @handle-confirm-add-knowledge-base="handleConfirmKBAuth"
      @handle-close-k-b-auth="handleCloseKBAuth"
    />
    <AddOrEditKBDrawer
      v-model="isOpenAddKnowledgeBaseDrawer"
      :group-options="groupsOptions"
      :initial-data="currentDataSourceItem"
      :title="isEditMode ? 'Edit Knowledge Base information' : 'Add Knowledge Base'"
      @handle-confirm-add-knowledge-base="handleConfirmAddKnowledgeBase"
      @handle-close-add-knowledge-base="handleCloseAddKnowledgeBase"
      :is-confirm-loading="isAddOrEditGroupLoading"
    />
    <AddOrEditGroupModal
      v-model="createGroupModalVisible"
      :is-edit="isEditGroupMode"
      :initial-value="groupNameToEdit"
      @confirm="handleGroupConfirm"
      @after-close="handleCloseAddOrEditGroupModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
import { ABtn, AInput, ASelect, ATable, APagination, ATooltip } from '@libs/a-comps'
import type { TableProps } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import {
  getKnowledgeList,
  getGroupList,
  deleteKnowledge,
  createGroup,
  updateGroup,
  createKB,
  updateKB,
  deleteGroup
} from '@/renderer/src/api/myKnowledge'
import { KnowledgeRecord, AuthorizedData, GroupItem } from '@/types'
import { FileStatus, IFileInfo } from '@/main/Service/fileSync/constants/types'
import { knowledgeStore } from '@/renderer/src/views/KnowledgeBase/store'
import DeleteModal from './DeleteModal.vue'
import AddOrEditKBDrawer from './AddOrEditKBDrawer.vue'
import AddOrEditGroupModal from './AddOrEditGroupModal.vue'
import useGetCutTaskList from './useGetCutTaskList'
import KBAuthDrawer from './KBAuthDrawer.vue'
import { mouseEnterTooltip } from '@/renderer/src/hooks/ellipsisTooltip'
// import EditUserDrawer from './EditUserDrawer.vue'
import { emitter } from '@/utils/EventBus'

const router = useRouter()
const route = useRoute()

interface AddKnowledgeBaseFormState {
  kbName: string
  groupId: string | null
  description: string
}

interface FormState {
  userName: string
  nickName: string
  role: string
}

interface DataItem {
  name: string
  lastModified: number
  modified: string
  isDirectory: boolean
  status: string
  path: string
}

// 当前行索引
const currentRowIndex = ref<number | null>(null)
const selectedRowIndex = ref<number | null>(null)
const selectedRowKeyArr = ref<string[]>([])
const selectedRowArr = ref<KnowledgeRecord[]>([])

// 角色选项
const groupsOptions = ref<GroupItem[]>([])
const groupsSelectValue = ref()

const searchInputValue = ref('')

const sortedInfo = ref()
const columns = computed(() => {
  const sorted = sortedInfo.value || {}

  return [
    {
      key: 'displayName',
      title: 'Name',
      dataIndex: 'displayName',
      // width: '21%',
      // ellipsis: true,
      customCell: (record: KnowledgeRecord, index: number) => {
        return {
          colSpan: record.displayType === 'GROUP' ? 2 : 1
        }
      }
    },
    {
      key: 'knowledgeBaseDesc',
      title: 'Description',
      dataIndex: 'knowledgeBaseDesc',
      width: '16%',
      ellipsis: true,
      // sorter: (a: DataItem, b: DataItem) => {
      //   return a.name.localeCompare(b.name)
      // }
      customCell: (record: KnowledgeRecord, index: number) => {
        return {
          colSpan: record.displayType === 'GROUP' ? 0 : 1
        }
      }
    },
    {
      key: 'createUserName',
      title: 'Owner',
      dataIndex: ['createUser', 'userName'],
      width: '12%',
      ellipsis: true
    },
    {
      key: 'documentCount',
      title: 'Files Num',
      dataIndex: 'documentCount',
      width: '10%',
      align: 'center'
    },
    // {
    //   key: 'relatedAgentListLength',
    //   title: 'Related agent',
    //   dataIndex: ['relatedAgentList', 'length'],
    //   width: '13%',
    //   align: 'center'
    // },
    {
      key: 'updateTime',
      title: 'Last Modified',
      dataIndex: 'updateTime',
      width: '18%',
      ellipsis: true,
      showSorterTooltip: false,
      sorter: (a: KnowledgeRecord, b: KnowledgeRecord) => {
        // 比较时间字符串
        return new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime()
      },
      sortOrder: sorted.columnKey === 'updateTime' && sorted.order
    },
    {
      key: 'action',
      title: 'Action',
      dataIndex: 'action',
      width: '13%',
      align: 'center'
    }
  ]
})

const handleChangeKnowledgeTable: TableProps['onChange'] = (pagination, filters, sorter) => {
  sortedInfo.value = sorter
}

const customRow = (record: KnowledgeRecord, index: number) => {
  return {
    onClick: () => {
      selectedRowIndex.value = index
    },
    onMouseenter: (event: MouseEvent) => {
      currentRowIndex.value = index
    },
    onMouseleave: () => {
      currentRowIndex.value = null
    }
  }
}

const getCheckboxProps = (record: KnowledgeRecord) => {
  const { displayType, knowledgeBaseType, permission } = record
  return {
    class:
      displayType === 'KB' && knowledgeBaseType === 'TEAM' && [3, 4].includes(permission)
        ? 'knowledge-table__checkbox'
        : 'knowledge-table__checkbox_group'
  }
}

const dataSource = ref<KnowledgeRecord[]>([])
const expandedRowKeys = ref<string[]>([])
const currentDataSourceItem = ref<KnowledgeRecord>()

const isKnowledgeDeleteButtonLoading = ref<boolean>(false)
const isOpenDeleteKnowledgeModal = ref<boolean>(false)
const isOpenBatchDeleteModal = ref<boolean>(false)
const isOpenDeleteGroupModal = ref<boolean>(false)

const isOpenInviteUserDrawer = ref<boolean>(false)
const isOpenEditUserDrawer = ref<boolean>(false)
const isEditUser = ref<boolean>(false)
const isEditMode = ref<boolean>(false)
const isEditGroupMode = ref<boolean>(false)

const drawerKey = ref(0)

const authorizedList = ref<AuthorizedData>([])
const knowledgeAuthorizedList = ref<AuthorizedData>([])
const agentAuthorizedList = ref<AuthorizedData>([])

const isOpenAddKnowledgeBaseDrawer = ref<boolean>(false)
const isAddOrEditGroupLoading = ref<boolean>(false)

const createGroupModalVisible = ref<boolean>(false)
const groupNameToEdit = ref('')
const currentEditedGroup = ref<KnowledgeRecord | null>(null)

const isOpenKBAuthDrawer = ref<boolean>(false)
const isScrolling = ref(false)
let scrollTimeout: ReturnType<typeof setTimeout> | null = null

watch(isOpenAddKnowledgeBaseDrawer, (isOpening) => {
  if (!isOpening) {
    currentDataSourceItem.value = undefined
    isEditMode.value = false
  }
})

/**
 * 处理改变选中行
 * @param selectedRowKeys 选中行的key数组
 * @returns void
 */
const handleChangSelectedRowKeys = (selectedRowKeys: string[], selectedRows: KnowledgeRecord[]) => {
  console.log('selectedRows', selectedRows)

  selectedRowKeyArr.value = selectedRowKeys
  selectedRowArr.value = selectedRows
}

const rowSelection = {
  hideSelectAll: true,
  checkStrictly: true,
  selectedRowKeys: selectedRowKeyArr,
  getCheckboxProps: getCheckboxProps,
  onChange: handleChangSelectedRowKeys
}

/**
 * @description 获取分组选项
 * @param {*} void
 * @return {*} void
 */
const getGroupOptions = () => {
  getGroupList().then((result) => {
    const { success, data } = result.data

    if (success && data) {
      groupsOptions.value = data
    }
  })
}

/**
 * 获取标准表格数据源据源
 * @returns void
 */
const getKnowledgeTableDataSource = () => {
  getKnowledgeList({
    knowledgeBaseName: searchInputValue.value,
    groupId: groupsSelectValue.value || ''
  }).then((result) => {
    const { success, data } = result.data

    if (success && data) {
      const { privateKb, teamKbList } = data

      const privateKbArr = privateKb ? [privateKb] : []
      const shareKbArr = teamKbList ?? []
      dataSource.value = [...privateKbArr, ...shareKbArr]

      if (teamKbList) {
        expandedRowKeys.value = teamKbList.map((teamKbItem) => teamKbItem.displayId)
      }

      // 创建知识库任务映射
      knowledgeStore.createKnowledgeTaskMapByGroupKnowledgeArr(dataSource.value)

      dataSource.value.forEach((dataSourceItem) => {
        const { displayType, kbList, entityId, knowledgeBaseId } = dataSourceItem

        if (displayType === 'KB') {
          useGetCutTaskList({
            knowledgeId: entityId,
            knowledgeBaseId
          })
        } else if (displayType === 'GROUP') {
          if (kbList) {
            kbList.forEach((kbItem) => {
              useGetCutTaskList({
                knowledgeId: kbItem.entityId,
                knowledgeBaseId: kbItem.knowledgeBaseId
              })
            })
          }
        }
      })
    }
  })
}

/**
 * 处理点击搜索按钮
 * @returns
 */
const handleClickSearchButton = () => {
  getKnowledgeTableDataSource()
}

/**
 * 处理角色选择框改变事件
 * @param value 选中的值
 * @returns
 */
const handleChangeGroupsSelect = (value: string) => {
  getKnowledgeTableDataSource()
}

/**
 * 处理点击同步按钮
 * @returns
 */
const handleClickSynchronizeButton = () => {
  getKnowledgeTableDataSource()
}

/**
 * 处理点击批量删除按钮
 * @returns
 */
const handleClickBatchDeleteButton = () => {
  if (selectedRowArr.value.length) {
    isOpenBatchDeleteModal.value = true
  }
}

/**
 * @description 处理确认批量删除
 * @param {*} void
 * @returns
 */
const handleConfirmBatchDelete = () => {
  if (selectedRowArr.value.length) {
    isKnowledgeDeleteButtonLoading.value = true
    deleteKnowledge({
      knowledgeBaseIds: selectedRowArr.value.map(
        (selectedRowItem) => selectedRowItem.knowledgeBaseId
      )
    })
      .then((result) => {
        const { success } = result.data
        if (success) {
          isOpenBatchDeleteModal.value = false

          selectedRowKeyArr.value = []
          selectedRowArr.value = []
          selectedRowIndex.value = null
          getKnowledgeTableDataSource()
        }
      })
      .finally(() => {
        isKnowledgeDeleteButtonLoading.value = false
      })
  }
}

/**
 * @description 打开编辑用户抽屉
 * @param record
 * @return {*} void
 */
const openEditUserDrawer = (record: KnowledgeRecord) => {
  isOpenEditUserDrawer.value = true
  currentDataSourceItem.value = record

  authorizedList.value = []
  knowledgeAuthorizedList.value = []
  agentAuthorizedList.value = []

  // getAuthorizedList({
  //   toUserId: record.userId
  // }).then((result) => {
  //   const { success, data } = result.data

  //   if (success && data) {
  //     authorizedList.value = data

  //     knowledgeAuthorizedList.value = data.filter((item) => item.resourceType === 2)
  //     agentAuthorizedList.value = data.filter((item) => item.resourceType === 1)
  //   }
  // })
}

/**
 * 编辑知识库信息
 * @param record
 */
const handleKBClick = (record: KnowledgeRecord) => {
  getGroupOptions()
  isEditMode.value = true
  currentDataSourceItem.value = record
  // drawerKey.value = Date.now()
  isOpenAddKnowledgeBaseDrawer.value = true
}

/**
 * 点击知识库授权
 * @param record
 */
const handleKBAuthClick = (record: KnowledgeRecord) => {
  isOpenKBAuthDrawer.value = true
  currentDataSourceItem.value = record
}

/**
 * 处理点击删除按钮
 * @param record
 */
const handleClickDeleteButton = (record: KnowledgeRecord) => {
  isOpenDeleteKnowledgeModal.value = true
  currentDataSourceItem.value = record
}

/**
 * 处理确认删除弹窗
 * @param
 */
const handleConfirmDeleteKnowledge = () => {
  if (currentDataSourceItem.value) {
    isKnowledgeDeleteButtonLoading.value = true

    deleteKnowledge({
      knowledgeBaseIds: [currentDataSourceItem.value.knowledgeBaseId]
    })
      .then((result: any) => {
        const { success } = result.data
        if (success) {
          isOpenDeleteKnowledgeModal.value = false
          selectedRowIndex.value = null
          getKnowledgeTableDataSource()
        }
      })
      .finally(() => {
        isKnowledgeDeleteButtonLoading.value = false
      })
  }
}

const handleGroupConfirm = (groupName: string) => {
  const handleSuccess = (result: any) => {
    const { success } = result.data
    if (success) {
      message.success(`${isEditGroupMode.value ? 'Update' : 'Add'} successfully.`)
      getGroupOptions()
      getKnowledgeTableDataSource()
    } else {
      handleError()
    }
  }

  const handleError = () => {
    message.error(`${isEditGroupMode.value ? 'Update' : 'Add'} failed. Please try again later.`)
  }

  if (isEditGroupMode.value && currentEditedGroup.value) {
    updateGroup({ groupId: currentEditedGroup.value.groupId, groupName })
      .then(handleSuccess)
      .catch(handleError)
  } else {
    createGroup({ groupName }).then(handleSuccess).catch(handleError)
  }
}

const createNewGroup = () => {
  isEditGroupMode.value = false
  groupNameToEdit.value = ''
  currentEditedGroup.value = null
  createGroupModalVisible.value = true
}

/**
 * @description: 处理点击添加知识库
 * @param {*}
 * @return {*}
 */
const handleAddKBClick = () => {
  getGroupOptions()
  isEditMode.value = false
  currentDataSourceItem.value = undefined
  drawerKey.value = Date.now()
  isOpenAddKnowledgeBaseDrawer.value = true
}

/**
 * @description: 处理点击删除分组按钮
 * @param {KnowledgeRecord} record
 * @return {*}
 */
const handleClickDeleteGroupButton = (record: KnowledgeRecord) => {
  isOpenDeleteGroupModal.value = true
  currentDataSourceItem.value = record
}

/**
 * @description: 处理确认删除分组
 * @param {*}
 * @return {*}
 */
const handleConfirmDeleteGroup = () => {
  if (currentDataSourceItem.value) {
    isKnowledgeDeleteButtonLoading.value = true

    deleteGroup({
      groupId: currentDataSourceItem.value.groupId || ''
    })
      .then((result) => {
        const { success } = result.data
        if (success) {
          isOpenDeleteGroupModal.value = false
          selectedRowIndex.value = null
          getKnowledgeTableDataSource()

          // 更新分组选项
          getGroupOptions()
        }
      })
      .finally(() => {
        isKnowledgeDeleteButtonLoading.value = false
      })
  }
}

/**
 * @description: 处理点击编辑分组按钮
 * @param {KnowledgeRecord} record
 * @return {*}
 */
const handleClickEditGroupButton = (record: KnowledgeRecord) => {
  isEditGroupMode.value = true
  createGroupModalVisible.value = true
  groupNameToEdit.value = record.groupName || ''
  currentEditedGroup.value = record
}

/**
 * @description: 处理点击授权应用单元格
 * @param {*}
 * @return {*}
 */
const handleClickAuthorizedCell = (record: KnowledgeRecord) => {
  isEditUser.value = false
  openEditUserDrawer(record)
}

const handleConfirmKBAuth = () => {
  getKnowledgeTableDataSource()
}

const handleConfirmAddKnowledgeBase = (submitData: AddKnowledgeBaseFormState) => {
  if (isAddOrEditGroupLoading.value) return

  const handleSuccess = (result: any) => {
    const { success } = result.data
    if (success) {
      if (isEditMode.value) {
        message.success('Update successfully.')
      } else {
        message.success('Add successfully.')
      }
      getKnowledgeTableDataSource()
      isOpenAddKnowledgeBaseDrawer.value = false
    } else {
      handleError()
    }
  }

  const handleError = () => {
    if (isEditMode.value) {
      message.error('Update failed. Please try again later.')
    } else {
      message.error('Add failed. Please try again later.')
    }
  }

  if (!submitData.kbName) {
    return
  }

  isAddOrEditGroupLoading.value = true

  if (isEditMode.value && currentDataSourceItem.value) {
    updateKB({
      knowledgeBaseName: submitData.kbName,
      groupId: submitData.groupId ? parseInt(submitData.groupId, 10) : null,
      knowledgeBaseDesc: submitData.description,
      knowledgeBaseId: currentDataSourceItem.value.knowledgeBaseId
    })
      .then(handleSuccess)
      .catch(handleError)
      .finally(() => {
        isAddOrEditGroupLoading.value = false
      })
  } else {
    createKB({
      knowledgeBaseName: submitData.kbName,
      groupId: submitData.groupId ? parseInt(submitData.groupId, 10) : null,
      knowledgeBaseDesc: submitData.description
    })
      .then(handleSuccess)
      .catch(handleError)
      .finally(() => {
        isAddOrEditGroupLoading.value = false
      })
  }
}

const handleCloseAddKnowledgeBase = () => {
  // 关闭抽屉 清空选择背景色
  selectedRowIndex.value = null
}

const handleCloseKBAuth = () => {
  // 关闭KB授权抽屉 清空选择背景色
  selectedRowIndex.value = null
}

const handleCancelDelete = () => {
  // 取消删除 清空选择背景色
  selectedRowIndex.value = null
}

const handleCloseAddOrEditGroupModal = () => {
  // 关闭编辑分组弹窗 清空选择背景色
  selectedRowIndex.value = null
}

const handleCancelDeleteKnowledge = () => {
  selectedRowIndex.value = null
}

/**
 * @description: 点击知识库名称
 * @param {KnowledgeRecord} record
 */
const handleClickKnowledgeName = (record: KnowledgeRecord) => {
  if (record.knowledgeBaseId) {
    const { entityId, knowledgeBaseId, knowledgeBaseName, knowledgeBaseType, permission } = record

    router.push({
      // path: `/knowledge/knowledge-base/${record.knowledgeBaseId}`
      name: 'knowledgeBase',
      query: {
        entityId,
        knowledgeBaseId,
        knowledgeBaseName,
        knowledgeBaseType,
        permission
      }
    })
  }
}

/**
 * @description 获取总任务数量
 * @param entityId
 */
const getTaskCount = (entityId: string) => {
  const { importingFileList, cutingTaskArr } = knowledgeStore.knowledgeTaskMap[entityId]

  const importingTaskArr = importingFileList.filter((importingFileItem) => {
    return importingFileItem.status !== FileStatus.FAILED
  })

  return importingTaskArr.length + cutingTaskArr.length
}

const setupScrollDetection = () => {
  const knowledgeTableBody = document.querySelector(
    '.knowledge-table .ant-table-body'
  ) as HTMLElement
  if (knowledgeTableBody) {
    knowledgeTableBody.addEventListener('scroll', handleScroll)
  }
}

const handleScroll = () => {
  isScrolling.value = true
  const knowledgeTableBody = document.querySelector(
    '.knowledge-table .ant-table-body'
  ) as HTMLElement
  if (knowledgeTableBody) {
    knowledgeTableBody.classList.add('scrolling')
  }

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
    if (knowledgeTableBody) {
      knowledgeTableBody.classList.remove('scrolling')
    }
  }, 800)
}

// 页面数据加载函数
const loadPageData = () => {
  getKnowledgeTableDataSource()
  getGroupOptions()
}

onMounted(() => {
  // 初始化页面数据
  loadPageData()
  setupScrollDetection()

  // 获取待处理文件
  window.api.getPendingFiles().then((pendingFiles: IFileInfo[]) => {
    // 创建知识库任务映射
    knowledgeStore.createKnowledgeTaskMap(pendingFiles)

    knowledgeStore.updateKnowledgeImportingFileListByPendingFiles({
      pendingFiles
    })
  })

  // 取消监听文件上传相关事件
  window.api.removeAllListeners()

  // 监听选择文件后的上传列表变化
  window.api.onFileListUpdated(
    (currentKBFilesInfo: { source: string; files: IFileInfo[]; pendingFiles: IFileInfo[] }) => {
      const { source, files, pendingFiles } = currentKBFilesInfo

      try {
        // 创建知识库任务映射
        knowledgeStore.createKnowledgeTaskMap(pendingFiles)

        // 更新知识库导入文件列表
        knowledgeStore.updateKnowledgeImportingFileList({
          knowledgeId: source,
          importingFileList: files
        })
      } catch (error) {
        console.error('Failed to handle file list update:', error)
      }
    }
  )

  //UPLOAD success
  window.api.onOneUploadCompleted(
    (uploadSuccessInfo: {
      source: string
      sourceFiles: IFileInfo[]
      pendingFiles: IFileInfo[]
      resourceId: string
      resourceType: string
    }) => {
      const { source, sourceFiles, pendingFiles, resourceId, resourceType } = uploadSuccessInfo

      try {
        knowledgeStore.updateKnowledgeImportingFileList({
          knowledgeId: source,
          importingFileList: sourceFiles
        })

        // 获取切片任务列表
        useGetCutTaskList({
          knowledgeId: source,
          knowledgeBaseId: resourceId,
          callback() {
            // 如果已经没有定时任务则开始
            if (!knowledgeStore.knowledgeTaskMap[source].cutingTaskTimer) {
              useGetCutTaskList({
                knowledgeId: source,
                knowledgeBaseId: resourceId
              })
            }
          }
        })
      } catch (error) {
        console.error('Failed to handle OneUploadCompleted:', error)
      }
    }
  )
})

// 监听路由变化，当进入myKnowledge页面时刷新数据
watch(
  () => route.name,
  (newRouteName) => {
    if (newRouteName === 'myKnowledge') {
      loadPageData()
    }
  }
)

onUnmounted(() => {
  // 取消监听文件上传相关事件
  window.api.removeAllListeners()

  // 移除知识库列表刷新事件监听
  emitter.removeEvent('refresh-knowledge-list')

  // 清除定时器
  for (const knowledgeId in knowledgeStore.knowledgeTaskMap) {
    const { cutingTaskTimer } = knowledgeStore.knowledgeTaskMap[knowledgeId]
    if (cutingTaskTimer) {
      clearTimeout(cutingTaskTimer)
      knowledgeStore.updateKnowledgeCutingTaskTimer({
        knowledgeId,
        cutingTaskTimer: null
      })
    }
  }

  const knowledgeTableBody = document.querySelector(
    '.knowledge-table .ant-table-body'
  ) as HTMLElement
  if (knowledgeTableBody) {
    knowledgeTableBody.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})
</script>

<style lang="less" scoped>
.my-knowledge {
  padding: 0 17px 0 25px;

  .my-knowledge__header {
    position: relative;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
  }

  .my-knowledge__import-status-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    font-weight: 400;
    font-size: 14px;
    color: #6441ab;
  }

  .my-knowledge__import-number {
    display: inline-block;
    margin-right: 16px;
  }

  .my-knowledge__import-status-button {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-color: #f2f3f5;
    color: #6441ab;
  }

  .my-knowledge__buttons-wrapper {
    margin-bottom: 10px;
  }

  .my-knowledge__add-knowledge-button,
  .my-knowledge__add-group-button {
    height: initial;
    margin-right: 12px;
    padding: 4px 15px;
    vertical-align: middle;
    border-color: #6441ab;
    &:hover {
      border-color: #4f3387;
    }
    &:active {
      border-color: #3c2766;
    }
  }

  .my-knowledge__add-knowledge-button-icon {
    margin-right: 5.5px;
    vertical-align: initial;
    color: #fff;
  }

  .my-knowledge__add-group-button-icon {
    margin-right: 5.5px;
    vertical-align: initial;
  }

  .my-knowledge__search-input {
    width: 168px;
    height: 32px;
    margin-right: 12px;
    // border-color: #f2f3f5;
    background: #f2f3f5;
    vertical-align: middle;

    ::v-deep(.ant-input) {
      background-color: #f2f3f5;

      &::placeholder {
        color: var(--text-color4);
      }
    }
  }

  .my-knowledge__search-input-icon {
    color: #3b3b3b;
  }

  .my-knowledge__delete-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    padding: 0;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
    &:hover {
      background-color: #e5e7ec;
      border-radius: 2px;
    }

    &:active {
      background-color: #c9cdd4;
      border-radius: 2px;
    }
  }

  .my-knowledge__delete-button-icon {
    vertical-align: baseline;
    color: #3b3b3b;
  }

  .my-knowledge__groups-select {
    width: 168px;
    height: 32px;
    margin-right: 12px;
    vertical-align: middle;

    ::v-deep(.ant-select-selector) {
      border-color: #f2f3f5;
    }
  }

  .my-knowledge__synchronize-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    margin-right: 12px;
    padding: 0;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  .my-knowledge__synchronize-button-icon {
    vertical-align: middle;
    color: #3b3b3b;
  }

  .my-knowledge__knowledge-table-wrapper {
    height: calc(100vh - 204px);
    margin-bottom: 14px;
  }

  .knowledge-table__header-wrapper {
    padding-right: 6px;
  }

  .knowledge-table_header {
    ::v-deep(.ant-table-thead > tr > th) {
      font-weight: initial;
      color: #696969;
    }

    ::v-deep(.ant-table-tbody) {
      display: none;
    }
  }

  .knowledge-table {
    ::v-deep(.ant-table) {
      .selected-row {
        background-color: #f5f2fe;

        td {
          background-color: #f5f2fe !important;
        }

        &:hover > td {
          background-color: #f5f2fe !important;
        }
      }
    }

    ::v-deep(.ant-table-body) {
      overflow-y: auto;
      max-height: calc(100vh - 284px);

      &::-webkit-scrollbar {
        width: 6px;
        background: transparent;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 3px;
        transition: background 0.2s ease;
      }

      /* 滚动时显示滚动条 */
      &.scrolling::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);

        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
      }
    }

    ::v-deep(.ant-table-thead > tr > th) {
      font-weight: initial;
      color: #696969;
    }
  }

  ::v-deep(.knowledge-table__row) {
    .ant-checkbox-wrapper {
      display: none;
    }
    &.ant-table-row-level-1 {
      background-color: #f7f8fa;
    }

    // .ant-checkbox-wrapper-checked {
    //   display: inline-flex;
    // }

    // &:hover {
    //   .knowledge-table__checkbox {
    //     display: inline-flex;
    //   }
    // }
  }

  .knowledge-table__expand-icon {
    margin-right: 11px;
    vertical-align: middle;
    cursor: pointer;
  }

  .knowledge-table__expand-icon_collapse {
    transform: rotate(270deg);
  }

  .knowledge-table__group-name {
    vertical-align: middle;
    color: #000;
    font-weight: 600;
  }

  .knowledge-table__group-children-count {
    // display: inline-block;
    margin-left: 4px;
    padding: 0 5px;
    vertical-align: middle;
    background-color: #f2f3f5;
    color: #858585;
  }

  .knowledge-table__knowledge-name-wrapper {
    display: inline-block;
    max-width: calc(100% - 50px);
    margin-right: 8px;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #696969;
    cursor: pointer;
    // font-weight: 600;
  }

  .knowledge-table__knowledge-name-wrapper_private {
    color: #000;
    font-weight: 600;
  }

  .knowledge-table__knowledge-icon {
    margin-right: 8px;
    vertical-align: text-bottom;
  }

  .knowledge-table__knowledge-storage-icon {
    vertical-align: middle;
  }

  .knowledge-table__description,
  .knowledge-table__update-time {
    color: #696969;
  }

  .knowledge-table__create-user,
  .knowledge-table__document-ccount {
    color: #000;
  }

  .knowledge-table_related-agent-tag {
  }

  .knowledge-table__row-button-icon {
    color: #3b3b3b;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;

    &:not(:last-child) {
      margin-right: 14px;
    }

    &:hover {
      background-color: #e5e7ec;
    }

    &:active {
      background-color: #c9cdd4;
    }
  }
}

.my-knowledge__delete-modal {
  .delete-modal__title {
    padding: 4px 0;
    text-align: center;
  }

  .delete-modal__title-icon {
    margin-right: 8px;
    vertical-align: middle;
  }

  .delete-modal__title-text {
    vertical-align: middle;
  }

  .delete-modal__body {
    padding: 16px 0 24px;
    text-align: center;
    color: #000;
  }
}

.my-knowledge__create-group-modal {
  .create-group-modal__title {
    padding: 4px 0;
    text-align: center;
  }

  .create-group-modal__title-text {
    vertical-align: middle;
  }

  .create-group-modal__body {
    padding: 16px 24px 24px;
    text-align: left;
    color: #000;

    .create__group-input {
      width: 100%;
      height: 32px;
      margin-right: 12px;
      // border-color: #f2f3f5;
      background: #f2f3f5;
      vertical-align: middle;

      ::v-deep(.ant-input) {
        background-color: #f2f3f5;

        &::placeholder {
          color: var(--text-color4);
        }
      }
    }

    .group-name-label {
      display: block;
      margin-bottom: 8px;
    }

    .group-name-label::after {
      content: '*';
      color: #f2463d;
      margin-left: 2px;
    }

    .group-name-error-message {
      margin-top: 8px;
      color: #f2463d;
    }
  }
}
.ellipsis-cell {
  width: 100%;
  > div {
    span {
      max-width: 172px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
  }

  :deep(.hide-tooltip) {
    display: none;
  }
}
</style>
