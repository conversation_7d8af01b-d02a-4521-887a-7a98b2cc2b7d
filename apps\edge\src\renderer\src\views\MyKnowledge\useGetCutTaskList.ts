/*
 * @Description:
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\MyKnowledge\useGetCutTaskList.ts
 */
import mySDK from '@renderer/views/Chat/sdkService'
import { getCutTaskListData } from '@/renderer/src/api/knowledgeBase'
import { CutingTask } from '@/types'
import { knowledgeStore } from '@/renderer/src/views/KnowledgeBase/store'
import { cutStatusMap, CutStatusEnum } from '@/renderer/src/views/KnowledgeBase/type'

/**
 * 获取切片任务列表
 * @param knowledgeId
 * @param callback
 * @returns
 */
const useGetCutTaskList = async (params: {
  knowledgeId: string
  knowledgeBaseId: string
  callback?: () => void
}) => {
  const { knowledgeId, knowledgeBaseId, callback } = params

  try {
    const res = await getCutTaskListData({
      knowledgeId,
      userId: mySDK.userId,
      knowledgeBaseId
    })
    const { code, data } = res.data

    if (code === 200 && data) {
      let cutingTaskArr: CutingTask[] = []
      cutingTaskArr = data.map((item: CutingTask) => {
        return {
          ...item,
          name: item.documentName,
          taskType: 'cut',
          cutStatus: cutStatusMap[item.status] || item.status,
          fileId: item.documentId
        }
      })

      if (callback) {
        callback()
      } else {
        const { cutingTaskTimer } = knowledgeStore.knowledgeTaskMap[knowledgeId] || {}
        if (cutingTaskTimer) {
          clearTimeout(cutingTaskTimer)
          knowledgeStore.updateKnowledgeCutingTaskTimer({
            knowledgeId,
            cutingTaskTimer: null
          })
        }

        // 过滤PROCESSING的切片任务
        const processingCutingTaskArr = cutingTaskArr.filter((cutingTaskItem) => {
          return cutingTaskItem.cutStatus === CutStatusEnum.PROCESSING
        })

        if (processingCutingTaskArr.length > 0) {
          const newCutingTaskTimer = setTimeout(() => {
            useGetCutTaskList({
              knowledgeId,
              knowledgeBaseId
            })
          }, 1000)

          knowledgeStore.updateKnowledgeCutingTaskTimer({
            knowledgeId,
            cutingTaskTimer: newCutingTaskTimer
          })
        }
      }

      // 更新知识库任务映射
      knowledgeStore.updateKnowledgeCutingTaskArr({
        knowledgeId,
        cutingTaskArr
      })
    }
  } catch (error) {
    console.error('root: Get cut task list failed:', error)

    // 出错后的重试逻辑 延长重试间隔
    const newCutingTaskTimer = setTimeout(() => {
      useGetCutTaskList({
        knowledgeId,
        knowledgeBaseId
      })
    }, 3000)

    knowledgeStore.updateKnowledgeCutingTaskTimer({
      knowledgeId,
      cutingTaskTimer: newCutingTaskTimer
    })
  }
}

export default useGetCutTaskList
