<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\StandardKnowledgeBase\index.vue
-->
<template>
  <div class="standard-knowledge-base">
    <div class="standard-knowledge-base__header">
      Knowledge Base
      <div class="standard-knowledge-base__import-status-wrapper">
        <span class="standard-knowledge-base__import-number"> 0 files </span>
        <div class="standard-knowledge-base__import-status-button">
          <SvgIcon
            class="standard-knowledge-base__import-status-button-icon"
            name="import-status-icon"
            size="11"
          />
        </div>
      </div>
    </div>
    <div class="standard-knowledge-base__body">
      <div class="standard-knowledge-base__buttons-wrapper">
        <ABtn class="standard-knowledge-base__import-button" type="primary">
          <SvgIcon
            class="standard-knowledge-base__import-button-icon"
            name="import-icon"
            size="11"
          />
          Import
        </ABtn>
        <AInput class="standard-knowledge-base__search-input" placeholder="Search by name">
          <template #suffix>
            <SvgIcon
              class="standard-knowledge-base__search-input-icon"
              name="import-icon"
              size="11"
            />
          </template>
        </AInput>
        <ABtn class="standard-knowledge-base__synchronize-button">
          <SvgIcon
            class="standard-knowledge-base__synchronize-button-icon"
            name="synchronize-icon"
            size="11"
          />
        </ABtn>
      </div>
      <div class="standard-knowledge-base__table-wrapper">
        <ATable
          class="standard-knowledge-base__table"
          :columns="columns"
          :data-source="dataSource"
          :pagination="false"
          size="small"
          :showSorterTooltip="false"
          :scroll="{ y: 430 }"
          :custom-row="customRowFunc"
          :row-class-name="() => 'standard-knowledge-base__table-row'"
          row-key="uuid"
          :row-selection="{
            selectedRowKeys: selectedRowKeyArr,
            onChange: handleChangSelectedRowKeys
          }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'name'">
              <div class="standard-knowledge-base__file-name-wrapper">
                <span
                  class="standard-knowledge-base__file-type-icon"
                  :style="{
                    backgroundImage: `url(${getFileSvgIcon(record.isDirectory ? 'folder' : record.name)})`
                  }"
                ></span
                >{{ record.name }}
              </div>
            </template>
            <template v-if="column.key === 'from'">
              <span v-show="currentRowIndex !== index">local</span>
              <span
                class="standard-knowledge-base__table-row-action-wrapper"
                v-show="currentRowIndex === index"
              >
                <ABtn type="link">delete</ABtn>
              </span>
            </template>
            <template v-if="column.key === 'modified'">
              {{ dayjs(record.lastModified).format('MMM DD, YYYY') }}
            </template>
          </template>
        </ATable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ABtn, AInput, ATable } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { getFileSvgIcon } from '@renderer/hooks/fileType'
import dayjs from 'dayjs'

interface DataItem {
  name: string
  lastModified: number
  modified: string
  isDirectory: boolean
  status: string
  path: string
}

// 当前行索引
const currentRowIndex = ref<number | null>(null)
const selectedRowKeyArr = ref<string[]>([])

const columns = [
  {
    key: 'name',
    title: 'Name',
    dataIndex: 'name',
    width: '60%',
    ellipsis: true,
    sorter: (a: DataItem, b: DataItem) => {
      return a.name.localeCompare(b.name)
    }
  },
  {
    key: 'from',
    title: 'From',
    dataIndex: 'from',
    width: '20%',
    ellipsis: true,
    customCell: (record: DataItem, index: number) => ({
      colSpan: currentRowIndex.value === index ? 2 : 1
    })
  },
  {
    key: 'modified',
    title: 'Modified',
    dataIndex: 'modified',
    width: '20%',
    ellipsis: true,
    customCell: (record: DataItem, index: number) => ({
      colSpan: currentRowIndex.value === index ? 0 : 1
    }),
    sorter: (a: DataItem, b: DataItem) => {
      return dayjs(a.lastModified).isBefore(dayjs(b.lastModified)) ? 1 : -1
    }
  }
]

const dataSource = ref<DataItem[]>([])

/**
 * 自定义行
 * @param record 行数据
 */
const customRowFunc = (record: DataItem, index: number) => {
  return {
    onMouseenter: (event: MouseEvent) => {
      currentRowIndex.value = index
    },
    onMouseleave: () => {
      currentRowIndex.value = null
    }
  }
}
/**
 * 处理改变选中行
 * @param selectedRowKeys 选中行的key数组
 * @returns void
 */
const handleChangSelectedRowKeys = (selectedRowKeys: string[]) => {
  selectedRowKeyArr.value = selectedRowKeys
}

/**
 * 获取标准表格数据源据源
 * @returns void
 */
const getStandardTableDataSource = () => {
  // getStandardFileData().then((res) => {

  //   if (res.data) {
  //     dataSource.value = res.data
  //   }
  // })

  dataSource.value = [
    {
      name: 'anowledge Base 1.xlsx',
      modified: '2021-01-02',
      lastModified: 823132132132,
      isDirectory: false,
      status: 'SUCCESSED',
      path: 'C:\\Users\\<USER>\\Downloads\\USER_Feedback_20250105_20250106.xlsx'
    },
    {
      name: 'znowledge Base 1.doc',
      modified: '2021-01-01',
      lastModified: 523132132131,
      isDirectory: false,
      status: 'SUCCESSED',
      path: 'C:\\Users\\<USER>\\Downloads\\USER_Feedback_20250105_20250106.xlsx'
    },
    {
      name: 'fnowledge Base 1.png',
      modified: '2021-01-04',
      lastModified: 923132132135,
      isDirectory: false,
      status: 'SUCCESSED',
      path: 'C:\\Users\\<USER>\\Downloads\\USER_Feedback_20250105_20250106.xlsx'
    }
  ]
}

onMounted(() => {
  getStandardTableDataSource()
})
</script>

<style lang="less" scoped>
.standard-knowledge-base {
  padding: 7px 16px;

  .standard-knowledge-base__header {
    position: relative;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
  }

  .standard-knowledge-base__import-status-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    font-weight: 400;
    font-size: 14px;
    color: #6441ab;
  }

  .standard-knowledge-base__import-number {
    display: inline-block;
    margin-right: 16px;
  }

  .standard-knowledge-base__import-status-button {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-color: #f2f3f5;
    color: #6441ab;
  }

  .standard-knowledge-base__buttons-wrapper {
    margin-bottom: 10px;
  }

  .standard-knowledge-base__import-button {
    height: initial;
    margin-right: 12px;
    padding: 4px 15px;
    vertical-align: middle;
  }

  .standard-knowledge-base__import-button-icon {
    margin-right: 5.5px;
    vertical-align: initial;
    color: #fff;
  }

  .standard-knowledge-base__search-input {
    width: 168px;
    height: 32px;
    margin-right: 12px;
    border-color: #f2f3f5;
    background: #f2f3f5;
    vertical-align: middle;

    ::v-deep(.ant-input) {
      background-color: #f2f3f5;

      &::placeholder {
        color: var(--text-color4);
      }
    }
  }

  .standard-knowledge-base__search-input-icon {
    color: #3b3b3b;
  }

  .standard-knowledge-base__synchronize-button {
    height: initial;
    padding: 4px 10.46px;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  .standard-knowledge-base__synchronize-button-icon {
    vertical-align: middle;
    color: #3b3b3b;
  }

  ::v-deep(.ant-table-thead) {
    tr > th {
      background-color: #fff;
    }
  }

  .standard-knowledge-base__file-name-wrapper {
    display: inline-block;
    max-width: 94%;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  @media print {
    .standard-knowledge-base__file-name-wrapper {
      overflow: visible;
    }
  }
  .standard-knowledge-base__file-type-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    background-repeat: no-repeat;
    background-size: initial;
    background-position: center center;
    vertical-align: bottom;
  }

  ::v-deep(.standard-knowledge-base__table-row) {
    .ant-btn-link {
      padding: 0;
    }

    .ant-checkbox-wrapper {
      display: none;
    }

    &:hover {
      .ant-checkbox-wrapper {
        display: inline-flex;
      }
    }
  }
}
</style>
