<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\UserManagement\EditUserDrawer.vue
-->
<template>
  <Drawer
    class="edit-user-drawer"
    :title="title"
    v-model:open="isOpenEditUserDrawer"
    root-class-name="edit-user-drawer-root"
    get-container="#app"
    :closable="true"
    :mask-closable="true"
    :body-style="{ padding: '16px 20px' }"
    :footer-style="{ textAlign: 'right' }"
    @after-visible-change="handleVisibleChange"
  >
    <div class="invite-user-drawer__body">
      <div class="invite-user-modal__form" v-if="isEditUser">
        <Form
          ref="formReference"
          :model="formState"
          autocomplete="off"
          v-bind="formItemLayout"
          label-align="left"
        >
          <FormItem
            label="Account"
            name="userName"
            :rules="[
              {
                required: true,
                message: 'Please input phone/mail!'
              }
            ]"
          >
            <AInput
              class="user-management__form-input"
              v-model:value="formState.userName"
              :disabled="true"
            />
          </FormItem>
          <FormItem label="Nickname" name="nickName">
            <AInput
              class="user-management__form-input"
              v-model:value="formState.nickName"
              placeholder="Please enter"
              maxLength="30"
            />
          </FormItem>
          <FormItem
            label="Role"
            name="role"
            :rules="[{ required: true, message: 'Please select role!' }]"
          >
            <ASelect
              class="user-management__roles-select"
              placeholder="Please select"
              :options="rolesOptions"
              v-model:value="formState.role"
              @change="handleChangeRoleSelect"
            ></ASelect>
          </FormItem>
        </Form>
      </div>
      <div class="user-drawer__authorized-wrapper-header" v-if="isEditUser">
        Authorized Application
      </div>
      <div class="user-drawer__authorized-wrapper">
        <AInput
          class="user-drawer__authorized-input"
          v-model:value="searchInputValue"
          placeholder="Search by name"
          allow-clear
          @change="handleChangeSearchInput"
        >
          <template #suffix>
            <SvgIcon class="user-management__search-input-icon" name="search-icon" size="11" />
          </template>
        </AInput>
        <div class="user-drawer__authorized" v-show="!searchInputValue">
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse':
                  authorizedListCollapseState.knowledge
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('knowledge')"
            />
            <span
              v-if="currentDataSourceItem.role === 'Super Administrator'"
              class="user-drawer__authorized-header-text"
              >Knowledge Base</span
            >
            <ACheckbox
              v-else
              class="user-drawer__authorized-header-checkbox"
              :checked="isKnowledgeCheckAll"
              :indeterminate="isKnowledgeIndeterminate"
              :disabled="!knowledgeAuthorizedList.length"
              @change="handleChangeKnowledgeCheckAll"
            >
              <span class="user-drawer__authorized-header-text">Knowledge Base</span>
            </ACheckbox>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.knowledge">
            <div
              class="user-drawer__authorized-item"
              v-for="(knowledgeAuthorizedItem, index) in knowledgeAuthorizedList"
              :key="knowledgeAuthorizedItem.resourceId"
            >
              <template v-if="currentDataSourceItem.role === 'Super Administrator'">
                <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                  <div class="ellipsis-cell__tooltip hide-tooltip">
                    <ATooltip color="#525A69">
                      <template #title>{{ knowledgeAuthorizedItem.resourceName }}</template>
                      <span>{{ knowledgeAuthorizedItem.resourceName }}</span>
                    </ATooltip>
                  </div>
                  <div class="ellipsis-cell__text">
                    <span>{{ knowledgeAuthorizedItem.resourceName }}</span>
                  </div>
                </div>
                <span
                  v-if="!!knowledgeAuthorizedItem.permission"
                  class="user-drawer__authorized-tag"
                  :class="`user-drawer__authorized-tag_${knowledgeAuthorizedItem.permission}`"
                >
                  {{ authorizedMap[knowledgeAuthorizedItem.permission] }}
                </span>
              </template>
              <templete v-else>
                <ACheckbox
                  class="user-drawer__authorized-checkbox"
                  :checked="!!knowledgeAuthorizedItem.permission"
                  @change="handleChangeKnowledgeCheckbox($event, index)"
                  :disabled="knowledgeAuthorizedItem.permission === 4"
                >
                  <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                    <div class="ellipsis-cell__tooltip hide-tooltip">
                      <ATooltip color="#525A69">
                        <template #title>{{ knowledgeAuthorizedItem.resourceName }}</template>
                        <span>{{ knowledgeAuthorizedItem.resourceName }}</span>
                      </ATooltip>
                    </div>
                    <div class="ellipsis-cell__text">
                      <span>{{ knowledgeAuthorizedItem.resourceName }}</span>
                    </div>
                  </div>
                </ACheckbox>
                <ASelect
                  v-if="knowledgeAuthorizedItem.permission === 4"
                  class="user-drawer__authorized-select"
                  v-model:value="knowledgeAuthorizedItem.permission"
                  :options="createrOptions"
                  disabled
                ></ASelect>
                <ASelect
                  v-else
                  v-show="!!knowledgeAuthorizedItem.permission"
                  class="user-drawer__authorized-select"
                  v-model:value="knowledgeAuthorizedItem.permission"
                  :options="knowledgeAuthorizedOptions"
                ></ASelect>
              </templete>
            </div>
          </div>
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse': authorizedListCollapseState.agent
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('agent')"
            />
            <span
              v-if="currentDataSourceItem.role === 'Super Administrator'"
              class="user-drawer__authorized-header-text"
              >Agent</span
            >
            <ACheckbox
              v-else
              class="user-drawer__authorized-header-checkbox"
              :checked="isAgentCheckAll"
              :indeterminate="isAgentIndeterminate"
              :disabled="!agentAuthorizedList.length"
              @change="handleChangeAgentCheckAll"
            >
              <span class="user-drawer__authorized-header-text">Agent</span>
            </ACheckbox>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.agent">
            <div
              class="user-drawer__authorized-item"
              v-for="(agentAuthorizedItem, index) in agentAuthorizedList"
              :key="agentAuthorizedItem.resourceId"
            >
              <template v-if="currentDataSourceItem.role === 'Super Administrator'">
                <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                  <div class="ellipsis-cell__tooltip hide-tooltip">
                    <ATooltip color="#525A69">
                      <template #title>{{ agentAuthorizedItem.resourceName }}</template>
                      <span>{{ agentAuthorizedItem.resourceName }}</span>
                    </ATooltip>
                  </div>
                  <div class="ellipsis-cell__text">
                    <span>{{ agentAuthorizedItem.resourceName }}</span>
                  </div>
                </div>
                <span
                  v-if="!!agentAuthorizedItem.permission"
                  class="user-drawer__authorized-tag"
                  :class="`user-drawer__authorized-tag_${agentAuthorizedItem.permission}`"
                >
                  {{ authorizedMap[agentAuthorizedItem.permission] }}
                </span>
              </template>
              <template v-else>
                <ACheckbox
                  class="user-drawer__authorized-checkbox"
                  :checked="!!agentAuthorizedItem.permission"
                  @change="handleChangeAgentCheckbox($event, index)"
                  :disabled="agentAuthorizedItem.permission === 4"
                >
                  <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                    <div class="ellipsis-cell__tooltip hide-tooltip">
                      <ATooltip color="#525A69">
                        <template #title>{{ agentAuthorizedItem.resourceName }}</template>
                        <span>{{ agentAuthorizedItem.resourceName }}</span>
                      </ATooltip>
                    </div>
                    <div class="ellipsis-cell__text">
                      <span>{{ agentAuthorizedItem.resourceName }}</span>
                    </div>
                  </div>
                </ACheckbox>
                <ASelect
                  v-if="agentAuthorizedItem.permission === 4"
                  class="user-drawer__authorized-select"
                  v-model:value="agentAuthorizedItem.permission"
                  :options="createrOptions"
                  disabled
                ></ASelect>
                <ASelect
                  v-else
                  v-show="!!agentAuthorizedItem.permission"
                  class="user-drawer__authorized-select"
                  v-model:value="agentAuthorizedItem.permission"
                  :options="agentAuthorizedOptions"
                ></ASelect>
              </template>
            </div>
          </div>
        </div>
        <div class="user-drawer__authorized_search-result" v-show="searchInputValue">
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse':
                  authorizedListCollapseState.knowledge
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('knowledge')"
            />
            <span class="user-drawer__authorized-header-text">Knowledge Base</span>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.knowledge">
            <div
              class="user-drawer__authorized-item"
              v-for="(knowledgeSearchResultItem, index) in knowledgeSearchResultList"
              :key="knowledgeSearchResultItem.resourceId"
            >
              <template v-if="currentDataSourceItem.role === 'Super Administrator'">
                <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                  <div class="ellipsis-cell__tooltip hide-tooltip">
                    <ATooltip color="#525A69">
                      <template #title>{{ knowledgeSearchResultItem.resourceName }}</template>
                      <span>{{ knowledgeSearchResultItem.resourceName }}</span>
                    </ATooltip>
                  </div>
                  <div class="ellipsis-cell__text">
                    <span>{{ knowledgeSearchResultItem.resourceName }}</span>
                  </div>
                </div>
                <span
                  v-if="!!knowledgeSearchResultItem.permission"
                  class="user-drawer__authorized-tag"
                  :class="`user-drawer__authorized-tag_${knowledgeSearchResultItem.permission}`"
                >
                  {{ authorizedMap[knowledgeSearchResultItem.permission] }}
                </span>
              </template>
              <template v-else>
                <ACheckbox
                  class="user-drawer__authorized-checkbox"
                  :checked="!!knowledgeSearchResultItem.permission"
                  @change="handleChangeKnowledgeSearchResultCheckbox($event, index)"
                  :disabled="knowledgeSearchResultItem.permission === 4"
                >
                  <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                    <div class="ellipsis-cell__tooltip hide-tooltip">
                      <ATooltip color="#525A69">
                        <template #title>{{ knowledgeSearchResultItem.resourceName }}</template>
                        <span>{{ knowledgeSearchResultItem.resourceName }}</span>
                      </ATooltip>
                    </div>
                    <div class="ellipsis-cell__text">
                      <span>{{ knowledgeSearchResultItem.resourceName }}</span>
                    </div>
                  </div>
                </ACheckbox>
                <ASelect
                  v-if="knowledgeSearchResultItem.permission === 4"
                  class="user-drawer__authorized-select"
                  v-model:value="knowledgeSearchResultItem.permission"
                  :options="createrOptions"
                  disabled
                ></ASelect>
                <ASelect
                  v-else
                  v-show="!!knowledgeSearchResultItem.permission"
                  class="user-drawer__authorized-select"
                  v-model:value="knowledgeSearchResultItem.permission"
                  :options="knowledgeAuthorizedOptions"
                ></ASelect>
              </template>
            </div>
          </div>
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse': authorizedListCollapseState.agent
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('agent')"
            />
            <span class="user-drawer__authorized-header-text">Agent</span>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.agent">
            <div
              class="user-drawer__authorized-item"
              v-for="(agentSearchResultItem, index) in agentSearchResultList"
              :key="agentSearchResultItem.resourceId"
            >
              <template v-if="currentDataSourceItem.role === 'Super Administrator'">
                <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                  <div class="ellipsis-cell__tooltip hide-tooltip">
                    <ATooltip color="#525A69">
                      <template #title>{{ agentSearchResultItem.resourceName }}</template>
                      <span>{{ agentSearchResultItem.resourceName }}</span>
                    </ATooltip>
                  </div>
                  <div class="ellipsis-cell__text">
                    <span>{{ agentSearchResultItem.resourceName }}</span>
                  </div>
                </div>
                <span
                  v-if="!!agentSearchResultItem.permission"
                  class="user-drawer__authorized-tag"
                  :class="`user-drawer__authorized-tag_${agentSearchResultItem.permission}`"
                >
                  {{ authorizedMap[agentSearchResultItem.permission] }}
                </span>
              </template>
              <template v-else>
                <ACheckbox
                  class="user-drawer__authorized-checkbox"
                  :checked="!!agentSearchResultItem.permission"
                  @change="handleChangeAgentSearchResultCheckbox($event, index)"
                  :disabled="agentSearchResultItem.permission === 4"
                >
                  <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                    <div class="ellipsis-cell__tooltip hide-tooltip">
                      <ATooltip color="#525A69">
                        <template #title>{{ agentSearchResultItem.resourceName }}</template>
                        <span>{{ agentSearchResultItem.resourceName }}</span>
                      </ATooltip>
                    </div>
                    <div class="ellipsis-cell__text">
                      <span>{{ agentSearchResultItem.resourceName }}</span>
                    </div>
                  </div>
                </ACheckbox>
                <ASelect
                  v-if="agentSearchResultItem.permission === 4"
                  class="user-drawer__authorized-select"
                  v-model:value="agentSearchResultItem.permission"
                  :options="createrOptions"
                  disabled
                ></ASelect>
                <ASelect
                  v-else
                  v-show="!!agentSearchResultItem.permission"
                  class="user-drawer__authorized-select"
                  v-model:value="agentSearchResultItem.permission"
                  :options="agentAuthorizedOptions"
                ></ASelect>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <ABtn
        v-if="currentDataSourceItem.role === 'Super Administrator'"
        type="primary"
        class="edit-user-drawer__drawer-button"
        @click="handleCloseDrawer"
        >OK</ABtn
      >
      <template v-else>
        <ABtn
          style="margin-right: 16px"
          class="edit-user-drawer__drawer-button drawer-button-cancel"
          type="default"
          @click="handleCloseDrawer"
          >Cancel</ABtn
        >
        <ABtn
          type="primary"
          class="edit-user-drawer__drawer-button"
          @click="handleClickConfirmButton"
          >Confirm</ABtn
        >
      </template>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, useTemplateRef, watch } from 'vue'
import { ABtn, AInput, ASelect, Drawer, Form, FormItem, ACheckbox, ATooltip } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { getAuthorizedList } from '@/renderer/src/api/userManagement'
import { AuthorizedData, Authorized, AccountRecord } from '@/types'
import { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface'
import { mouseEnterTooltip } from '@/renderer/src/hooks/ellipsisTooltip'

interface FormState {
  userName: string
  nickName: string
  role: string
}

const props = defineProps<{
  title: string
  isEditUser: boolean
  currentDataSourceItem: AccountRecord
}>()

//authorizedMap
const authorizedMap: Record<number, string> = {
  1: 'can view',
  2: 'can edit',
  3: 'can admin'
}

// 定义事件
const emit = defineEmits<{
  (e: 'handleConfirmEditUser', submitData?: FormState): void
  (e: 'handleCloseEditUser'): void
}>()

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 }
}

watch(
  () => props.currentDataSourceItem,
  (newVal) => {
    if (newVal) {
      formState.userName = newVal.userName
      formState.nickName = newVal.nickName
      formState.role = newVal.role
    }
  }
)

const isOpenEditUserDrawer = defineModel({
  default: false
})

const knowledgeAuthorizedList = defineModel<AuthorizedData>('knowledgeAuthorizedList', {
  default: []
})
const agentAuthorizedList = defineModel<AuthorizedData>('agentAuthorizedList', {
  default: []
})

const formState = reactive<FormState>({
  userName: '',
  nickName: '',
  role: ''
})

const isKnowledgeCheckAll = computed(() => {
  return knowledgeAuthorizedList.value.length
    ? knowledgeAuthorizedList.value.every((item) => item.permission !== 0)
    : false
})

const isKnowledgeIndeterminate = computed(() => {
  return (
    !isKnowledgeCheckAll.value &&
    knowledgeAuthorizedList.value.some((item) => item.permission !== 0)
  )
})

const isAgentCheckAll = computed(() => {
  return agentAuthorizedList.value.length
    ? agentAuthorizedList.value.every((item) => item.permission !== 0)
    : false
})

const isAgentIndeterminate = computed(() => {
  return !isAgentCheckAll.value && agentAuthorizedList.value.some((item) => item.permission !== 0)
})

const searchInputValue = ref('')
const knowledgeSearchResultList = ref<AuthorizedData>([])
const agentSearchResultList = ref<AuthorizedData>([])

const formReference = useTemplateRef<typeof Form>('formReference')

interface AuthorizedListCollapseState {
  knowledge: boolean
  agent: boolean
}

const authorizedListCollapseState = reactive<AuthorizedListCollapseState>({
  knowledge: false,
  agent: false
})

const rolesOptions = ref([
  // {
  //   label: 'Super Administrator',
  //   value: 'Super Administrator'
  // },
  {
    label: 'Administrator',
    value: 'Administrator'
  },
  {
    label: 'Member',
    value: 'Member'
  }
])

const createrOptions = ref([
  {
    label: 'can admin',
    value: 4
  }
])

const knowledgeAuthorizedOptions = ref([
  {
    label: 'can admin',
    value: 3
  },
  {
    label: 'can edit',
    value: 2
  },
  {
    label: 'can view',
    value: 1
  }
])

const agentAuthorizedOptions = ref([
  // {
  //   label: 'can admin',
  //   value: 4
  // },
  {
    label: 'can admin',
    value: 3
  },
  // {
  //   label: 'can edit',
  //   value: 2
  // },
  {
    label: 'can view',
    value: 1
  }
])

/**
 * @description: 处理改变搜索输入框
 * @param {*}
 * @return {*}
 */
const handleChangeSearchInput = (e: InputEvent) => {
  knowledgeSearchResultList.value = knowledgeAuthorizedList.value.filter((item: Authorized) =>
    item.resourceName.includes(searchInputValue.value)
  )

  agentSearchResultList.value = agentAuthorizedList.value.filter((item: Authorized) =>
    item.resourceName.includes(searchInputValue.value)
  )
}

/**
 * @description: 处理点击搜索按钮
 * @param {*}
 * @return {*}
 */
// const handleClickSearchButton = (e: KeyboardEvent) => {
//   console.log('searchInputValue', searchInputValue.value)

//   knowledgeSearchResultList.value = knowledgeAuthorizedList.value
//     .filter((item: Authorized) => item.resourceName.includes(searchInputValue.value))

//   agentSearchResultList.value = agentAuthorizedList.value
//     .filter((item: Authorized) => item.resourceName.includes(searchInputValue.value))
// }

/**
 * @description: 处理改变角色下拉框
 * @param {*} value 选中值
 * @return {*}
 */
const handleChangeRoleSelect = (value: string) => {
  // 清空所有知识库权限
  knowledgeAuthorizedList.value.forEach((item) => {
    item.permission = 0
  })

  // 清空所有智能体权限
  agentAuthorizedList.value.forEach((item) => {
    item.permission = 0
  })
}

/**
 * @description: 处理点击知识库展开图标
 * @param {keyof AuthorizedListCollapseState} collapseTarget 目标状态
 * @return {*}
 */
const handleClickKnowledgeExpandIcon = (collapseTarget: keyof AuthorizedListCollapseState) => {
  authorizedListCollapseState[collapseTarget] = !authorizedListCollapseState[collapseTarget]
}

/**
 * @description: 处理改变全选
 * @param {*}
 * @return {*}
 */
const handleChangeKnowledgeCheckAll = (e: CheckboxChangeEvent) => {
  knowledgeAuthorizedList.value.forEach((item) => {
    item.permission = e.target.checked ? item.permission || 1 : 0
  })
}

/**
 * @description: 处理改变智能体全选
 * @param value
 */
const handleChangeAgentCheckAll = (e: CheckboxChangeEvent) => {
  agentAuthorizedList.value.forEach((item) => {
    item.permission = e.target.checked ? item.permission || 1 : 0
  })
}

/**
 * @description: 处理改变知识库复选框
 * @param {*} checked
 * @param {*} index
 * @return {*}
 */
const handleChangeKnowledgeCheckbox = (e: CheckboxChangeEvent, index: number) => {
  knowledgeAuthorizedList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理改变知识库搜索结果复选框
 * @param e
 * @param index
 */
const handleChangeKnowledgeSearchResultCheckbox = (e: CheckboxChangeEvent, index: number) => {
  knowledgeSearchResultList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理改变智能体搜索结果复选框
 * @param e
 * @param index
 */
const handleChangeAgentSearchResultCheckbox = (e: CheckboxChangeEvent, index: number) => {
  agentSearchResultList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理改变智能体复选框
 * @param {boolean} checked 选中状态
 * @param {number} index 索引
 * @return {*}
 */
const handleChangeAgentCheckbox = (e: CheckboxChangeEvent, index: number) => {
  agentAuthorizedList.value[index].permission = e.target.checked ? 1 : 0
}

const handleChangeKnowledgeSelect = () => {
  console.log('knowledgeAuthorizedList', knowledgeAuthorizedList.value)
}

/**
 * @description: 清空搜索状态
 */
const clearSearchState = () => {
  searchInputValue.value = ''
  knowledgeSearchResultList.value = []
  agentSearchResultList.value = []
}

const handleCloseDrawer = () => {
  clearSearchState()
  isOpenEditUserDrawer.value = false
}

/**
 * @description: 处理抽屉可见性变化
 */
const handleVisibleChange = (visible: boolean) => {
  if (!visible) {
    emit('handleCloseEditUser')
    clearSearchState()
  }
}

/**
 * @description: 处理点击确认按钮
 * @return {*}
 */
const handleClickConfirmButton = () => {
  if (props.isEditUser && formReference.value) {
    formReference.value
      .validate()
      .then(() => {
        emit('handleConfirmEditUser', formState)
        clearSearchState()
      })
      .catch((error: Error) => {
        console.error('error', error)
      })
  } else {
    emit('handleConfirmEditUser')
    clearSearchState()
  }
}

// onMounted(() => {})
</script>

<style lang="less" scoped>
.edit-user-drawer {
  .user-management__form-input {
    height: 32px;
  }

  .user-drawer__authorized-wrapper {
  }

  .user-drawer__authorized-wrapper-header {
    margin-top: 35px;
    margin-bottom: 9px;
    color: #3b3b3b;
  }

  .user-drawer__authorized-input {
    height: 32px;
    margin-bottom: 20px;
  }

  .user-drawer__authorized-header {
    margin-bottom: 16px;
    padding: 0 4px;
  }

  .user-drawer__authorized-header-icon {
    margin-right: 4px;
    cursor: pointer;
  }

  .user-drawer__authorized-header-icon_collapse {
    transform: rotate(270deg);
  }

  .user-drawer__authorized-header-checkbox {
    margin-right: 8px;
  }

  .user-drawer__authorized-header-text {
    font-weight: 600;
    color: #000;
  }

  .user-drawer__authorized-item {
    margin-bottom: 16px;
    padding-left: 20px;
  }

  .user-drawer__authorized-checkbox {
    // margin-right: 8px;
    vertical-align: middle;
  }

  .user-drawer__authorized-title {
    display: inline-block;
    max-width: 172px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;

    // // 超出两行后显示省略号
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 2;
  }

  .user-drawer__authorized-select {
    float: right;
    width: 107px;

    ::v-deep(.ant-select-selector) {
      height: 28px;
    }

    ::v-deep(.ant-select-selection-item) {
      line-height: 25px;
    }
  }

  .user-drawer__authorized-tag {
    padding: 3px 8px;
    float: right;
  }

  .user-drawer__authorized-tag_3,
  .user-drawer__authorized-tag_2 {
    background-color: #eff5fe;
    color: #0e4bce;
  }

  .user-drawer__authorized-tag_1 {
    background-color: #fff2d9;
    color: #735823;
  }

  .edit-user-drawer__drawer-button {
    width: 102px;
    &.drawer-button-cancel {
      color: #000;
    }
  }
}
:deep(
    .ant-form-item
      .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)
  )::after {
  display: inline-block;
  margin-inline-end: 4px;
  color: #e1251b;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
:deep(
  .ant-form-item
    .ant-form-item-label
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before
) {
  content: '';
}
.ellipsis-cell {
  width: 100%;
  > div {
    span {
      max-width: 172px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  :deep(.hide-tooltip) {
    display: none;
  }
}
</style>
<style lang="less">
.edit-user-drawer-root {
  position: absolute;
}
</style>
