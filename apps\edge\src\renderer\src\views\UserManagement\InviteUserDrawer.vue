<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\UserManagement\InviteUserDrawer.vue
-->
<template>
  <Drawer
    class="invite-user-drawer"
    title="Invite User"
    v-model:open="isOpenInviteUserDrawer"
    root-class-name="invite-user-drawer-root"
    get-container="#app"
    :closable="false"
    :body-style="{ padding: '16px 20px' }"
    :footer-style="{ textAlign: 'right' }"
  >
    <div class="invite-user-drawer__body">
      <div class="invite-user-modal__form">
        <Form
          ref="formReference"
          :model="formState"
          autocomplete="off"
          v-bind="formItemLayout"
          label-align="left"
          :rules="rules"
        >
          <FormItem label="Phone/Mail" name="userName">
            <AInput
              class="user-management__form-input"
              v-model:value="formState.userName"
              placeholder="Please enter"
            />
          </FormItem>
          <FormItem label="Role" name="role">
            <ASelect
              class="user-management__roles-select"
              placeholder="Please select"
              :options="rolesOptions"
              v-model:value="formState.role"
            ></ASelect>
          </FormItem>
        </Form>
      </div>
      <div class="user-drawer__authorized-wrapper-header">Authorized Application</div>
      <div class="user-drawer__authorized-wrapper">
        <AInput
          class="user-drawer__authorized-input"
          v-model:value="searchInputValue"
          placeholder="Search by name"
          allow-clear
          @change="handleChangeSearchInput"
        >
          <template #suffix>
            <SvgIcon class="user-management__search-input-icon" name="search-icon" size="11" />
          </template>
        </AInput>
        <div class="user-drawer__authorized" v-show="!searchInputValue">
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse':
                  authorizedListCollapseState.knowledge
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('knowledge')"
            />
            <ACheckbox
              class="user-drawer__authorized-header-checkbox"
              :checked="isKnowledgeCheckAll"
              :indeterminate="isKnowledgeIndeterminate"
              :disabled="!knowledgeAuthorizedList.length"
              @change="handleChangeKnowledgeCheckAll"
            >
              <span class="user-drawer__authorized-header-text">Knowledge Base</span>
            </ACheckbox>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.knowledge">
            <div
              v-for="(knowledgeAuthorizedItem, index) in knowledgeAuthorizedList"
              :key="knowledgeAuthorizedItem.resourceId"
              class="user-drawer__authorized-item"
            >
              <ACheckbox
                class="user-drawer__authorized-checkbox"
                :checked="!!knowledgeAuthorizedItem.permission"
                @change="handleChangeKnowledgeCheckbox($event, index)"
              >
                <ATooltip color="#525A69">
                  <template #title>{{ knowledgeAuthorizedItem.resourceName }}</template>
                  <span class="user-drawer__authorized-title">{{
                    knowledgeAuthorizedItem.resourceName
                  }}</span>
                </ATooltip>
              </ACheckbox>
              <ASelect
                v-show="!!knowledgeAuthorizedItem.permission"
                class="user-drawer__authorized-select"
                v-model:value="knowledgeAuthorizedItem.permission"
                :options="knowledgeAuthorizedOptions"
              ></ASelect>
            </div>
          </div>
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse': authorizedListCollapseState.agent
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('agent')"
            />
            <ACheckbox
              class="user-drawer__authorized-header-checkbox"
              :checked="isAgentCheckAll"
              :indeterminate="isAgentIndeterminate"
              :disabled="!agentAuthorizedList.length"
              @change="handleChangeAgentCheckAll"
            >
              <span class="user-drawer__authorized-header-text">Agent</span>
            </ACheckbox>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.agent">
            <div
              class="user-drawer__authorized-item"
              v-for="(agentAuthorizedItem, index) in agentAuthorizedList"
              :key="agentAuthorizedItem.resourceId"
            >
              <ACheckbox
                class="user-drawer__authorized-checkbox"
                :checked="!!agentAuthorizedItem.permission"
                @change="handleChangeAgentCheckbox($event, index)"
              >
                <ATooltip color="#525A69">
                  <template #title>{{ agentAuthorizedItem.resourceName }}</template>
                  <span class="user-drawer__authorized-title">
                    {{ agentAuthorizedItem.resourceName }}
                  </span>
                </ATooltip>
              </ACheckbox>
              <ASelect
                v-show="!!agentAuthorizedItem.permission"
                class="user-drawer__authorized-select"
                v-model:value="agentAuthorizedItem.permission"
                :options="agentAuthorizedOptions"
              ></ASelect>
            </div>
          </div>
        </div>

        <div class="user-drawer__authorized_search-result" v-show="searchInputValue">
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse':
                  authorizedListCollapseState.knowledge
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('knowledge')"
            />
            <span class="user-drawer__authorized-header-text">Knowledge Base</span>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.knowledge">
            <div
              class="user-drawer__authorized-item"
              v-for="(knowledgeSearchResultItem, index) in knowledgeSearchResultList"
              :key="knowledgeSearchResultItem.resourceId"
            >
              <ACheckbox
                class="user-drawer__authorized-checkbox"
                :checked="!!knowledgeSearchResultItem.permission"
                @change="handleChangeKnowledgeSearchResultCheckbox($event, index)"
              >
                <ATooltip color="#525A69">
                  <template #title>{{ knowledgeSearchResultItem.resourceName }}</template>
                  <span class="user-drawer__authorized-title">{{
                    knowledgeSearchResultItem.resourceName
                  }}</span>
                </ATooltip>
              </ACheckbox>
              <ASelect
                v-show="!!knowledgeSearchResultItem.permission"
                class="user-drawer__authorized-select"
                v-model:value="knowledgeSearchResultItem.permission"
                :options="knowledgeAuthorizedOptions"
              ></ASelect>
            </div>
          </div>
          <div class="user-drawer__authorized-header">
            <SvgIcon
              class="user-drawer__authorized-header-icon"
              :class="{
                'user-drawer__authorized-header-icon_collapse': authorizedListCollapseState.agent
              }"
              name="arrow-down-icon"
              size="12"
              @click="handleClickKnowledgeExpandIcon('agent')"
            />
            <span class="user-drawer__authorized-header-text">Agent</span>
          </div>
          <div class="user-drawer__authorized-body" v-show="!authorizedListCollapseState.agent">
            <div
              class="user-drawer__authorized-item"
              v-for="(agentSearchResultItem, index) in agentSearchResultList"
              :key="agentSearchResultItem.resourceId"
            >
              <ACheckbox
                class="user-drawer__authorized-checkbox"
                :checked="!!agentSearchResultItem.permission"
                @change="handleChangeAgentSearchResultCheckbox($event, index)"
              >
                <ATooltip color="#525A69">
                  <template #title>{{ agentSearchResultItem.resourceName }}</template>
                  <span class="user-drawer__authorized-title">
                    {{ agentSearchResultItem.resourceName }}
                  </span>
                </ATooltip>
              </ACheckbox>
              <ASelect
                v-show="!!agentSearchResultItem.permission"
                class="user-drawer__authorized-select"
                v-model:value="agentSearchResultItem.permission"
                :options="agentAuthorizedOptions"
              ></ASelect>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <ABtn
        style="margin-right: 16px"
        class="invite-user-drawer__drawer-button drawer-button-cancel"
        type="default"
        @click="() => (isOpenInviteUserDrawer = false)"
        >Cancel</ABtn
      >
      <ABtn
        type="primary"
        class="invite-user-drawer__drawer-button"
        @click="handleClickConfirmButton"
        >Confirm</ABtn
      >
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, useTemplateRef, watch } from 'vue'
import { ABtn, AInput, ASelect, Drawer, Form, FormItem, ACheckbox, ATooltip } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { AuthorizedData, Authorized } from '@/types'
import { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface'
import type { Rule } from 'ant-design-vue/es/form'

export interface FormState {
  userName: string
  role: string | undefined
}

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 }
}

const rules: Record<string, Rule[]> = {
  userName: [
    { required: true, message: 'Please input phone/mail' },
    // 校验手机号
    {
      // pattern: /^1[3-9]\d{9}$/,
      // type: 'email',
      validator: (rule, value: string, callback) => {
        const regPhone = /^1[3-9]\d{9}$/
        const regEmail = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
        if (!value || regPhone.test(value) || regEmail.test(value)) {
          return Promise.resolve()
        } else {
          return Promise.reject('Please input valid phone/mail')
        }
      },
      trigger: 'blur'
    }
    // { type: 'email', message: 'Please input valid email', trigger: 'blur' },
  ],
  role: [{ required: true, message: 'Please select role' }]
}

const emit = defineEmits<{
  (e: 'handleConfirmInviteUser', submitData: { userName: string; role: string }): void
}>()

const isOpenInviteUserDrawer = defineModel({
  default: false
})

const knowledgeAuthorizedList = defineModel<AuthorizedData>('knowledgeAuthorizedList', {
  default: []
})
const agentAuthorizedList = defineModel<AuthorizedData>('agentAuthorizedList', {
  default: []
})

watch(isOpenInviteUserDrawer, (newVal) => {
  if (newVal && formReference.value) {
    formReference.value.resetFields()
  }
})

const formState = reactive<FormState>({
  userName: '',
  role: undefined
})

const formReference = useTemplateRef<typeof Form>('formReference')

const searchInputValue = ref('')
const knowledgeSearchResultList = ref<AuthorizedData>([])
const agentSearchResultList = ref<AuthorizedData>([])

const rolesOptions = ref([
  {
    label: 'Administrator',
    value: 'Administrator'
  },
  {
    label: 'Member',
    value: 'Member'
  }
])

const authorizedListCollapseState = reactive({
  knowledge: false,
  agent: false
})

const knowledgeAuthorizedOptions = ref([
  {
    label: 'can admin',
    value: 3
  },
  {
    label: 'can edit',
    value: 2
  },
  {
    label: 'can view',
    value: 1
  }
])

const agentAuthorizedOptions = ref([
  {
    label: 'can admin',
    value: 3
  },
  {
    label: 'can view',
    value: 1
  }
])

const isKnowledgeCheckAll = computed(() => {
  return knowledgeAuthorizedList.value.length
    ? knowledgeAuthorizedList.value.every((item) => item.permission !== 0)
    : false
})

const isKnowledgeIndeterminate = computed(() => {
  return (
    !isKnowledgeCheckAll.value &&
    knowledgeAuthorizedList.value.some((item) => item.permission !== 0)
  )
})

const isAgentCheckAll = computed(() => {
  return agentAuthorizedList.value.length
    ? agentAuthorizedList.value.every((item) => item.permission !== 0)
    : false
})

const isAgentIndeterminate = computed(() => {
  return !isAgentCheckAll.value && agentAuthorizedList.value.some((item) => item.permission !== 0)
})

/**
 * @description: 处理搜索输入框变化
 * @param {*} value 输入的搜索值
 * @return {*}
 */
const handleChangeSearchInput = (e: InputEvent) => {
  knowledgeSearchResultList.value = knowledgeAuthorizedList.value.filter((item: Authorized) =>
    item.resourceName.includes(searchInputValue.value)
  )
  agentSearchResultList.value = agentAuthorizedList.value.filter((item: Authorized) =>
    item.resourceName.includes(searchInputValue.value)
  )
}

/**
 * @description: 处理知识授权全选框变化
 * @param {*} e 事件对象
 * @return {*}
 */
const handleChangeKnowledgeCheckAll = (e: CheckboxChangeEvent) => {
  knowledgeAuthorizedList.value.forEach((item) => {
    item.permission = e.target.checked ? item.permission || 1 : 0
  })
}

/**
 * @description: 处理智能体授权全选框变化
 * @param {*} e 事件对象
 * @return {*}
 */
const handleChangeAgentCheckAll = (e: CheckboxChangeEvent) => {
  agentAuthorizedList.value.forEach((item) => {
    item.permission = e.target.checked ? item.permission || 1 : 0
  })
}

/**
 * @description: 处理知识授权复选框变化
 * @param {*} e 事件对象
 * @param {*} index 索引
 * @return {*}
 */
const handleChangeKnowledgeCheckbox = (e: CheckboxChangeEvent, index: number) => {
  knowledgeAuthorizedList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理智能体授权复选框变化
 * @param {*} e 事件对象
 * @param {*} index 索引
 * @return {*}
 */
const handleChangeAgentCheckbox = (e: CheckboxChangeEvent, index: number) => {
  agentAuthorizedList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理知识搜索结果复选框变化
 * @param {*} e 事件对象
 * @param {*} index 索引
 * @return {*}
 */
const handleChangeKnowledgeSearchResultCheckbox = (e: CheckboxChangeEvent, index: number) => {
  knowledgeSearchResultList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理智能体搜索结果复选框变化
 * @param {*} e 事件对象
 * @param {*} index 索引
 * @return {*}
 */
const handleChangeAgentSearchResultCheckbox = (e: CheckboxChangeEvent, index: number) => {
  agentSearchResultList.value[index].permission = e.target.checked ? 1 : 0
}

/**
 * @description: 处理确认按钮点击
 * @param {*}
 * @return {*}
 */
const handleClickConfirmButton = () => {
  if (formReference.value) {
    formReference.value
      .validate()
      .then((result: { userName: string; role: string }) => {
        emit('handleConfirmInviteUser', result)
      })
      .catch((error: Error) => {
        console.error('error', error)
      })
  }
}

/**
 * @description: 处理角色选择变化
 * @param {*} value 选择的角色值
 * @return {*}
 */
// const handleChangeRoleSelect = (value: string) => {
//   // 清空所有知识库权限
//   knowledgeAuthorizedList.value.forEach((item) => {
//     item.permission = 0
//   })
//   // 清空所有智能体权限
//   agentAuthorizedList.value.forEach((item) => {
//     item.permission = 0
//   })
// }

/**
 * @description: 处理知识授权折叠图标点击
 * @param {*} collapseTarget 目标折叠项
 * @return {*}
 */
const handleClickKnowledgeExpandIcon = (
  collapseTarget: keyof typeof authorizedListCollapseState
) => {
  authorizedListCollapseState[collapseTarget] = !authorizedListCollapseState[collapseTarget]
}
</script>

<style lang="less" scoped>
.invite-user-drawer {
  .user-management__form-input {
    height: 32px;
  }

  .user-drawer__authorized-wrapper-header {
    margin-top: 35px;
    margin-bottom: 9px;
    color: #3b3b3b;
  }

  .user-drawer__authorized-input {
    height: 32px;
    margin-bottom: 20px;
  }

  .user-drawer__authorized-header {
    margin-bottom: 16px;
    padding: 0 4px;
  }

  .user-drawer__authorized-header-icon {
    margin-right: 4px;
    cursor: pointer;
  }

  .user-drawer__authorized-header-icon_collapse {
    transform: rotate(270deg);
  }

  .user-drawer__authorized-header-checkbox {
    margin-right: 8px;
  }

  .user-drawer__authorized-header-text {
    font-weight: 600;
    color: #000;
  }

  .user-drawer__authorized-item {
    margin-bottom: 16px;
    padding-left: 20px;
  }

  .user-drawer__authorized-checkbox {
    // margin-right: 8px;
    vertical-align: middle;
  }

  .user-drawer__authorized-title {
    display: inline-block;
    max-width: 172px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;

    // // 超出两行后显示省略号
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 2;
  }

  .user-drawer__authorized-select {
    float: right;
    width: 107px;

    ::v-deep(.ant-select-selector) {
      height: 28px;
    }

    ::v-deep(.ant-select-selection-item) {
      line-height: 25px;
    }
  }

  .invite-user-drawer__drawer-button {
    width: 102px;
    &.drawer-button-cancel {
      color: #000;
    }
  }
}

:deep(
    .ant-form-item
      .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)
  )::after {
  display: inline-block;
  margin-inline-end: 4px;
  color: #e1251b;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
:deep(
  .ant-form-item
    .ant-form-item-label
    > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before
) {
  content: '';
}
</style>

<style lang="less">
.invite-user-drawer-root {
  position: absolute;
}
</style>
