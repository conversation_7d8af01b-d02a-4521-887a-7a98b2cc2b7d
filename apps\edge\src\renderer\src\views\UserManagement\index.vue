<!--
 * @Description: 
 * @FilePath: \ainow-ui\apps\edge\src\renderer\src\views\UserManagement\index.vue
-->
<template>
  <div class="user-management">
    <div class="user-management__header">User Management</div>
    <div class="user-management__body">
      <div class="user-management__buttons-wrapper">
        <ABtn
          class="user-management__import-button"
          type="primary"
          @click="handleClickInviteUserButton"
        >
          <SvgIcon class="user-management__import-button-icon" name="import-icon" size="11" />
          Invite User
        </ABtn>
        <AInput
          class="user-management__search-input"
          v-model:value="searchInputValue"
          placeholder="Search by account"
          @pressEnter="handleClickSearchButton"
        >
          <template #suffix>
            <SvgIcon
              class="user-management__search-input-icon"
              name="search-icon"
              size="11"
              @click="handleClickSearchButton"
            />
          </template>
        </AInput>
        <ASelect
          class="user-management__roles-select"
          placeholder="All Roles"
          :options="rolesOptions"
          :allow-clear="true"
          v-model:value="rolesSelectValue"
          @change="handleChangeRolesSelect"
        ></ASelect>
        <ABtn class="user-management__synchronize-button" @click="handleClickSynchronizeButton">
          <SvgIcon
            class="user-management__synchronize-button-icon"
            name="synchronize-icon"
            size="11"
          />
        </ABtn>
        <!-- <ABtn
          class="user-management__delete-button"
          @click="handleClickBatchDeleteButton"
          v-if="selectedRowKeyArr.length"
        >
          <SvgIcon class="user-management__delete-button-icon" name="delete-icon" size="14" />
        </ABtn> -->
      </div>
      <div class="user-management__user-table-wrapper">
        <ATable
          class="user-table_header"
          :columns="columns"
          :pagination="false"
          size="small"
        ></ATable>
        <ATable
          class="user-table"
          :show-header="false"
          :columns="columns"
          :data-source="dataSource"
          :pagination="false"
          size="small"
          :scroll="{ y: 'calc(100vh - 240px)' }"
          :row-class-name="
            (record: any, index: any) =>
              `user-table__row ${selectedRowIndex === index ? 'selected-row' : ''}`
          "
          row-key="userId"
          :custom-row="customRow"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'userName'">
              <span style="color: #000">
                {{ record.userName }}
              </span>
            </template>
            <template v-if="column.key === 'nickName'">
              <div class="ellipsis-cell" @mouseenter="mouseEnterTooltip($event.currentTarget)">
                <div class="ellipsis-cell__tooltip hide-tooltip">
                  <ATooltip color="#525A69">
                    <template #title>{{ record.nickName }}</template>
                    <span style="color: #000">{{ record.nickName }}</span>
                  </ATooltip>
                </div>
                <div class="ellipsis-cell__text">
                  <span style="color: #000">{{ record.nickName }}</span>
                </div>
              </div>
            </template>
            <template v-if="column.key === 'role'">
              <span class="user-table__role-cell">
                <SvgIcon
                  class="user-table__role-cell-icon"
                  :name="`${record.role}-icon`"
                  size="16"
                />
                <span class="user-table__role-cell-text">
                  {{ record.role }}
                </span>
              </span>
            </template>
            <template v-if="column.key === 'authorized'">
              <span
                class="user-table__authorized-cell-wrapper"
                @click="handleClickAuthorizedCell(record)"
              >
                <span class="user-table__authorized-cell user-table__authorized-cell_knowledge">
                  <SvgIcon
                    class="user-table__authorized-cell-icon"
                    name="knowledge-icon"
                    size="16"
                  /><span class="user-table__authorized-cell-text"
                    >: {{ record.kbCount || 0 }}</span
                  >
                </span>
                <span class="user-table__authorized-cell user-table__authorized-cell_agent">
                  <SvgIcon
                    class="user-table__authorized-cell-icon"
                    name="agent-icon"
                    size="16"
                  /><span class="user-table__authorized-cell-text"
                    >: {{ record.agentCount || 0 }}</span
                  >
                </span>
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <span v-show="currentRowIndex === index" style="vertical-align: sub">
                <div
                  class="user-table__row-button user-table__row-button_delete"
                  v-if="record.role !== 'Super Administrator'"
                  @click="handleClickDeleteButton(record)"
                >
                  <SvgIcon
                    class="user-table__row-button-icon user-table__row-button-icon_delete"
                    name="delete-icon"
                    size="16"
                  />
                </div>
                <div
                  class="user-table__row-button"
                  v-if="record.role !== 'Super Administrator'"
                  @click="handleClickEditButton(record)"
                >
                  <SvgIcon class="user-table__row-button-icon" name="edit-icon" size="16" />
                </div>
              </span>
              <span
                v-if="record.role !== 'Super Administrator'"
                v-show="currentRowIndex !== index"
                style="vertical-align: sub"
              >
                <SvgIcon name="more-icon" size="16" />
              </span>
            </template>
          </template>
        </ATable>
      </div>
      <div class="user-management__pagination-wrapper">
        <APagination
          class="user-management__pagination"
          v-model:current="currentPage"
          :pageSize="20"
          :total="totalCount"
          show-quick-jumper
          :show-size-changer="false"
          hide-on-single-page
          :show-total="(total: number) => `Total ${total} items  ${currentPage}/${pageCount}`"
          @change="handleChangePageNumber"
        ></APagination>
      </div>
    </div>
    <AModal
      class="user-management__delete-modal"
      width="464px"
      centered
      v-model:open="isOpenDeleteModal"
      @ok="handleOkDeleteModal"
      @cancel="cancelDelete"
    >
      <template #title>
        <div class="delete-modal__title">
          <SvgIcon class="delete-modal__title-icon" name="warn-icon" size="20" />
          <span class="delete-modal__title-text">Delete?</span>
        </div>
      </template>
      <template #footer>
        <div style="display: flex; justify-content: center">
          <ABtn type="primary" :loading="isDeleteButtonLoading" @click="handleOkDeleteModal"
            >Delete</ABtn
          >
          <ABtn @click="cancelDelete" class="delete-cancel-text">Cancel</ABtn>
        </div>
      </template>
      <div class="delete-modal__body">
        <div>Are you sure to delete User {{ currentDataSourceItem?.userName }} ?</div>
      </div>
    </AModal>
    <!-- <AModal v-model:open="isOpenMatchDeleteModal" title="Delete" @ok="handleOkMatchDeleteModal">
      <div class="user-management__match-delete-modal-body">
        <div></div>
        <div>Are you sure to delete {{ selectedRowKeyArr.length }} users from the User list?</div>
      </div>
    </AModal> -->
    <InviteUserDrawer
      v-model="isOpenInviteUserDrawer"
      v-model:knowledgeAuthorizedList="knowledgeAuthorizedList"
      v-model:agentAuthorizedList="agentAuthorizedList"
      @handleConfirmInviteUser="handleConfirmInviteUser"
    />
    <EditUserDrawer
      :title="isEditUser ? 'Edit user information' : 'Authorized application'"
      :isEditUser="isEditUser"
      v-model="isOpenEditUserDrawer"
      v-model:knowledgeAuthorizedList="knowledgeAuthorizedList"
      v-model:agentAuthorizedList="agentAuthorizedList"
      :currentDataSourceItem="currentDataSourceItem"
      @handleConfirmEditUser="handleConfirmEditUser"
      @handleCloseEditUser="handleCloseEditUser"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ABtn, AInput, ASelect, ATable, AModal, APagination, ATooltip, AMsg } from '@libs/a-comps'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import {
  getAccountList,
  deleteAccount,
  getAuthorizedList,
  updateAccount,
  updateAuthorisation,
  inviteAccount
} from '@/renderer/src/api/userManagement'
import { AccountListData, AccountRecord, AuthorizedData } from '@/types'
import InviteUserDrawer from './InviteUserDrawer.vue'
import EditUserDrawer from './EditUserDrawer.vue'
import { mouseEnterTooltip } from '@/renderer/src/hooks/ellipsisTooltip'
import { useRoute } from 'vue-router'

// 添加指令

interface FormState {
  userName: string
  nickName: string
  role: string
}

interface DataItem {
  name: string
  lastModified: number
  modified: string
  isDirectory: boolean
  status: string
  path: string
}

// :row-selection="{
//   selectedRowKeys: selectedRowKeyArr,
//   onChange: handleChangSelectedRowKeys
// }"
// 当前行索引
const currentRowIndex = ref<number | null>(null)
const selectedRowKeyArr = ref<string[]>([])
const selectedRowIndex = ref<number | null>(null)

// 角色选项
const rolesOptions = ref([
  {
    label: 'Super Administrator',
    value: 'Super Administrator'
  },
  {
    label: 'Administrator',
    value: 'Administrator'
  },
  {
    label: 'Member',
    value: 'Member'
  }
])
const rolesSelectValue = ref()

const searchInputValue = ref('')
const currentPage = ref(1)
const totalCount = ref(0)
const pageCount = ref(0)

// const pagination = {
//   total: totalCount.value,
//   current: currentPage.value,
//   pageSize: 10,
//   showQuickJumper: true,
//   showTotal: (total: number) => {
//     return `Total ${total} items  ${currentPage.value}/${totalCount.value}`;
//   },
// }

const columns = [
  {
    key: 'userName',
    title: 'Account',
    dataIndex: 'userName',
    width: '19%',
    ellipsis: true
  },
  {
    key: 'nickName',
    title: 'Nickname',
    dataIndex: 'nickName',
    width: '16%',
    ellipsis: true
    // sorter: (a: DataItem, b: DataItem) => {
    //   return a.name.localeCompare(b.name)
    // }
  },
  {
    key: 'role',
    title: 'Role',
    dataIndex: 'role',
    width: '25%',
    ellipsis: true
  },
  {
    key: 'authorized',
    title: 'Authorized Application',
    dataIndex: 'authorized',
    width: '20%'
  },
  // {
  //   key: 'status',
  //   title: 'Status',
  //   dataIndex: 'status',
  // },
  // {
  //   key: 'modified',
  //   title: 'Last modified',
  //   dataIndex: 'modified',
  // },
  {
    key: 'action',
    title: 'Action',
    dataIndex: 'action',
    // width: '10%'
    align: 'center'
  }
]

const customRow = (record: AccountRecord, index: number) => {
  return {
    onClick: () => {
      selectedRowIndex.value = index
    },
    onMouseenter: (event: MouseEvent) => {
      currentRowIndex.value = index
    },
    onMouseleave: () => {
      currentRowIndex.value = null
    }
  }
}

const dataSource = ref<AccountRecord[]>([])
const currentDataSourceItem = ref<AccountRecord>({
  createTime: '',
  id: 0,
  kbCount: '',
  agentCount: '',
  nickName: '',
  role: '', // Super Administrator/Administrator/Member
  status: '',
  updateTime: '',
  userId: '',
  userName: ''
})

const isDeleteButtonLoading = ref<boolean>(false)
const isOpenDeleteModal = ref<boolean>(false)
const isOpenMatchDeleteModal = ref<boolean>(false)

const isOpenInviteUserDrawer = ref<boolean>(false)
const isOpenEditUserDrawer = ref<boolean>(false)
const isEditUser = ref<boolean>(false)

const authorizedList = ref<AuthorizedData>([])
const knowledgeAuthorizedList = ref<AuthorizedData>([])
const agentAuthorizedList = ref<AuthorizedData>([])
const isScrolling = ref(false)
let scrollTimeout: ReturnType<typeof setTimeout> | null = null

const route = useRoute()
let lastRouteName = ref<string>('')

/**
 * 处理改变选中行
 * @param selectedRowKeys 选中行的key数组
 * @returns void
 */
const handleChangSelectedRowKeys = (selectedRowKeys: string[]) => {
  selectedRowKeyArr.value = selectedRowKeys
}

/**
 * 获取标准表格数据源据源
 * @returns void
 */
const getUserTableDataSource = (currentPageNumber: number = 1) => {
  getAccountList({
    current: currentPageNumber,
    size: 20,
    userName: searchInputValue.value,
    role: rolesSelectValue.value || ''
  }).then((result) => {
    const { success, data } = result.data

    if (success && data) {
      const { records, total, current, pages } = data

      dataSource.value = records
      totalCount.value = +total
      currentPage.value = +current
      pageCount.value = +pages
    }
  })
}

/**
 * 处理点击搜索按钮
 * @returns
 */
const handleClickSearchButton = () => {
  getUserTableDataSource()
}

/**
 * 处理角色选择框改变事件
 * @param value 选中的值
 * @returns
 */
const handleChangeRolesSelect = (value: string) => {
  getUserTableDataSource()
}

/**
 * 处理点击同步按钮
 * @returns
 */
const handleClickSynchronizeButton = () => {
  getUserTableDataSource()
}

/**
 * 处理点击批量删除按钮
 * @returns
 */
const handleClickBatchDeleteButton = () => {
  if (selectedRowKeyArr.value.length) {
    isOpenMatchDeleteModal.value = true
  }
}

/**
 * 处理确认批量删除弹窗
 * @returns
 */
const handleOkMatchDeleteModal = () => {
  if (selectedRowKeyArr.value.length) {
    deleteAccount({
      userIds: selectedRowKeyArr.value
    }).then((result) => {
      const { success } = result.data
      if (success) {
        isOpenMatchDeleteModal.value = false
        getUserTableDataSource()
      }
    })
  }
}

/**
 * @description 打开编辑用户抽屉
 * @param record
 * @return {*} void
 */
const openEditUserDrawer = (record: AccountRecord) => {
  isOpenEditUserDrawer.value = true
  currentDataSourceItem.value = record

  authorizedList.value = []
  knowledgeAuthorizedList.value = []
  agentAuthorizedList.value = []

  getAuthorizedList({
    toUserId: record.userId
  }).then((result) => {
    const { success, data } = result.data

    if (success && data) {
      authorizedList.value = data

      knowledgeAuthorizedList.value = data.filter((item) => item.resourceType === 2)
      agentAuthorizedList.value = data.filter((item) => item.resourceType === 1)
    }
  })
}

/**
 * 处理点击编辑按钮
 * @param record
 */
const handleClickEditButton = (record: AccountRecord) => {
  isEditUser.value = true
  openEditUserDrawer(record)
}

/**
 * 处理点击删除按钮
 * @param record
 */
const handleClickDeleteButton = (record: AccountRecord) => {
  isOpenDeleteModal.value = true
  currentDataSourceItem.value = record
}

/**
 * 处理确认删除弹窗
 * @param
 */
const handleOkDeleteModal = () => {
  if (currentDataSourceItem.value) {
    isDeleteButtonLoading.value = true

    deleteAccount({
      userIds: [currentDataSourceItem.value.userId]
    })
      .then((result) => {
        const { success } = result.data
        if (success) {
          isOpenDeleteModal.value = false
          selectedRowIndex.value = null
          getUserTableDataSource()
        }
      })
      .finally(() => {
        isDeleteButtonLoading.value = false
      })
  }
}

//取消删除
const cancelDelete = () => {
  isOpenDeleteModal.value = false
  selectedRowIndex.value = null
}

/**
 * @description: 处理点击邀请用户按钮
 * @param {*}
 * @return {*}
 */
const handleClickInviteUserButton = () => {
  isOpenInviteUserDrawer.value = true

  authorizedList.value = []
  knowledgeAuthorizedList.value = []
  agentAuthorizedList.value = []

  getAuthorizedList().then((result) => {
    const { success, data } = result.data

    if (success && data) {
      authorizedList.value = data

      knowledgeAuthorizedList.value = data.filter((item) => item.resourceType === 2)
      agentAuthorizedList.value = data.filter((item) => item.resourceType === 1)
    }
  })
}

/**
 * @description: 处理邀请用户弹窗提交
 * @param {FormState} submitData
 * @return {*}
 */
const handleValidateInviteFormFinish = (submitData: FormState): any => {
  console.log('submitData', submitData)
}

/**
 * 处理改变页数
 * @param {number} page 页码
 * @param {number} pageSize 每页条数
 */
const handleChangePageNumber = (page: number, pageSize: number) => {
  getUserTableDataSource(page)
}

/**
 * @description: 处理点击授权应用单元格
 * @param {*}
 * @return {*}
 */
const handleClickAuthorizedCell = (record: AccountRecord) => {
  isEditUser.value = false
  openEditUserDrawer(record)
}

/**
 * 处理确认编辑用户
 * @param submitData 提交数据
 * @returns
 */
const handleConfirmEditUser = (submitData?: FormState) => {
  let resultPromise = null
  if (isEditUser && submitData) {
    resultPromise = updateAccount({
      userId: currentDataSourceItem.value.userId,
      nickName: submitData.nickName,
      role: submitData.role,
      resourceList: [...knowledgeAuthorizedList.value, ...agentAuthorizedList.value]
    })
  } else {
    resultPromise = updateAuthorisation({
      userId: currentDataSourceItem.value.userId,
      resourceList: [...knowledgeAuthorizedList.value, ...agentAuthorizedList.value]
    })
  }

  resultPromise.then((result) => {
    const { success } = result.data
    if (success) {
      isOpenEditUserDrawer.value = false
      getUserTableDataSource()

      AMsg.success('Saved')
    }
  })
}

/**
 * 关闭抽屉  清空选择背景色
 */
const handleCloseEditUser = () => {
  selectedRowIndex.value = null
}

/**
 * @description 处理确认邀请用户
 * @param submitData 提交数据
 */
const handleConfirmInviteUser = (submitData: { userName: string; role: string }) => {
  inviteAccount({
    userName: submitData.userName,
    role: submitData.role,
    resourceList: [...knowledgeAuthorizedList.value, ...agentAuthorizedList.value]
  }).then((result) => {
    const { success } = result.data
    if (success) {
      isOpenInviteUserDrawer.value = false
      getUserTableDataSource()

      AMsg.success('Invitation sent.')
    }
  })
}

const setupScrollDetection = () => {
  const userTableBody = document.querySelector('.user-table .ant-table-body') as HTMLElement
  if (userTableBody) {
    userTableBody.addEventListener('scroll', handleScroll)
  }
}

const handleScroll = () => {
  isScrolling.value = true
  const userTableBody = document.querySelector('.user-table .ant-table-body') as HTMLElement
  if (userTableBody) {
    userTableBody.classList.add('scrolling')
  }

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
    if (userTableBody) {
      userTableBody.classList.remove('scrolling')
    }
  }, 800)
}

onMounted(() => {
  // 初始化用户表格数据
  getUserTableDataSource()
  setupScrollDetection()

  // 初始化上一个路由名称
  lastRouteName.value = route.name as string
})

watch(
  () => route.name,
  (newRouteName, oldRouteName) => {
    // 从其他页面切换到User Management
    if (newRouteName === 'userManagement' && oldRouteName !== 'userManagement') {
      getUserTableDataSource()
    }
    lastRouteName.value = newRouteName as string
  }
)

onUnmounted(() => {
  const userTableBody = document.querySelector('.user-table .ant-table-body') as HTMLElement
  if (userTableBody) {
    userTableBody.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})
</script>

<style lang="less" scoped>
.user-management {
  padding: 7px 16px 0 25px;

  .user-management__header {
    position: relative;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
  }

  .user-management__import-status-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    font-weight: 400;
    font-size: 14px;
    color: #6441ab;
  }

  .user-management__import-number {
    display: inline-block;
    margin-right: 16px;
  }

  .user-management__import-status-button {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background-color: #f2f3f5;
    color: #6441ab;
  }

  .user-management__buttons-wrapper {
    margin-bottom: 10px;
  }

  .user-management__import-button {
    height: initial;
    margin-right: 12px;
    padding: 4px 15px;
    vertical-align: middle;
  }

  .user-management__import-button-icon {
    margin-right: 5.5px;
    vertical-align: initial;
    color: #fff;
  }

  .user-management__search-input {
    width: 168px;
    height: 32px;
    margin-right: 12px;
    // border-color: #f2f3f5;
    background: #f2f3f5;
    vertical-align: middle;

    ::v-deep(.ant-input) {
      background-color: #f2f3f5;

      &::placeholder {
        color: var(--text-color4);
      }
    }
  }

  .user-management__search-input-icon {
    color: #3b3b3b;
  }

  .user-management__delete-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    padding: 0;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  .user-management__delete-button-icon {
    vertical-align: baseline;
    color: #3b3b3b;
  }

  .user-management__roles-select {
    width: 168px;
    height: 32px;
    margin-right: 12px;
    vertical-align: middle;

    ::v-deep(.ant-select-selector) {
      border-color: #f2f3f5;
    }
  }

  .user-management__synchronize-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    margin-right: 12px;
    padding: 0;
    border-color: #f2f3f5;
    background-color: #f2f3f5;
    vertical-align: middle;
    cursor: pointer;
  }

  .user-management__synchronize-button-icon {
    vertical-align: middle;
    color: #3b3b3b;
  }

  .user-management__user-table-wrapper {
    height: calc(100vh - 204px);
    margin-bottom: 14px;
    overflow: hidden;
  }

  .user-table {
    ::v-deep(.ant-table) {
      .selected-row {
        background-color: #f5f2fe;

        td {
          background-color: #f5f2fe !important;
        }

        &:hover > td {
          background-color: #f5f2fe !important;
        }
      }
    }

    ::v-deep(.ant-table-body) {
      overflow-y: auto;
      max-height: calc(100vh - 284px);

      &::-webkit-scrollbar {
        width: 6px;
        background: transparent;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 3px;
        transition: background 0.2s ease;
      }

      /* 滚动时显示滚动条 */
      &.scrolling::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);

        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
      }
    }
  }

  .user-table_header {
    ::v-deep(.ant-table-tbody) {
      display: none;
    }
  }

  ::v-deep(.ant-table-thead) {
    tr > th {
      background-color: #fff;
    }
  }

  ::v-deep(.user-table-row) {
    .ant-btn-link {
      padding: 0;
      margin-right: 8px;
    }

    // .ant-checkbox-wrapper {
    //   display: none;
    // }

    // &:hover {
    //   .ant-checkbox-wrapper {
    //     display: inline-flex;
    //   }
    // }
  }

  .user-table__role-cell-icon {
    margin-right: 6px;
    vertical-align: middle;
  }

  .user-table__role-cell-text {
    vertical-align: middle;
    color: #696969;
  }

  .user-table__authorized-cell-wrapper {
    display: inline-block;
    padding: 0 10px;
    border: 1px solid #c9cdd4;
    border-radius: 2px;
    background-color: #f2f3f5;
    color: #6441ab;
    cursor: pointer;

    &:hover {
      background-color: #e5e7ec;
    }

    &:active {
      background-color: #c9cdd4;
    }
  }

  .user-table__authorized-cell {
    vertical-align: text-bottom;
  }

  .user-table__authorized-cell_knowledge {
    margin-right: 10px;
    padding-right: 8px;
    border-right: 1px solid #e5e7ec;
  }

  .user-table__authorized-cell-icon {
    margin-right: 4px;
    vertical-align: middle;
  }

  .user-table__authorized-cell-text {
    vertical-align: middle;
  }

  .user-table__row-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    cursor: pointer;

    &:hover {
      background-color: #e5e7ec;
    }

    &:active {
      background-color: #c9cdd4;
    }
  }

  .user-table__row-button_delete {
    margin-right: 14px;
  }

  .user-management__pagination-wrapper {
    display: flex;
    justify-content: center;
  }
}

.user-management__delete-modal {
  .delete-modal__title {
    padding: 4px 0;
    text-align: center;
  }

  .delete-modal__title-icon {
    margin-right: 8px;
    vertical-align: middle;
  }

  .delete-modal__title-text {
    vertical-align: middle;
  }

  .delete-modal__body {
    padding: 16px 0 24px;
    text-align: center;
    color: #000;
  }
  .delete-cancel-text {
    color: #000000;
  }
}

.ellipsis-cell {
  width: 100%;
  > div {
    span {
      max-width: 172px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }
  }

  :deep(.hide-tooltip) {
    display: none;
  }
}
</style>
