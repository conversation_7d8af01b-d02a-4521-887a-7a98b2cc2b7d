import axios from 'axios'
import { emitter } from '@/utils/EventBus'
import { GlobalConfig } from '../renderer/src/common'

let resourceId: string
let resourceType: string | undefined
emitter.on('agent-changed', (id: string, type: string) => {
  resourceId = id
  resourceType = type
})

function generateBoundary() {
  return '------------------------' + Date.now().toString(16)
}
export const httpFileUpLoad = async (file: File, id: string) => {
  const boundary = generateBoundary()
  const headers: Record<string, any> = {
    'Content-Type': `multipart/form-data; boundary=${boundary}`
  }
  if (resourceId) {
    headers['resource-Id'] = resourceId
    headers['resource-Type'] = resourceType
  }
  const result = await axios.post(
    GlobalConfig.kbFileServer + '/api/v1/document/upload?v=1',
    {
      knowledgeId: id,
      file: file
    },
    {
      headers
    }
  )
  return result
}
