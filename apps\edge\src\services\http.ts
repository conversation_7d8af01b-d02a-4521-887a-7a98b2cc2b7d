// http.ts
import { FetchClient, FetchResponse } from './Fetch'
import { GlobalConfig } from '../renderer/src/common'
import { Res } from '@/types'
import { AMsg } from '@libs/a-comps'
import { emitter } from '../utils/EventBus'
let resourceId: string
let resourceType: string | undefined
// emitter.on('agent-changed', (id: string, type: string) => {
//   resourceId = id
//   resourceType = type
// })
const http = new FetchClient({
  // baseURL: GlobalConfig.edgeServer,
  headers: {
    'Content-Type': 'application/json'
  }
})
http.useRequestInterceptor((config) => {
  config.baseURL = GlobalConfig.edgeServer + '/aclapi'

  if (config.headers) {
    config.headers['token'] = GlobalConfig.tokens.access_token
    // if (resourceId) {
    //   config.headers['resource-Id'] = resourceId
    // }
    // if (resourceType) {
    //   config.headers['resource-Type'] = resourceType
    // }
  }
  return config
})

http.useResponseInterceptor((response: FetchResponse) => {
  const { data } = response
  const { success, msg } = data as Res
  if (msg && success === false) {
    AMsg.error(msg)
  }
  return response
})

http.useErrorHandler((err) => {
  //   AMsg.error('network error')
})
export default http
