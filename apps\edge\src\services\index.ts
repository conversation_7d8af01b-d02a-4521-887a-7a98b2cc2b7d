// index.ts
import {
  ExchangeTokenData,
  ExchangeTokenParams,
  RefreshTokenParams,
  Res,
  CutingTask,
  UserInfoData
} from '@/types'
import http from './http' // 导出封装好的http模块
import { FetchResponse } from './Fetch'
import { jsonToUrlParams } from '@/utils'
import sha256 from 'crypto-js/sha256'
import { GlobalConfig } from '@/renderer/src/common'
export * from './explore'

export type HttpRes<T> = Promise<FetchResponse<Res<T>>>

const authBaseUrl = GlobalConfig.authServer

console.log('process.env.NODE_ENV', process.env.NODE_ENV)

// user相关url前缀, 区分dev和build
const userUrlPrefix =
  process.env.NODE_ENV === 'development' ? 'https://m1.apifoxmock.com/m1/6558758-0-default' : ''

export const verifyUser = (): HttpRes<undefined> => {
  return http.get(`/acl/verify`)
}

export function exchangeToken(
  nonceToken: string,
  params: ExchangeTokenParams
): HttpRes<ExchangeTokenData> {
  // http.withCredentials = false
  return http.post(authBaseUrl + '/v2/token', params, {
    headers: {
      csrf_token: nonceToken
    }
  })
}
export function exchangeCode(params: {
  grant_type: string
  redirect_uri: string
  code: string
  scope: string
  client_id: string
  code_verifier: string
}): Promise<
  FetchResponse<{
    access_token: string
  }>
> {
  params.code_verifier = sha256(params.code_verifier) as string
  const str = jsonToUrlParams(params)
  return http.post('https://passport.lenovo.com/v1.0/utility/lenovoid/oauth2/token?' + str)
}
export function refreshToken(
  nonceToken: string,
  params: RefreshTokenParams
): HttpRes<ExchangeTokenData> {
  return http.post(authBaseUrl + '/v2/refresh', params, {
    headers: {
      csrf_token: nonceToken
    }
  })
}
export function getNonceToken(): HttpRes<string> {
  return http.get(authBaseUrl + '/v2/nonce', {})
}
export function getLoginUrl(clientId: string): string {
  // const clientId = '043af4c56d82afaa21e0cc34b64cea38de110e4effb64b9db480423b032a2ac6'
  const redirectUrl = (window as any).electron ? 'ainow.row://' : 'http://localhost:5173' // todo
  const host = 'https://passport.lenovo.com' //TODO:处理跳转地址'
  return (
    host +
    `/v1.0/utility/lenovoid/oauth2/authorize?client_id=${clientId}&response_type=code&redirect_uri=${redirectUrl}&scope=openid&code_challenge_method=S256`
  )
}

export function getAgentList(): HttpRes<string> {
  return http.get('/agent/list')
}

// 获取切片任务列表数据
export function getCutTaskListData(params: {
  knowledgeId: string
  knowledgeBaseId: string
}): HttpRes<CutingTask[]> {
  const { knowledgeId, knowledgeBaseId } = params
  return http.get(`${GlobalConfig.kbFileServer}/api/v1/document/getCutTasks`, {
    headers: {
      'resource-Id': knowledgeBaseId,
      'resource-Type': '2'
    },
    params: {
      id: knowledgeId
    }
  })
}

export const chatInterface = (params) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    Accept: 'text/event-stream'
  }

  if (params.resourceId) {
    headers['resource-Id'] = params.resourceId
    headers['resource-Type'] = '1'
  }

  return http.stream(`${GlobalConfig.modelServer}/execute2`, {
    method: 'POST',
    data: params,
    headers,
    signal: params.signal
  })
}

export const getUserId = (): HttpRes<string> => {
  return http.get('/acl/getUserId')
}

export const getFileView = (id: string) => {
  return http.get(`${GlobalConfig.kbFileServer}/api/v1/document/viewDocument?documentId=${id}`)
}

export const postRegisterChat = (id: string) => {
  return http.post(`${GlobalConfig.kbFileServer}/api/v1/knowledge/registerChat`, {
    sessionId: id
  })
}

// 获取用户信息
export const getUserInfo = (): HttpRes<UserInfoData> => {
  return http.get('/acl/get_current')
}
