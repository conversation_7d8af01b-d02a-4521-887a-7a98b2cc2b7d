import { FetchResponse } from '@/services/Fetch'

export interface ExchangeTokenParams {
  client_key: string
  device_info: {
    device_id: string
    product_id: string
  }
  lenovo_id_info: {
    access_token: string
    realm: string
  }
}
export interface RefreshTokenParams {
  client_key: string
  refresh_token: string
}
export interface Res<T = undefined> {
  code: number | string
  msg: string
  data?: T
  success?: boolean
}
export interface ExchangeTokenData {
  access_token: string
  refresh_token: string
  refresh_expire_in: string
  lenovo_username: string
  lenovo_id: string
  expire_in: string
}
export interface UserListItem {
  approved: boolean
  createTime: string
  id: string
  lenovoId: string
  lenovoIdUsername: string
  updateTime: string
  userId: string
  workspaceId: string
}
export enum LoginStatus {
  ADMIN,
  NO_LOGIN,
  USER,
  ERR
}
export interface AgentVo {
  id: number
  createTime: string
  updateTime: string
  agentId: string
  entityId: string
  origin: string
  agentUrl: string
  workspaceId: string
  agentName: string
  agentIcon: string
  agentDesc: string
  createUserId: string
}
export interface CutingTask {
  createdAt: string
  documentId: string
  documentName: string
  knowledgeId: string
  knowledgeName: string
  status: string
  updatedAt: string

  // 手动添加
  name: string
  taskType: 'cut'
  cutStatus: string
  fileId: string
}

export type HttpRes<T = undefined> = Promise<FetchResponse<Res<T>>>

export interface AccountRecord {
  createTime: string
  id: number
  kbCount: string
  agentCount: string
  nickName: string
  role: string // Super Administrator/Administrator/Member
  status: string
  updateTime: string
  userId: string
  userName: string
}

export interface AccountListData {
  current: number
  pages: number
  records: AccountRecord[]
  size: number
  total: number
}

export interface UserInfoData {
  agentCount: number
  cteateTime: string
  id: number
  kbCount: number
  nickName: string
  role: string
  status: string
  updateTime: string
  userId: string
  userName: string
}

export interface Authorized {
  canSelectPermissionList?: number[]
  groupId?: number
  groupName?: string
  permission?: number // 1位可读，2位可写，3为可管理，4位owner
  resourceName: string
  resourceId?: string
  resourceType?: number
}

export type AuthorizedData = Authorized[]

// KnowledgeData
export interface KbItem {
  displayId: string
  displayName: string
  displayType: 'GROUP' | 'KB'
  auditFlag: string
  createTime: string
  createUser: {
    nickName: string
    userId: string
    userName: string
  }
  documentCount: number
  entityId: string
  id: string
  knowledgeBaseDesc: string
  knowledgeBaseIcon: string
  knowledgeBaseId: string
  knowledgeBaseName: string
  knowledgeBaseType: string
  origin: string
  permission: number
  relatedAgentList: string
  tags: string
  updateTime: string
}
export interface KnowledgeRecord extends KbItem {
  groupId?: string
  groupName?: string
  kbList?: KbItem[]
}

export interface KnowledgeData {
  privateKb: KbItem
  teamKbList: KnowledgeRecord[]
}

// GroupListData
export interface GroupItem {
  id: number
  groupName: string
}

export type GroupListData = GroupItem[]
