import { IpcRendererEvent } from 'electron'
export * from './Service'
export * from './Auth'
// import { Electron } from 'electron'
export interface IPCRes<T = undefined> {
  success: boolean
  res?: T
  timestamp: number
}
export interface Bounds {
  x: number
  y: number
  width: number
  height: number
}
export interface Rect {
  originX: number
  originY: number
  maxX: number
  maxY: number
}
export type Size = Pick<Bounds, 'width' | 'height'>
export type Pos = Pick<Bounds, 'x' | 'y'>
export interface CustomApi {
  setIgnoreMouseEvents: (ignore: boolean, option?: { forward: boolean }) => void
  openWebSite: (url: string) => Promise<boolean>
  toLoginWithCode: (
    cb: (e: Electron.IpcRendererEvent, p: { code: string; scope: string }) => void
  ) => void
  // onMaximize: (cb: (e: Electron.IpcRendererEvent, msg: boolean) => void) => void
  getConfig: (needUpdate?: boolean) => Promise<Record<string, unknown>>
  handleMoveWin: (bounds: Bounds) => void
  sendMiniMsg: (param: Record<string, unknown>) => Promise<boolean>
  translateMiniMsg: (param: Record<string, unknown>) => Promise<boolean>
  getWinState: () => Promise<WinState>
  openSystemSettings: (param: string) => Promise<boolean>
  openApp: (path: string) => Promise<boolean>
  openProgram: (path: string, args: string) => Promise<boolean>
  setBounds: (bounds: Partial<Bounds>) => void
  handleLogin: () => Promise<string>
  windowMove: (canMoving: boolean) => void
  search: (keyword: string) => Promise<ListItem[]>
  setWindowBlur: () => void
  winMax: () => void
  winMin: () => void
  winClose: () => void
  winRestore: () => void
  onVisibleFocus: (arg) => void
  printToPdf: () => Promise<Buffer>
  showSaveDialog: (
    options: Electron.SaveDialogOptions
  ) => Promise<{ filePath?: string; canceled: boolean }>
  savePdf: (data: { buffer: Buffer; path: string }) => Promise<void>
  dropFiles: (options) => Promise<void>
  saveFile: (fileName: string, blob: Blob) => Promise<void>
}

export interface FloatingApi {
  sendRightClick: () => void
  openSecondaryWindow: () => void
  windowMoveDrag: (param: boolean) => void
  hideWindow: () => void
  selectMuenItem: (args: IpcRendererEvent, param?: string) => void
  openAinow: () => void
}

declare global {
  interface Window {
    api: CustomApi & FloatingApi
  }
}
// 菜单列表可以执行的操作类型
export enum ACTION_TYPE {
  URL = 'Website',
  MINIASK = 'miniask',
  MINITRANSLATE = 'minitranslate',
  APP = 'app',
  SETTING = 'setting',
  BING = 'bing'
}
// 搜索列表每条数据的结构
export interface ListItem {
  id: string | number
  icon?: string
  getIcon?: string
  command: string
  name: string
  type: string
  [key: string]: unknown
}
//向ainow小窗提问的类型  本地search--0 本地ask--1 Explain--2  Translate--3 Polish--4 Summarize--5 Cloud提问--6
export enum ASK_TYPE {
  SEARCH = 0,
  ASK,
  EXPLAIN,
  TRANSLATE,
  POLISH,
  SUMMARIZE,
  CLOUD_ASK
}
// export enum SearchType {
//   APP = 'app',
//   webSite = 'web-site',
//   config = 'config'
// }

export interface SearchItem {
  [key: string]: unknown
  name: string
  type: ACTION_TYPE
  command: string
  icon?: string
  getIcon?: () => Promise<Electron.NativeImage>
  keywords: string[]
  id: string
}
export enum WinState {
  Max = 'max',
  Min = 'min',
  Normal = 'normal'
}
