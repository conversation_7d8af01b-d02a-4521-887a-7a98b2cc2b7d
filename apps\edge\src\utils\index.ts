// import Logger from '../main/LogService/log'
import fs from 'fs'
import path from 'path'
// import { <PERSON><PERSON>erWindow, screen } from 'electron'
// import logInit from './log'
// import path from 'path'
import { emitter } from './EventBus'

export function getResourcesPath(fullPath: string): string {
  return fullPath.replace('app.asar', 'app.asar.unpacked')
}
export const getCurrentUTCDateStr = () => {
  const currentDate = new Date()
  return (
    currentDate.getFullYear().toString() +
    currentDate.getUTCMonth().toString() +
    currentDate.getUTCDate().toString()
  )
}
export const checkFileExistsSync = (filePath: string) => {
  try {
    fs.statSync(filePath)
    return true
  } catch (error) {
    return false
  }
}
export const createFile = (path: string, initStr: string = '', force = false) => {
  try {
    if (force) {
      fs.writeFileSync(path, initStr)
    } else {
      const isExist = checkFileExistsSync(path)
      if (!isExist) {
        fs.writeFileSync(path, initStr)
      }
    }
  } catch (err) {
    console.error(err)
  }
}
// export const ChineseTest=(str:string)=>{}
export const getFile = async (path: string) => {
  try {
    const data = await fs.promises.readFile(path, 'utf-8')
    return data
  } catch (error) {
    console.log(error)
    return ''
  }
}
export const getSyncFile = (path: string) => {
  try {
    const data = fs.readFileSync(path, 'utf-8')
    return data
  } catch (error) {
    console.log(error)
    return ''
  }
}

export function parseQuery(queryStr: string, options = { multiple: false }) {
  // 分割键值对（处理多个等号情况）
  function splitKeyValue(pair) {
    const eqIndex = pair.indexOf('=')
    if (eqIndex === -1) {
      return [pair, null]
    }
    return [pair.substring(0, eqIndex), pair.substring(eqIndex + 1)]
  }

  // 处理重复键逻辑
  function handleDuplicateKeys(result, key, value, isMultiple) {
    if (!result.hasOwnProperty(key)) {
      result[key] = isMultiple ? [value] : value
      return
    }

    const existing = result[key]
    if (isMultiple) {
      Array.isArray(existing) ? existing.push(value) : (result[key] = [existing, value])
    } else {
      result[key] = value
    }
  }
  const result = {}

  // 处理非字符串或空输入
  if (typeof queryStr !== 'string' || queryStr.trim() === '') {
    return result
  }

  // 移除开头的 ? 或 #
  const query = queryStr.replace(/^[?#]/, '')

  // 分割键值对
  const pairs = query.split('&')

  for (const pair of pairs) {
    let [key, value] = splitKeyValue(pair)

    // 解码处理
    key = decodeURIComponent(key.replace(/\+/g, ' '))
    value = value !== null ? decodeURIComponent(value.replace(/\+/g, ' ')) : ''

    // 处理重复键
    handleDuplicateKeys(result, key, value, options.multiple)
  }

  return result
}
export function jsonToUrlParams(json: Record<string, string | number | boolean>): string {
  const params: string[] = []
  for (const key in json) {
    // if (json.hasOwnProperty(key)) {
    // 对键和值进行 URL 编码
    const encodedKey = encodeURIComponent(key)
    const encodedValue = encodeURIComponent(json[key].toString())
    // 将编码后的键值对添加到数组中
    params.push(`${encodedKey}=${encodedValue}`)
    // }
  }
  // 使用 & 符号将所有键值对组合在一起
  return params.join('&')
}

export function getFilenameFromPath(filePath: string): string {
  return path.basename(filePath)
}

/**
 * resourceId资源ID: 取agent列表里的 agentId 或知识库列表里 knowledgebaseId
 * resourceType 资源类型: 1=agent; 2=kb
 */
export const emitAgentChanged = (
  resourceId: string | number | undefined,
  resourceType: string | undefined
) => {
  emitter.emit('agent-changed', resourceId, resourceType)
}

export const truncateText = (text: string, maxLength: number = 10): string => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}
