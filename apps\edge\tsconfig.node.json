{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": ["electron.vite.config.*", "src/main/**/*", "src/renderer/**/*", "src/preload/**/*","src/types/**/*", "src/config/**/*", "src/utils/**/*", "src/tools/**/*", "electron.vite.config copy.ts","src/services/**/*"],
  "exclude": ["../../packages/msix/**/*", "../../packages/@libs/**/*"],
  "compilerOptions": {
    "noEmit": false,
    "allowImportingTsExtensions": true,
    "composite": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "types": ["electron-vite/node","vite/client"],
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@renderer/*": [
          "src/renderer/src/*"
      ],
      "@main/*": [
        "src/main/*"
      ],
    }
  }
}
