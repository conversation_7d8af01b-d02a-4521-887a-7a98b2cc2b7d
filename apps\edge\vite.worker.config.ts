import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/main/Service/fileSync/core/FileUploadWorkerNative.ts'),
      formats: ['cjs'],
      fileName: () => 'fileUpload.js'
    },
    outDir: 'resources/worker',
    rollupOptions: {
      external: ['node:worker_threads', 'node:fs', 'node:http', 'node:path'],
      output: {
        preserveModules: true,
        indent: true,
        generatedCode: {
          constBindings: true,
          arrowFunctions: true
        }
      }
    },
    target: 'node20',
    minify: false,
    sourcemap: true
  },
  optimizeDeps: {
    disabled: true
  },
  resolve: {
    mainFields: ['module', 'main'],
    conditions: ['node'],
    alias: {
      // 确保使用 node 原生模块
      'node:worker_threads': 'worker_threads',
      'node:fs': 'fs',
      'node:http': 'http',
      'node:path': 'path'
    }
  }
})
