{"name": "lenovoai-now-2.0", "displayName": "Lenovo AI Agent 2.0", "version": "1.0.0", "description": "", "main": "index.js", "engines": {"pnpm": ">=9.0.0"}, "scripts": {"dev": "pnpm build:pkgs && pnpm -r --parallel --filter @ainow/desktop dev", "dev:searchbar": "pnpm build:pkgs && pnpm -r --parallel --filter @ainow/searchbar dev", "dev:web": "pnpm build:pkgs && concurrently \"pnpm --filter @ainow/backend dev\" \"pnpm --filter @ainow/desktop dev:web\"", "dev:edge": "pnpm --filter @ainow/edge dev", "typescript:desktop": "pnpm -r --filter=@ainow/desktop run typecheck", "typescript": "pnpm -r run typecheck", "build:unpack": "pnpm --filter @ainow/edge build:unpack", "build:installer": "pnpm --filter @ainow/edge build:installer", "build": "pnpm -r build", "build:edge": "pnpm --filter @ainow/edge build:unpack", "build:win": "pnpm build:pkgs && pnpm --filter @ainow/desktop build:win", "build:msix": "pnpm --filter @ainow/msix build && pnpm build:pkgs && pnpm --filter @ainow/edge build:msix", "build:pkgs": "concurrently \"pnpm --filter @ainow/types build\" \"pnpm --filter @ainow/shared build\" \"pnpm --filter @ainow/services build\"", "build:web": "pnpm build:pkgs && pnpm --filter @ainow/backend build", "preview:web": "pnpm build:web && cd apps/backend/dist && npm i && node index.js", "build:searchbar": "pnpm build:pkgs && pnpm --filter @ainow/searchbar build:win", "test": "pnpm -r test", "clean": "rimraf node_modules packages/**/node_modules apps/**/node_modules --glob", "init:db": "pnpm --filter @ainow/shared db:migrate && pnpm --filter @ainow/shared db:generate", "postinstall": "node ./scripts/install.js && pnpm init:db", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "workspaces": ["packages/*"], "lint-staged": {"**/src/**/*.{ts,vue}": ["prettier --write"]}, "dependencies": {"chokidar": "^4.0.3", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "vue": "^3.3.4", "vue-router": "4.3.0"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@types/node": "^20.17.17", "@typescript-eslint/eslint-plugin": "^6.4.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^9.0.0", "concurrently": "^9.1.2", "eslint": "^8.52.0", "eslint-config-standard-with-typescript": "^40.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.18.1", "husky": "^9.1.7", "less": "^4.2.0", "lint-staged": "^15.4.3", "prettier": "^3.3.2", "rimraf": "^5.0.0", "typescript": "^5.5.2", "vite": "^4.4.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.8"}}