

.a-btn.ant-btn {
    padding: 16px;
    padding-top: 4px;
    padding-bottom: 4px;
    height: fit-content;
    // border-radius: 8px;
    box-shadow: initial;

    &:disabled{
        opacity: .6;
        border-color: transparent;
    }
    &.ant-btn-primary {
        background-color: var(--primary-color);
        color: #fff;
        &:hover{
            background-color:var(--primary-hover-color);
        }
    }
    &.ant-btn-default{
        // border-color: var(--primary-color);
        color:var(--primary-color); 
        // background-color:transparent;

        border-color: #f2f3f5;
        background-color: #f2f3f5;
    
        &:hover{
            background-color: #E5E7EC;
        }

        &:active {
            background-color: #C9CDD4;
        }
    }

    &.ant-btn-text {
        color: var(--primary-color);
    }

    &.ant-btn-link{
        color: var(--primary-color);

        &:disabled{
            background-color:transparent;
        }
        &:hover{
            background-color: #006AFF13;
        }

    }

    &.ant-btn-bordered {
        border-color: var(--primary-color);
    }

    &:focus-visible{
        outline:1.5px solid var(--btn-focus-outline-color);
        // outline-color: #2A2E89;
    }
    &.ant-btn-sm{
        // padding: 16px;
        padding-top: 2px;
        padding-bottom: 2px;
        height: 28px;
        font-size: 12px;

    }
}             