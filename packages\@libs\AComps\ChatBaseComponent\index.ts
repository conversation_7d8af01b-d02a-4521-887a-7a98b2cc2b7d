import ChatBase from "./pages/index.vue";
import { AnswerType, InputType, QuestionType, WelcomeType, StreamAnswerType, ChatMessageType, ChatStatus, ChatComponentType, SDKResponse, ChatSDK } from "./types";
import { <PERSON><PERSON><PERSON><PERSON>roll<PERSON> } from "./types/ChatClass";
export { ChatB<PERSON>, ChatController, type WelcomeType, type QuestionType, type AnswerType, type InputType, type StreamAnswerType, type ChatMessageType, ChatStatus, ChatComponentType, type SDKResponse, type ChatSDK };