<template>
    <slot name="welcomeSlot"></slot>
    <div :class="'chat-box ' + (chat?.isWelcome ? 'chat-box-flex' : '')">
        <div v-if="chat && chat.chatAction.length > 0" class="chat-box_region" ref="ContainerRef">
            <!-- 遍历 chatAction 并区分类型 -->
            <div style="max-width: 940px;width: 100%;">
                <template v-for="(item, index) in chat.chatAction" :key="index">
                    <div>
                        <!-- 问题部分 -->
                        <div v-if="item.getData().type === ChatComponentType.QUERY" class="chat-box_region_content">
                            <QuestionPage :questionItem="item as Question" class="chat-box_content_query">
                                <template #question="item">
                                    <slot name="questionSlot" :questions="item.questionItem"></slot>
                                </template>
                            </QuestionPage>
                        </div>

                        <!-- 回答部分 -->
                        <div v-else-if="item.getData().type === ChatComponentType.ANSWER"
                            class="chat-box_region_answer">
                            <AnswerCom :answerItem="item as Answer">
                                <template #answer="item">
                                    <slot name="answerSlot" :answers="item"></slot>
                                </template>
                            </AnswerCom>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <InputView class="chat-box_input">
            <template #input>
                <slot name="inputSlot"></slot>
            </template>
        </InputView>
    </div>
</template>

<script setup lang="ts">
import AnswerCom from "./answer/index.vue";
import QuestionPage from "./question/index.vue";
import InputView from "./input/index.vue"
import { Answer, ChatController, Question } from '../types/ChatClass'
import { ChatComponentType } from "../types";
import { onMounted, ref, watchEffect, onUnmounted } from "vue";
import { electronHooks } from '@/renderer/src/electron'

const props = defineProps<{
    chat: ChatController | undefined
}>();
const ContainerRef = ref<HTMLElement | null>(null);
const isScrolling = ref(false);
const isUserScrolling = ref(false);
let scrollTimeout: ReturnType<typeof setTimeout> | null = null;

watchEffect(() => {
    props.chat?.setMessageContainerRef(ContainerRef.value);
    // 当 ContainerRef 更新时重新设置滚动检测
    if (ContainerRef.value) {
        setupScrollDetection();
    }
});
const electronApi = electronHooks()
const setupScrollDetection = () => {
    if (ContainerRef.value) {
        // 移除之前的事件监听器避免重复
        ContainerRef.value.removeEventListener('scroll', handleScroll);
        ContainerRef.value.removeEventListener('mouseenter', handleMouseEnter);
        ContainerRef.value.removeEventListener('mouseleave', handleMouseLeave);
        ContainerRef.value.removeEventListener('wheel', handleWheel);
        
        ContainerRef.value.addEventListener('scroll', handleScroll);
        ContainerRef.value.addEventListener('mouseenter', handleMouseEnter);
        ContainerRef.value.addEventListener('mouseleave', handleMouseLeave);
        ContainerRef.value.addEventListener('wheel', handleWheel);
    }
}

const handleMouseEnter = () => {
    isUserScrolling.value = true;
}

const handleMouseLeave = () => {
    // 延迟重置滚动状态，避免鼠标快速移动时闪烁
    setTimeout(() => {
        isUserScrolling.value = false;
    }, 100);
}

const handleWheel = () => {
    isUserScrolling.value = true;
    // 滚轮操作后保持滚动状态更长时间
    setTimeout(() => {
        isUserScrolling.value = false;
    }, 2000);
}

const handleScroll = () => {
    // 只有用户主动滚动时才显示滚动条
    if (isUserScrolling.value) {
        isScrolling.value = true;
        if (ContainerRef.value) {
            ContainerRef.value.classList.add('scrolling');
        }

        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }

        scrollTimeout = setTimeout(() => {
            isScrolling.value = false;
            if (ContainerRef.value) {
                ContainerRef.value.classList.remove('scrolling');
            }
        }, 800);
    }
}

onMounted(() => {
    document.onclick = event => {
        let target = event.target as HTMLElement;
        while (target && target.tagName !== "A") {
            target = target.parentElement as HTMLElement;
        }
        if (target && target.tagName === "A" && target.hasAttribute("href")) {
            event.preventDefault();
            const href = target.getAttribute("href");
            console.log("链接地址:", href);
            electronApi?.openWebSite(href?.toString() || "")
        }
    };

    setupScrollDetection();
})

onUnmounted(() => {
    if (ContainerRef.value) {
        ContainerRef.value.removeEventListener('scroll', handleScroll);
        ContainerRef.value.removeEventListener('mouseenter', handleMouseEnter);
        ContainerRef.value.removeEventListener('mouseleave', handleMouseLeave);
        ContainerRef.value.removeEventListener('wheel', handleWheel);
    }
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }
})
</script>

<style scoped lang="less">

.chat-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    align-items: center;
    position: relative;
    justify-content: space-between;
    
        &_region {
        width: 100%;
        height: 100%;
        /* max-width: 940px; */
        /* min-width: 378px; */
        min-height: 50%;
        /* flex: 1 1 auto; */
        overflow-y: auto;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;

        &::-webkit-scrollbar {
            width: 6px;
            background: transparent;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        /* 滚动时显示滚动条 */
        &.scrolling::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.3);

            &:hover {
                background: rgba(0, 0, 0, 0.5);
            }
        }
        &_content {
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
            align-content: space-between;
            align-content: flex-end;

            &_query {
                background: rgba(235, 233, 248, 1);
                padding: 8px 24px;
                color: #000;
                font-weight: 400;
                font-size: 14px;
                vertical-align: middle;
                border-top-left-radius: 25px;
                border-bottom-right-radius: 20px;
                border-bottom-left-radius: 25px;
                display: flex;
                width: fit-content;
                word-break: break-all;
                height: 22px;
            }
        }

        &_answer {
            margin-top: 24px;
            width: fit-content;
            max-width: 85%;
            display: flex;
            /* word-break: break-all; */
        }
    }

    &_input {
        width: 100%;
        max-width: 940px;
        // min-width: 378px;
        position: relative;
        /* min-height: 260px; */
        display: flex;
        flex-direction: column;
        margin-bottom: 68px;
    }
}
.chat-box-flex {
    justify-content: center;
    align-items: center;
}
</style>