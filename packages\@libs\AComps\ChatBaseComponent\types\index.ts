import { Answer, Question } from "./ChatClass";

// 欢迎语类型定义
export interface WelcomeType {
    avatar?: string;
    title: string; // 标题
    quickQuestions?: QuickQusetionType; // 快速问答部分

}

// 欢迎语的快速问答类型定义
interface QuickQusetionType {
    title: string;
}

export interface QuestionType {
    id: string;
    content: string;
    timestamp: number;
    type: string;
    files?: Array<{
        name: string;
        path: string;
        fileId: string;
        documentId?: string;
    }>;
}

export interface AnswerType {
    id: string;
    questionId: string;
    content: string;
    timestamp: number;
    type: string;
    queryData?: QuestionType;
    response: string; // 回答
    // regen:boolean
}

export interface InputType {
    content: string;
    type: string;
}

// 返回的流逝答案类型定义
export type StreamAnswerType<T = {}> = {
    status: ChatStatus;
    response: string;
    content: string;
    references: ReferencesType[];
    citations: CitationsType[];
    documentList: DocumentListType[];
    stepMode?: string;
    payload?: T;
    meta:Meta;
};



export type ChatMessageType = Question | Answer;


// 返回的当前流式消息状态枚举
export enum ChatStatus {
    SUCCESS = "success", // 用户请求成功，当前请求结束
    FAILURE = "failure", // 用户请求失败，当前请求结束
    ONGOING = "ongoing",// 用户请求正在进行中，当前请求未结束
    AFU = "afu", // 用户请求需要用户确认，当前请求结束
    START = "start", // 用户请求开始，当前请求未结束
    STOP = "stop", // 用户结束请求
    DONE = "done",//用户结束请求
    FAIL = "fail"//用户结束请求
}

export enum ChatComponentType {
    QUERY = "query",
    ANSWER = "answer",
    INPUT = "input"
}

export interface SDKResponse {
    // 状态，可以根据实际需求扩展状态值
    status: ChatStatus;
    // 返回的消息文本
    response: string;
    stepMode?: string;
    requestId?: string;
    toolResult?: string;
}

export interface ChatSDK {
    streamController: unknown;
    /**
     * 发送消息，返回一个 Promise，该 Promise resolve 时返回 SDKResponse 对象。
     * @param text 消息文本
     */
    sendMessage(text: string, sessionId: string, isDeepThink: boolean, isWebSearch: boolean, regen:boolean,history: historyItemType[], documentIds: Array<string>, knowledgeIds: Array<string>): Promise<SDKResponse>;

    /**
     * 停止消息发送的操作，无返回值。
     */
    stopMessage(): void;
    /**
     * 初始化SDK。
     */
    init(): void;
    /**
     * 消息回调
     */
    setDataCallback(callback: (data: StreamAnswerType) => void): void;
}

export interface docInfoItemType {
    documentID: string;
    documentName: string;
}


export interface historyItemType {
    role: string;
    content: string;
    docInfo?: docInfoItemType[];
}

export interface ReferencesType {
    documentSource: DocumentSource;
    siteName?: string;
    documentCreateTime:string;
    url: string;
    content:string;
    documentName?: string;
    documentId?: string;
    knowledgeId?: string;
}

export interface ToolType {
  toolIndex: string
  references: ReferencesType[]
}
 


export interface Meta {
  isDeepThink: boolean; // 布尔值类型，此处为 false
}
export interface RefKBNameType {
    kbID: string;
    KBName: string;
}
export interface refciticlesNum {
    citicleId:string;
    citicleContent:string;
}

export enum DocumentSource {
    /** Edge Share TKB（公开知识库） */
    Public = "public",

    /** Edge Private KB（私有知识库） */
    Private = "private",

    /** 会话中上传的临时文档 */
    Session = "session",

    /** 网络搜索获取的内容 */
    Network = "network"
}

export interface DocumentListType{
    documentId: string;
    documentName: string;
    knowledgeId: string;
    knowledgeBaseId: string;
    knowledgeName:string;
}

export interface CitationsType{
     citationsId:string;
     referenceItem:ReferencesType
}
