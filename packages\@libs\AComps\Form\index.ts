import { Form, FormItem } from "ant-design-vue";
import { h, defineComponent } from "vue";
import './index.less'
export const AForm = defineComponent({
  render() {
    return h(
      Form,

      {

      },
      this.$slots
    );
  },
});
export const AFormItem = defineComponent({
  render() {
    return h(
      FormItem,

      {
        class: 'a-form-item'
      },
      this.$slots
    );
  },
});
