import { Modal } from 'ant-design-vue'
import { H<PERSON>Hooks } from '../HOC'

const defaultProps = {
    class: 'a-modal',
    wrapClassName: 'a-modal-wrap',
    getContainer: () => document.getElementById('app') || document.body
}

const BaseModal = HOCHooks(Modal, defaultProps)


// 封装静态方法
const modalMethods = {
    info: (config: any) => Modal.info({ ...defaultProps, ...config }),
    success: (config: any) => Modal.success({ ...defaultProps, ...config }),
    error: (config: any) => Modal.error({ ...defaultProps, ...config }),
    warning: (config: any) => Modal.warning({ ...defaultProps, ...config }),
    confirm: (config: any) => Modal.confirm({ ...defaultProps, ...config }),
    destroyAll: Modal.destroyAll
}

export default Object.assign(BaseModal, modalMethods)