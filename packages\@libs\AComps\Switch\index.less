.a-switch{
    height: 20px;
    width: 40px ;
    min-width: 40px ;
    box-sizing: border-box ;
    border-radius: 10px;
    background-color: var( --text-color4);
    &:focus-visible{
        outline:1.5px solid var(--btn-focus-outline-color);
        // outline-color: #2A2E89;
    }
    &:hover:not(.ant-switch-disabled){
        
        background-color: var( --text-color3); 
    }
    &.ant-switch-checked{
        background-color: var( --primary-color);
        &:hover{
            background-color: var( --text-color4 );
    
        }
        .ant-switch-handle{
        inset-inline-start:calc(100% - 18px);

    }
    }
    .ant-switch-handle{
        width: 14px;
        height: 14px;
        top:3px;
        inset-inline-start:4px;
        &::before{

            background-color: var(--bg-color1);
        }
    }
}