/*
 * @Description: 
 * @FilePath: \ainow-ui\packages\@libs\AComps\Table\index.less
 */
.ant-table {

  .ant-table-thead > tr > th {
    background-color: initial;
  }

  .ant-table-thead th.ant-table-column-sort {
    background-color: initial;
  }

  td.ant-table-column-sort {
    background-color: initial;
  }

  .ant-table-column-sorter-up,
  .ant-table-column-sorter-down {
    font-size: 9px;
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: var(--primary-color-bg);
  }

  .ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background-color: var(--primary-color-bg);
  }
}