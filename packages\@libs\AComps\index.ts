// import './index.module.less'
import Button from './Button'
import Input from './Input'
import Modal from './Modal'
import { AFormItem, AForm } from './Form'
import ATable from "./Table";
import ASelect from "./Select";
import { ATabs, ATabPane } from "./Tabs";
import ADropdown from './Dropdown';
import AConfigProvider from "./ConfigProvider";
import APopover from "./Popover";
import { ACheckboxGroup, ACheckbox } from './Checkbox'
import APagination from './Pagination';
import ATextarea from './Textarea';
import AMessage, { msg } from './Message';
import { AMenu, AMenuItem } from './Menu'
import ASpin from './Spin'
import ABadage from './Badge';
import ATooltip from './Tooltip';
import ASwitch from './Switch';
import { THEME, ThemeData } from './type';
import { CommonTheme, DarkTheme } from './themeData';
import { Chat<PERSON><PERSON>, ChatController, type WelcomeType, type QuestionType, type AnswerType, type InputType, type StreamAnswerType, type ChatMessageType, ChatStatus, ChatComponentType, type SDKResponse, type ChatSDK } from './ChatBaseComponent';
export * from 'ant-design-vue'
export * from './type'
export {
    ASwitch,
    APopover,
    AMenu,
    AMenuItem,
    Button as ABtn,
    Input as AInput,
    Modal as AModal,
    ATable,
    ASelect,
    ATabs,
    ATabPane,
    AFormItem,
    AForm,
    AConfigProvider,
    ACheckboxGroup, ACheckbox,
    ADropdown,
    APagination,
    ATextarea,
    AMessage,
    msg as AMsg,
    ASpin,
    ABadage,
    ATooltip,
    

    ChatBase, ChatController, type WelcomeType, type QuestionType, type AnswerType, type InputType, type StreamAnswerType, type ChatMessageType, ChatStatus, ChatComponentType, type SDKResponse, type ChatSDK
}

export const themeHooks = (customThemeData?: Record<string, ThemeData>) => {
    // const CustomTheme = Object.keys(customThemeData || {})
    const getThemeData = (theme: string | THEME = THEME.COMMON) => {
        if (theme === THEME.DARK) {
            return DarkTheme
        } else if (customThemeData && customThemeData[theme]) {
            return customThemeData[theme]
        }
        return CommonTheme


    }

    const setTheme = (theme: string | THEME = THEME.COMMON, el?: HTMLElement) => {
        const rootStyles = el?.style || document.documentElement.style

        const themeData = getThemeData(theme)
        for (const key in themeData) {
            rootStyles.setProperty(key, themeData[key as keyof typeof themeData])
        }
    }
    const getCurrentVars = (el: HTMLElement = document.documentElement) => {

        const rootStyles = el.style

        const variables = {} as Record<string, string>;

        for (const prop of rootStyles) {

            if (prop.startsWith('--')) {
                variables[prop] = rootStyles.getPropertyValue(prop).trim()

            }
        }

        return variables;


    }
    return {
        setTheme, getThemeData, getCurrentVars
    }
}
const { setTheme } = themeHooks()
setTheme(THEME.COMMON)