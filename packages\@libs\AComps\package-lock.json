{"name": "@libs/a-comps", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@libs/a-comps", "version": "1.0.0", "license": "ISC", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "4.x", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/markdown-it": "^14.1.2", "less": "^4.2.0", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^2.2.8"}}, "../../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@5.8.3_/node_modules/@ant-design/icons-vue": {"version": "7.0.1", "license": "MIT", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-svg": "^4.2.1"}, "devDependencies": {"@ant-design-vue/tools": "^3.1.2", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-export-default-from": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-transform-member-expression-literals": "^7.14.5", "@babel/plugin-transform-object-assign": "^7.14.5", "@babel/plugin-transform-property-literals": "^7.14.5", "@babel/plugin-transform-spread": "^7.14.6", "@babel/plugin-transform-template-literals": "^7.14.5", "@babel/runtime": "^7.11.2", "@types/jest": "^24.9.1", "@types/lodash": "^4.14.165", "@types/node": "^13.13.15", "@typescript-eslint/eslint-plugin": "^4.14.0", "@typescript-eslint/parser": "^4.14.0", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/cli-plugin-babel": "^5.0.0-0", "@vue/cli-plugin-eslint": "^5.0.0-0", "@vue/cli-plugin-typescript": "^5.0.0-0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.3", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "@vue/test-utils": "^2.0.0-beta.2", "core-js": "^3.32.2", "cross-env": "^7.0.3", "eslint": "^7.16.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest": "^24.4.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0", "gulp-less": "^5.0.0", "jest": "^26.0.0", "jest-serializer-vue": "^2.0.2", "lodash": "^4.17.15", "prettier": "^1.19.1", "ts-node": "^10.1.0", "typescript": "^4.2.0", "vue": "^3.0.3", "vue-jest": "^5.0.0-alpha.10"}, "peerDependencies": {"vue": ">=3.0.3"}}, "../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue": {"version": "4.2.6", "license": "MIT", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^7.0.0", "@babel/runtime": "^7.10.5", "@ctrl/tinycolor": "^3.5.0", "@emotion/hash": "^0.9.0", "@emotion/unitless": "^0.8.0", "@simonwep/pickr": "~1.8.0", "array-tree-filter": "^2.1.0", "async-validator": "^4.0.0", "csstype": "^3.1.1", "dayjs": "^1.10.5", "dom-align": "^1.12.1", "dom-scroll-into-view": "^2.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.15", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.25", "shallow-equal": "^1.0.0", "stylis": "^4.1.3", "throttle-debounce": "^5.0.0", "vue-types": "^3.0.0", "warning": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.10.5", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-export-default-from": "^7.8.3", "@babel/plugin-proposal-export-namespace-from": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.10.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-member-expression-literals": "^7.8.3", "@babel/plugin-transform-object-assign": "^7.8.3", "@babel/plugin-transform-property-literals": "^7.8.3", "@babel/plugin-transform-runtime": "^7.10.5", "@babel/plugin-transform-template-literals": "^7.8.3", "@babel/plugin-transform-typescript": "^7.12.1", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.10.4", "@commitlint/cli": "^12.0.0", "@commitlint/config-conventional": "^12.0.0", "@octokit/rest": "^18.0.0", "@rollup/plugin-babel": "^5.3.0", "@types/compression": "^1.7.0", "@types/fs-extra": "^9.0.8", "@types/jest": "^28.1.4", "@types/koa": "^2.11.6", "@types/lodash-es": "^4.17.3", "@types/lru-cache": "^5.1.0", "@types/markdown-it": "^10.0.2", "@types/node": "^14.0.0", "@types/postcss-load-config": "^2.0.1", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-legacy": "^2.2.0", "@vitejs/plugin-vue": "^3.0.0", "@vitejs/plugin-vue-jsx": "^2.0.0", "@vue/babel-plugin-jsx": "^1.0.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.0.2", "@vue/vue3-jest": "28", "@vueuse/core": "^8.3.1", "@webpack-cli/serve": "^1.3.1", "acorn": "^8.0.0", "ali-oss": "^6.16.0", "autoprefixer": "^10.2.0", "axios": "^0.22.0", "babel-eslint": "^10.0.1", "babel-jest": "^28.1.2", "babel-loader": "^8.0.0", "babel-plugin-import": "^1.1.1", "babel-plugin-inline-import-data-uri": "^1.0.1", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-transform-require-context": "^0.1.1", "case-sensitive-paths-webpack-plugin": "^2.1.2", "chalk": "^4.1.1", "cheerio": "^1.0.0-rc.2", "codecov": "^3.0.0", "codesandbox": "^2.2.3", "colorful": "^2.1.0", "commander": "^6.1.0", "compare-versions": "^3.3.0", "cross-env": "^7.0.0", "css-loader": "^5.0.0", "css-minimizer-webpack-plugin": "^3.0.0", "cz-git": "^1.3.8", "date-fns": "^2.24.0", "diacritics": "^1.3.0", "docsearch.js": "^2.6.3", "duplicate-package-checker-webpack-plugin": "^3.0.0", "enquire-js": "^0.2.1", "esbuild": "~0.12.29", "esbuild-loader": "^3.0.0", "escape-html": "^1.0.3", "eslint": "^8.3.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-html": "^6.0.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jest": "^26.0.0", "eslint-plugin-markdown": "^2.0.0", "eslint-plugin-no-explicit-type-exports": "^0.12.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^9.17.0", "fast-glob": "^3.2.7", "fetch-jsonp": "^1.1.3", "fs-extra": "^10.0.0", "glob": "^7.1.2", "globby": "^11.1.0", "gray-matter": "^4.0.3", "gulp": "^4.0.1", "gulp-babel": "^8.0.0", "gulp-strip-code": "^0.1.4", "gulp-typescript": "^6.0.0-alpha.1", "html-webpack-plugin": "^5.3.1", "husky": "^6.0.0", "ignore-emit-webpack-plugin": "^2.0.6", "is-windows": "^1.0.2", "jest": "^28.1.2", "jest-environment-jsdom": "^28.0.0", "jest-environment-node": "^28.0.2", "jest-serializer-vue": "^2.0.0", "jest-transform-stub": "^2.0.0", "js-base64": "^3.0.0", "json-templater": "^1.2.0", "jsonp": "^0.2.1", "less": "^4.0.0", "less-loader": "^10.0.0", "less-plugin-npm-import": "^2.1.0", "less-vars-to-js": "^1.3.0", "lint-staged": "^11.0.0", "majo": "^0.10.1", "markdown-it": "^8.4.2", "markdown-it-anchor": "^8.0.4", "markdown-it-container": "^3.0.0", "markdown-it-emoji": "^2.0.0", "markdown-it-table-of-contents": "^0.5.2", "marked": "0.3.18", "merge2": "^1.2.1", "mini-css-extract-plugin": "^2.4.5", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "mockdate": "^2.0.2", "moment": "^2.29.1", "nprogress": "^0.2.0", "postcss": "^8.2.12", "postcss-loader": "^6.0.0", "prettier": "^2.2.0", "pretty-quick": "^3.0.0", "prismjs": "^1.23.0", "progress": "^2.0.3", "raw-loader": "^4.0.2", "remark-frontmatter": "^2.0.0", "remark-parse": "^8.0.0", "remark-stringify": "^8.0.0", "remark-yaml-config": "^4.1.0", "remove-files-webpack-plugin": "^1.5.0", "reqwest": "^2.0.5", "rimraf": "^3.0.0", "rucksack-css": "^1.0.2", "selenium-server": "^3.0.1", "semver": "^7.0.0", "slash": "^3.0.0", "string-replace-loader": "^3.1.0", "style-loader": "^3.0.0", "stylelint": "^14.0.0", "stylelint-config-prettier": "^9.0.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^25.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^5.0.0", "terser-webpack-plugin": "^5.1.1", "through2": "^3.0.0", "tinycolor2": "^1.6.0", "ts-jest": "^28.0.5", "ts-loader": "^9.1.0", "tsx": "^3.12.10", "typedoc": "^0.23.25", "typescript": "~4.9.3", "umi-request": "^1.3.5", "unified": "9.2.2", "url-loader": "^3.0.0", "vanilla-jsoneditor": "^0.15.1", "vite": "^3.0.0", "vue": "^3.2.0", "vue-clipboard2": "0.3.3", "vue-drag-resize": "^2.0.3", "vue-eslint-parser": "^9.3.1", "vue-i18n": "^9.1.7", "vue-infinite-scroll": "^2.0.2", "vue-loader": "^17.0.0", "vue-request": "^1.0.2", "vue-router": "^4.0.0", "vue-style-loader": "^4.1.2", "vue-tsc": "^1.0.6", "vuex": "^4.0.0", "webpack": "^5.0.0", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "^4.6.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.0.0", "webpackbar": "^5.0.2", "xhr-mock": "^2.5.1"}, "engines": {"node": ">=12.22.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design-vue"}, "peerDependencies": {"vue": ">=3.2.0"}}, "../../../node_modules/.pnpm/less@4.3.0/node_modules/less": {"version": "4.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0"}, "bin": {"lessc": "bin/lessc"}, "devDependencies": {"@less/test-data": "4.3.0", "@less/test-import-module": "4.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.6.0", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "playwright": "1.50.1", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^10.9.1", "typescript": "^4.3.4", "uikit": "2.27.4"}, "engines": {"node": ">=14"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}}, "../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "../../../node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it": {"version": "14.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1", "entities": "^4.4.0", "linkify-it": "^5.0.0", "mdurl": "^2.0.0", "punycode.js": "^2.3.1", "uc.micro": "^2.1.0"}, "bin": {"markdown-it": "bin/markdown-it.mjs"}, "devDependencies": {"@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "ansi": "^0.3.0", "benchmark": "~2.1.0", "c8": "^8.0.1", "chai": "^4.2.0", "eslint": "^8.4.1", "eslint-config-standard": "^17.1.0", "express": "^4.14.0", "gh-pages": "^6.1.0", "highlight.js": "^11.9.0", "jest-worker": "^29.7.0", "markdown-it-abbr": "^2.0.0", "markdown-it-container": "^4.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-for-inline": "^2.0.1", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-testgen": "^0.1.3", "mocha": "^10.2.0", "ndoc": "^6.0.0", "needle": "^3.0.0", "rollup": "^4.5.0", "shelljs": "^0.8.4", "supertest": "^6.0.1"}}, "../../../node_modules/.pnpm/uuid@11.1.0/node_modules/uuid": {"version": "11.1.0", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.0.4", "typescript-eslint": "8.18.2"}}, "../../../node_modules/.pnpm/vite-plugin-eslint@1.8.1_eslint@8.57.1_vite@5.4.17_@types+node@20.17.30_less@4.3.0_lightningcss@1.29.2_terser@5.39.0_/node_modules/vite-plugin-eslint": {"version": "1.8.1", "dev": true, "license": "MIT", "dependencies": {"@rollup/pluginutils": "^4.2.1", "@types/eslint": "^8.4.5", "rollup": "^2.77.2"}, "devDependencies": {"@types/node": "^18.0.6", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.7.1", "standard-version": "^9.5.0", "tsup": "^6.2.1", "typescript": "^4.7.4", "vite": "^3.0.8"}, "peerDependencies": {"eslint": ">=7", "vite": ">=2"}}, "../../../node_modules/.pnpm/vue-tsc@2.2.8_typescript@5.8.3/node_modules/vue-tsc": {"version": "2.2.8", "dev": true, "license": "MIT", "dependencies": {"@volar/typescript": "~2.4.11", "@vue/language-core": "2.2.8"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "devDependencies": {"@types/node": "^22.10.4"}, "peerDependencies": {"typescript": ">=5.0.0"}}, "node_modules/@ant-design/icons-vue": {"resolved": "../../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.13_typescript@5.8.3_/node_modules/@ant-design/icons-vue", "link": true}, "node_modules/@types/linkify-it": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/@types/linkify-it/-/linkify-it-5.0.0.tgz", "integrity": "sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==", "dev": true}, "node_modules/@types/markdown-it": {"version": "14.1.2", "resolved": "https://registry.npmmirror.com/@types/markdown-it/-/markdown-it-14.1.2.tgz", "integrity": "sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==", "dev": true, "dependencies": {"@types/linkify-it": "^5", "@types/mdurl": "^2"}}, "node_modules/@types/mdurl": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/@types/mdurl/-/mdurl-2.0.0.tgz", "integrity": "sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==", "dev": true}, "node_modules/ant-design-vue": {"resolved": "../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.13_typescript@5.8.3_/node_modules/ant-design-vue", "link": true}, "node_modules/less": {"resolved": "../../../node_modules/.pnpm/less@4.3.0/node_modules/less", "link": true}, "node_modules/lodash-es": {"resolved": "../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es", "link": true}, "node_modules/markdown-it": {"resolved": "../../../node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it", "link": true}, "node_modules/uuid": {"resolved": "../../../node_modules/.pnpm/uuid@11.1.0/node_modules/uuid", "link": true}, "node_modules/vite-plugin-eslint": {"resolved": "../../../node_modules/.pnpm/vite-plugin-eslint@1.8.1_eslint@8.57.1_vite@5.4.17_@types+node@20.17.30_less@4.3.0_lightningcss@1.29.2_terser@5.39.0_/node_modules/vite-plugin-eslint", "link": true}, "node_modules/vue-tsc": {"resolved": "../../../node_modules/.pnpm/vue-tsc@2.2.8_typescript@5.8.3/node_modules/vue-tsc", "link": true}}}