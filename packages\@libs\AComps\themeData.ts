import { ThemeData } from "./type"
// import vars from "./index.module.less"
// console.log(vars);


export const CommonTheme: ThemeData = {
    "--hover-color": " rgba(141, 100, 221, 0.05)",
    "--select-color": " #d9e6ff",
    "--chat-bg": " #f4f9ff",
    "--chat-pkb-bg": " #f2f7ff",
    "--br-color": " #eaeaea",
    "--color-error": " #ea1313",
    "--color-error-bg": " #FEF2F2",
    // "--br-color": " #eaeaea",
    "--primary-color": " #6441AB",
    "--primary-color-bg": " #F5F2FE",
    "--primary-hover-color": "#3C2766",
    "--text-color1": " #18181B",
    "--text-color2": " #3F3F46",
    "--text-color3": " #52525B",
    "--text-color4": " #696969",
    "--text-color5": " #D4D4D8",
    "--bg-color1": " #FFFFFF",
    "--bg-color2": " #F4F4F5",
    "--font-family": 'Segoe UI',
    "--bg-menu": "linear-gradient(121.75deg, rgba(241, 242, 255, 0.98) 0.51%, rgba(247, 249, 255, 0.98) 40.31%, rgba(234, 241, 255, 0.98) 100%)",
    "--wireframe-color1": " #E4E4E7",
    "--btn-focus-outline-color": " #6441AB ",
}
export const DarkTheme: ThemeData = {
    "--color-error-bg": " #FEF2F2",
    "--font-family": 'Segoe UI',
    "--bg-menu": "linear-gradient(121.75deg, rgba(241, 242, 255, 0.98) 0.51%, rgba(247, 249, 255, 0.98) 40.31%, rgba(234, 241, 255, 0.98) 100%)",
    "--hover-color": " #edf5ff",
    "--select-color": " #d9e6ff",
    "--chat-bg": " #000",
    "--chat-pkb-bg": " #f2f7ff",
    "--br-color": " #eaeaea",
    "--color-error": " #ea1313",


    "--primary-color": " #82A6FF",
    "--primary-hover-color": "#719AFF",
    "--text-color1": " #F4F4F5",
    "--text-color2": " #E4E4E7",
    "--text-color3": " #D4D4D8",
    "--text-color4": " #A9A9B2",
    "--text-color5": "#6E6E78",
    "--bg-color1": " #393C49",
    "--bg-color2": " #3F3F46 ",
    "--wireframe-color1": " #52525B ",
    "--btn-focus-outline-color": " #E5EFFF",
}