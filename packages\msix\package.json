{"name": "@ainow/msix", "version": "1.1.7", "private": true, "description": "MSIX packager for Electron Apps", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"prepublish": "npm run build", "build": "tsc", "test": "tsc & node ./test/testing.js"}, "keywords": ["MSIX", "packager", "Electron", "installer"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/minimatch": "5.1.2", "minimatch": "5.1.6", "typescript": "^5.3.3"}, "dependencies": {"chalk": "^3.0.0", "fs-extra": "^11.2.0", "iconv-lite": "^0.6.3"}}