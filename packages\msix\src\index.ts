import { make, pri, priConfig, sign, run } from './msix'
import { PackagingOptions, ProgramOptions } from './types'
import {
  createLayout,
  getManifestVariables,
  makeProgramOptions,
  setLogLevel,
  verifyOptions
} from './utils'

export const packageMSIX = async (options: PackagingOptions) => {
  setLogLevel(options)
  const manifestVars = await getManifestVariables(options)
  await verifyOptions(options, manifestVars)
  const program: ProgramOptions = await makeProgramOptions(options, manifestVars)
  // console.log('sign1')
  if (program.sign && typeof options.signCallback === 'function') {
    try {
      await options.signCallback(program, run)
    } catch (ex) {
      console.log(ex)
    }
  }
  // console.log('sign2')
  await createLayout(program)
  await priConfig(program)
  await pri(program)
  await make(program)
  if (program.sign) {
    await sign(program)
  }
  // console.log('signed ===')
}
