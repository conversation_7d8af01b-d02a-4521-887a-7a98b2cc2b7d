<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
         xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
         xmlns:uap2="http://schemas.microsoft.com/appx/manifest/uap/windows10/2"
         xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
         xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
         xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
         xmlns:uap10="http://schemas.microsoft.com/appx/manifest/uap/windows10/10"
         IgnorableNamespaces="uap uap2 uap3 rescap desktop uap10">
    <Identity Name="Electron.20.0.Sparse"
              Publisher="CN=Electron"
              ProcessorArchitecture="x64"
              Version="1.0.0.0" />
    <Properties>
        <DisplayName>Electron Sparse App</DisplayName>
        <PublisherDisplayName>Electron Sparse</PublisherDisplayName>
        <Logo>assets\StoreLogo.png</Logo>
        <uap10:AllowExternalContent>true</uap10:AllowExternalContent>
    </Properties>
    <Dependencies>
        <TargetDeviceFamily Name="Windows.Desktop"
                            MinVersion="10.0.19041.0"
                            MaxVersionTested="10.0.19041.0" />
    </Dependencies>
    <Resources>
        <Resource Language="en-us" />
    </Resources>
    <Applications>
        <Application Id="Electron"
                     Executable="Electron.exe"
                     uap10:TrustLevel="mediumIL"
                     uap10:RuntimeBehavior="win32App">
            <uap:VisualElements DisplayName="Electron Sparse App"
                                Description="Electron running with Identity"
                                BackgroundColor="transparent"
                                Square150x150Logo="assets\Square150x150Logo.png"
                                Square44x44Logo="assets\Square44x44Logo.png">
                <uap:DefaultTile Wide310x150Logo="assets\Wide310x150Logo.png" />
            </uap:VisualElements>
        </Application>
    </Applications>
    <Capabilities>
        <Capability Name="internetClient" />
        <rescap:Capability Name="runFullTrust" />
        <rescap:Capability Name="unvirtualizedResources" />
    </Capabilities>
</Package>