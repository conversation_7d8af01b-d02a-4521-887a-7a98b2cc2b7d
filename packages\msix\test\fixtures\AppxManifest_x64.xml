<?xml version="1.0" encoding="utf-8"?>
<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
  xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3"
  xmlns:desktop2="http://schemas.microsoft.com/appx/manifest/desktop/windows10/2"
  IgnorableNamespaces="uap uap3 desktop2">
<Identity Name="Electron.MySuite.HelloMSIX"
          ProcessorArchitecture="x64"
          Version="1.0.0.0"
          Publisher="CN=Electron"/>
  <Properties>
    <DisplayName>HelloMSIX App</DisplayName>
    <PublisherDisplayName>Electron</PublisherDisplayName>
    <Logo>assets\icon.png</Logo>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.22000.0" MaxVersionTested="10.0.22000.0" />
  </Dependencies>
  <Resources>
    <Resource Language="en-US" />
  </Resources>
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
    <Capability Name="internetClient" />
  </Capabilities>
  <Applications>
    <Application Id="HelloMSIX"  Executable="app\hellomsix.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements
          DisplayName="Hello MSIX"
          Description="Say hello to MSIX"
          Square44x44Logo="assets\Square44x44Logo.png"
          Square150x150Logo="assets\Square150x150Logo.png"
          BackgroundColor="transparent">
      </uap:VisualElements>
    </Application>
  </Applications>
</Package>
