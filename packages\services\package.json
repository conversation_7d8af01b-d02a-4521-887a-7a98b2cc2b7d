{"name": "@ainow/services", "version": "1.0.0", "private": true, "main": "dist/index.js", "scripts": {"dev": "tsup src/index.ts --format cjs,esm --dts --sourcemap --watch", "build": "npm run clean && tsup src/index.ts --format cjs,esm --dts --sourcemap && npm run copy-assets", "copy-assets": "copyfiles -u 1 \"assets/**/*\" dist/assets", "clean": "<PERSON><PERSON><PERSON> dist", "postinstall": "pnpm run build"}, "devDependencies": {"@langchain/core": "^0.3.39", "@langchain/ollama": "^0.1.5", "@types/node": "^20.14.8", "copyfiles": "^2.4.1", "langchain": "^0.3.15", "langsmith": "^0.3.7", "nodemon": "^3.1.9", "rimraf": "^5.0.0", "ts-node": "^10.9.2", "tsup": "^8.0.0"}, "dependencies": {"@langchain/openai": "^0.4.3", "typescript": "^5.3.3", "@ainow/types": "workspace:*"}}