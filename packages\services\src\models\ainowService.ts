import { ChatReq, ChatRes } from '@ainow/types'
import { resolve, join } from 'path'

let AinowPlugIn: any = null

const ainowInit = async () => {
  const isDev = !__dirname.includes('app.asar')
  // @ts-ignore
  // console.log('isDev:', __dirname, isDev, process.resourcesPath)
  const toolPath =
    process.env.AINOW_PLUGIN_DIR ||
    (isDev
      ? resolve(__dirname, './assets/tools')
      : // @ts-ignore
        join(process.resourcesPath, 'tools'))
  const pluginPath = `${toolPath}/ainow.node`
  // const test = resolve(process.env.RES_DIR, `models`)
  // console.log('pluginPath:', pluginPath, toolPath, __dirname, process.env)
  AinowPlugIn = require(pluginPath)

  try {
    const ret = AinowPlugIn.install(toolPath)
    console.log('ainowInit ret:', ret)
  } catch (e) {
    console.log(e, 'err')
  }
}

export const executeAinow = async (
  obj: ChatReq,
  cb: (res: ChatRes) => void,
  method = 'request',
  abortSignal?: AbortSignal
): Promise<ChatRes> => {
  if (!AinowPlugIn) {
    await ainowInit()
  }

  return new Promise((resolve, reject) => {
    const res = AinowPlugIn[method](JSON.stringify(obj), (text: string) => {
      if (abortSignal?.aborted) {
        reject(abortSignal.reason)
      }
      abortSignal?.addEventListener('abort', () => {
        reject(abortSignal.reason)
      })
      try {
        const response: ChatRes = JSON.parse(text)
        console.log(obj, '======>response:', response)
        cb && cb(response)
        if (response.data.done === true) {
          resolve(response)
        }
      } catch (error) {
        console.error('executeAinow error:', error, obj)
        reject(error)
      }
    })
    if (method === 'call') {
      resolve(res)
    }
    if (!res) {
      console.error('executeAinow request return false:', obj)
      reject('request return false')
    }
  })
}
