import { ChatOllama } from "@langchain/ollama";
import {
  HumanMessage,
  AIMessage,
  SystemMessage,
} from "@langchain/core/messages";

import {
  type Message,
  type ChatOptions,
  type ChatResponse,
  type ChatError,
  Role,
  ChatStreamChunk,
} from "../types/chat";
import { DEFAULT_CONFIG } from "../config/default";

export class ChatService {
  private model: ChatOllama;

  constructor(options: Partial<ChatOptions> = {}) {
    this.model = new ChatOllama({
      baseUrl: DEFAULT_CONFIG.ollama.baseUrl,
      model: options.model || DEFAULT_CONFIG.ollama.model,
      temperature: options.temperature || DEFAULT_CONFIG.ollama.temperature,
    });
  }

  private convertToLangChainMessages(messages: Message[]) {
    return messages.map((msg) => {
      if (msg.role === Role.User) {
        return new HumanMessage(msg.content);
      } else if (msg.role === Role.System) {
        return new SystemMessage(msg.content);
      }
      return new AIMessage(msg.content);
    });
  }

  async chat(messages: Message[]): Promise<ChatResponse> {
    try {
      const langChainMessages = this.convertToLangChainMessages(messages);
      const response = await this.model.invoke(langChainMessages);

      return {
        message: response.content,
        tokens: 0, // Note: Ollama currently doesn't provide token count directly
      };
    } catch (error: any) {
      const chatError: ChatError = {
        code: "CHAT_ERROR",
        message: error.message || "Failed to get response from Ollama",
      };
      throw chatError;
    }
  }

  // 流式聊天方法 - 使用异步生成器
  async *chatStream(messages: Message[]): AsyncGenerator<ChatStreamChunk> {
    try {
      const langChainMessages = this.convertToLangChainMessages(messages);
      const stream = await this.model.stream(langChainMessages);

      // 处理每个数据块
      for await (const chunk of stream) {
        if (chunk.content) {
          yield {
            chunk: chunk.content,
            done: false,
          };
        }
      }

      // 发送完成标记
      yield {
        chunk: "",
        done: true,
      };
    } catch (error: any) {
      throw {
        code: "CHAT_STREAM_ERROR",
        message: error.message || "Failed to get stream response from Ollama",
      } as ChatError;
    }
  }
}
// 创建单例实例
export const createChatService = (options?: Partial<ChatOptions>) => {
  return new ChatService(options);
};
// function chatStream(messages: any, arg1: any) {
//   throw new Error("Function not implemented.");
// }
