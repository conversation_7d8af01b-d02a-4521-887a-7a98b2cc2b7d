import {
  // AIMessage,
  HumanMessage,
  SystemMessage,
} from "@langchain/core/messages";
import { ChatOllama } from "@langchain/ollama";
// import { Client } from "langsmith";
export const LANGSMITH_TRACING = true;
export const LANGSMITH_ENDPOINT = "https://api.smith.langchain.com";
export const LANGSMITH_API_KEY =
  "***************************************************";
export const LANGSMITH_PROJECT = "pr-linear-croissant-55";
export const OPENAI_API_KEY = "<your-openai-api-key>";

process.env.LANGCHAIN_TRACING_V2 = "true";
process.env.LANGCHAIN_ENDPOINT = LANGSMITH_ENDPOINT;
process.env.LANGCHAIN_API_KEY = LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = LANGSMITH_PROJECT;

async function main() {
  // 初始化 LangSmith 客户端
  // const client = new Client({
  //   apiUrl: LANGSMITH_ENDPOINT,
  //   apiKey: LANGSMITH_API_KEY,
  // });

  // 配置 ChatOllama 模型
  const model = new ChatOllama({
    model: "deepseek-r1:1.5b", // 修改为 Ollama 模型
    baseUrl: "http://127.0.0.1:11434", // 添加 Ollama API 地址
    // LangSmith 追踪配置
  });

  const messages = [
    new SystemMessage("Translate the following from English into Italian"),
    new HumanMessage("hi!"),
  ];

  //   const result: AIMessage = await model.invoke(messages);
  //   console.log(result);

  const stream = await model.stream(messages);

  const chunks = [];
  for await (const chunk of stream) {
    // @ts-ignore
    chunks.push(chunk);
    // console.log(`${chunk.content}|`);
    console.log("Content:", chunk.content);
    console.log("Generation Info:", {
      finishReason: chunk.response_metadata.finish_reason,
      completion: chunk.id,
      toolCalls: chunk.tool_calls,
    });
    console.log("------------------");
  }
}

main().catch(console.error);
