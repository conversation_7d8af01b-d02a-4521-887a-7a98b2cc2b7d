import { createChatService } from "../models/chat";
import { Message, Role } from "../types/chat";

const chatService = createChatService({
  model: "deepseek-r1:1.5b",
  temperature: 0.7,
});

async function handleChatRequest(messages: Message[]) {
  try {
    const response = await chatService.chat(messages);
    return response;
  } catch (error) {
    console.error("Chat error:", error);
    throw error;
  }
}

async function main() {
  const res = await handleChatRequest([
    {
      role: Role.System,
      content: "You are a helpful assistant.",
    },
    {
      role: Role.User,
      content: "你好",
    },
  ]);
  console.log(res);
}

main();
