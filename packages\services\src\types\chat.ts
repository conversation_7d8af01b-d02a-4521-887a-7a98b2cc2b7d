import { MessageContent } from '@langchain/core/messages'

export enum Role {
  User = 'user',
  System = 'system',
  Assistant = 'assistant'
}

export interface Message {
  role: Role
  content: string
}
export interface ChatOptions {
  model: string // ollama模型名称
  temperature?: number
  maxTokens?: number
}

export interface ChatResponse {
  message: MessageContent
  tokens: number
}

export interface ChatStreamChunk {
  chunk: MessageContent
  done: boolean
}

export interface ChatError {
  code: string
  message: string
}
