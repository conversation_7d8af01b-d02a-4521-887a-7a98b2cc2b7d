export interface User {
  id: string
  name: string
  email: string
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
}
export enum AinowCommand {
  CHAT = 'ainow.chat',
  PLUGINID = 'main'
}
export interface ChatReq<T = { query: string }> {
  command: AinowCommand
  plugInID: AinowCommand
  data: T
}
export enum IntentCategory {
  DEVICE_VANTAGE = 'DEVICE_VANTAGE',
  DEVICE_WINDOWS = 'DEVICE_WINDOWS'
}
export interface ChatResData {
  done: boolean
  intentType: string
  intentCategory: IntentCategory
  chatAnswer: string
  DeviceIntentContent?: string
  lid: string
}
export interface ChatRes {
  data: ChatResData
}
