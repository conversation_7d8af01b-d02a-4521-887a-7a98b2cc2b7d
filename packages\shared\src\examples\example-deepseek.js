const path = require("path");

const envPath = path.resolve(__dirname, "../../../../.env");
require("dotenv").config({ path: envPath });

const facadePath = path.resolve(__dirname, "../facade");
const { getModels, streamChat } = require(facadePath);

// 用终端参数 --apikey=xxx 传递值
const apikey = (
  process.argv.find((item) => item.startsWith("--apikey=")) || ""
).slice("--apikey=".length);
if (!apikey) throw new Error("no apikey");

getModels("deepseek", apikey).then((result) => {
  const { models, error } = result;
  console.log("模型列表", models, error);

  if (error || !models?.length) return;

  streamChat("hello! who r u?", {
    chatProvider: "deepseek",
    chatModel: models[0].id, // deepseek-chat
    streamCallback(data) {
      console.log(data.textResponse);
    },
  });
});
