const path = require("path");
const LLMResponse = require("../utils/helpers/LLMResponse");

const envPath = path.resolve(__dirname, "../../../../.env");
require("dotenv").config({ path: envPath });

const facadePath = path.resolve(__dirname, "../facade");
const { getModels, streamChat } = require(facadePath);

getModels("ollama", null, "http://localhost:11434").then(async (result) => {
  const { models, error } = result;
  console.log("ollama模型列表", models, error);

  if (error || !models?.length) return;

  const myResponse = new LLMResponse();
  myResponse.on("data", (data) => {
    const json = JSON.parse(data.replace(/^data:\s*/, ""));
    console.log(json.textResponse);
  });
  streamChat("hello! who r u?", {
    response: myResponse,
    chatProvider: "ollama",
    chatModel: models[0].id,
  });
});
