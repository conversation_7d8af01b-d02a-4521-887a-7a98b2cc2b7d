const path = require("path");

const envPath = path.resolve(__dirname, "../../../../.env");
require("dotenv").config({ path: envPath });

const facadePath = path.resolve(__dirname, "../facade");
const { getModels, streamChat } = require(facadePath);

// 用终端参数 --apikey=xxx 传递值
const apikey = (
  process.argv.find((item) => item.startsWith("--apikey=")) || ""
).slice("--apikey=".length);
if (!apikey) throw new Error("no apikey");

(async function () {
  console.log("开始获取...", process.env.PROXY_URL);
  const { models, error } = await getModels("openai", apikey);
  console.log("模型列表", models.length, error);

  if (error || !models?.length) return;

  const streamCallback = (data) => {
    console.log(data.textResponse);
  };

  streamChat("hello! who r u?", {
    chatProvider: "openai",
    chatModel: "gpt-3.5-turbo", // models[0].id,
    streamCallback,
  });
})();
