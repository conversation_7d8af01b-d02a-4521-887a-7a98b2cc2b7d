const { chatSync, streamChat } = require('./utils/chats/chatHandler')
const { getCustomModels } = require('./utils/helpers/customModels')
const LLMResponse = require('./utils/helpers/LLMResponse')
const LLMModels = require('./utils/helpers/LLMModels')
const formatChunk = require('./utils/helpers/chat/formatChunk')
const { fetchOpenAICompatibilityStreamChat } = require('./utils/helpers/openaiCompat')
const getPrisma = require('./utils/prisma')
const runPrismaCommand = require('./utils/prisma/runPrismaCommand')
const getDBConstants = require('./utils/prisma/dbConstants')
const DBModels = require('./models')
const { recentChatHistory } = require('./utils/chats')

/**
 * @type {import('./types').ISharedFacade}
 */
const SharedFacade = {
  getPrisma,
  runPrismaCommand,
  getDBConstants,
  DBModels,
  LLMResponse,
  LLMModels,
  formatChunk,
  fetchOpenAICompatibilityStreamChat,
  recentChatHistory,
  getModels: getCustomModels,
  chat: chatSync,
  streamChat
}

module.exports = SharedFacade
