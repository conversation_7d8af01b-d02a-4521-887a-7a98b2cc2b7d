-- RedefineTables
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_ThreadMessage" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "accountId" TEXT NOT NULL,
    "threadId" TEXT NOT NULL,
    "chatProvider" TEXT NOT NULL DEFAULT '',
    "chatModel" TEXT NOT NULL DEFAULT '',
    "prompt" TEXT NOT NULL,
    "promptId" TEXT NOT NULL,
    "response" TEXT NOT NULL,
    "updateTime" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createTime" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ThreadMessage_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_ThreadMessage" ("accountId", "chatModel", "chatProvider", "id", "prompt", "promptId", "response", "threadId", "updateTime") SELECT "accountId", "chatModel", "chatProvider", "id", "prompt", "promptId", "response", "threadId", "updateTime" FROM "ThreadMessage";
DROP TABLE "ThreadMessage";
ALTER TABLE "new_ThreadMessage" RENAME TO "ThreadMessage";
CREATE UNIQUE INDEX "ThreadMessage_promptId_key" ON "ThreadMessage"("promptId");
CREATE INDEX "ThreadMessage_accountId_idx" ON "ThreadMessage"("accountId");
CREATE TABLE "new_Thread" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "setting" TEXT,
    "chatProvider" TEXT,
    "chatModel" TEXT,
    "vectorTag" TEXT,
    "vectorSearchMode" TEXT DEFAULT 'default',
    "updateTime" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createTime" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "Thread_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_Thread" ("accountId", "chatModel", "chatProvider", "id", "name", "setting", "updateTime", "vectorSearchMode", "vectorTag") SELECT "accountId", "chatModel", "chatProvider", "id", "name", "setting", "updateTime", "vectorSearchMode", "vectorTag" FROM "Thread";
DROP TABLE "Thread";
ALTER TABLE "new_Thread" RENAME TO "Thread";
CREATE INDEX "Thread_accountId_idx" ON "Thread"("accountId");
PRAGMA foreign_key_check;
PRAGMA foreign_keys=ON;
