import { PrismaClient, Prisma } from '@prisma/client'
import LLMResponse from './utils/helpers/LLMResponse'
import { DBModelsGetterMap } from './models'
import * as AINowTypes from './ainow/types'

export interface IContextDB {
  getDBPath(): string
  getPrismaEnginesDir(): string
  getPrismaEnginesBase?: () => string
  getSchemaPrismaPath?: () => string
  getPrismaPath?: () => string | undefined
  getEnvPath?: () => string
}

export interface PrismaMigration {
  id: string
  checksum: string
  finished_at: string
  migration_name: string
  logs: string
  rolled_back_at: string
  started_at: string
  applied_steps_count: string
}

export type TypeDBConstants = {
  isDev: boolean
  dbPath: string
  dbUrl: string
  latestMigration: string
  mePath: string
  qePath: string
  prismaPath?: string
}

export interface ChatOption {
  prompt?: string | null
  promptId?: string
  temperature?: number
  chatProvider: string // 模型提供商
  chatModel: string // 模型
  streamCallback?: (res: Response, data: any) => void
  response?: Writable
  accountId?: string
  threadId?: string
}

export type TypeGetPrisma = (ctx: IContextDB) => PrismaClient

export type TypeGetDBConstants = (ctx: IContextDB) => TypeDBConstants

export type TypeDBModels = {
  [K in Prisma.ModelName as `get${K}Model`]: DBModelsGetterMap[K]
}

export type RunPrismaCmdParam = {
  command: string[]
  ctx: IContextDB
  prismaPath?: string
}
export type TypeRunPrismaCommand = (param: RunPrismaCmdParam) => Promise<number>

export type TypeFormatChunk = (chunk: any) => {
  datas: any
  message: string
  token: string
}

export type TypeRecentChatHistory = (option: { chatOpt: ChatOption; messageLimit: number }) => {
  rawHistory: Array<unknown>
  chatHistory: Array<unknown>
}

export type TypeGetModels = (
  provider: 'ollama' | 'ainow' | 'dfai' | 'edge',
  apiKey?: string,
  basePath?: string
) => Promise<{
  models: { id: string }[]
  error?: Error | string
}>

export type TypeStreamChat = (
  message?: string,
  chatOption?: ChatOption,
  abortSignal?: AbortSignal
) => Promise<ResponseObject>

export type TypeFetchOpenAICompatStream = (
  url: string,
  params: {
    accountId: string
    threadId: string
    provider: string
    model: string
    message: string
    messageId?: string
    callback?: (data: any) => void
    chekcIsDone?: (chunk: string) => boolean
    doneCallback?: () => void
    abortSignal?: AbortSignal
  }
) => Promise<void>

export type TypeListModels = (providerName: string) => Promise<Array<{ id: string }>>

export interface ISharedFacade {
  getPrisma: TypeGetPrisma
  getDBConstants: TypeGetDBConstants
  DBModels: TypeDBModels
  runPrismaCommand: TypeRunPrismaCommand
  formatChunk: TypeFormatChunk
  fetchOpenAICompatibilityStreamChat: TypeFetchOpenAICompatStream
  recentChatHistory: TypeRecentChatHistory
  getModels: TypeGetModels
  streamChat: TypeStreamChat
  LLMModels: {
    listModels: TypeListModels
  }
  LLMResponse
  AINowTypes
}
