const fs = require("fs");
const { v4: uuidv4 } = require("uuid");
const { getLLMProvider } = require("../helpers");
const { writeResponseChunk } = require("../helpers/chat/responses");
const { chatPrompt, recentChatHistory } = require("./index");
const LLMResponse = require("../helpers/LLMResponse");

function _log(text, ...args) {
  console.log(`\x1b[32m[chatHandler]\x1b[0m ${text}`, ...args);
  // try {
  // if (process.versions.electron) {
  //   const timestamp = new Date().toISOString();
  //   const logMessage = `[${timestamp}] ${text} ${args.join(' ')}\n`;
  //   fs.appendFile('D:\log.txt', logMessage, (err) => {
  //     if (err) {
  //       console.error('Failed to append log message:', err);
  //     }
  //   });
  // }
  // } catch(ex) {
  //   console.log(ex)
  // }
}

/**
 * Handle synchronous chats
 * @param {string} [message]
 * @param {import('../../types.d.ts').ChatOption} [ChatOption]
 * @returns {Promise<ResponseObject>}
 */
async function chatSync(message = null, chatOpt = {}) {
  const uuid = uuidv4();

  const LLMConnector = getLLMProvider({
    provider: chatOpt?.chatProvider,
    model: chatOpt?.chatModel,
  });

  let contextTexts = [];
  let sources = [];
  contextTexts = [...contextTexts];
  sources = [...sources];

  const messageLimit = 20; 
  const rawHistory = [];

  const messages = await LLMConnector.compressMessages(
    {
      systemPrompt: chatPrompt(chatOpt),
      userPrompt: message,
      contextTexts,
    },
    rawHistory
  );

  const { textResponse, metrics: performanceMetrics } =
    await LLMConnector.getChatCompletion(messages, {
      temperature: chatOpt?.temperature ?? LLMConnector.defaultTemp,
    });

  if (!textResponse) {
    return {
      id: uuid,
      type: "abort",
      textResponse: null,
      sources: [],
      close: true,
      error: "No text completion could be completed with this input.",
      metrics: performanceMetrics,
    };
  }

  return {
    id: uuid,
    type: "textResponse",
    close: true,
    error: null,
    chatId: null, // chat.id,
    textResponse,
    sources,
    metrics: performanceMetrics,
  };
}

/**
 * Handle streamable HTTP chunks for chats
 * @type {import('../../types.d.ts').TypeStreamChat}
 */
async function streamChat(message = null, chatOpt = {}, abortSignal = void 0) {
  const response = chatOpt.response || new LLMResponse();
  // _log("streamChat ===START===", response)

  if (abortSignal) {
    abortSignal.addEventListener("abort", () => {
      console.log('[shared::chatHandler] SMREAMCHAT ABORT', !!stream.abortController?.signal, abortSignal.reason)
      writeResponseChunk(response, {
        uuid,
        type: "abort",
        error: false,
        close: true,
      });
      stream.abortController?.abort()
      response.end();
    });
  }

  response.on("data", (chunk) => {
    try {
      // _log("streamChat ===ON_DATA===", chunk)
      const data = JSON.parse(chunk.replace(/^data:\s*/, ""));
      if (data.error) {
        throw new Error(data.error);
      } else if (data.close) {
        console.log("FINISH!");
      } else {
        chatOpt.streamCallback?.(data);
      }
    } catch (ex) {
      console.warn(ex);
    }
  });
  response.on("finish", (data) => { });

  const uuid = uuidv4();
  const updatedMessage = message; //await grepCommand(message, null);

  const LLMConnector = getLLMProvider({
    provider: chatOpt?.chatProvider,
    model: chatOpt?.chatModel,
  });

  let completeText;
  let metrics = {};
  let contextTexts = [];
  let sources = [];

  const messageLimit = 20;
  const { rawHistory, chatHistory } = await recentChatHistory({
    chatOpt,
    messageLimit,
  });

  const { fillSourceWindow } = require("../helpers/chat");
  const filledSources = fillSourceWindow({
    nDocs: 4, // workspace?.topN || 4,
    // searchResults: vectorSearchResults.sources,
    history: rawHistory,
    // filterIdentifiers: pinnedDocIdentifiers,
  });

  contextTexts = [...contextTexts, ...filledSources.contextTexts];
  sources = [...sources];

  const messages = await LLMConnector.compressMessages(
    {
      systemPrompt: chatPrompt(chatOpt),
      userPrompt: updatedMessage,
      contextTexts,
      chatHistory,
      // attachments
    },
    rawHistory
  );
  // console.log("[chatHandler.js]聊天历史🦖", rawHistory.length, messages);

  let stream;
  if (LLMConnector.streamingEnabled() !== true) {
    console.log(
      `\x1b[31m[STREAMING DISABLED]\x1b[0m Streaming is not available for ${LLMConnector.constructor.name}. Will use regular chat method.`
    );
    const { textResponse, metrics: performanceMetrics } =
      await LLMConnector.getChatCompletion(messages, {
        temperature: chatOpt?.temperature ?? LLMConnector.defaultTemp,
      });
    completeText = textResponse;
    metrics = performanceMetrics;
    writeResponseChunk(response, {
      uuid,
      sources,
      type: "textResponseChunk",
      textResponse: completeText,
      close: true,
      error: false,
      metrics,
    });
  } else {
    stream = await LLMConnector.streamGetChatCompletion(messages, {
      temperature: chatOpt?.temperature ?? LLMConnector.defaultTemp
    });
    completeText = await LLMConnector.handleStream(response, stream, {
      uuid,
      sources,
    });
    // _log("streamChat ===completeText===", completeText)
    metrics = stream.metrics;
  }

  if (completeText?.length > 0) {
    writeResponseChunk(response, {
      uuid,
      type: "finalizeResponseStream",
      close: true,
      error: false,
      chatId: null, // chat.id,
      metrics,
    });
    return response;
  }

  writeResponseChunk(response, {
    uuid,
    type: "finalizeResponseStream",
    close: true,
    error: false,
  });

  // _log("streamChat ===FUNC_RETURN===", completeText)
  return response;
}

module.exports = {
  chatSync,
  streamChat,
};
