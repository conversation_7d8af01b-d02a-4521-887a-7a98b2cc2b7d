const { getThreadMessageModel } = require("../../models/ThreadMessageModel");
const { convertToPromptHistory } = require("../helpers/chat/responses");

/**
 * @type {import('../../types').TypeRecentChatHistory} 
 */
async function recentChatHistory({
  chatOpt,
  messageLimit = 20,
}) {
  try {
    let rawHistory = chatOpt.accountId && chatOpt.threadId
      ? (await getThreadMessageModel(global.prisma).where({
        accountId: chatOpt.accountId,
        threadId: chatOpt.threadId,
      }, messageLimit, { id: "desc" })).reverse()
      : [];
    rawHistory = rawHistory.map(
      history => {
        try {
          const data = JSON.parse(history.response);
          if (!data.text)
            history = {
              ...history,
              response: JSON.stringify({ text: history.response })
            }
        } catch (ex) {
          console.log(ex)
        }
        return history
      })
    return { rawHistory, chatHistory: convertToPromptHistory(rawHistory) };
  } catch (error) {
    console.error("Error fetching recent chat history:", error);
  }
}

function chatPrompt(workspace) {
  return (
    (workspace?.prompt || workspace?.openAiPrompt) ??
    "Given the following conversation, relevant context, and a follow up question, reply with an answer to the current question the user is asking. Return only your response to the question given the above information following the users instructions as needed."
  );
}

module.exports = {
  recentChatHistory,
  chatPrompt,
};
