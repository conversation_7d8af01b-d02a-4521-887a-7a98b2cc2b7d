const { AINOW_MODEL, PROVIDER } = require('@ainow/types')
const { getCustomModels: getModels } = require('./customModels')

/**
 * @type {import('../../types').TypeListModels} 
 */
async function listModels(providerName) {
  switch (providerName) {
    case PROVIDER.DFAI:
      return [{ id: 'dfai', name: 'dfai' }]
    case PROVIDER.EDGE:
      return [
        { id: 'edge.chat', name: 'chat' },
        { id: 'edge.web', name: 'online search' }
      ]
    case PROVIDER.AINOW:
      // 判断是本地ainow模型 使用本地列表
      return [
        { id: AINOW_MODEL.AINOW_MODEL_1, name: AINOW_MODEL.AINOW_MODEL_1 }
        // { id: AINOW_MODEL.AINOW_MODEL_2, name: AINOW_MODEL.AINOW_MODEL_2 }
      ]
    case PROVIDER.EMBEDDING:
      const response = await fetch('https://embedding.lenovo.com/v1/models', {
        method: 'GET',
        headers: {
          accept: 'application/json'
        }
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const json = await response.json()
      // @ts-ignore json
      return json?.data || []
    case PROVIDER.OLLAMA:
      const { models, error } = await getModels.call(
        null,
        providerName,
        void 0,
        'http://localhost:11434'
      )
      if (error) throw error
      return models
    default:
      throw new Error('unknown providerName')
  }
}

module.exports = {
    listModels
}