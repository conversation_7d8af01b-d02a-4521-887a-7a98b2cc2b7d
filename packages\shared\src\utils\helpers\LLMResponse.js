const { Writable } = require("stream");
const EventEmitter = require("events");

class LLMResponse extends Writable {
  constructor() {
    super();
    this.data = [];
    this.headers = {};
    this.statusCode = 200;
    this.finished = false;
  }

  write(chunk) {
    // console.log('[LLMResponse] write begin', chunk)
    this.emit("data", chunk);
    this.data.push(chunk);
    // console.log('[LLMResponse] write end', this.data.length)
    return true; // 表示成功接收数据
  }

  end() {
    this.emit("finish");
    this.finished = true;
  }

  setHeader(name, value) {
    this.headers[name] = value;
  }

  getHeader(name) {
    return this.headers[name];
  }

  removeHeader(name) {
    delete this.headers[name];
  }
}
Object.assign(LLMResponse.prototype, EventEmitter.prototype);

module.exports = LLMResponse;
