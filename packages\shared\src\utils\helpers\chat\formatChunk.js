/**
 * @type {import('../../../types').TypeFormatChunk}
 */
function formatChunk(chunk) {
  const result = []

  const message = chunk?.choices?.[0]
  const token = message?.delta?.content

  if (token) {
    result.push({
      // uuid,
      // sources: [],
      type: 'textResponseChunk',
      textResponse: token,
      close: false,
      error: false
    })
  }

  // LocalAi returns '' and others return null on chunks - the last chunk is not "" or null.
  // Either way, the key `finish_reason` must be present to determine ending chunk.
  if (
    message?.hasOwnProperty('finish_reason') && // Got valid message and it is an object with finish_reason
    message.finish_reason !== '' &&
    message.finish_reason !== null
  ) {
    result.push({
      // uuid,
      // sources,
      type: 'textResponseChunk',
      textResponse: '',
      close: true,
      error: false
    })
  }

  return {
    datas: result,
    message,
    token
  }
}

module.exports = formatChunk
