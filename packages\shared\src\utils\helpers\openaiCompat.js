const formatChunk = require('./chat/formatChunk')
const { recentChatHistory } = require('../chats')

/**
 * @type {import('../../types').TypeFetchOpenAICompatStream}
 */
async function fetchOpenAICompatibilityStreamChat(
  url,
  {
    accountId, // 账号
    threadId, // 对话
    provider,
    model,
    message,
    callback,
    doneCallback,
    chekcIsDone,
    abortSignal
  }
) {
  // TODO 账号等逻辑
  console.log('[fetchOpenAICompatibilityStreamChat]', accountId, threadId, provider)

  let history = []
  try {
    const { chatHistory } = await recentChatHistory({
      chatOpt: {
        accountId,
        threadId
      },
      messageLimit: 3
    })
    history = chatHistory.filter((h) => h['role'] === 'assistant')
  } catch (ex) {
    console.error(ex)
  }

  const options = {
    method: 'POST',
    headers: {
      Authorization: 'Bearer ainow',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model,
      // name: 'test',
      messages: [
        ...history,
        {
          role: 'user',
          content: message
        }
      ],
      stream: true
    }),
    signal: abortSignal
  }
  try {
    const response = await fetch(url, options)
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    while (true) {
      const { done, value } = await reader.read()
      const chunk = decoder
        .decode(value, { stream: true })
        .replace(/^data:\s*/, '')
        .replace(/[\r\n]*$/, '')
      const customDone = typeof chekcIsDone === 'function' ? chekcIsDone(chunk) : false
      if (done || customDone) {
        typeof doneCallback === 'function' && doneCallback({ provider, model, threadId, close: true })
        break
      }

      try {
        const json = JSON.parse(chunk)
        const { datas } = formatChunk(json)
        for (const chunkData of datas) {
          const data = Object.assign(chunkData, { provider, model, threadId })
          typeof callback === 'function' && callback(data)
        }
      } catch (ex) {
        console.log(ex.message, 'CHUNK==:', chunk, '==KNUHC')
      }
    }
  } catch (err) {
    console.error('Request error:', err)
  }
}

module.exports = {
  fetchOpenAICompatibilityStreamChat
}
