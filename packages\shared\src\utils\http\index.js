const { jsonrepair } = require("jsonrepair");
const extract = require("extract-json-from-string");

function safeJsonParse(jsonString, fallback = null) {
  if (jsonString === null) return fallback;

  try {
    return JSON.parse(jsonString);
  } catch {}

  if (jsonString?.startsWith("[") || jsonString?.startsWith("{")) {
    try {
      const repairedJson = jsonrepair(jsonString);
      return JSON.parse(repairedJson);
    } catch {}
  }

  try {
    return extract(jsonString)[0];
  } catch {}

  return fallback;
}

module.exports = {
  safeJsonParse,
};
