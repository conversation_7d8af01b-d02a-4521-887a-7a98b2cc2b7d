// TODO ainow特定的类型加上前缀

export enum PROVIDER {
  AINOW = 'ainow',
  OLLAMA = 'ollama',
  DFAI = 'dfai',
  EDGE = 'edge',
  EMBEDDING = 'embedding'
}
export enum AINOW_MODEL {
  AINOW_MODEL_1 = 'ainow.chat',
  AINOW_MODEL_2 = 'ainow.find.all.files.pkb',
  AINOW_MODEL_3 = 'ainow-model-3',
  AINOW_MODEL_4 = 'ainow-model-4',
  AINOW_MODEL_5 = 'ainow-model-5',
  AINOW_MODEL_6 = 'ainow-model-6'
}
export enum PLUGINID {
  MAIN = 'main'
}
export interface ChatReq<T = object | null> {
  command: AINOW_MODEL
  plugInID: PLUGINID
  data: T
}
export enum IntentCategory {
  DEVICE_VANTAGE = 'DEVICE_VANTAGE',
  DEVICE_WINDOWS = 'DEVICE_WINDOWS'
}
export interface ChatResData {
  done: boolean
  intentType: string
  intentCategory: IntentCategory
  chatAnswer: string
  DeviceIntentContent?: string
  lid: string
}
export interface ChatRes {
  data: ChatResData
}
export interface IASKModel {
  msg: null | string
  markingWords: {
    content: null | string
  }
  // askType: ASK_TYPE,
  ChatText?: null | string
  sessionType: sessionType
  translate?: null | {
    dest: string
    source: string
  }
  type?: null | TYPE
  chatType?: CHAT_TYPE
  modelCode?: string
}
export enum TYPE {
  SELF_AWARENESS = 'SELF_AWARENESS',
  WORK_ASSISTANT_IMAGE_SEARCH = 'WORK_ASSISTANT_IMAGE_SEARCH',
  GENERAL_GENERATION = 'GENERAL_GENERATION',
  WORK_ASSISTANT_TRANSLATION = 'WORK_ASSISTANT_TRANSLATION', // 翻译
  WORK_ASSISTANT_DOCUMENT_SUMMARY = 'WORK_ASSISTANT_DOCUMENT_SUMMARY', // Document Summary
  WORK_ASSISTANT_DOCUMENT_COMPARE = 'WORK_ASSISTANT_DOCUMENT_COMPARE', // Document Comparison
  WORK_ASSISTANT_DOCUMENT_SEARCH = 'WORK_ASSISTANT_DOCUMENT_SEARCH', // Document Search
  DOCUMENT_INSIGHTS = '', //  Document Insights
  WORK_ASSISTANT_DOCUMENT_POLISHING = 'WORK_ASSISTANT_DOCUMENT_POLISHING', // Content Polishing
  ERROR = 'ERROR',
  DEVICE_DEFAULT = 'DEVICE_DEFAULT'
}
export enum ChatCategory {
  GENERAL = 'GENERAL',
  WORK = 'WORK',
  DEVICE_VANTAGE = 'DEVICE_VANTAGE',
  DEVICE_WINDOWS = 'DEVICE_WINDOWS',
  SERVICE = 'SERVICE',
  TIPS = 'TIPS',
  MEETING = 'MEETING',
  LENOVO_DEVICE_YB9 = 'LENOVO_DEVICE_YB9',
  LENOVO_DEVICE_TBPLUS = 'LENOVO_DEVICE_TBPLUS'
}
export enum TemplateID {
  DeviceToggleSwitchWithCheckBox = 'DeviceToggleSwitchWithCheckBox',
  DeviceRadioButton = 'DeviceRadioButton',
  DeviceDataGrid = 'DeviceDataGrid',
  Info = 'Info',
  Error = 'Error',
  JumpWindows = 'JumpWindows',
  BLANK = 'BLANK'
}

export interface DOCITEM {
  targetPath: string
  [key: string]: any
}

export interface IChatParams<A = unknown> {
  accountId: any
  threadId: any
  provider: string
  model: string
  message?: string
  messageId: string
  ainowParams?: A
  from?: string
}

export interface IChatResponseParams<T = object> {
  messageId: string
  provider: string
  model: string
  threadId: string
  json: T
}

export enum WindowActions {
  MIN,
  MAX,
  CLOSE
}
export enum WindowType {
  MAIN = 'main',
  MINI = 'mini',
  WEBSITE = 'website'
}
//向ainow小窗提问的类型  本地search--0 本地ask--1 Explain--2  Translate--3 Polish--4 Summarize--5 Cloud提问--6
export enum ASK_TYPE {
  SEARCH = 0,
  ASK,
  EXPLAIN,
  TRANSLATE,
  POLISH,
  SUMMARIZE,
  CLOUD_ASK,
  PCASSISTANT
}
export enum sessionType {
  AINowGeneral = 'AINowGeneral',
  AINowPKB = 'AINowPKB',
  AINowMini = 'AINowMini',
  AINowCloud = 'AINowCloud',
  AINowPC = 'AINowPC'
}
export enum CLOUD_MODEL {
  GPT_40_MINI = 'GPT_4O_MINI',
  GPT_4O = 'GPT_4O'
}
export enum CHAT_TYPE {
  CHAT = 'CHAT',
  CLOUDCHAT = 'CLOUDCHAT'
}
export enum ipcfunc {
  AIGCChat = 'AIGCChat',
  DeviceControl = 'DeviceControl'
}
