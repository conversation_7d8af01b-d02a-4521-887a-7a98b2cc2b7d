chcp 65001

cd "%workspace%\\ainow_edge"

:: build unpacked
;;rmdir /s /q "%workspace%\\ainow_edge\\apps\\edge\\dist"
set PATH=%PATH%;C:\Users\<USER>\AppData\Local\pnpm
call pnpm i
call pnpm build:unpack || IF ERRORLEVEL 1 GOTO buildErr
if exist "%workspace%\\ainow_edge\\apps\\edge\\dist\\win-unpacked" (
    echo "build unpacked OK!"
) else (
    echo "build unpacked FAILED!"
    goto buildErr
)

::sign unpackeds
call "D:\\ainow_ota\\sign.exe" -ts -dir "%workspace%\\ainow_edge\\apps\\edge\\dist"

:: installer
call pnpm build:installer || IF ERRORLEVEL 1 GOTO buildErr

set exedir=%workspace%\\ainow_edge\\apps\\edge\\dist

:: sign the installer-exe
call "D:\\ainow_ota\\sign.exe" -ts -dir "%exedir%"

:: copy to ftp dir
set DesDir2=E:\package\
cd "%exedir%"
setlocal enabledelayedexpansion
for %%F in (Lenovo*.exe) do (
    set "name=%%~nF"
    copy "%%F" "%DesDir2%!name!.%BUILD_NUMBER%.exe"
)
